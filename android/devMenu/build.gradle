
buildscript {
    repositories {
        mavenCentral()
        maven {
            url 'https://maven.google.com/'
            name 'Google'
        }
        maven {
            // All of React Native (JS, Obj-C sources, Android binaries) is installed from npm
            url "$rootDir/../node_modules/react-native/android"
        }
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:4.2.2'
    }
}

apply plugin: 'com.android.library'

android {
    compileSdkVersion rootProject.properties.get('compileSdkVersion', 29)
    buildToolsVersion rootProject.properties.get('buildToolsVersion', '29.0.2')

    defaultConfig {
        minSdkVersion 21
        targetSdkVersion rootProject.properties.get('targetSdkVersion', 29)
        versionCode 1
        versionName "1.0"
    }
    lintOptions {
        abortOnError false
    }
}

repositories {
    mavenCentral()
    maven {
        url 'https://maven.google.com/'
        name 'Google'
    }
}

dependencies {
    implementation 'com.facebook.react:react-native:+'
}
