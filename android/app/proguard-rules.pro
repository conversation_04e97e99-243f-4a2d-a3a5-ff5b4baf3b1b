# 移除调试信息

-keepattributes *Annotation*


# okhttp3
-keep class okhttp3.** { *; }
-keep interface okhttp3.** { *; }
-keepattributes Signature, Annotation

# noro-pro
-keep class com.prestamo.credito.dinero.efectivo.facil.rapido.noro.plus.** { *; }
-keep interface com.prestamo.credito.dinero.efectivo.facil.rapido.noro.plus.** { *; }

-keep class com.google.gson.reflect.** {
    *;
}

# assets
-keepdirectories assets

-keep class com.facebook.soloader.** { *; }

# face++
-keep class com.megvii.meglive_sdk.** { *; }

# zhiSycData
 -keep class com.zhi.syc.data.beans.** {*;}
 -keep class com.zhi.syc.data.services.ASNetParams {*;}
 -keep interface com.zhi.syc.data.** {*;}

# installreferrer
-keep public class com.android.installreferrer.** { *; }

# adjsut
-keep class com.adjust.sdk.** { *; }
-keep class com.google.android.gms.common.ConnectionResult {
    int SUCCESS;
}
-keep class com.google.android.gms.ads.identifier.AdvertisingIdClient {
    com.google.android.gms.ads.identifier.AdvertisingIdClient$Info getAdvertisingIdInfo(android.content.Context);
}
-keep class com.google.android.gms.ads.identifier.AdvertisingIdClient$Info {
    java.lang.String getId();
    boolean isLimitAdTrackingEnabled();
}

-keep class com.google.android.gms.ads.identifier.AdvertisingIdClient {*;}


-keep class com.facebook.hermes.unicode.** { *; }
-keep class com.facebook.jni.** { *; }

# okhttp3
-keep class okhttp3.** { *; }
-keep interface okhttp3.** { *; }
-keepattributes Signature, Annotation

-keep class androidx.credentials.playservices.** {
  *;
}

# Keep entry points and classes referenced in AndroidManifest
-keep public class * extends android.app.Activity
-keep public class * extends android.app.Application
-keep public class * extends android.app.Service
-keep public class * extends android.content.BroadcastReceiver
-keep public class * extends android.content.ContentProvider
-keep public class * extends com.facebook.react.ReactActivity
-keep public class * extends com.zhi.syc.data.MyApplication

# Keep classes that are referenced by native code
-keepclasseswithmembernames class * {
    native <methods>;
}


