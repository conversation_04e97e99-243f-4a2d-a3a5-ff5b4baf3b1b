apply plugin: "com.android.application"
apply plugin: "org.jetbrains.kotlin.android"
apply plugin: "com.facebook.react"
apply plugin: 'com.google.gms.google-services'

/**
 * This is the configuration block to customize your React Native Android app.
 * By default you don't need to apply any configuration, just uncomment the lines you need.
 */
react {
    /* Folders */
    //   The root of your project, i.e. where "package.json" lives. Default is '..'
    // root = file("../")
    //   The folder where the react-native NPM package is. Default is ../node_modules/react-native
    // reactNativeDir = file("../node_modules/react-native")
    //   The folder where the react-native Codegen package is. Default is ../node_modules/@react-native/codegen
    // codegenDir = file("../node_modules/@react-native/codegen")
    //   The cli.js file which is the React Native CLI entrypoint. Default is ../node_modules/react-native/cli.js
    // cliFile = file("../node_modules/react-native/cli.js")

    /* Variants */
    //   The list of variants to that are debuggable. For those we're going to
    //   skip the bundling of the JS bundle and the assets. By default is just 'debug'.
    //   If you add flavors like lite, prod, etc. you'll have to list your debuggableVariants.
    // debuggableVariants = ["liteDebug", "prodDebug"]

    /* Bundling */
    //   A list containing the node command and its flags. Default is just 'node'.
    // nodeExecutableAndArgs = ["node"]
    //
    //   The command to run when bundling. By default is 'bundle'
    // bundleCommand = "ram-bundle"
    //
    //   The path to the CLI configuration file. Default is empty.
    // bundleConfig = file(../rn-cli.config.js)
    //
    //   The name of the generated asset file containing your JS bundle
    // bundleAssetName = "MyApplication.android.bundle"
    //
    //   The entry file for bundle generation. Default is 'index.android.js' or 'index.js'
    // entryFile = file("../js/MyApplication.android.js")
    //
    //   A list of extra flags to pass to the 'bundle' commands.
    //   See https://github.com/react-native-community/cli/blob/main/docs/commands.md#bundle
    // extraPackagerArgs = []

    /* Hermes Commands */
    //   The hermes compiler command to run. By default it is 'hermesc'
    // hermesCommand = "$rootDir/my-custom-hermesc/bin/hermesc"
    //
    //   The list of flags to pass to the Hermes compiler. By default is "-O", "-output-source-map"
    // hermesFlags = ["-O", "-output-source-map"]
}

/**
 * Set this to true to Run Proguard on Release builds to minify the Java bytecode.
 */
def enableProguardInReleaseBuilds = true

/**
 * The preferred build flavor of JavaScriptCore (JSC)
 *
 * For example, to use the international variant, you can use:
 * `def jscFlavor = 'org.webkit:android-jsc-intl:+'`
 *
 * The international variant includes ICU i18n library and necessary data
 * allowing to use e.g. `Date.toLocaleString` and `String.localeCompare` that
 * give correct results when using with locales other than en-US. Note that
 * this variant is about 6MiB larger per architecture than default.
 */
def jscFlavor = 'org.webkit:android-jsc:+'

def releaseTime() {
    return new Date().format("yyyy-MM-dd", TimeZone.getTimeZone("UTC"))
}

def reactNativeArchitectures() {
    def value = project.getProperties().get("reactNativeArchitectures")
    return value ? value.split(",") : ["armeabi-v7a", "x86", "x86_64", "arm64-v8a"]
}

def isNewArchitectureEnabled() {
    return project.hasProperty("newArchEnabled")
}

android {
    ndkVersion rootProject.ext.ndkVersion
    buildToolsVersion rootProject.ext.buildToolsVersion
    compileSdkVersion rootProject.ext.compileSdkVersion

    namespace "com.prestamo.credito.dinero.efectivo.facil.rapido.noro.plus"

    aaptOptions {
        noCompress 'arsc'
    }

    defaultConfig {
        applicationId "com.prestamo.credito.dinero.efectivo.facil.rapido.noroplus"
        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion rootProject.ext.targetSdkVersion
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        versionCode 58
        versionName "v1.4.0"
        ndk {
            abiFilters 'armeabi-v7a', 'arm64-v8a', "x86_64", "x86"
        }

    }

    splits {
        abi {
            reset()
            enable true
            universalApk true 
            include(*reactNativeArchitectures())
        }
    }

    signingConfigs {
       debug {
            storeFile file('noro-plus-test.keystore')
            storePassword 'biluowa'
            keyAlias 'noro-plus-test.keystore'
            keyPassword 'biluowa'
        }
        release {
            storeFile file('../noro-plus-prod.keystore')
            storePassword 'biluowa'
            keyAlias 'noro-plus-prod.keystore'
            keyPassword 'biluowa'
        }
    }

    buildTypes {

        debug {
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }

        release {
            // Caution! In production, you need to generate your own keystore file.
            // see https://reactnative.dev/docs/signed-apk-android.
            ndk {
                debugSymbolLevel = 'FULL'
            }
            shrinkResources true
            minifyEnabled enableProguardInReleaseBuilds
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            testProguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    sourceSets {

        main {
            if (isNewArchitectureEnabled()) {
                java.srcDirs += ['src/newarch/java']
            } else {
                java.srcDirs += ['src/oldarch/java']
            }
        }
    }

    lintOptions {

        checkReleaseBuilds false

        abortOnError false

    }

    flavorDimensions 'classification'

    productFlavors {

        // 测试环境
        noroplus_test {
            // 热更新测试密钥
            resValue "string", "CodePushDeploymentKey", '"0LJB6RTAQt0DFeL7dF7tTIYPlKn6Ka3ia0pq3"'
            signingConfig signingConfigs.debug
            dimension "classification"
            applicationId "com.prestamo.credito.dinero.efectivo.facil.rapido.noroplus"
            versionCode 58
            versionName "v1.4.0"
            manifestPlaceholders = [
                    ENV              : "test",
                    PRODUCT_NAME     : "noropresta-plus",
                    SCHEME_NAME      : "noroplus",
                    HOST             : "norotech",
                    ALLOW_CLEARTEXT_TRAFFIC: true,
            ]
        }

        // 生产环境
        noroplus_prod {
            // 热更新生产密钥
            resValue "string", "CodePushDeploymentKey", '"hc9fq-PCKCCxImnoCLXo6nzDv7liUT5jNzUnd"'
            signingConfig signingConfigs.release
            dimension "classification"
            applicationId "com.prestamo.credito.dinero.efectivo.facil.rapido.noroplus"
            versionCode 58
            versionName "v1.4.0"
            manifestPlaceholders = [
                    ENV              : "prod",
                    PRODUCT_NAME     : "noropresta-plus",
                    SCHEME_NAME      : "noroplus",
                    HOST             : "norotech",
                    ALLOW_CLEARTEXT_TRAFFIC: false,
            ]
        }
    }

    // applicationVariants are e.g. debug, release
    applicationVariants.all { variant ->
        variant.outputs.each { output ->
            // For each separate APK per architecture, set a unique version code as described here:
            // https://developer.android.com/studio/build/configure-apk-splits.html
            // Example: versionCode 1 will generate 1001 for armeabi-v7a, 1002 for x86, etc.
            def versionCodes = ["armeabi-v7a": 1, "x86": 2, "arm64-v8a": 3, "x86_64": 4]
            def abi = output.getFilter(com.android.build.OutputFile.ABI)
            def flavorName = variant.flavorName
            def releaseApkName
            
            if (abi != null) {  // null for the universal-debug, universal-release variants
                releaseApkName = flavorName + "_" + versionName + '_' + versionCode + '_' + abi + '_' + releaseTime() + '.apk'
            } else {
                releaseApkName = flavorName + "_" + versionName + '_' + releaseTime() + '.apk'
            }

            output.outputFileName = releaseApkName

        }
    }

    compileOptions {

        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    
    }

    kotlinOptions {

        jvmTarget = '17'
    
    }
}

dependencies {
    // The version of react-native is set by the React Native Gradle Plugin
    implementation("com.facebook.react:react-android")

    // The version of react-native is set by the React Native Gradle Plugin
    implementation("com.facebook.react:react-android")
    implementation("com.facebook.soloader:soloader:0.10.4+")

    implementation("androidx.swiperefreshlayout:swiperefreshlayout:1.1.0")
    implementation 'androidx.constraintlayout:constraintlayout:2.0.4'

    // For animated GIF support
    implementation 'com.facebook.fresco:animated-gif:3.1.3'

    // For WebP support, including animated WebP
    implementation 'com.facebook.fresco:animated-webp:3.1.3'
    implementation 'com.facebook.fresco:webpsupport:3.1.3'

    // google 服务
    // 广告id
    implementation 'com.google.android.gms:play-services-ads-identifier:18.0.1'

    // kotlin
    implementation "org.jetbrains.kotlin:kotlin-stdlib:$rootProject.ext.kotlinVersion"
    // 协程
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-android:1.4.1"

    implementation 'com.android.installreferrer:installreferrer:2.2'

    // Firebase Cloud Messaging (Kotlin)
    implementation 'com.google.firebase:firebase-messaging-ktx:17.1.3'

//    implementation 'com.google.firebase:firebase-installations-ktx'

    implementation 'androidx.work:work-runtime:2.8.1'

//    implementation 'com.google.firebase:firebase-analytics-ktx'
    implementation platform('com.google.firebase:firebase-bom:32.1.0')

    //face++ 活体检测
    implementation files('libs/meglive_still.aar')

    implementation fileTree(dir: 'libs', include: ['*.jar'])
    //zhiSycData sdk
    implementation project(':zhiSycData')
    // gson
    implementation 'com.google.code.gson:gson:2.8.6'
    implementation 'com.google.android.gms:play-services-base:18.0.1'
    implementation 'com.google.android.gms:play-services-auth:21.3.0'
    implementation 'com.google.android.gms:play-services-auth-api-phone:18.0.1'
    implementation "androidx.activity:activity-ktx:1.8.0"

    debugImplementation project(':devMenu')

    if (hermesEnabled.toBoolean()) {
        implementation("com.facebook.react:hermes-android")
    } else {
        implementation jscFlavor
    }
}

apply from: file("../../node_modules/@react-native-community/cli-platform-android/native_modules.gradle"); applyNativeModulesAppBuildGradle(project)
