<resources>

    <!-- Base application theme. -->
    <style name="AppTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <!-- Customize your theme here. -->
        <item name="android:editTextBackground">@drawable/rn_edit_text_material</item>
        <!-- Customize your theme here. -->
<!--        <item name="android:statusBarColor">@color/input_cursor_color</item>-->
        <!-- <item name="colorControlActivated">@color/input_cursor_color</item> -->
    </style>

    <style name="liveness_preview" parent="liveness_horizontal_center">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">0dp</item>
        <item name="layout_constraintDimensionRatio">"H,1:1"</item>
        <item name="layout_constraintWidth_percent">0.8</item>
    </style>

    <style name="liveness_horizontal_center">
        <item name="layout_constraintLeft_toLeftOf">parent</item>
        <item name="layout_constraintRight_toRightOf">parent</item>
    </style>

    <style name="RadioButtonStyle" parent="Widget.AppCompat.CompoundButton.RadioButton">
    </style>

    <style name="CustomCheckBoxStyle" parent="Widget.AppCompat.CompoundButton.CheckBox">
    </style>

    <!-- res/values/styles.xml -->
    <style name="SpinnerStyle" parent="Widget.AppCompat.Spinner">
        <item name="android:popupBackground">@color/white</item> <!-- 下拉选择框背景颜色 -->
        <item name="android:dropDownVerticalOffset">0dp</item> <!-- 下拉偏移量 -->
        <item name="android:dropDownWidth">match_parent</item> <!-- 下拉宽度 -->
        <item name="android:popupTheme">@style/SpinnerDropdownStyle</item> <!-- 下拉选择框的样式 -->
    </style>

    <style name="SpinnerDropdownStyle" parent="ThemeOverlay.AppCompat.Light">
        <item name="android:background">@color/white</item> <!-- 下拉选择框背景颜色 -->
        <item name="android:popupBackground">@color/white</item> <!-- 下拉选择框弹出菜单背景颜色 -->
        <item name="android:textColor">@color/black</item> <!-- 下拉选择框文字颜色 -->
    </style>

</resources>
