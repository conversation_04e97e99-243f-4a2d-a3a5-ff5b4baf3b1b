<resources xmlns:tools="http://schemas.android.com/tools">
<!-- 浅色主题 -->
    <style name="AppTheme.Light" parent="Theme.MaterialComponents.Light.NoActionBar">
        <!-- 设置应用程序的主色调和辅助色调 -->
        <item name="colorPrimary">@color/primaryColor</item>
        <item name="colorPrimaryVariant">@color/primaryDarkColor</item>
        <item name="colorOnPrimary">@color/primaryTextColor</item>
        <item name="colorSecondary">@color/secondaryColor</item>
        <item name="colorSecondaryVariant">@color/secondaryDarkColor</item>
        <item name="colorOnSecondary">@color/secondaryTextColor</item>
        <!-- 其他样式 -->
        <!-- ... -->
    </style>

    <!-- 深色主题 -->
    <style name="AppTheme.Dark" parent="Theme.MaterialComponents.NoActionBar">
        <!-- 设置应用程序的主色调和辅助色调 -->
        <item name="colorPrimary">@color/primaryColorDark</item>
        <item name="colorPrimaryVariant">@color/primaryColorDarkVariant</item>
        <item name="colorOnPrimary">@color/primaryTextColorDark</item>
        <item name="colorSecondary">@color/secondaryColorDark</item>
        <item name="colorSecondaryVariant">@color/secondaryColorDarkVariant</item>
        <item name="colorOnSecondary">@color/secondaryTextColorDark</item>
        <!-- 其他样式 -->
        <!-- ... -->
    </style>
</resources>
