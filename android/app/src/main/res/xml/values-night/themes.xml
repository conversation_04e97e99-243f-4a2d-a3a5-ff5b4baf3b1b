<?xml version="1.0" encoding="utf-8"?>
<resources>

    <!-- res/values/styles.xml -->
    <style name="SpinnerStyle" parent="Widget.AppCompat.Spinner">
        <item name="android:background">@drawable/spinner_background</item> <!-- 背景资源 -->
        <item name="android:popupBackground">@color/white</item> <!-- 下拉选择框背景颜色 -->
        <item name="android:dropDownVerticalOffset">0dp</item> <!-- 下拉偏移量 -->
        <item name="android:dropDownWidth">match_parent</item> <!-- 下拉宽度 -->
        <item name="android:popupTheme">@style/SpinnerDropdownStyle</item> <!-- 下拉选择框的样式 -->
    </style>

    <style name="SpinnerDropdownStyle" parent="ThemeOverlay.AppCompat.Light">
        <item name="android:background">@color/white</item> <!-- 下拉选择框背景颜色 -->
        <item name="android:popupBackground">@color/white</item> <!-- 下拉选择框弹出菜单背景颜色 -->
        <item name="android:textColor">@color/black</item> <!-- 下拉选择框文字颜色 -->
    </style>
</resources>