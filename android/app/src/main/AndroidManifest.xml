<manifest xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android">

    <uses-feature android:name="android.hardware.telephony" android:required="false" />
    <uses-feature android:name="android.hardware.camera" android:required="false" />

    <application
        android:name="com.prestamo.credito.dinero.efectivo.facil.rapido.noro.plus.MyApplication"
        android:icon="@mipmap/app_launcher"
        android:roundIcon="@mipmap/app_launcher_round"
        android:allowBackup="true"
        android:hardwareAccelerated="true"
        android:label="@string/peso_app_name"
        android:networkSecurityConfig="@xml/los_network_security_config"
        android:supportsRtl="true" 
        android:theme="@style/Theme"
        tools:ignore="UnusedAttribute"
        tools:node="merge"
        tools:targetApi="n">

        <meta-data android:name="product_name" android:value="${PRODUCT_NAME}" android:exported="false" />
        <meta-data android:name="system_env" android:value="${ENV}" android:exported="false" />
        <!-- [START fcm_default_icon] -->
        <!-- Set custom default icon. This is used when no icon is set for incoming notification messages.
             See README(https://goo.gl/l4GJaQ) for more. -->
        <meta-data android:name="com.google.firebase.messaging.default_notification_icon" android:resource="@mipmap/ic_stat_ic_notification" />
        <!-- Set color used with incoming notification messages. This is used when no color is set for the incoming
             notification message. See README(https://goo.gl/6BKBk7) for more. -->
        <!-- [END fcm_default_icon] -->
        <!-- [START fcm_default_channel] -->
        <!-- [END fcm_default_channel] -->

        <meta-data android:name="firebase_messaging_auto_init_enabled" android:value="true" android:exported="false" />
        <meta-data android:name="firebase_analytics_collection_enabled" android:value="true" />
        <meta-data android:name="google_analytics_adid_collection_enabled" android:value="true" />

        <meta-data android:name="preloaded_splash_screen" android:resource="@layout/launch_screen" />
        <receiver
            android:name=".r.m.SmsBR"
            android:permission="com.google.android.gms.auth.api.phone.permission.SEND"
            android:exported="true">
            <intent-filter>
                <action android:name="com.google.android.gms.auth.api.phone.SMS_RETRIEVED" />
            </intent-filter>
        </receiver>

      <activity android:screenOrientation="portrait" android:theme="@style/Theme.AppCompat.Light.NoActionBar" android:name=".SplashActivity" android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode" android:launchMode="singleTask" android:windowSoftInputMode="adjustPan"
          tools:ignore="DiscouragedApi,LockedOrientationActivity" />

        <activity-alias android:name="com.prestamo.credito.dinero.efectivo.facil.rapido.noro.plus.SplashActivity" android:exported="true" android:targetActivity=".SplashActivity">
            <intent-filter android:priority="1000">
                <category android:name="android.intent.category.LAUNCHER" />
                <action android:name="android.intent.action.MAIN" />
            </intent-filter>
                <intent-filter android:autoVerify="true">
                <category android:name="android.intent.category.BROWSABLE" />
                <action android:name="android.intent.action.VIEW" />
                <data android:scheme="${SCHEME_NAME}" android:host="${HOST}" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
             <intent-filter>
                <action android:name="${applicationId}_NOTIFICATION_ACTION" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity-alias>
        <provider android:name="androidx.core.content.FileProvider" android:authorities="${applicationId}.file_provider" android:exported="false" android:grantUriPermissions="true">
            <meta-data android:name="android.support.FILE_PROVIDER_PATHS" android:resource="@xml/file_provider_paths" />
        </provider>
        <!-- [START firebase_service] -->
        <service android:name=".s.MFIIDS" android:exported="false">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>
    </application>

    <uses-permission android:name="android.permission.INTERNET" />
    <!-- 摄像头 & 相册 & 多媒体相关权限 -->
    <uses-permission android:name="android.permission.CAMERA" />
    <!--  监控系统的内存状态	-->
    <uses-permission android:name="android.permission.ACCESS_MEMORY_USAGE_STATS" />
    <!--  粗略定位	-->
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <!-- 短信相关权限 -->
    <uses-permission android:name="android.permission.READ_SMS" />

    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />

    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <!-- 获取安装渠道来源 -->
    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
    <!-- 谷歌广告ID -->
    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
    <!--  修改Wi-Fi状态  -->
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <!-- 屏幕跳转权限 -->
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <!-- 获取META_DATA权限 -->
    <uses-permission android:name="android.permission.GET_META_DATA" />
    <!-- 通知权限 -->
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <!-- 判断有没有 push 通知权限 -->
    <uses-permission android:name="android.permission.ACCESS_NOTIFICATION_POLICY" />

    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" tools:node="remove" tools:ignore="ScopedStorage" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" tools:node="remove" tools:ignore="ScopedStorage" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" tools:node="remove" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" tools:node="remove" />

    <queries>
        <intent>
            <action android:name="android.intent.action.MAIN" />
        </intent>
    </queries>

</manifest>
