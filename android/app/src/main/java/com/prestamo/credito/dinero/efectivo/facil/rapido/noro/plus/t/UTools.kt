package com.prestamo.credito.dinero.efectivo.facil.rapido.noro.plus.t

import android.content.Intent
import android.net.Uri
import android.provider.Settings
import com.prestamo.credito.dinero.efectivo.facil.rapido.noro.plus.BuildConfig
import com.prestamo.credito.dinero.efectivo.facil.rapido.noro.plus.SplashActivity

object UTools {
    /** 验证是否是生产 release 环境 */
    val iPR: Boolean = BuildConfig.FLAVOR.contains("prod", true) && !BuildConfig.DEBUG

    fun jASA() {
        val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS)
        val uri = Uri.fromParts("package", DCTools.gALId(), null)
        intent.data = uri
        SplashActivity.mActivity.startActivity(intent)
    }
}