package com.prestamo.credito.dinero.efectivo.facil.rapido.noro.plus.t

import android.content.Context
import android.content.pm.PackageManager.NameNotFoundException
import androidx.core.content.pm.PackageInfoCompat
import com.google.android.gms.common.ConnectionResult
import com.google.android.gms.common.GoogleApiAvailability
import com.prestamo.credito.dinero.efectivo.facil.rapido.noro.plus.MyApplication

object GPSTools {
    private const val MINIMAL_VERSION = 10200000
    const val UNAVAILABLE_ERROR_TYPE = "UNAVAILABLE_ERROR_TYPE"
    const val UNSUPORTED_VERSION_ERROR_TYPE = "UNSUPORTED_VERSION_ERROR_TYPE"
    const val UNAVAILABLE_ERROR_MESSAGE = "Google Play Services is not available."
    const val UNSUPORTED_VERSION_ERROR_MESSAGE = "The device version of Google Play Services is not supported."

    // 判断谷歌服务可不可用
    fun iGPSA(): Boolean {
        try {
            val googleApiAvailability = GoogleApiAvailability.getInstance()
            val resultCode = googleApiAvailability.isGooglePlayServicesAvailable(MyApplication.mApplication)
            return resultCode == ConnectionResult.SUCCESS
        } catch (e: Exception) {
            e.printStackTrace()
            return false
        }
    }

    // 判断是否谷歌服务是否高于最低版本
    fun hSV(context: Context): Boolean {
        val manager = context.packageManager
        return try {
            val version = PackageInfoCompat.getLongVersionCode(manager.getPackageInfo(GoogleApiAvailability.GOOGLE_PLAY_SERVICES_PACKAGE, 0))
            version >= MINIMAL_VERSION
        } catch (e: NameNotFoundException) {
            false
        }
    }
}