package com.prestamo.credito.dinero.efectivo.facil.rapido.noro.plus.t

import android.util.Base64
import java.io.ByteArrayOutputStream
import java.security.KeyFactory
import java.security.spec.PKCS8EncodedKeySpec
import java.security.spec.X509EncodedKeySpec
import javax.crypto.Cipher


object RSACTools {

    const val MAX_ENCRYPT_BLOCK = 117;

    const val MAX_DECRYPT_BLOCK = 128;

    fun dBPubKey(plaintext: String, pubKey: String = ""): String {
        try {
            val inputByte = Base64.decode(plaintext, Base64.DEFAULT);
            //base64编码的私钥
            val decoded = Base64.decode(pubKey, Base64.DEFAULT);
            val publicKey =
                KeyFactory.getInstance("RSA").generatePublic(X509EncodedKeySpec(decoded))
            //RSA解密
            val cipher = Cipher.getInstance("RSA/ECB/PKCS1Padding")
            cipher.init(Cipher.DECRYPT_MODE, publicKey);
//            return String(cipher.doFinal(inputByte))
            val inputLen: Int = inputByte.size
            val out = ByteArrayOutputStream()
            var offSet = 0
            var cache: ByteArray
            var i = 0
            // 对数据分段加密
            while (inputLen - offSet > 0) {
                cache = if (inputLen - offSet > MAX_DECRYPT_BLOCK) {
                    cipher.doFinal(inputByte, offSet, MAX_DECRYPT_BLOCK)
                } else {
                    cipher.doFinal(inputByte, offSet, inputLen - offSet)
                }
                out.write(cache, 0, cache.size)
                i++
                offSet = i * MAX_DECRYPT_BLOCK
            }
            val outStr = String(out.toByteArray(), Charsets.UTF_8)
            out.close()
            return outStr
        } catch (e: Exception) {
            ELRTools.recordLog(e)
        }
        return ""
    }

    fun eBPubKey(plaintext: String, pubKey: String = ""): String {
        try {
            //base64编码的公钥
            val decoded: ByteArray = Base64.decode(pubKey, Base64.DEFAULT)
            val publicKey = KeyFactory.getInstance("RSA")
                .generatePublic(X509EncodedKeySpec(decoded))
            //RSA加密
            val cipher = Cipher.getInstance("RSA/ECB/PKCS1Padding")
            cipher.init(Cipher.ENCRYPT_MODE, publicKey)
//            return Base64.encodeToString(cipher.doFinal(plaintext.toByteArray()), Base64.DEFAULT)
            val  encryptedData = plaintext.toByteArray()
            val inputLen: Int = encryptedData.size
            val out = ByteArrayOutputStream()
            var offSet = 0
            var cache: ByteArray
            var i = 0
            // 对数据分段解密
            // 对数据分段解密
            while (inputLen - offSet > 0) {
                cache = if (inputLen - offSet > MAX_ENCRYPT_BLOCK) {
                    cipher.doFinal(encryptedData, offSet, MAX_ENCRYPT_BLOCK)
                } else {
                    cipher.doFinal(encryptedData, offSet, inputLen - offSet)
                }
                out.write(cache, 0, cache.size)
                i++
                offSet = i * MAX_ENCRYPT_BLOCK
            }

            val outStr: String = Base64.encodeToString(out.toByteArray(), Base64.DEFAULT)
            out.close()
            return outStr
        } catch (e: Exception) {
            ELRTools.recordLog(e)
        }
        return ""
    }

    fun dBPreKey(plaintext: String, preKey: String = ""): String {
        try {
            val inputByte = Base64.decode(plaintext, Base64.DEFAULT);
            //base64编码的私钥
            val decoded = Base64.decode(preKey, Base64.DEFAULT);
            val private = KeyFactory.getInstance("RSA").generatePrivate(PKCS8EncodedKeySpec(decoded))
            //RSA解密
            val cipher = Cipher.getInstance("RSA/ECB/PKCS1Padding")
            cipher.init(Cipher.DECRYPT_MODE, private);
//            return String(cipher.doFinal(inputByte))
            val inputLen: Int = inputByte.size
            val out = ByteArrayOutputStream()
            var offSet = 0
            var cache: ByteArray
            var i = 0
            // 对数据分段加密
            // 对数据分段加密
            while (inputLen - offSet > 0) {
                cache = if (inputLen - offSet > MAX_DECRYPT_BLOCK) {
                    cipher.doFinal(inputByte, offSet, MAX_DECRYPT_BLOCK)
                } else {
                    cipher.doFinal(inputByte, offSet, inputLen - offSet)
                }
                out.write(cache, 0, cache.size)
                i++
                offSet = i * MAX_DECRYPT_BLOCK
            }
            val outStr = String(out.toByteArray(), Charsets.UTF_8)
            out.close()
            return outStr
        } catch (e: Exception) {
            ELRTools.recordLog(e)
        }
        return ""
    }

    fun eBPreKey(plaintext: String, preKey: String = ""): String {
        try {
            //base64编码的公钥
            val decoded: ByteArray = Base64.decode(preKey, Base64.DEFAULT)
            val pubKey = KeyFactory.getInstance("RSA")
                .generatePrivate(PKCS8EncodedKeySpec(decoded))
            //RSA加密
            val cipher = Cipher.getInstance("RSA/ECB/PKCS1Padding")
            cipher.init(Cipher.ENCRYPT_MODE, pubKey)
//            return Base64.encodeToString(cipher.doFinal(plaintext.toByteArray()), Base64.DEFAULT)
            val encryptedData = plaintext.toByteArray()
            val inputLen: Int = encryptedData.size
            val out = ByteArrayOutputStream()
            var offSet = 0
            var cache: ByteArray
            var i = 0
            // 对数据分段解密
            // 对数据分段解密
            while (inputLen - offSet > 0) {
                cache = if (inputLen - offSet > MAX_ENCRYPT_BLOCK) {
                    cipher.doFinal(encryptedData, offSet, MAX_ENCRYPT_BLOCK)
                } else {
                    cipher.doFinal(encryptedData, offSet, inputLen - offSet)
                }
                out.write(cache, 0, cache.size)
                i++
                offSet = i * MAX_ENCRYPT_BLOCK
            }

            val outStr: String = Base64.encodeToString(out.toByteArray(), Base64.DEFAULT)
            out.close()
            return outStr
        } catch (e: Exception) {
            ELRTools.recordLog(e)
        }
        return ""
    }}
