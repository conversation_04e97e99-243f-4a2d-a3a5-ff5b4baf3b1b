package com.prestamo.credito.dinero.efectivo.facil.rapido.noro.plus.r.m

import com.facebook.react.bridge.Promise
import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.bridge.ReactContextBaseJavaModule
import com.facebook.react.bridge.ReactMethod
import com.facebook.react.bridge.WritableMap
import com.facebook.react.modules.core.DeviceEventManagerModule
import com.prestamo.credito.dinero.efectivo.facil.rapido.noro.plus.t.DCTools
import com.prestamo.credito.dinero.efectivo.facil.rapido.noro.plus.t.ELRTools
import com.prestamo.credito.dinero.efectivo.facil.rapido.noro.plus.t.FCMTools
import com.prestamo.credito.dinero.efectivo.facil.rapido.noro.plus.t.FTools
import com.prestamo.credito.dinero.efectivo.facil.rapido.noro.plus.t.GPARTools
import com.prestamo.credito.dinero.efectivo.facil.rapido.noro.plus.t.GTools
import com.prestamo.credito.dinero.efectivo.facil.rapido.noro.plus.t.GPSTools
import com.prestamo.credito.dinero.efectivo.facil.rapido.noro.plus.t.LCTools
import com.prestamo.credito.dinero.efectivo.facil.rapido.noro.plus.t.RSACTools
import com.prestamo.credito.dinero.efectivo.facil.rapido.noro.plus.t.UTools

class NM(reactContext: ReactApplicationContext) :
    ReactContextBaseJavaModule(reactContext) {
    override fun getName() = "NativeMethod"

    init {
        mRC = reactContext
    }
    /** 获取设备数据 */

    /** 获取 gaid */
    @ReactMethod
    fun gGaid(promise: Promise) {
        GTools.getGaid {
            promise.resolve(it)
        }
    }

    @ReactMethod
    fun gIsLimitAdTrackingEnabled(promise: Promise) {
        GTools.getIsLimitAdTrackingEnabled {
            promise.resolve(it)
        }
    }

    /** 获取 android 的系统版本 */
    @ReactMethod()
    fun gSdkVersionCode(promise: Promise) = promise.resolve(DCTools.gSdkVC())

    /** 检测设备上的谷歌服务是否可用 */
    @ReactMethod()
    fun gGPServerEnabled(promise: Promise) =
        promise.resolve(GPSTools.iGPSA())

    /** 获取appId */
    @ReactMethod()
    fun gAppId(promise: Promise) = promise.resolve(DCTools.gAppId())

    /** 获取版本code */
    @ReactMethod
    fun gVersionCode(promise: Promise) = promise.resolve(DCTools.gVC())

    /** 获取版本名称 */
    @ReactMethod
    fun gVersionName(promise: Promise) = promise.resolve(DCTools.gVN())

    /** android id */
    @ReactMethod
    fun gAndroidID(promise: Promise) =
        promise.resolve(DCTools.gAndroidId(reactApplicationContext))

    /** 获取构建类型 */
    @ReactMethod
    fun gBulidType(promise: Promise) = promise.resolve(DCTools.gBT())

    /** 获取构建 */
    @ReactMethod
    fun gPackageName(promise: Promise) = promise.resolve(DCTools.gPN())

    /** 获取 application */
    @ReactMethod
    fun gApplicationId(promise: Promise) = promise.resolve(DCTools.gALId())

    /** 是否是 debug 模式 */
    @ReactMethod
    fun gDebug(promise: Promise) = promise.resolve(DCTools.gDeb())

    /** 是否使用 hermes 引擎 */
    @ReactMethod
    fun gIsHermesEnabled(promise: Promise) =
        promise.resolve(DCTools.gIHE())

    /** 是否开始 jsi 架构 */
    @ReactMethod
    fun gIsNewArchitectrueEnabled(promise: Promise) =
        promise.resolve(DCTools.gINAE())

    /** 产品类型 */
    @ReactMethod
    fun gFlavor(promise: Promise) = promise.resolve(DCTools.gFla())

    /** 获取谷歌play store 的商店地址 */
    @ReactMethod
    fun gPlayStoreUrl(promise: Promise) = promise.resolve(DCTools.gPSU())

    /** 自定义方法 */
    /** 前往GP商店 */
    @ReactMethod
    fun requestGPAppReview() = GPARTools.rGPAR()

    /** 前往应用设置页面 */
    @ReactMethod
    fun jumpAppSettingActivity() = UTools.jASA()

    /** 错误日志模块 */
    /** 记录日志 */
    @ReactMethod
    fun recordLog(msg: String) = ELRTools.recordLog(msg)

    /** 清理日志 */
    @ReactMethod
    fun clearLogs() = ELRTools.clearLogs()

    /** 获取错误日志文件 */
    @ReactMethod
    fun gLogFilePath(promise: Promise) =
        promise.resolve(ELRTools.getLogFilePath())

    // 是否有通知权限
    @ReactMethod
    fun hNP(promise: Promise) = promise.resolve(FCMTools.hNP())

    /** 数据上报模块 */
    /** 初始化上报数据模块 */
    @ReactMethod
    fun initASDeviceLib(baseUrl: String, promise: Promise) = run {
        FTools.sUDBU(baseUrl)
        promise.resolve(null)
    }

    /** 上报数据 */
    @ReactMethod
    fun fDeviceData(
        token: String = "",
        conditions: String = "APPLIST_MESSAGE",
        applyOrderId: String = "",
        event: String = ""
    ) = FTools.fDD(token, conditions, applyOrderId, event)

    /** 缓存模块 */
    /** 获取 string */
    @ReactMethod
    fun gCacheString(k: String = "", promise: Promise) =
        promise.resolve(LCTools.gString(k))

    /** 设置 string */
    @ReactMethod
    fun sCacheString(k: String = "", v: String = "", promise: Promise) =
        promise.resolve(LCTools.sString(k, v))

    /** 获取 int */
    @ReactMethod
    fun gCacheInt(k: String = "", promise: Promise) =
        promise.resolve(LCTools.gInt(k))

    /** 设置 int */
    @ReactMethod
    fun sCacheInt(k: String = "", v: Int = 0, promise: Promise) =
        promise.resolve(LCTools.sInt(k, v))

    /** 获取 boolean */
    @ReactMethod
    fun gCacheBoolean(k: String = "", promise: Promise) =
        promise.resolve(LCTools.gBoolean(k))

    /** 设置 boolean */
    @ReactMethod
    fun sCacheBoolean(k: String = "", v: Boolean = false, promise: Promise) =
        promise.resolve(LCTools.sBoolean(k, v))

    /** rsa 公钥加密 */
    @ReactMethod
    fun rsaEBPubKey(plaintext: String, pubKey: String, promise: Promise) =
        promise.resolve(RSACTools.eBPubKey(plaintext, pubKey))

    /** rsa 公钥解密 */
    @ReactMethod
    fun rsaDBPubKey(plaintext: String, pubKey: String, promise: Promise) =
        promise.resolve(RSACTools.dBPubKey(plaintext, pubKey))

    /** rsa 私钥加密 */
    @ReactMethod
    fun rsaEBPreKey(plaintext: String, preKey: String, promise: Promise) =
        promise.resolve(RSACTools.eBPreKey(plaintext, preKey))

    /** rsa 私钥解密 */
    @ReactMethod
    fun rsaDBPreKey(plaintext: String, preKey: String, promise: Promise) =
        promise.resolve(RSACTools.dBPreKey(plaintext, preKey))

    companion object {
        /** 事件订阅方法，往js端传递数据 */
        lateinit var mRC: ReactApplicationContext
        fun sE(eventName: String, params: WritableMap?) {
            mRC.getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter::class.java)
                ?.emit(eventName, params)
        }
    }
}
