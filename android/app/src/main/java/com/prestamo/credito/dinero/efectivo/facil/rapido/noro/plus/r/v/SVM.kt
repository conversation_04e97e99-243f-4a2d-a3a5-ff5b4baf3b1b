package com.prestamo.credito.dinero.efectivo.facil.rapido.noro.plus.r.v

import android.view.LayoutInflater
import android.view.View
import android.widget.FrameLayout
import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.uimanager.SimpleViewManager
import com.facebook.react.uimanager.ThemedReactContext
import com.prestamo.credito.dinero.efectivo.facil.rapido.noro.plus.*

class SVM(reactContext: ReactApplicationContext): SimpleViewManager<FrameLayout>() {
    override fun getName(): String {
        return REACT_CLASS
    }

    init {
        mRC = reactContext
    }

    companion object {
        const val REACT_CLASS = "SplashView"
        lateinit var mRC: ReactApplicationContext
    }


    override fun createViewInstance(reactContext: ThemedReactContext): FrameLayout {
        val frameLayout = FrameLayout(reactContext)
        frameLayout.id = View.generateViewId()
        val inflater = LayoutInflater.from(reactContext)
        inflater.inflate(R.layout.launch_screen, frameLayout, true)
        return frameLayout
    }
}
