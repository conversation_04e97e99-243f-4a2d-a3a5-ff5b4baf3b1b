package com.prestamo.credito.dinero.efectivo.facil.rapido.noro.plus.t

import android.content.Context
import com.google.android.gms.ads.identifier.AdvertisingIdClient
import com.prestamo.credito.dinero.efectivo.facil.rapido.noro.plus.MyApplication
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch


/**
 * 广告 ID 工具类
 */
object GTools {

    private var gaid: String = ""
    private var isLimitAdTrackingEnabled: Boolean = true
    private val mJob = Job()
    private val mScope = CoroutineScope(mJob + Dispatchers.IO)

    /**
     * 初始化广告 ID
     */
    fun initGaid(context: Context) {
       mScope.launch {
            try {
                val adInfo = AdvertisingIdClient.getAdvertisingIdInfo(context)
                gaid = adInfo.id ?: ""
                isLimitAdTrackingEnabled = adInfo.isLimitAdTrackingEnabled
            } catch (e: Exception) {
                ELRTools.recordLog(e);
            }
           mJob.cancel()
       }
    }

    /**
     * 初始化广告 ID
     */
    fun initGaid(context: Context, callback: (gaid: String, isLimitAdTrackingEnabled: Boolean) -> Unit?) {
        mScope.launch {
            try {
                val adInfo = AdvertisingIdClient.getAdvertisingIdInfo(context)
                gaid = adInfo.id ?: ""
                isLimitAdTrackingEnabled = adInfo.isLimitAdTrackingEnabled
                callback(gaid, isLimitAdTrackingEnabled)
            } catch (e: Exception) {
                callback("", true)
                ELRTools.recordLog(e);
            }
        }
    }

    fun getGaid(callback: (s: String) -> Unit) {
        if (gaid.isBlank()) {
            if (GPSTools.iGPSA()) {
                initGaid(MyApplication.mApplication) { gaid, _ ->
                    callback(gaid)
                    mJob.cancel()
                }
            } else run {
                callback(gaid)
            }
        } else {
            callback(gaid)
        }
    }

    fun getIsLimitAdTrackingEnabled(callback: (b: Boolean) -> Unit) {
        if (gaid.isBlank()) {
            if (GPSTools.iGPSA()) {
                initGaid(MyApplication.mApplication) { _, isLimitAdTrackingEnabled ->
                    callback(isLimitAdTrackingEnabled)
                    mJob.cancel()
                }
            } else run {
                callback(isLimitAdTrackingEnabled)
            }
        } else {
            callback(isLimitAdTrackingEnabled)
        }
    }
}