package com.prestamo.credito.dinero.efectivo.facil.rapido.noro.plus.t

import android.content.Context
import android.os.Build
import android.provider.Settings
import com.prestamo.credito.dinero.efectivo.facil.rapido.noro.plus.BuildConfig

object DCTools {

    /** 初始化构建环境 */
    fun gBT(): String = BuildConfig.BUILD_TYPE

    /** 初始化appId */
    fun gAppId(): String = "one"
    /** 获取 Android 系统版本 */
    fun gSdkVC(): Int = Build.VERSION.SDK_INT
    /** 初始化版本code */
    fun gVC(): Int = BuildConfig.VERSION_CODE

    fun gALId(): String = BuildConfig.APPLICATION_ID

    fun gVN(): String = BuildConfig.VERSION_NAME

    fun gPN(): String = BuildConfig.APPLICATION_ID

    fun gFla(): String = BuildConfig.FLAVOR

    fun gDeb(): Boolean = BuildConfig.DEBUG

    fun gPSU(): String = "https://play.google.com/store/apps/details?id=${BuildConfig.APPLICATION_ID}"

    fun gIHE(): Boolean = BuildConfig.IS_HERMES_ENABLED

    fun gINAE(): Boolean = BuildConfig.IS_NEW_ARCHITECTURE_ENABLED

    fun gAndroidId(context: Context): String {
        val androidID = Settings.System.getString(context.contentResolver, Settings.Secure.ANDROID_ID)
        if (androidID.isBlank())
            return System.currentTimeMillis().toString()
        if (androidID.contains("00000000"))
            return System.currentTimeMillis().toString()
        return androidID
    }

    fun gFLCU(): String = "/faceid/v3/sdk/internal/get_license_and_config"
}