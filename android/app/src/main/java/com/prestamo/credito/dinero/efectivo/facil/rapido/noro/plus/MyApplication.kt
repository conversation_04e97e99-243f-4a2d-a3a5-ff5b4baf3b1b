package com.prestamo.credito.dinero.efectivo.facil.rapido.noro.plus

import android.app.Application
import com.facebook.react.PackageList
import com.facebook.react.ReactApplication
import com.facebook.react.ReactHost
import com.facebook.react.ReactNativeHost
import com.facebook.react.ReactPackage
import com.facebook.react.defaults.DefaultNewArchitectureEntryPoint.load
import com.facebook.react.defaults.DefaultReactHost.getDefaultReactHost
import com.facebook.react.defaults.DefaultReactNativeHost
import com.facebook.soloader.SoLoader
import com.prestamo.credito.dinero.efectivo.facil.rapido.noro.plus.r.ARP
import com.prestamo.credito.dinero.efectivo.facil.rapido.noro.plus.t.ELRTools
import com.prestamo.credito.dinero.efectivo.facil.rapido.noro.plus.t.FTools
import com.prestamo.credito.dinero.efectivo.facil.rapido.noro.plus.t.GTools
import com.prestamo.credito.dinero.efectivo.facil.rapido.noro.plus.t.GPSTools
import com.zhi.syc.data.MyApplication


class MyApplication : MyApplication(), ReactApplication {

    companion object {
        lateinit var mApplication: Application;
    }

    override val reactNativeHost: ReactNativeHost =
      object : DefaultReactNativeHost(this) {
        override fun getPackages(): MutableList<ReactPackage>? {
            val packageList = PackageList(this).packages.apply {
                // Packages that cannot be autolinked yet can be added manually here, for example:
                 add(ARP())
            }

            if (BuildConfig.DEBUG) {
                // 只在 debug 模式下才加载特定的模块
                try {
                    val devMenuPackageClass = Class.forName("com.devmenu.DevMenuPackage")
                    val devMenuPackage = devMenuPackageClass.newInstance() as ReactPackage
                    packageList.add(devMenuPackage)
                } catch (e: Exception) {
                    ELRTools.recordLog(e)
                }
            }

            return packageList
        }
        override fun getJSMainModuleName(): String = "index"

        override fun getUseDeveloperSupport(): Boolean = BuildConfig.DEBUG

//        override fun getJSBundleFile(): String = CodePush.getJSBundleFile()

        override val isNewArchEnabled: Boolean = BuildConfig.IS_NEW_ARCHITECTURE_ENABLED
        override val isHermesEnabled: Boolean = BuildConfig.IS_HERMES_ENABLED
      }


  override val reactHost: ReactHost
    get() = getDefaultReactHost(applicationContext, reactNativeHost)

  override fun onCreate() {
    super.onCreate()
      mApplication = this
      SoLoader.init(this, false)
      // 初始化日志系统
      ELRTools.init(this)
      // 抓取设备数据模块初始化
      FTools.initASDL(this)
      // 如果检测谷歌服务可不可用
      if (GPSTools.iGPSA()) {
          // 可用
          // init gaid
          GTools.initGaid(this)
      }

    if (BuildConfig.IS_NEW_ARCHITECTURE_ENABLED) {
      // If you opted-in for the New Architecture, we load the native entry point for this app.
      load()
    }
  }
}
