package com.prestamo.credito.dinero.efectivo.facil.rapido.noro.plus

import android.annotation.SuppressLint
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.util.Log
import com.facebook.react.ReactActivity
import com.facebook.react.ReactActivityDelegate
import com.facebook.react.defaults.DefaultNewArchitectureEntryPoint.fabricEnabled
import com.facebook.react.defaults.DefaultReactActivityDelegate
import com.prestamo.credito.dinero.efectivo.facil.rapido.noro.plus.t.ELRTools
import org.devio.rn.splashscreen.SplashScreen

@SuppressLint("CustomSplashScreen")
class SplashActivity : ReactActivity() {

    companion object {
        lateinit var mActivity: ReactActivity
    }

  /**
   * Returns the name of the main component registered from JavaScript. This is used to schedule
   * rendering of the component.
   */
  override fun getMainComponentName(): String = "NoroPrestaPlus"

    override fun onCreate(savedInstanceState: Bundle?) {
        mActivity = this
        SplashScreen.show(this, false) // here
        super.onCreate(null)
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        try {
            // 处理新的 Intent
            val action: String? = intent.action
            val data: Uri? = intent.data
//            Log.d("TAG", "action $action data $data")

            mActivity.intent.extras
            mActivity.intent.apply {
                setAction(action)
                setData(data)
            }
            val bootLabel = intent.extras?.getString("bootLabel", "")
            val event = intent.extras?.getString("event", "")
            mActivity.intent.putExtra("bootLabel", "$bootLabel")
            mActivity.intent.putExtra("event", "$event")
        } catch (e: Exception) {
            ELRTools.recordLog(e)
        }

    }

    /**
   * Returns the instance of the [ReactActivityDelegate]. We use [DefaultReactActivityDelegate]
   * which allows you to enable New Architecture with a single boolean flags [fabricEnabled]
   */
  override fun createReactActivityDelegate(): ReactActivityDelegate =
      DefaultReactActivityDelegate(this, mainComponentName, fabricEnabled)
}
