package com.prestamo.credito.dinero.efectivo.facil.rapido.noro.plus.r.m

import com.facebook.react.bridge.Arguments
import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.bridge.ReactContextBaseJavaModule
import com.facebook.react.bridge.ReactMethod
import com.facebook.react.bridge.WritableMap
import com.megvii.meglive_sdk.listener.PreCallback
import com.megvii.meglive_sdk.manager.MegLiveManager
import com.prestamo.credito.dinero.efectivo.facil.rapido.noro.plus.SplashActivity
import com.prestamo.credito.dinero.efectivo.facil.rapido.noro.plus.c.IAUC
import com.prestamo.credito.dinero.efectivo.facil.rapido.noro.plus.r.REEEN
import com.prestamo.credito.dinero.efectivo.facil.rapido.noro.plus.t.DCTools
import com.prestamo.credito.dinero.efectivo.facil.rapido.noro.plus.t.ELRTools
import java.io.File
import java.io.FileOutputStream
import java.io.InputStream
import java.util.HashMap

class FLV(reactContext: ReactApplicationContext) : ReactContextBaseJavaModule(reactContext),
    PreCallback {
    override fun getName(): String = "FaceLiveVerification"

    // 模型的位置
    var mFMP: String = ""
    //
    private var mBT: String = ""
    // 活体的原始数据
    private var mFLFP: String = ""

    override fun onPreStart() {
        // 开始活体验证
        sE(F_L_PRE_START)
    }

    override fun onPreFinish(p0: String?, p1: Int, p2: String?) {
        try {
//            println("onPreFinish==========")
//            println("bizToken:$p0")
//            println("code:$p1")
//            println("msg:$p2")
            if (p1 == 1000) {
                sE(F_L_START_DETECT)
                // 埋点开始
                MegLiveManager.getInstance()
                    .setVerticalDetectionType(MegLiveManager.DETECT_VERITICAL_FRONT)
                MegLiveManager.getInstance()
                    .startDetectForLivenessFile { s, i, s2, s3, s4 ->
//                        println("onDetectFinish==========")
//                        println("token:$s, errorCode:$i, errorMessage:$s2")
//                        println("read ==========")
//                        println("data:$s3")
//                        println("livenessFilePath:$s4")
//                        println("read finish==========")
                        try {
                            // 上传活体加密数据
                            if (!s4.filePath.isNullOrEmpty()) {
                                mFLFP = s4.filePath
                            }
                            if (mBT.isNotEmpty() && !s3.isNullOrEmpty() && i == 1000) {
                                sE(F_L_START_DETECT_STATE_SUCCESS, mFLFP, s3, mBT)                                // 验证人脸信息
                            } else {
                                ELRTools.recordLog("faceLive onDetectFinish fail token:$s, errorCode:$i, errorMessage:$s2")
                                if(((s2 == "FACE_INIT_FAIL" ||  s2 == "NOT_ADD_RESOURCE") && i == 6000) || i == 4200 || i == 4300 || i == 6200 ) {
                                    sE(F_L_START_DETECT_STATE_ERROR)
                                } else {
                                    sE(F_L_START_DETECT_STATE_FAIL)
                                }
                            }
                            // 上传埋点数据
                        } catch (e: Exception) {
                            ELRTools.recordLog("faceLive onDetectFinish fail token:$s, errorCode:$i, errorMessage:$s2")
                            ELRTools.recordLog(e)
                        }
                    }
            } else {
                ELRTools.recordLog("faceLive onPreFinish fail token:$p0, errorCode:$p1, errorMessage:$p2")
                sE(F_L_PRE_FINISH_STATE_FAIL)
            }
        } catch (e: Exception) {
            ELRTools.recordLog("faceLive onPreFinish fail token:$p0, errorCode:$p1, errorMessage:$p2")
            ELRTools.recordLog(e)
        }
    }

        fun sFMFA(): String {
            val inAppDir =
                "${reactApplicationContext.filesDir}${File.separator}${DCTools.gAppId()}Megvii${File.separator}model"
//            println("#inAppDir $inAppDir")
            val dir = File(inAppDir)

            if (!dir.exists()) {
                if (!dir.mkdirs()) {
                    return ""
                }
            }

            val fileName = "faceidmodel.bin"
            val file = File(dir, fileName)

            var fos: FileOutputStream? = null
            var inputStream: InputStream? = null
            var ret = ""

            try {
                var count: Int
                val buffer = ByteArray(1024)
                fos = FileOutputStream(file)
                inputStream = reactApplicationContext.assets.open(fileName)
                while (inputStream.read(buffer).also { count = it } != -1) {
                    fos.write(buffer, 0, count)
                }
                ret = file.absolutePath
            } catch (e: java.lang.Exception) {
                ELRTools.recordLog(e)
                return ret
            } finally {
                try {
                    fos?.close()
                    inputStream?.close()
                } catch (e: java.lang.Exception) {
                    ELRTools.recordLog(e)
                }
            }
//            println("#ret $ret")
            return ret
        }


        /** 开始活体验证  */
    @ReactMethod fun sFLV(biz_token: String = "") {
            try {
                mBT = biz_token
                mFMP = sFMFA()
                // 创建
                val maps = HashMap<String, Any>()
                maps["url_path"] = DCTools.gFLCU()
                MegLiveManager.getInstance().preDetect(
                    SplashActivity.mActivity,
                    mBT,
                    "en",
                    IAUC.B_U_F,
                    mFMP,
                    maps,
                    this
                )
                sE(F_L_PRE_DETECT)
            } catch (e: Exception) {
                sE(F_L_PRE_DETECT_STATE_FAIL)
                ELRTools.recordLog(e)
            }
        }

    fun cFLEP(eventType: String,  faceLivenessFilePath: String? = null, megliveData: String? = null, bizToken: String? = null): WritableMap {
        return Arguments.createMap().apply {
            // 事件名称
            putString("eventType", eventType)
            // 原始活体文件地址
            faceLivenessFilePath?.let {
                putString("faceLivenessFilePath", it)
            }
            // 活体检测数据
            megliveData?.let {
                putString("megliveData", it)
            }
            // bizToken
            bizToken?.let {
                putString("mBizToken", it)
            }
        }
    }

    fun sE(eventType: String,  faceLivenessFilePath: String? = null, megliveData: String? = null, bizToken: String? = null) {
        NM.sE(
            REEEN.F_L_E_N_S,
            cFLEP(eventType, faceLivenessFilePath, megliveData, bizToken)
        )
    }

    companion object {
        const val F_L_PRE_DETECT = "FACE_LIVE_PRE_DETECT"
        /** face 活体预检测失败 */
        const val F_L_PRE_DETECT_STATE_FAIL = "FACE_LIVE_PRE_DETECT_STATE_FAIL"
        /** face 活体在开始前 */
        const val F_L_PRE_START = "FACE_LIVE_PRE_START"
        /** face 活体在开始完成 */
        const val F_L_PRE_FINISH = "FACE_LIVE_PRE_FINISH"
        /** face 活体在开始完成之后发生错误 */
        const val F_L_PRE_FINISH_STATE_FAIL = "FACE_LIVE_PRE_FINISH_STATE_FAIL"
        /** face 活体在开始完成之后成功 */
//        const val FACE_LIVE_PRE_FINISH_STATE_SUCCESS = "FACE_LIVE_PRE_FINISH_STATE_SUCCESS"
        const val F_L_START_DETECT = "FACE_LIVE_START_DETECT"
        /** face 活体在执行完成之后, 因为用户行为导致的失败，例如用户取消、人脸移出等 */
        const val F_L_START_DETECT_STATE_FAIL = "FACE_LIVE_START_DETECT_STATE_FAIL"
        /** face 活体在执行完成之后, 因为token失效、sdk版本过低导致的错误 */
        const val F_L_START_DETECT_STATE_ERROR = "FACE_LIVE_START_DETECT_STATE_ERROR"
        /** face 活体在执行完成之后成功 */
        const val F_L_START_DETECT_STATE_SUCCESS = "FACE_LIVE_END_FINISH_STATE_SUCCESS"
    }
}