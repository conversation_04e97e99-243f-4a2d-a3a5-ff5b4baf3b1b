package com.prestamo.credito.dinero.efectivo.facil.rapido.noro.plus.r.m

import android.annotation.SuppressLint
import android.app.Activity
import android.app.Activity.RESULT_OK
import android.content.Intent
import android.provider.ContactsContract
import com.facebook.react.bridge.*
import com.prestamo.credito.dinero.efectivo.facil.rapido.noro.plus.r.REEEN
import java.util.regex.Pattern

class CP(reactContext: ReactApplicationContext) : ActivityEventListener, ReactContextBaseJavaModule(reactContext) {
    override fun getName() = "ContactPicker";

    companion object {
        private const val REQUEST_CONTRACT_CODE = 1006
        /** 获取 google email */
        private const val C_GET_START = "CONTRACT_GET_START"
        /** google email 获取成功 */
        private const val C_GET_SUCCESS = "CONTRACT_GET_SUCCESS"
        /** google email 获取失败 */
        private const val C_GET_FAIL = "CONTRACT_GET_FAIL"

        lateinit var mRC: ReactApplicationContext
    }

    init {
        mRC = reactContext
        mRC.addActivityEventListener(this)
    }

    /** 获取手机邮箱*/
    @ReactMethod fun pC() {
        sE(C_GET_START)
        mRC.startActivityForResult(cCPI(),
            REQUEST_CONTRACT_CODE, null)
    }

    private fun cCPI(): Intent {
        return Intent(Intent.ACTION_PICK, ContactsContract.CommonDataKinds.Phone.CONTENT_URI)
    }

    private fun cGCPP(eventType: String, name: String? = null, number: String? = null): WritableMap? {
        return Arguments.createMap().apply {
            // 事件名称
            putString("eventType", eventType)
            // 联系人名
            name?.let {
                putString("name", it)
            }
            // 联系人手机号
            number?.let {
                putString("number", it)
            }
        }
    }

    fun sE(eventType: String,  name: String? = null, number: String? = null) {
        NM.sE(
            REEEN.C_P_E_N_S,
            cGCPP(eventType, name, number)
        )
    }
    /** 格式化手机号 */
    private fun mF(str: String): String {
        val regEx = "[^0-9]"
        val p = Pattern.compile(regEx)
        val m = p.matcher(str)
        return m.replaceAll("").trim()
    }

    @SuppressLint("Recycle", "Range", "SuspiciousIndentation")
    override fun onActivityResult(p0: Activity?, requestCode: Int, resultCode: Int, data: Intent?) {
        if (requestCode == REQUEST_CONTRACT_CODE) {
            if (resultCode == RESULT_OK) {
                if (data != null) {
                    val contactUri = data.data
                    contactUri?.let {
                        var name = ""
                        var number = ""
                        val queryFields = arrayOf("data1", "display_name")
                        mRC.contentResolver.query(contactUri, queryFields, null, null, null)?.use { cursor ->
                            if (cursor.moveToFirst()) {
                                number = cursor.getString(cursor.getColumnIndex("data1"))
                                name = cursor.getString(cursor.getColumnIndex("display_name"))
                            } else {
//                                Log.e("tag", "Cursor is empty")
                            }
                        }
                        number = mF(number)
//                        Log.d("TAG", "name:$name, phone:$number ")

                        if (number.isNotBlank()) {
                            if (name.isBlank()) {
                                name = number
                            }
                        }
                        if (name.isBlank() || number.isBlank()) {
                            // 获取失败
                            sE(C_GET_FAIL)
                        } else {
                            // 获取成功
                            sE(C_GET_SUCCESS, name, number)
                        }
                    }

                }
            } else {
                sE(C_GET_FAIL)
            }
        }
    }



    override fun onNewIntent(p0: Intent?) {

    }

}
