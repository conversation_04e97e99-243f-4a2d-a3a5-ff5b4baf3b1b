package com.prestamo.credito.dinero.efectivo.facil.rapido.noro.plus.t

import android.content.Context
import com.prestamo.credito.dinero.efectivo.facil.rapido.noro.plus.MyApplication
import java.io.IOException

object LCTools {

    fun sString(cacheKey: String, value: String): Bo<PERSON>an {
        try {
            val sp =
                MyApplication.mApplication.getSharedPreferences(DCTools.gAppId(), Context.MODE_PRIVATE)
                    .edit()
            sp.putString(cacheKey, value)
            sp.apply()
        } catch (e: IOException) {
            return false
        }
        return true
    }

    fun sBoolean(cacheKey: String, value: Boolean): Boolean {
        try {
            val sp =
                MyApplication.mApplication.getSharedPreferences(DCTools.gAppId(), Context.MODE_PRIVATE)
                    .edit()
            sp.putBoolean(cacheKey, value)
            sp.apply()
        } catch (e: IOException) {
            e.printStackTrace()
            return false
        }
        return true
    }

    fun sLong(cacheKey: String, value: Long): Boolean {
        return try {
            val sp = MyApplication.mApplication.getSharedPreferences(DCTools.gAppId(), Context.MODE_PRIVATE)
                    .edit()
            sp.putLong(cacheKey, value)
            sp.apply()
            true
        } catch (e: IOException) {
            e.printStackTrace()
            false
        }
    }

    fun sInt(cacheKey: String, value: Int): Boolean {
        return try {
            val sp = MyApplication.mApplication.getSharedPreferences(DCTools.gAppId(), Context.MODE_PRIVATE).edit()
            sp.putInt(cacheKey, value)
            sp.apply()
            true
        } catch (e: IOException) {
            e.printStackTrace()
            false
        }
    }

    fun sFloat(cacheKey: String, value: Float): Boolean {
        return try {
            val sp = MyApplication.mApplication.getSharedPreferences(DCTools.gAppId(), Context.MODE_PRIVATE)
                    .edit()
            sp.putFloat(cacheKey, value)
            sp.apply()
            true
        } catch (e: IOException) {
            e.printStackTrace()
            false
        }

    }

    fun gString(cacheKey: String): String {
        return try {
            val sp = MyApplication.mApplication.getSharedPreferences(DCTools.gAppId(), Context.MODE_PRIVATE)
            sp.getString(cacheKey, "").toString()

        } catch (e: IOException) {
            ""
        }
    }

    fun gBoolean(cacheKey: String): Boolean {
        return try {
            val sp = MyApplication.mApplication.getSharedPreferences(DCTools.gAppId(), Context.MODE_PRIVATE)
            sp.getBoolean(cacheKey, false)
        } catch (e: IOException) {
            ELRTools.recordLog(e)
            false
        }
    }

    fun gLong(cacheKey: String): Long {
        return try {
            val sp = MyApplication.mApplication.getSharedPreferences(DCTools.gAppId(), Context.MODE_PRIVATE)
            sp.getLong(cacheKey, 0L)
        } catch (e: IOException) {
            ELRTools.recordLog(e)
            0L
        }
    }

    fun gInt(cacheKey: String): Int {
        return try {
            val sp = MyApplication.mApplication.getSharedPreferences(DCTools.gAppId(), Context.MODE_PRIVATE)
            sp.getInt(cacheKey, 0)
        } catch (e: IOException) {
            ELRTools.recordLog(e)
            0
        }
    }

    fun gFloat(cacheKey: String, value: Float): Float {
        return try {
            val sp = MyApplication.mApplication.getSharedPreferences(DCTools.gAppId(), Context.MODE_PRIVATE)
            sp.getFloat(cacheKey, value)
        } catch (e: IOException) {
            ELRTools.recordLog(e)
            0.0F
        }
    }
}