package com.prestamo.credito.dinero.efectivo.facil.rapido.noro.plus.r.m

import android.annotation.SuppressLint
import com.facebook.react.bridge.*
import java.util.*
import android.content.BroadcastReceiver
import android.content.IntentFilter
import android.util.Log
import com.facebook.react.bridge.Promise
import com.facebook.react.bridge.ReactApplicationContext
import com.google.android.gms.auth.api.phone.SmsRetriever
import com.google.android.gms.tasks.OnFailureListener
import com.google.android.gms.tasks.OnSuccessListener
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Build
import androidx.annotation.RequiresApi
import androidx.core.app.ActivityCompat.startIntentSenderForResult
import com.facebook.react.bridge.ActivityEventListener
import com.facebook.react.bridge.BaseActivityEventListener
import com.google.android.gms.auth.api.identity.GetPhoneNumberHintIntentRequest
import com.google.android.gms.auth.api.identity.Identity
import com.prestamo.credito.dinero.efectivo.facil.rapido.noro.plus.r.REEEN.SMS_CODE_RECEIVE_NAME_SPACE
import com.prestamo.credito.dinero.efectivo.facil.rapido.noro.plus.t.GPSTools


class SMS (reactContext: ReactApplicationContext) : ReactContextBaseJavaModule(reactContext) {

    private val mPhoneNumberHelper by lazy {
        PhoneNumberHelper()
    }

    private val mSmsHelper by lazy {
        SMSHelper(reactContext)
    }

    private val mAppSignatureHelper by lazy {
        AppSignatureHelper(reactContext)
    }

    override fun getName(): String {
        return "SmsRetriever"
    }

    override fun getConstants(): MutableMap<String, Any> {
        val constants: MutableMap<String, Any> = HashMap()
        constants["SMS_EVENT"] = SMS_CODE_RECEIVE_NAME_SPACE
        return constants
    }

    @ReactMethod
    fun getAppSignature(promise: Promise) {
        val firstSignature = mAppSignatureHelper.appSignatures.first()

        Log.d(name, "getAppSignature:"+firstSignature)
        promise.resolve(Arguments.createMap().apply {
            putString("signature", firstSignature)
        })
    }

    @ReactMethod
    fun startSmsRetriever(promise: Promise) {
        mSmsHelper.startRetriever(promise)
    }

    @ReactMethod
    fun requestPhoneNumber(promise: Promise) {
        val context = reactApplicationContext
        val activity = currentActivity
        val eventListener: ActivityEventListener = mPhoneNumberHelper.activityEventListener
        context.addActivityEventListener(eventListener)
        mPhoneNumberHelper.setListener(object : PhoneNumberHelper.Listener {
            override fun phoneNumberResultReceived() {
                context.removeActivityEventListener(eventListener)
            }
        })
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.DONUT) {
            mPhoneNumberHelper.requestPhoneNumber(context, activity, promise)
        }
    }
}

class PhoneNumberHelper {
    private var mPromise: Promise? = null
    private var mListener: Listener? = null

    fun setListener(listener: Listener) {
        mListener = listener
    }

    @RequiresApi(Build.VERSION_CODES.DONUT)
    fun requestPhoneNumber(context: Context, activity: Activity?, promise: Promise?) {
        if (promise == null) {
            callAndResetListener()
            return
        }
        mPromise = promise
        if (!GPSTools.iGPSA()) {
            promiseReject(GPSTools.UNAVAILABLE_ERROR_TYPE, GPSTools.UNAVAILABLE_ERROR_MESSAGE)
            callAndResetListener()
            return
        }
        if (!GPSTools.hSV(context)) {
            promiseReject(GPSTools.UNSUPORTED_VERSION_ERROR_TYPE, GPSTools.UNSUPORTED_VERSION_ERROR_MESSAGE)
            callAndResetListener()
            return
        }
        if (activity == null) {
            promiseReject(ACTIVITY_NULL_ERROR_TYPE, ACTIVITY_NULL_ERROR_MESSAGE)
            callAndResetListener()
            return
        }
        val request = GetPhoneNumberHintIntentRequest.builder().build()
        Identity.getSignInClient(activity).getPhoneNumberHintIntent(request).addOnSuccessListener {
                pendingIntent ->
            try {
                startIntentSenderForResult(activity, pendingIntent.intentSender, 1993, null, 0, 0, 0, null)
            } catch (e: Exception) {
                Log.d("PhoneNumber", e.stackTraceToString())
            }
        }
//        val hintRequest = HintRequest.Builder()
//            .setPhoneNumberIdentifierSupported(true)
//            .build()
//        val intent = Credentials.getClient(activity).getHintPickerIntent(hintRequest)
//        val phoneNumberHintIntentRequest = GetPhoneNumberHintIntentRequest.builder().build()
//         ActivityResultContracts.StartActivityForResult()
//        try {
//            activity.startIntentSenderForResult(intent.intentSender,
//                REQUEST_PHONE_NUMBER_REQUEST_CODE, null, 0, 0, 0)
//        } catch (e: SendIntentException) {
//            promiseReject(SEND_INTENT_ERROR_TYPE, SEND_INTENT_ERROR_MESSAGE)
//            callAndResetListener()
//        }
    }

    private fun callAndResetListener() {
        if (mListener != null) {
            mListener!!.phoneNumberResultReceived()
            mListener = null
        }
    }

    //endregion
    //region - Promises
    private fun promiseResolve(value: Any) {
        if (mPromise != null) {
            mPromise!!.resolve(value)
            mPromise = null
        }
    }

    private fun promiseReject(type: String, message: String) {
        if (mPromise != null) {
            mPromise!!.reject(type, message)
            mPromise = null
        }
    }

    //region - Package Access
    val activityEventListener: ActivityEventListener = object : BaseActivityEventListener() {
        override fun onActivityResult(activity: Activity, requestCode: Int, resultCode: Int, data: Intent?) {
            super.onActivityResult(activity, requestCode, resultCode, data)
//            if (requestCode == REQUEST_PHONE_NUMBER_REQUEST_CODE && resultCode == Activity.RESULT_OK && data != null) {
//                val credential: Credential? = data.getParcelableExtra(Credential.EXTRA_KEY)
//                if (credential == null) {
//                    promiseReject(ACTIVITY_RESULT_NOOK_ERROR_TYPE, ACTIVITY_RESULT_NOOK_ERROR_MESSAGE)
//                    callAndResetListener()
//                    return
//                }
//                val phoneNumber = credential.id
//                promiseResolve(phoneNumber)
//                callAndResetListener()
//                return
//            }
            promiseReject(ACTIVITY_RESULT_NOOK_ERROR_TYPE, ACTIVITY_RESULT_NOOK_ERROR_MESSAGE)
            callAndResetListener()
        }
    }

    //endregion
    //region - Classes
    interface Listener {
        fun phoneNumberResultReceived()
    } //endregion

    companion object {
        private const val REQUEST_PHONE_NUMBER_REQUEST_CODE = 1
        private const val ACTIVITY_NULL_ERROR_TYPE = "ACTIVITY_NULL_ERROR_TYPE"
        private const val ACTIVITY_RESULT_NOOK_ERROR_TYPE = "ACTIVITY_RESULT_NOOK_ERROR_TYPE"
        private const val CONNECTION_SUSPENDED_ERROR_TYPE = "CONNECTION_SUSPENDED_ERROR_TYPE"
        private const val CONNECTION_FAILED_ERROR_TYPE = "CONNECTION_FAILED_ERROR_TYPE"
        private const val SEND_INTENT_ERROR_TYPE = "SEND_INTENT_ERROR_TYPE"
        private const val ACTIVITY_NULL_ERROR_MESSAGE = "Activity is null."
        private const val ACTIVITY_RESULT_NOOK_ERROR_MESSAGE = "There was an error trying to get the phone number."
        private const val CONNECTION_SUSPENENDED_ERROR_MESSAGE = "Client is temporarily in a disconnected state."
        private const val CONNECTION_FAILED_ERROR_MESSAGE = "There was an error connecting the client to the service."
        private const val SEND_INTENT_ERROR_MESSAGE = "There was an error trying to send intent."
    }
}


class SMSHelper(private val mContext: ReactApplicationContext) {
    private var mReceiver: BroadcastReceiver? = null
    private var mPromise: Promise? = null

    //region - Package Access
    fun startRetriever(promise: Promise?) {
        mPromise = promise
//        if (!GPSTools.iGPSA(mContext)) {
//            promiseReject(GPSTools.UNAVAILABLE_ERROR_TYPE, GPSTools.UNAVAILABLE_ERROR_MESSAGE)
//            return
//        }
//        if (!GPSTools.hSV(mContext)) {
//            promiseReject(GPSTools.UNSUPORTED_VERSION_ERROR_TYPE, GPSTools.UNSUPORTED_VERSION_ERROR_MESSAGE)
//            return
//        }
        Log.d("SMS_TEST","startRetriever")
        val client = SmsRetriever.getClient(mContext)
        val task = client.startSmsRetriever()
        task.addOnSuccessListener(mOnSuccessListener)
        task.addOnFailureListener(mOnFailureListener)
    }

    //endregion
    //region - Privates
    @SuppressLint("UnspecifiedRegisterReceiverFlag")
    private fun tryToRegisterReceiver(): Boolean {
        mReceiver = SmsBR(mContext)
        val intentFilter = IntentFilter(SmsRetriever.SMS_RETRIEVED_ACTION)
//        mContext.registerReceiver(mReceiver, intentFilter)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            mContext.registerReceiver(
                mReceiver,
                intentFilter,
                SmsRetriever.SEND_PERMISSION, // 可选：声明发送权限（若需要）
                null,
                Context.RECEIVER_EXPORTED   // 仅接收本应用或系统发送的广播
            )
        } else {
//            mContext.registerReceiver(mReceiver, intentFilter)
            mContext.registerReceiver(mReceiver, intentFilter);
        }
        return true
//        return try {
//            registerReceiver(
//                mContext,
//                mReceiver,
//                intentFilter
//            )
//            true
//        } catch (e: Exception) {
//            e.printStackTrace()
//            false
//        }
    }

    private fun unregisterReceiverIfNeeded() {
        if (mReceiver == null) {
            return
        }
        try {
            mContext.unregisterReceiver(mReceiver)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    //endregion
    //region - Promises
    private fun promiseResolve(value: Any) {
        if (mPromise != null) {
            mPromise!!.resolve(value)
            mPromise = null
        }
    }

    private fun promiseReject(type: String, message: String) {
        if (mPromise != null) {
            mPromise!!.reject(type, message)
            mPromise = null
        }
    }

    //endregion
    //region - Listeners
    private val mOnSuccessListener: OnSuccessListener<Void> = OnSuccessListener {
        val registered = tryToRegisterReceiver()
        Log.d("SMS_TEST", "mOnSuccessListener"+registered)
        promiseResolve(registered)
    }
    private val mOnFailureListener = OnFailureListener {
        Log.d("SMS_TEST", "mOnFailureListener")
        unregisterReceiverIfNeeded()
        promiseReject(TASK_FAILURE_ERROR_TYPE, TASK_FAILURE_ERROR_MESSAGE)
    } //endregion

    companion object {
        private const val TASK_FAILURE_ERROR_TYPE = "TASK_FAILURE_ERROR_TYPE"
        private const val TASK_FAILURE_ERROR_MESSAGE = "Task failed."
    }

}