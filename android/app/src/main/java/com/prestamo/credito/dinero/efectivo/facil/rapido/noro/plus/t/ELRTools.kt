package com.prestamo.credito.dinero.efectivo.facil.rapido.noro.plus.t

import android.annotation.SuppressLint
import android.content.Context
import android.util.Log
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import java.io.File
import java.io.FileWriter
import java.io.IOException
import java.text.SimpleDateFormat
import java.util.*
import java.util.regex.Pattern
import kotlin.system.exitProcess

/** 日志等级 */
enum class ErrorLevel {
    /** 内容 */
    INFO,

    /** 警告 */
    WARN,

    /** debug */
    DEBUG,

    /** 错误 */
    ERROR,
}


// 定义日志记录类
data class LogRecord(
    val message: String,
    val logTime: String,
    val errorLevel: String = ErrorLevel.ERROR.name
)

object ELRTools {
    @SuppressLint("StaticFieldLeak")
    lateinit var mErrorLogRecordControl: ELRC

    fun init(context: Context) {
        mErrorLogRecordControl = ELRC(context)
        // 收集未被捕获的异常信息，在监测到未被异常捕获的错误信息后，主动退出 App
        Thread.setDefaultUncaughtExceptionHandler(ExceptionHandler())
    }

    class ELRC(context: Context) {

        companion object {
            private const val LOG_DIR_NAME = "error_logs"
            private const val LOG_FILE_NAME = "error.log"
            private const val TAG = "ErrorLogCollector"
        }

        private val logDir = File(context.cacheDir, LOG_DIR_NAME)
        private val logFile = File(logDir, LOG_FILE_NAME)
        private val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS", Locale.getDefault())

        /** 缓存日志数据 */
        private var recordsCache = emptyArray<LogRecord>()

        init {
            if (!logFile.exists()) {
                logFile.parentFile?.mkdirs()
                logFile.createNewFile()
            }
        }

        fun getLogFilePath(): String = logFile.absolutePath

        // 将日志记录数组保存为JSON字符串
        fun saveLogRecords(records: Array<LogRecord>, file: File): Boolean {
            var saveResult = true
            try {
                val json = Gson().toJson(records)
                file.writeText(json)
            } catch (e: IOException) {
                // 处理IO异常
                saveResult = false
                e.printStackTrace()
            }
            return saveResult
        }

        fun isValidJson(jsonString: String): Boolean {
            val pattern = Pattern.compile("^[\\{\\[].*[\\}\\]]$")
            return pattern.matcher(jsonString).matches()
        }

        // 读取日志记录数组并添加新记录
        fun appendLogRecord(record: LogRecord? = null) {
            synchronized(logFile) {
                var records = emptyArray<LogRecord>()
                try {
                    // 读取文件中的JSON字符串
                    val json = logFile.readText()
                    if (json.isNotEmpty() && isValidJson(json)) {
                        // 验证字符串不为空。并且为json格式的字符串
                        // 解析JSON字符串
                        val type = object : TypeToken<Array<LogRecord>>() {}.type
                        records = Gson().fromJson(json, type)
                    }
                } catch (e: IOException) {
                    // 处理IO异常
                    e.printStackTrace()
                }
                // 如果缓存的错误日志数据不为空就把缓存数据加上
                if (recordsCache.isNotEmpty()) {
                    records += recordsCache
                }
                // 将新记录添加到数组中
                record?.let {
                    records += it
                }
                // 保存日志记录数组
                val saveResult = saveLogRecords(records, logFile)

                if (saveResult) {
                    recordsCache = emptyArray()
                } else {
                    record?.let {
                        recordsCache += it
                    }
                }
            }
        }

        /**
         * 记录日志
         */
        fun recordLog(logMessage: String) {
            val logTime: String = dateFormat.format(Date())
            if (!UTools.iPR) {
                Log.e(TAG, logMessage)
            }
            try {
                appendLogRecord(LogRecord(message = logMessage, logTime = logTime))
            } catch (e: IOException) {
                Log.e(TAG, "Error writing to file: ${e.message}")
            }
        }

        /**
         * 清空日志
         */
        fun clearLogs() {
            if (logFile.exists()) {
                synchronized(logFile) {
                    try {
                        FileWriter(logFile, false).use { writer ->
                            writer.write("")
                        }
                    } catch (e: IOException) {
                        Log.e(TAG, "Error clearing log file: ${e.message}")
                    }
                }
            }
        }
    }

    class ExceptionHandler : Thread.UncaughtExceptionHandler {
        override fun uncaughtException(t: Thread, e: Throwable) {
            val stackTrace = StringBuilder()
            for (element in e.stackTrace) {
                stackTrace.append(element.toString())
                stackTrace.append("\n")
            }
            val logMessage = "Uncaught Exception:\n${e.message}\n${stackTrace}"
             mErrorLogRecordControl.recordLog(logMessage)
            android.os.Process.killProcess(android.os.Process.myPid())
            exitProcess(1)
        }
    }

    /** RN 记录日志 */
    fun recordLog(logMessage: String) = mErrorLogRecordControl.recordLog(logMessage)

    /** native 记录错误日志 */
    fun recordLog(e: Exception) {
        // 记录到内部的日志系统
        mErrorLogRecordControl.recordLog(e.stackTraceToString())
    }

    /** 缓存日志 */
    fun clearLogs() = mErrorLogRecordControl.clearLogs()

    /** 获取错误日志文件 */
    fun getLogFilePath(): String = mErrorLogRecordControl.getLogFilePath()
}
