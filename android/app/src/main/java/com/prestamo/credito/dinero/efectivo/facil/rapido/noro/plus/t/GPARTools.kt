package com.prestamo.credito.dinero.efectivo.facil.rapido.noro.plus.t

import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import com.prestamo.credito.dinero.efectivo.facil.rapido.noro.plus.SplashActivity
import com.prestamo.credito.dinero.efectivo.facil.rapido.noro.plus.MyApplication

object GPARTools {
    fun rGPAR() {
        if(GPSTools.iGPSA()) {
            try {
                if (iGPI()) {
                    SplashActivity.mActivity.startActivity(Intent(Intent.ACTION_VIEW, Uri.parse("market://details?id=${DCTools.gALId()}")))
                } else {
                    SplashActivity.mActivity.startActivity(Intent(Intent.ACTION_VIEW, Uri.parse(DCTools.gPSU())))
                }
            } catch ( e: Exception ) {
                e.printStackTrace()
            }
        } else {
                // 没有 谷歌服务不可以的的情况 跳转到 GP play 网站
            SplashActivity.mActivity.startActivity(Intent(Intent.ACTION_VIEW, Uri.parse(DCTools.gPSU())))
        }
    }

    private fun iGPI(): Boolean {
        return try {
            // 获取当前应用的 PackageManager 对象
            val packageManager = MyApplication.mApplication.packageManager
            // 获取 Google Play 的 PackageInfo 对象
            val packageInfo = packageManager.getPackageInfo("com.android.vending", 0)

            // 检查 Google Play 是否启用
            val enabled = packageManager.getApplicationEnabledSetting("com.android.vending")
            (enabled == PackageManager.COMPONENT_ENABLED_STATE_DEFAULT || enabled == PackageManager.COMPONENT_ENABLED_STATE_ENABLED)
                    && packageInfo != null
        } catch (e: Exception) {
            // 如果发生异常，则假定 Google Play 未安装
            false
        }
    }


}