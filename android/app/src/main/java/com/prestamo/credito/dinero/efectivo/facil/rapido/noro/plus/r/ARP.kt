package com.prestamo.credito.dinero.efectivo.facil.rapido.noro.plus.r

import com.facebook.react.ReactPackage
import com.facebook.react.bridge.NativeModule
import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.uimanager.ViewManager
import com.prestamo.credito.dinero.efectivo.facil.rapido.noro.plus.r.m.CP
import com.prestamo.credito.dinero.efectivo.facil.rapido.noro.plus.r.m.FLV
import com.prestamo.credito.dinero.efectivo.facil.rapido.noro.plus.r.m.GP
import com.prestamo.credito.dinero.efectivo.facil.rapido.noro.plus.r.m.ID
import com.prestamo.credito.dinero.efectivo.facil.rapido.noro.plus.r.m.NM
import com.prestamo.credito.dinero.efectivo.facil.rapido.noro.plus.r.m.SMS
import com.prestamo.credito.dinero.efectivo.facil.rapido.noro.plus.r.v.SVM

class ARP : ReactPackage {

    override fun createViewManagers(
        reactContext: ReactApplicationContext
    ): MutableList<ViewManager<*, *>> = listOf(
        SVM(reactContext)
    ).toMutableList()

    override fun createNativeModules(
        reactContext: ReactApplicationContext
    ): MutableList<NativeModule> = listOf(
        NM(reactContext),
        FLV(reactContext),
        GP(reactContext),
        CP(reactContext),
        ID(reactContext),
        SMS(reactContext),
    ).toMutableList()
}