package com.prestamo.credito.dinero.efectivo.facil.rapido.noro.plus.r.m
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import com.facebook.react.bridge.Arguments
import com.facebook.react.bridge.Promise
import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.bridge.ReactContextBaseJavaModule
import com.facebook.react.bridge.ReactMethod
import com.prestamo.credito.dinero.efectivo.facil.rapido.noro.plus.SplashActivity
import com.prestamo.credito.dinero.efectivo.facil.rapido.noro.plus.BuildConfig

class ID(reactContext: ReactApplicationContext) : ReactContextBaseJavaModule(reactContext) {
  override fun getName() = "IntentData";

    @ReactMethod fun gLM(promise: Promise) {
      val intent: Intent? = SplashActivity.mActivity.intent
      if (intent != null) {
          val action: String? = intent.action
          val data: Uri? = intent.data
          if (action != null) {
//                  Log.d("TAG", "action $action data $data")
              when (action) {
                  Intent.ACTION_MAIN -> {
                      // 通过点击应用程序图标打开
                      promise.resolve(Arguments.createMap().apply {
                          // 启动方式
                          putString("launchMethod", "icon_launch")
                          putString("data", "1")
                      })
                  }
                  Intent.ACTION_VIEW -> {
                      promise.resolve(Arguments.createMap().apply {
                          putString("launchMethod", "deeplink_launch")
                          putString("data", "$data")
                      })
                  }
                  "${BuildConfig.APPLICATION_ID}_NOTIFICATION_ACTION" -> {
//                          Log.d("TAG", "Opened from push notification")
                      val bootLabel = intent.extras?.getString("bootLabel", "")
                      val event = intent.extras?.getString("event", "")
                      promise.resolve(Arguments.createMap().apply {
                          putString("launchMethod", "push_launch")
                          putString("data", "$bootLabel$event")
                      })
                  }
              }
          }
      }
  }
    @ReactMethod fun cID() {
        val intent: Intent? = SplashActivity.mActivity.intent
        intent?.action = null
        intent?.data = null
        intent?.replaceExtras(Bundle())
    }
}

