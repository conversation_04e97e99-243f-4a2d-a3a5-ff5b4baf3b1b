package com.prestamo.credito.dinero.efectivo.facil.rapido.noro.plus.t

import android.Manifest
import android.app.NotificationManager
import android.content.Context
import android.content.pm.PackageManager
import android.os.Build
import androidx.core.content.ContextCompat
import com.prestamo.credito.dinero.efectivo.facil.rapido.noro.plus.MyApplication

object FCMTools {

    fun hNP(): Boolean {
        val context = MyApplication.mApplication.applicationContext
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N && Build.VERSION.SDK_INT  < Build.VERSION_CODES.TIRAMISU) {
            val notificationManager =
                context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            // 检查是否具有通知访问权限
            return notificationManager.areNotificationsEnabled()
        }
        else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            return PackageManager.PERMISSION_GRANTED == ContextCompat.checkSelfPermission(
                context,
                Manifest.permission.POST_NOTIFICATIONS
            )
        }
        // Android 5.1 以下版本默认有通知权限
        return true
    }

//    const val TAG = "NcFCMTools"
}