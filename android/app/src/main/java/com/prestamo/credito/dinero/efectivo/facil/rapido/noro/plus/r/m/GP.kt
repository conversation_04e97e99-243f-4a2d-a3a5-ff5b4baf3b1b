package com.prestamo.credito.dinero.efectivo.facil.rapido.noro.plus.r.m
import android.accounts.AccountManager
import android.app.Activity
import android.app.Activity.RESULT_OK
import android.content.Intent
import com.facebook.react.bridge.*
import com.google.android.gms.common.AccountPicker
import com.prestamo.credito.dinero.efectivo.facil.rapido.noro.plus.R
import com.prestamo.credito.dinero.efectivo.facil.rapido.noro.plus.r.REEEN

/** 谷歌邮箱选择 */
class GP(reactContext: ReactApplicationContext) : ActivityEventListener, ReactContextBaseJavaModule(reactContext) {
    override fun getName() = "GmailPicker";

    companion object {
        private const val REQUEST_GOOGLE_EMAIL = 1005
        /** 获取 google email */
        private const val G_GET_START = "GMAIL_GET_START"
        /** google email 获取成功 */
        private const val G_GET_SUCCESS = "GMAIL_GET_SUCCESS"
        /** google email 获取失败 */
        private const val G_GET_FAIL = "GMAIL_GET_FAIL"

        lateinit var mRC: ReactApplicationContext
    }

    init {
        mRC = reactContext
        mRC.addActivityEventListener(this)
    }

    /** 获取手机邮箱*/
    @ReactMethod fun pG() {
        sE(G_GET_START)
        mRC.startActivityForResult(cAPI(),
            REQUEST_GOOGLE_EMAIL, null)
    }

    private fun cAPI(): Intent {
        val optionsBuilder = AccountPicker.AccountChooserOptions.Builder()
        optionsBuilder.setAllowableAccountsTypes(listOf("com.google"))
        optionsBuilder.setTitleOverrideText("${mRC.getString(R.string.peso_app_name)} desea obtener su dirección de Email.")
        // 继续配置其他选项...
        val options = optionsBuilder.build()
        // 创建账户选择器的意图
        val intent: Intent = AccountPicker.newChooseAccountIntent(options)
        return intent
    }

    fun cGGEP(eventType: String, email: String? = null): WritableMap? {
        return Arguments.createMap().apply {
            // 事件名称
            putString("eventType", eventType)
            // 活体检测数据
            email?.let {
                putString("email", it)
            }
        }
    }

    fun sE(eventType: String,  email: String? = null) {
        NM.sE(
            REEEN.G_P_E_N_S,
            cGGEP(eventType, email)
        )
    }

    override fun onActivityResult(p0: Activity?, requestCode: Int, resultCode: Int, data: Intent?) {
        if (requestCode == REQUEST_GOOGLE_EMAIL) {
            if (resultCode == RESULT_OK) {
                val accountName = data?.getStringExtra(AccountManager.KEY_ACCOUNT_NAME)
                if (accountName.isNullOrEmpty()) {
                    sE(G_GET_FAIL, accountName)
                } else {
                    sE(G_GET_SUCCESS, accountName)
                }
            } else {
                sE(G_GET_FAIL)
            }
        }
    }

    override fun onNewIntent(p0: Intent?) {

    }

}
