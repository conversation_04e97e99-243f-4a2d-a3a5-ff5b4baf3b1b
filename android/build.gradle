buildscript {
    ext {
        compileSdkVersion = 35
        minSdkVersion = 23
        buildToolsVersion = "35.0.1"
        kotlinVersion = "1.9.22"
        ndkVersion = "26.1.10909125"
        targetSdkVersion = 35

    }
    repositories {
        mavenCentral ()
        maven { url "https://maven.aliyun.com/nexus/content/groups/public/" }
        google()
        maven { url "https://jitpack.io" }
        maven {
            url "https://maven.google.com"
        }
    }

    dependencies {
        classpath("com.android.tools.build:gradle")
        classpath("com.facebook.react:react-native-gradle-plugin")
        classpath 'com.google.gms:google-services:4.4.2'
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin")
    }
}

allprojects {
    repositories {
        mavenCentral ()
        maven { url "https://maven.aliyun.com/nexus/content/groups/public/" }
        google()
        maven { url "https://jitpack.io" }
        maven {
            url "https://maven.google.com"
        }
    }

    configurations.configureEach {
        // 将缓存动态版本的时间设为 1 天
        resolutionStrategy.cacheDynamicVersionsFor 24, 'hours'
        // 将缓存变更模块的时间设为 1 天
        resolutionStrategy.cacheChangingModulesFor 24, 'hours'
    }
}

tasks.withType(JavaCompile).configureEach {
    options.compilerArgs "-Xmaxerrs=1000"
}

task clean(type: Delete) {
    delete rootProject.buildDir
}

apply plugin: "com.facebook.react.rootproject"
