plugins {
    id 'com.android.library'
}

android {
    compileSdkVersion rootProject.properties.get('compileSdkVersion', 29)
    buildToolsVersion rootProject.properties.get('buildToolsVersion', '29.0.2')

    defaultConfig {
        minSdkVersion 21
        targetSdkVersion rootProject.properties.get('targetSdkVersion', 29)
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }
}

dependencies {

   implementation 'androidx.appcompat:appcompat:1.4.1'
//    implementation 'com.google.android.material:material:1.1.0'
//    testImplementation 'junit:junit:4.+'
//    androidTestImplementation 'androidx.test.ext:junit:1.1.1'
//    androidTestImplementation 'androidx.test.espresso:espresso-core:3.2.0'
    implementation 'com.google.android.gms:play-services-ads-identifier:18.0.1'
    implementation 'com.google.code.gson:gson:2.8.6'
    implementation "com.google.android.gms:play-services-location:20.0.0"

}