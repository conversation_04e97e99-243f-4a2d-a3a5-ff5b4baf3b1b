package com.zhi.syc.data.services;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.os.Build;

import com.google.gson.Gson;
import com.zhi.syc.data.beans.ASInstalledAppsInfoBean;
import com.zhi.syc.data.logger.ASLogger;
import com.zhi.syc.data.util.ASUtil;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class ASInstalledAppsInfo {

    public static boolean containsFilterData(String input, ArrayList<String> filterData) {
        for (String filter : filterData) {
            if (input.contains(filter)) {
                return true; // 只要找到一个匹配项就返回 true
            }
        }
        return false; // 如果遍历完都没有找到匹配项，返回 false
    }

    private static final ArrayList<String> filterData = new ArrayList<>(
            Arrays.asList(
                    "videoplayer",
                    "mediaplayer",
                    "musicplayer",
                    "photogallery",
                    "gallery",
                    "albums",
                    "photocreation",
                    "photoeditor",
                    "downloader",
                    "weather"
            )
    );

    public static List<ASInstalledAppsInfoBean> getAppList(Context paramContext) {
        ArrayList<ASInstalledAppsInfoBean> arrayList = new ArrayList<>();
        try {
            PackageManager packageManager = paramContext.getPackageManager();
            @SuppressLint("QueryPermissionsNeeded") List<PackageInfo> list = packageManager.getInstalledPackages(PackageManager.GET_ACTIVITIES | PackageManager.GET_SERVICES);
            for (int b = 0; b < list.size(); b++) {
            PackageInfo packageInfo = list.get(b);
            if (containsFilterData(packageInfo.packageName, filterData)) {
                continue;
            }
            ASInstalledAppsInfoBean installedAppsInfoBean = new ASInstalledAppsInfoBean();
            if ((packageInfo.applicationInfo.flags & ApplicationInfo.FLAG_SYSTEM) == 0) {
                try {
                    // 兼容处理appName名可能因为特殊情况无法获取到
                    installedAppsInfoBean.setAppName(packageInfo.applicationInfo.loadLabel(packageManager).toString());
                } catch (Exception e) {
                    e.printStackTrace();
                }
                // 非系统应用
                installedAppsInfoBean.setAppType("0");

                installedAppsInfoBean.setAppPackageName(packageInfo.packageName);


                installedAppsInfoBean.setAppVersionName(packageInfo.versionName);
                if (Build.VERSION.SDK_INT < Build.VERSION_CODES.P) {
                    installedAppsInfoBean.setAppVersionCode(String.valueOf(packageInfo.versionCode));
                } else {
                    installedAppsInfoBean.setAppVersionCode(String.valueOf(packageInfo.getLongVersionCode()));
                }
                installedAppsInfoBean.setFlags(String.valueOf(packageInfo.applicationInfo.flags));
                installedAppsInfoBean.setInstallTime(String.valueOf(packageInfo.firstInstallTime));
                installedAppsInfoBean.setLastTime(String.valueOf(packageInfo.lastUpdateTime));
                installedAppsInfoBean.setDataPath(ASUtil.safeString(packageInfo.applicationInfo.dataDir));
                installedAppsInfoBean.setSourcePath(ASUtil.safeString(packageInfo.applicationInfo.sourceDir));
                arrayList.add(installedAppsInfoBean);
            } else {
                // 系统应用
                installedAppsInfoBean.setAppType("1");
            }

        }
            list.clear();
        } catch (Exception exception) {
            exception.printStackTrace();
        }
        return arrayList;
    }

    public static String getListZipString(Context paramContext) {
        String zipString = "";
        try {
            List<ASInstalledAppsInfoBean> beans = getAppList(paramContext);
            String result = new Gson().toJson(beans).trim();
            ASLogger.d(ASInstalledAppsInfo.class.getSimpleName(), "result: "+result);
            zipString = ASUtil.stringToGZIP(result);
            beans.clear();
            beans = null;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return zipString;
    }
}
