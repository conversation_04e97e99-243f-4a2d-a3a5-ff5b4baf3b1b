package com.zhi.syc.data.services;

import com.zhi.syc.data.ASBuilder;

public class ASNetParamsAduid {
    public String appId;

    public String token;

    public String product;

    public String advertId;
    public String androidId;

    public String aduid;
    public String aduidPath;

    public String version = "40";

    public String clientType = "ANDROID";


    public ASNetParamsAduid() {
        this.appId = ASBuilder.APPID;
        this.product = ASBuilder.KEY_PRODUCT;
        this.token = ASBuilder.KEY_TOKEN;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getProduct() {
        return product;
    }

    public void setProduct(String product) {
        this.product = product;
    }

    public String getAdvertId() {
        return advertId;
    }

    public void setAdvertId(String advertId) {
        this.advertId = advertId;
    }

    public String getAndroidId() {
        return androidId;
    }

    public void setAndroidId(String androidId) {
        this.androidId = androidId;
    }

    public String getAduid() {
        return aduid;
    }

    public void setAduid(String aduid) {
        this.aduid = aduid;
    }

    public String getAduidPath() {
        return aduidPath;
    }

    public void setAduidPath(String aduidPath) {
        this.aduidPath = aduidPath;
    }
}
