package com.zhi.syc.data.beans;

import android.os.Build;

import com.zhi.syc.data.ASBuilder;

public class ASDeviceInfoBean {
    public String appId;

    public String token;

    public String product;

    public String applyOrderId;

    public String event;

    public String version;

    public String borrowId;

    //原来的数据----------------------------
    /**
     * 手机产品名
     */
    public String phoneModel = Build.PRODUCT;
    /**
     * 系统版本字符串
     */
    public String sysVersion = Build.VERSION.RELEASE;
    /**
     * 设备参数
     */
    public String deviceName = Build.DEVICE;
    /**
     * 系统定制商
     */
    public String brand = Build.BRAND;
    /**
     * 硬件制造商
     */
    public String manufacturer = Build.MANUFACTURER;
    /**
     * 语言
     */
    public String laguage;
    /**
     * 地区
     */
    public String area;
    /**
     * imei
     */
    // public String imei;
    /**
     * mac
     */
    // public String mac;
    /**
     * 屏幕高度
     */
    public String screenHeight;
    /**
     * 屏幕宽度
     */
    public String screenWidth;
    /**
     * 运营商名称
     */
    public String networkData;
    /**
     * 前摄像头分辨率
     */
    public String frontCameraPixels;
    /**
     * 后摄像头分辨率
     */
    public String rearCameraPixels;
    /**
     * 总内存
     */
    public String ram;
    /**
     * 总存储
     */
    public String rom;
    /**
     * ip
     */
    public String ip;
    /**
     * 网络类型：WIFI或MOBILE
     */
    public String networkEnvironment;
    /**
     * cpu型号
     */
    public String cpu;
    public Object blackBox;

    /** 设备启动时间 */
    public Long systemUptime;

    /** 系统启动次数 */
    public int bootCount;

    /** 设备指纹数量 */
    public int fingerprintCount = 0;

    public String advertId = "";

    public ASDeviceInfoBean(String event, String applyOrderId){
        this.appId = ASBuilder.APPID;
        this.product = ASBuilder.KEY_PRODUCT;
        this.token = ASBuilder.KEY_TOKEN;
        this.version = ASBuilder.SDK_VERSION;
        this.applyOrderId = applyOrderId;
        this.event = event;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getBorrowId() {
        return borrowId;
    }

    public void setBorrowId(String borrowId) {
        this.borrowId = borrowId;
    }

    public String getPhoneModel() {
        return phoneModel;
    }

    public void setPhoneModel(String phoneModel) {
        this.phoneModel = phoneModel;
    }

    public String getSysVersion() {
        return sysVersion;
    }

    public void setSysVersion(String sysVersion) {
        this.sysVersion = sysVersion;
    }

    public String getDeviceName() {
        return deviceName;
    }

    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public String getManufacturer() {
        return manufacturer;
    }

    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }

    public String getLaguage() {
        return laguage;
    }

    public void setLaguage(String laguage) {
        this.laguage = laguage;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    // public String getImei() {
    //     return imei;
    // }

    // public void setImei(String imei) {
    //     this.imei = imei;
    // }

    // public String getMac() {
    //     return mac;
    // }

    // public void setMac(String mac) {
    //     this.mac = mac;
    // }

    public String getScreenHeight() {
        return screenHeight;
    }

    public void setScreenHeight(String screenHeight) {
        this.screenHeight = screenHeight;
    }

    public String getScreenWidth() {
        return screenWidth;
    }

    public void setScreenWidth(String screenWidth) {
        this.screenWidth = screenWidth;
    }

    public String getNetworkData() {
        return networkData;
    }

    public void setNetworkData(String networkData) {
        this.networkData = networkData;
    }

    public String getFrontCameraPixels() {
        return frontCameraPixels;
    }

    public void setFrontCameraPixels(String frontCameraPixels) {
        this.frontCameraPixels = frontCameraPixels;
    }

    public String getRearCameraPixels() {
        return rearCameraPixels;
    }

    public void setRearCameraPixels(String rearCameraPixels) {
        this.rearCameraPixels = rearCameraPixels;
    }

    public String getRam() {
        return ram;
    }

    public void setRam(String ram) {
        this.ram = ram;
    }

    public String getRom() {
        return rom;
    }

    public void setRom(String rom) {
        this.rom = rom;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getNetworkEnvironment() {
        return networkEnvironment;
    }

    public void setNetworkEnvironment(String networkEnvironment) {
        this.networkEnvironment = networkEnvironment;
    }

    public String getCpu() {
        return cpu;
    }

    public void setCpu(String cpu) {
        this.cpu = cpu;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getProduct() {
        return product;
    }

    public void setProduct(String product) {
        this.product = product;
    }

    /** 设置设备启动时间 */
    public void setSystemUptime(Long systemUptime) {
        this.systemUptime = systemUptime;
    }

    public Long getSystemUptime() {
        return systemUptime;
    }

    /** 设置系统启动次数 */
    public void setBootCount(int bootCount) {
        this.bootCount = bootCount;
    }

    public int getBootCount() {
        return bootCount;
    }

    /** 设置设备指纹数量 */
    public void setFingerprintCount(int fingerprintCount) {
        this.fingerprintCount = fingerprintCount;
    }

    public int getFingerprintCount() {
        return fingerprintCount;
    }

    /** 谷歌广告id */
    public void setAadvertId(String advertId) {
        this.advertId = advertId;
    }

    public String getAdvertId() {
        return advertId;
    }
}