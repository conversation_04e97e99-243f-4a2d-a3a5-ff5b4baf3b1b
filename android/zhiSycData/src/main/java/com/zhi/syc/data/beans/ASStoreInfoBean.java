package com.zhi.syc.data.beans;

import com.zhi.syc.data.ASBuilder;

public class ASStoreInfoBean {
    //**********存储信息**********

    /**
     * 包含sd卡
     */
    public String containSd;
    /**
     * 可用内存
     */
    public String ramCanUse;
    /**
     * 总内存
     */
    public String ramTotal;
    /**
     * 可用存储
     */
    public String cashCanUse;
    /**
     * 总存储
     */
    public String cashTotal;
    /**
     * 包含外置sd卡
     */
    public String extraSD;
    /**
     * 总存储大小
     */
    public String internalTotal;
    /**
     * 可用存储大小
     */
    public String internalAvailable;
    /**
     * 内存已用大小
     */
    public String ramDidUsed;
    /**
     * 存储已用大小
     */
    public String cashDidUsed;
    /**
     * 内存卡大小
     */
    public String sdCardTotal;

    public String getContainSd() {
        return containSd;
    }

    public void setContainSd(String containSd) {
        this.containSd = containSd;
    }

    public String getRamCanUse() {
        return ramCanUse;
    }

    public void setRamCanUse(String ramCanUse) {
        this.ramCanUse = ramCanUse;
    }

    public String getRamTotal() {
        return ramTotal;
    }

    public void setRamTotal(String ramTotal) {
        this.ramTotal = ramTotal;
    }

    public String getCashCanUse() {
        return cashCanUse;
    }

    public void setCashCanUse(String cashCanUse) {
        this.cashCanUse = cashCanUse;
    }

    public String getCashTotal() {
        return cashTotal;
    }

    public void setCashTotal(String cashTotal) {
        this.cashTotal = cashTotal;
    }

    public String getExtraSD() {
        return extraSD;
    }

    public void setExtraSD(String extraSD) {
        this.extraSD = extraSD;
    }

    public String getInternalTotal() {
        return internalTotal;
    }

    public void setInternalTotal(String internalTotal) {
        this.internalTotal = internalTotal;
    }

    public String getInternalAvailable() {
        return internalAvailable;
    }

    public void setInternalAvailable(String internalAvailable) {
        this.internalAvailable = internalAvailable;
    }

    public String getRamDidUsed() {
        return ramDidUsed;
    }

    public void setRamDidUsed(String ramDidUsed) {
        this.ramDidUsed = ramDidUsed;
    }

    public String getCashDidUsed() {
        return cashDidUsed;
    }

    public void setCashDidUsed(String cashDidUsed) {
        this.cashDidUsed = cashDidUsed;
    }

    public String getSdCardTotal() {
        return sdCardTotal;
    }

    public void setSdCardTotal(String sdCardTotal) {
        this.sdCardTotal = sdCardTotal;
    }
}
