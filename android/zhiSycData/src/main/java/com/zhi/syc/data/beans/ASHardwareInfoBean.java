package com.zhi.syc.data.beans;

import android.os.Build;

import com.zhi.syc.data.ASBuilder;

public class ASHardwareInfoBean {
    //**********硬件信息**********

    /**
     * 指示设备电话类型的常量。 这表示用于传输语音
     * 呼叫的无线电的类型 GSM/...
     */
    public String phoneType;
    /**
     * 硬件序列号
     */
    // public String serial = Build.SERIAL;
    /**
     * 系统编译时间
     */
    public String productionDate = (Build.TIME + "");
    /**
     * 操作系统
     */
    public String operatingSystem = "ANDROID";
    /**
     * 应用版本号
     */
    public String versionCode;
    /**
     * 应用版本名称
     */
    public String versionName;
    /**
     * android id
     */
    public String androidId;
    /**
     * 运营商名称
     */
    public String telephony;
    /**
     * 是否vpn
     */
    public String isVpn;
    /**
     * 是否使用代理
     */
    public String isProxyPort;
    /**
     * 是否开启 debug 调试
     */
    public String isDebug;
    /**
     * sim卡状态
     */
    public String simState;
    /**
     * 是否越狱
     */
    public String root;
    /**
     * 物理尺寸
     */
    public String physicalSize;
    /**
     * 语言环境语⾔的名称
     */
    public String displayLanguage;
    /**
     * 语言环境的三字母缩写
     */
    public String iso3Language;
    /**
     * 此地区的国家/地区的缩写
     */
    public String iso3Country;
    /**
     * 网络运营商名称
     */
    public String networkOperatorName;
    /**
     * 网络类型
     */
    public String networkType;
    /**
     * 时区的 ID
     */
    public String timeZoneId;
    /**
     * 开机时间到现在的毫秒数
     */
    public String elapsedRealtime;
    /**
     * 传感器信息
     */
    public String sensorList;
    /**
     * 最后一次启动时间
     */
    public String lastBootTime;
    /**
     * 是否 root
     */
    public String rootJailbreak;
    /**
     * 连接到设备的键盘的种类
     */
    public String keyboard;
    /**
     * 是否为模拟器
     */
    public String isSimulator;
    /**
     * 检测模拟器详情 old
     */
    public ASSimulatorCheckInfoBean emulatorCheckInfoOld;
    /**
     * 是否为模拟器新
     */
    public String isSimulatorNew;
    /**
     * 检测模拟器详情 new
     */
    public ASSimulatorCheckInfoBean emulatorCheckInfoNew;
    /**
     * 手机的信号强度
     */
    public String dbm;
    //////////////////////v1.0.2新增
    /**
     * 手机号码
     */
    public String phoneNumber;
    /**
     * android sdk 版本
     */
    public String sdkVersion = (Build.VERSION.SDK_INT + "");
    /**
     * 手机MODE
     */
    public String model = Build.MODEL;

    public String getPhoneType() {
        return phoneType;
    }

    public void setPhoneType(String phoneType) {
        this.phoneType = phoneType;
    }

    // public String getSerial() {
    //     return serial;
    // }

    // public void setSerial(String serial) {
    //     this.serial = serial;
    // }

    public String getProductionDate() {
        return productionDate;
    }

    public void setProductionDate(String productionDate) {
        this.productionDate = productionDate;
    }

    public String getOperatingSystem() {
        return operatingSystem;
    }

    public void setOperatingSystem(String operatingSystem) {
        this.operatingSystem = operatingSystem;
    }

    public String getVersionCode() {
        return versionCode;
    }

    public void setVersionCode(String versionCode) {
        this.versionCode = versionCode;
    }

    public String getVersionName() {
        return versionName;
    }

    public void setVersionName(String versionName) {
        this.versionName = versionName;
    }

    public String getAndroidId() {
        return androidId;
    }

    public void setAndroidId(String androidId) {
        this.androidId = androidId;
    }

    public String getTelephony() {
        return telephony;
    }

    public void setTelephony(String telephony) {
        this.telephony = telephony;
    }

    public String getIsVpn() {
        return isVpn;
    }

    public void setIsVpn(String isVpn) {
        this.isVpn = isVpn;
    }

    public String getIsProxyPort() {
        return isProxyPort;
    }

    public void setIsProxyPort(String isProxyPort) {
        this.isProxyPort = isProxyPort;
    }

    public String getIsDebug() {
        return isDebug;
    }

    public void setIsDebug(String isDebug) {
        this.isDebug = isDebug;
    }

    public String getSimState() {
        return simState;
    }

    public void setSimState(String simState) {
        this.simState = simState;
    }

    public String getRoot() {
        return root;
    }

    public void setRoot(String root) {
        this.root = root;
    }

    public String getPhysicalSize() {
        return physicalSize;
    }

    public void setPhysicalSize(String physicalSize) {
        this.physicalSize = physicalSize;
    }

    public String getDisplayLanguage() {
        return displayLanguage;
    }

    public void setDisplayLanguage(String displayLanguage) {
        this.displayLanguage = displayLanguage;
    }

    public String getIso3Language() {
        return iso3Language;
    }

    public void setIso3Language(String iso3Language) {
        this.iso3Language = iso3Language;
    }

    public String getIso3Country() {
        return iso3Country;
    }

    public void setIso3Country(String iso3Country) {
        this.iso3Country = iso3Country;
    }

    public String getNetworkOperatorName() {
        return networkOperatorName;
    }

    public void setNetworkOperatorName(String networkOperatorName) {
        this.networkOperatorName = networkOperatorName;
    }

    public String getNetworkType() {
        return networkType;
    }

    public void setNetworkType(String networkType) {
        this.networkType = networkType;
    }

    public String getTimeZoneId() {
        return timeZoneId;
    }

    public void setTimeZoneId(String timeZoneId) {
        this.timeZoneId = timeZoneId;
    }

    public String getElapsedRealtime() {
        return elapsedRealtime;
    }

    public void setElapsedRealtime(String elapsedRealtime) {
        this.elapsedRealtime = elapsedRealtime;
    }

    public String getSensorList() {
        return sensorList;
    }

    public void setSensorList(String sensorList) {
        this.sensorList = sensorList;
    }

    public String getLastBootTime() {
        return lastBootTime;
    }

    public void setLastBootTime(String lastBootTime) {
        this.lastBootTime = lastBootTime;
    }

    public String getRootJailbreak() {
        return rootJailbreak;
    }

    public void setRootJailbreak(String rootJailbreak) {
        this.rootJailbreak = rootJailbreak;
    }

    public String getKeyboard() {
        return keyboard;
    }

    public void setKeyboard(String keyboard) {
        this.keyboard = keyboard;
    }

    public String getIsSimulator() {
        return isSimulator;
    }

    public void setIsSimulator(String isSimulator) {
        this.isSimulator = isSimulator;
    }

    public void setIsSimulatorNew(String isSimulator) {
        this.isSimulatorNew = isSimulator;
    }

    public void setEmulatorCheckInfoOld(ASSimulatorCheckInfoBean simulatorCheckInfo) {
        this.emulatorCheckInfoOld = simulatorCheckInfo;
    }

    public void setEmulatorCheckInfoNew(ASSimulatorCheckInfoBean simulatorCheckInfo) {
        this.emulatorCheckInfoNew = simulatorCheckInfo;
    }

    public String getDbm() {
        return dbm;
    }

    public void setDbm(String dbm) {
        this.dbm = dbm;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public String getSdkVersion() {
        return sdkVersion;
    }

    public void setSdkVersion(String sdkVersion) {
        this.sdkVersion = sdkVersion;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }
}
