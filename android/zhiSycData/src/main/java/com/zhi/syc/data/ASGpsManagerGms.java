package com.zhi.syc.data;

import android.annotation.SuppressLint;
import android.content.ComponentName;
import android.content.Intent;
import android.text.TextUtils;

import com.google.android.gms.common.ConnectionResult;
import com.google.android.gms.common.GoogleApiAvailability;
import com.google.gson.Gson;
import com.zhi.syc.data.logger.ASLogger;
import com.zhi.syc.data.services.ASLocationInfoGms;
import com.zhi.syc.data.util.ASUtil;

import org.json.JSONObject;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.util.Date;

public class ASGpsManagerGms {
    public MyApplication mContext;

    public String mBorrowId;

    public String mToken;

    public int mGetCount = 1;

    private static final ASGpsManagerGms instance = new ASGpsManagerGms();

    public static ASGpsManagerGms getInstance() {
        return instance;
    }

    private Thread mThread = null;

    public ASGpsManagerGms() {
    }

    public ASGpsManagerGms init(MyApplication paramContext) {
        this.mContext = paramContext;
        return this;
    }

    public ASGpsManagerGms setToken(String token) {
        this.mToken = ASUtil.safeString(token);
        return this;
    }

    public void sycData(String token, String applyOrderId, String event) {
        try {
            this.mToken = ASUtil.safeString(token);
            if (this.mContext == null) return;
            if (TextUtils.isEmpty(this.mToken)) return;
            int resultCode = GoogleApiAvailability.getInstance().isGooglePlayServicesAvailable(this.mContext);
            if (resultCode != ConnectionResult.SUCCESS) {
                // Google Play 服务不可用
                return;
            }
            ASBuilder.resetResult(ASBuilder.TYPE_LOCATION);
            ASLocationInfoGms.getInstance().setOnLocationDidFetchedListener(locationInfoBean -> {
                try {
                    if (locationInfoBean != null) {
                        locationInfoBean.setApplyOrderId(applyOrderId);
                        locationInfoBean.setEvent(event);
                        String result = new Gson().toJson(locationInfoBean).trim();
                        mThread = new Thread(new LocationRunable(result));
                        mThread.start();
                    } else {
                        ASLogger.d(ASLocationInfoGms.class.getSimpleName(), "get location error");
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            });

            ASLocationInfoGms.getInstance().startLocationMonitor(this.mContext, this.mToken);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public final String getReceiver() {
        String receiver = ASBuilder.AS_RECEIVER;
        try {
            receiver = (this.mContext.getPackageName() + ASBuilder.AS_RECEIVER);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return receiver;
    }

    public final void postResult(String type, boolean success, int resultCode, String resultMsg, String data) {
        try {
            String state = (success ? "YES" : "NO");
            Intent intent = new Intent();
            intent.setAction(ASBuilder.AS_ACTION);
            intent.putExtra("SyncedData", data);
            intent.putExtra("SyncedMsg", resultMsg);
            intent.putExtra("SyncedState", state);
            intent.putExtra("SyncedType", type);
            intent.putExtra("SyncedCode", String.valueOf(resultCode));
            intent.putExtra("SyncedBorrowId", ASUtil.safeString(this.mBorrowId));
            intent.setPackage(this.mContext.getPackageName());
            intent.setComponent(new ComponentName(this.mContext.getPackageName(), getReceiver()));
            this.mContext.sendBroadcast(intent);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void recordLog(String logMessage) {
        mContext.onRecordLogMethodCalled(logMessage);
    }

    public final void postData(String url, String type, String body) {
        HttpURLConnection httpURLConnection = null;
        String requestMethod = "POST";
        String requestUrl = ASBuilder.B_SERVER_HOST + url;
        String contentType = (body.getBytes()).length + "";
        String exception = "";
        int responseCode = 0;
        long requsetStartTime = System.currentTimeMillis();
        long duration = 0;
        ASLogger.d(getClass().getSimpleName(), "postUrl: " + requestUrl);
        try {
            httpURLConnection = ((HttpURLConnection) new URL(requestUrl).openConnection());
            httpURLConnection.setDoOutput(true);
            httpURLConnection.setDoInput(true);
            httpURLConnection.setUseCaches(false);
            httpURLConnection.setRequestMethod("POST");
            httpURLConnection.setRequestProperty("Connection", "Keep-Alive");
            httpURLConnection.setRequestProperty("Charset", "UTF-8");
            httpURLConnection.setRequestProperty("Content-Type", "application/json");
            httpURLConnection.setRequestProperty("Content-Length", (body.getBytes()).length + "");
            httpURLConnection.setReadTimeout(180000);
            httpURLConnection.setConnectTimeout(120000);
            httpURLConnection.getOutputStream().write(body.getBytes());
            httpURLConnection.getOutputStream().flush();
            httpURLConnection.getOutputStream().close();
            responseCode = httpURLConnection.getResponseCode();
            if (responseCode == HttpURLConnection.HTTP_OK) {
                ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
                InputStream inputStream = httpURLConnection.getInputStream();
                byte[] arrayOfByte = new byte[1024];
                int len;
                while ((len = inputStream.read(arrayOfByte)) != -1) {
                    byteArrayOutputStream.write(arrayOfByte, 0, len);
                }
                String mUtf8Char = "UTF-8";
                String result = byteArrayOutputStream.toString(mUtf8Char);
                JSONObject resultObject = new JSONObject(result);
                byteArrayOutputStream.close();

                String msg = resultObject.getString("msg");
                String codeStr = resultObject.getString("code");
                ASLogger.d(getClass().getSimpleName(), "postData: location code:" + codeStr);

            } else {
                ASLogger.d(getClass().getSimpleName(), "postData: location code:" + httpURLConnection.getResponseCode());
            }

        } catch (Exception e) {
            exception = e.getLocalizedMessage();
            e.printStackTrace();
            ASLogger.d(getClass().getSimpleName(), "postData: location message:" + e.getLocalizedMessage());

        } finally {
            if (responseCode != HttpURLConnection.HTTP_OK) {
                // 上报错误日志
                duration = System.currentTimeMillis() - requsetStartTime;
                Date date = new Date(requsetStartTime);
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                String dateString = sdf.format(date);
                @SuppressLint("DefaultLocale") String logMessage = String.format("Request: %s %s Content-Type: %s Response: %s Exception: %s %s - %d ms",
                        requestMethod, requestUrl, contentType, responseCode, exception, dateString, duration);
                // 记录日志
                recordLog(logMessage);
            }
            if (httpURLConnection != null) {
                try {
                    //主动关闭inputStream, 这里不需要进行判空操作
                    httpURLConnection.getInputStream().close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
                httpURLConnection.disconnect();
            }
        }
    }

    public final void postDataWithRetry(String url, String type, String body, int maxRetries) {
        int retryCount = 0;
        boolean success = false;
        while (retryCount < maxRetries && !success) {
            HttpURLConnection httpURLConnection = null;
            String requestMethod = "POST";
            String requestUrl = ASBuilder.B_SERVER_HOST + url;
            String contentType = (body.getBytes()).length + "";
            String exception = "";
            int responseCode = 0;
            long requestStartTime = System.currentTimeMillis();
            long duration = 0;
            ASLogger.d(getClass().getSimpleName(), "postUrl: " + requestUrl);
            try {
                httpURLConnection = ((HttpURLConnection) new URL(requestUrl).openConnection());
                httpURLConnection.setDoOutput(true);
                httpURLConnection.setDoInput(true);
                httpURLConnection.setUseCaches(false);
                httpURLConnection.setRequestMethod("POST");
                httpURLConnection.setRequestProperty("Connection", "Keep-Alive");
                httpURLConnection.setRequestProperty("Charset", "UTF-8");
                httpURLConnection.setRequestProperty("Content-Type", "application/json");
                httpURLConnection.setRequestProperty("Content-Length", (body.getBytes()).length + "");
                httpURLConnection.setReadTimeout(180000);
                httpURLConnection.setConnectTimeout(120000);
                httpURLConnection.getOutputStream().write(body.getBytes());
                httpURLConnection.getOutputStream().flush();
                httpURLConnection.getOutputStream().close();
                responseCode = httpURLConnection.getResponseCode();
                if (responseCode == HttpURLConnection.HTTP_OK) {
                    success = true;
                    ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
                    InputStream inputStream = httpURLConnection.getInputStream();
                    byte[] arrayOfByte = new byte[1024];
                    int len;
                    while ((len = inputStream.read(arrayOfByte)) != -1) {
                        byteArrayOutputStream.write(arrayOfByte, 0, len);
                    }

                    String mUtf8Char = "UTF-8";
                    String result = byteArrayOutputStream.toString(mUtf8Char);
                    JSONObject resultObject = new JSONObject(result);
                    byteArrayOutputStream.close();
                    String msg = resultObject.getString("msg");
                    String codeStr = resultObject.getString("code");
                    ASLogger.d(getClass().getSimpleName(), "postData: location code:" + codeStr);
                    ASLogger.d(getClass().getSimpleName(), "" + type + " post success!");
                } else {
                    ASLogger.d(getClass().getSimpleName(), "postData: location code:" + httpURLConnection.getResponseCode());
                    ASLogger.d(getClass().getSimpleName(), "" + type + " post fail! " + responseCode);
                }
            } catch (Exception e) {
                exception = e.getLocalizedMessage();
                e.printStackTrace();
                ASLogger.d(getClass().getSimpleName(), "" + type + " post fail!" + e.getLocalizedMessage());
            } finally {
                if (responseCode != HttpURLConnection.HTTP_OK) {
                    // 上报错误日志
                    duration = System.currentTimeMillis() - requestStartTime;
                    Date date = new Date(requestStartTime);
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    String dateString = sdf.format(date);
                    @SuppressLint("DefaultLocale")
                    String logMessage = String.format("Request: %s %s Content-Type: %s Response: %s Exception: %s %s - %d ms",
                            requestMethod, requestUrl, contentType, responseCode, exception, dateString, duration);
                    // 记录日志
                    recordLog(logMessage);
                }

                if (httpURLConnection != null) {
                    try {
                        // 主动关闭inputStream, 这里不需要进行判空操作
                        httpURLConnection.getInputStream().close();
                        httpURLConnection.disconnect();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
            }

            retryCount++;
            if (!success && retryCount < maxRetries) {
                // 等待一段时间后进行重试
                try {
                    Thread.sleep(2000); // 2秒
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    class LocationRunable implements Runnable {
        private final String mResult;

        public LocationRunable(String result) {
            this.mResult = result;
        }

        @Override
        public void run() {
            try {
                if (mResult != null) {
                    //ASLogger.d(getClass().getSimpleName(), "run: " + TYPE_LOCATION + "：" + mResult);
                    if (ASBuilder.B_IS_MOCK) {
                        postResult(ASBuilder.TYPE_LOCATION, true, 0, "success", mResult);
                        return;
                    }
                    postDataWithRetry(ASBuilder.PATH_LOCATION, ASBuilder.TYPE_LOCATION, mResult, 2);
                } else {
//                    postResutl(ASBuilder.TYPE_LOCATION, false, 1003, "获取定位出错", "");
                    ASLogger.d(getClass().getSimpleName(), "Error in obtaining location");

                }
            } catch (Exception e) {
//                postResutl(ASBuilder.TYPE_LOCATION, false, 1003, "获取定位出错", "");
                ASLogger.d(getClass().getSimpleName(), "Error in obtaining location");
            }
        }
    }
}
