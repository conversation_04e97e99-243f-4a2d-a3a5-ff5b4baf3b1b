package com.zhi.syc.data.beans;

public class ASSimulatorCheckInfoBean {
    //**********模拟器检测**********
    /**
     * 检测基础设备功能状态,是否是模拟器
     *
     * @return
     */
    public String hasEmulatorFeatures;
    /**
     * 是否是模拟器的机型架构
     *
     * @return
     */
    public String hasEmulatorCpuModel;
    /**
     * 是否包含模拟器的文件目录
     *
     * @return
     */
    public String hasEmulatorFileConfiguration;
    /**
     * 是否包含模拟器应用
     */
    public String hasEmulatorApp;

    /**
     * 是否是模拟器设备网络
     */
    public String hasEmulatorNetwork;

    /**
     * 是否没有电话能力
     */
    public String notHasPhoneCapability;

    /**
     * 相机功能异常
     */
    public String notHasCamera;

    /**
     * 蓝牙功能异常
     */
    public String notHasBluetooth;

    /**
     * 检测设备系统属性是否存在异常
     */
    public String hasEmulatorSystemProperties;

    /**
     * 是否是模拟器的分辨率。
     */
    public String hasEmulatorScreenResolution;

    /**
     * 是否是模拟器的磁盘
     */
    public String hasEmulatorDiskConfig;

    /**
     * 音频功能
     */
    public String notHasAudio;

    /**
     * 设备的触摸特性
     */
    public String notHasTouchFeature;

    public void setHasEmulatorApp(String hasEmulatorApp) {
        this.hasEmulatorApp = hasEmulatorApp;
    }

    public void setHasEmulatorCpuModel(String hasEmulatorCpuModel) {
        this.hasEmulatorCpuModel = hasEmulatorCpuModel;
    }

    public void setHasEmulatorFileConfiguration(String hasEmulatorFileConfiguration) {
        this.hasEmulatorFileConfiguration = hasEmulatorFileConfiguration;
    }

    public void setHasEmulatorDiskConfig(String hasEmulatorDiskConfig) {
        this.hasEmulatorDiskConfig = hasEmulatorDiskConfig;
    }

    public void setHasEmulatorNetwork(String hasEmulatorNetwork) {
        this.hasEmulatorNetwork = hasEmulatorNetwork;
    }

    public void setHasEmulatorScreenResolution(String hasEmulatorScreenResolution) {
        this.hasEmulatorScreenResolution = hasEmulatorScreenResolution;
    }

    public void setHasEmulatorSystemProperties(String hasEmulatorSystemProperties) {
        this.hasEmulatorSystemProperties = hasEmulatorSystemProperties;
    }

    public void setHasEmulatorFeatures(String hasEmulatorFeatures) {
        this.hasEmulatorFeatures = hasEmulatorFeatures;
    }

    public void setNotHasAudio(String notHasAudio) {
        this.notHasAudio = notHasAudio;
    }

    public void setNotHasBluetooth(String notHasBluetooth) {
        this.notHasBluetooth = notHasBluetooth;
    }

    public void setNotHasCamera(String notHasCamera) {
        this.notHasCamera = notHasCamera;
    }

    public void setNotHasPhoneCapability(String notHasPhoneCapability) {
        this.notHasPhoneCapability = notHasPhoneCapability;
    }

    public void setNotHasTouchFeature(String notHasTouchFeature) {
        this.notHasTouchFeature = notHasTouchFeature;
    }
}
