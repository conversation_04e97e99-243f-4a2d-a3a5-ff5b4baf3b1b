package com.zhi.syc.data;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.SharedPreferences;
import android.os.IBinder;
import android.os.IInterface;
import android.os.Parcel;
import android.os.RemoteException;
import android.text.TextUtils;
import android.util.Log;

import com.zhi.syc.data.util.ASUtil;

import com.google.android.gms.ads.identifier.AdvertisingIdClient;


@SuppressLint("StaticFieldLeak")
public class ASGaidManager {

    public static Context mContext;

    private static final String TAG = ASGaidManager.class.getSimpleName();

    public static final String GOOGLE_ADVERT_ID = "GOOGLE_ADVERT_ID";

    private String mGoogleAdvertIdFromInterface = "";

    private static final ASGaidManager instance = new ASGaidManager();

    public static ASGaidManager getInstance() {
        return instance;
    }

    public ASGaidManager() {

    }

    public void init(Context context) {
        mContext = context;
        startGetGaid();
        String gAdverIdFromInterface = ASUtil.safeString(mContext.getSharedPreferences(mContext.getPackageName(), Context.MODE_PRIVATE).getString(GOOGLE_ADVERT_ID, ""));
        Log.d(TAG, "gAdverIdFromInterface: " + gAdverIdFromInterface);

        setGoogleAdvertIdFromInterface(gAdverIdFromInterface);
    }

    public void setGoogleAdvertIdFromInterface(String googleAdvertId) {
        mGoogleAdvertIdFromInterface = ASUtil.safeString(googleAdvertId).toLowerCase();
        //保存
        try {
            SharedPreferences sp = mContext.getSharedPreferences(mContext.getPackageName(), Context.MODE_PRIVATE);
            sp.edit().putString(GOOGLE_ADVERT_ID, mGoogleAdvertIdFromInterface).commit();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public String getGpAdvertId() {
        mGoogleAdvertIdFromInterface = ASUtil.safeString(mGoogleAdvertIdFromInterface);
        return mGoogleAdvertIdFromInterface;
    }

    public void startGetGaid() {
        try {
            if (!TextUtils.isEmpty(getGpAdvertId())) {
                return;
            }

            new Thread(new FetchGoogleAdvertIdFomInterfaceRunnable()).start();

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    static class FetchGoogleAdvertIdFomInterfaceRunnable implements Runnable {
        @Override
        public void run() {
            try {
                String gid = ASGaidManager.getGoogleAdIdFromInterface();
                ASGaidManager.getInstance().setGoogleAdvertIdFromInterface(gid);
                Log.d(TAG, "getGoogleAdIdFromInterface: " + gid);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 这个方法是耗时的，不能在主线程调用
     */
    public static String getGoogleAdIdFromInterface() throws Exception {
        String advertisingId = null;
        try {
            AdvertisingIdClient.Info adInfo = AdvertisingIdClient.getAdvertisingIdInfo(mContext);
            if (adInfo != null) {
                advertisingId = adInfo.getId();  // 获取广告 ID
                boolean isLimitAdTrackingEnabled = adInfo.isLimitAdTrackingEnabled();  // 检查广告限制状态
                System.out.println("Advertising ID: " + advertisingId);
                System.out.println("Ad Tracking Limited: " + isLimitAdTrackingEnabled);
            }
        } catch (Exception e) {
            e.printStackTrace();
            System.err.println("Failed to get Advertising ID");
        }
        return advertisingId;
    }
}