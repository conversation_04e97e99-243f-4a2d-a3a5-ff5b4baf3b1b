package com.zhi.syc.data.beans;

import com.zhi.syc.data.ASBuilder;

public class ASLocationInfoBean {
    public String appId;

    public String token;

    public String applyOrderId;

    public String product;

    public String version;
    public String event;
    public String borrowId;

    /**
     * 经度
     */
    public String longitude;
    /**
     * 纬度
     */
    public String latitude;
    /**
     * 省
     */
    public String province;
    /**
     * 市
     */
    public String city;
    /**
     * 区
     */
    public String county;
    /**
     * 街道
     */
    public String street;
    /**
     * 详细
     */
    public String detail;

    public ASLocationInfoBean() {
        this.appId = ASBuilder.APPID;
        this.product = ASBuilder.KEY_PRODUCT;
        this.token = ASBuilder.KEY_TOKEN;
        this.version = ASBuilder.SDK_VERSION;
        this.applyOrderId = ASBuilder.KEY_APPLY_ORDER_ID;
        this.event = ASBuilder.KEY_EVENT;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getBorrowId() {
        return borrowId;
    }

    public void setBorrowId(String borrowId) {
        this.borrowId = borrowId;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getCounty() {
        return county;
    }

    public void setCounty(String county) {
        this.county = county;
    }

    public String getStreet() {
        return street;
    }

    public void setStreet(String street) {
        this.street = street;
    }

    public String getDetail() {
        return detail;
    }

    public void setDetail(String detail) {
        this.detail = detail;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getApplyOrderId() {
        return applyOrderId;
    }

    public void setApplyOrderId(String applyOrderId) {
        this.applyOrderId = applyOrderId;
    }

    public void  setEvent(String event) {
        this.event = event;
    }

    public String getProduct() {
        return product;
    }

    public void setProduct(String product) {
        this.product = product;
    }
}
