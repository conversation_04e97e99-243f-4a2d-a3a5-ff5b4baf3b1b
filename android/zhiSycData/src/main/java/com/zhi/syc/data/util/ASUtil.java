package com.zhi.syc.data.util;

import android.text.TextUtils;
import android.util.Base64;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.zip.GZIPOutputStream;

public class ASUtil {

    public static String stringToGZIP(String sourceString) {
        String zipString = "";

        if (TextUtils.isEmpty(sourceString)) {
            return zipString;
        }

        try {
            String mUtf8Char = "UTF-8";
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            GZIPOutputStream gzipOutputStream = new GZIPOutputStream(outputStream);
            gzipOutputStream.write(sourceString.getBytes(mUtf8Char));
            gzipOutputStream.close();

            byte[] zipBuffer = outputStream.toByteArray();
            zipString = Base64.encodeToString(zipBuffer, Base64.URL_SAFE | Base64.NO_WRAP | Base64.NO_PADDING);
            outputStream.close();

        } catch (IOException e) {
            e.printStackTrace();
        }

        return zipString;
    }

    public static String safeString(String sourceString) {
        if (TextUtils.isEmpty(sourceString)) {
            sourceString = "";
        }
        return sourceString;
    }

    public static String getFileName(String filePath) {
        try {
            int start = filePath.lastIndexOf("/");
            if (start != -1) {
                return filePath.substring(start + 1);
            } else {
                return "";
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }
}
