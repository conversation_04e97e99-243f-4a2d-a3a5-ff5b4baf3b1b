package com.zhi.syc.data;

import com.zhi.syc.data.logger.ASLogger;

import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

public class ASThreadPoolExecutor extends ThreadPoolExecutor {

    public ASThreadPoolExecutor(int corePoolSize, int maximumPoolSize, long keepAliveTime, TimeUnit unit, BlockingQueue<Runnable> workQueue) {
        super(corePoolSize, maximumPoolSize, keepAliveTime, unit, workQueue);
    }

    @Override
    protected void beforeExecute(Thread t, Runnable r) {
        super.beforeExecute(t, r);
        String threadName = t.getName();
        ASLogger.d(ASThreadPoolExecutor.class.getSimpleName(), "线程：" + threadName + " 准备执行");
    }

    @Override
    protected void afterExecute(Runnable r, Throwable t) {
        super.afterExecute(r, t);
        String threadName = Thread.currentThread().getName();
        ASLogger.d(ASThreadPoolExecutor.class.getSimpleName(), "线程：" + threadName + " 执行结束");
    }

    @Override
    protected void terminated() {
        super.terminated();
        ASLogger.d(ASThreadPoolExecutor.class.getSimpleName(), "线程：线程池结束！");
    }
}
