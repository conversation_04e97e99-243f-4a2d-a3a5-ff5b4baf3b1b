package com.zhi.syc.data.services;

import static android.content.ContentValues.TAG;

import android.content.Context;
import android.util.Log;

import com.zhi.syc.data.ASGaidManager;
import com.zhi.syc.data.beans.ASDeviceInfoBean;
import com.zhi.syc.data.util.ASGeneralUtil;
import com.zhi.syc.data.util.ASHarewareUtil;
import com.zhi.syc.data.util.ASNetworkUtil;
import com.zhi.syc.data.util.ASStoreUtil;

public class ASDeviceInfo {

    public static ASDeviceInfoBean getDeviceInfo(Context paramContext, String event, String applyOrderId) {
        ASDeviceInfoBean deviceInfo = new ASDeviceInfoBean(event, applyOrderId);
        try {
            // deviceInfo.setMac(ASNetworkUtil.getMacAddress(paramContext));
            // deviceInfo.setImei(ASGeneralUtil.getImei(paramContext));
            deviceInfo.setLaguage(ASGeneralUtil.getLanguage(paramContext));
            deviceInfo.setArea(ASGeneralUtil.getArea(paramContext));
            deviceInfo.setScreenHeight(ASGeneralUtil.getDeviceHeight(paramContext));
            deviceInfo.setScreenWidth(ASGeneralUtil.getDeviceWidth(paramContext));
            deviceInfo.setNetworkData(ASGeneralUtil.getSimOperatorName(paramContext));
            deviceInfo.setFrontCameraPixels(ASHarewareUtil.getFrontCameraPixels(paramContext));
            deviceInfo.setRearCameraPixels(ASHarewareUtil.getBackCameraPixels(paramContext));
            deviceInfo.setRam(ASStoreUtil.getRamTotal(paramContext));
            deviceInfo.setRom(ASStoreUtil.getCashTotal(paramContext));
            deviceInfo.setIp(ASNetworkUtil.getIPAddress(paramContext));
            deviceInfo.setNetworkEnvironment(ASGeneralUtil.getNetworkType(paramContext));
            deviceInfo.setCpu(ASGeneralUtil.getCpuModel(paramContext));
            deviceInfo.setBootCount(ASGeneralUtil.getBootCount(paramContext));
            deviceInfo.setAadvertId(ASGaidManager.getInstance().getGpAdvertId());

//            deviceInfo.setSystemUptime(ASGeneralUtil.getSystemUptime(paramContext));
//            deviceInfo.setFingerprintCount(ASGeneralUtil.getDeviceFingerprintCount(paramContext));

        } catch (Exception e) {
            e.printStackTrace();
        }
        return deviceInfo;
    }
}
