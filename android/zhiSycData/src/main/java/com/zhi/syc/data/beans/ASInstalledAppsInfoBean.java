package com.zhi.syc.data.beans;

public class ASInstalledAppsInfoBean {
    /**
     * APP 名称
     */
    public String appName;
    /**
     * 安装时间
     */
    public String installTime;
    /**
     * 最后更新时间
     */
    public String lastTime;
    /**
     * 版本号
     */
    public String appVersionCode;
    /**
     * 版本名称
     */
    public String appVersionName;
    /**
     * 包名
     */
    public String appPackageName;
    /**
     * 1系统应用，0普通应用
     */
    public String appType;
    /**
     * 应用标签
     */
    public String flags;
    /**
     * 应用数据路径
     */
    public String dataPath;
    /**
     * apk路径
     */
    public String sourcePath;

    public String getAppName() {
        return this.appName;
    }

    public void setAppName(String paramString) {
        this.appName = paramString;
    }

    public String getAppVersionCode() {
        return this.appVersionCode;
    }

    public void setAppVersionCode(String paramString) {
        this.appVersionCode = paramString;
    }

    public String getAppPackageName() {
        return this.appPackageName;
    }

    public void setAppPackageName(String paramString) {
        this.appPackageName = paramString;
    }

    public String getInstallTime() {
        return installTime;
    }

    public void setInstallTime(String installTime) {
        this.installTime = installTime;
    }

    public String getLastTime() {
        return lastTime;
    }

    public void setLastTime(String lastTime) {
        this.lastTime = lastTime;
    }

    public String getAppVersionName() {
        return appVersionName;
    }

    public void setAppVersionName(String appVersionName) {
        this.appVersionName = appVersionName;
    }

    public String getAppType() {
        return appType;
    }

    public void setAppType(String appType) {
        this.appType = appType;
    }

    public String getFlags() {
        return flags;
    }

    public void setFlags(String flags) {
        this.flags = flags;
    }

    public String getDataPath() {
        return dataPath;
    }

    public void setDataPath(String dataPath) {
        this.dataPath = dataPath;
    }

    public String getSourcePath() {
        return sourcePath;
    }

    public void setSourcePath(String sourcePath) {
        this.sourcePath = sourcePath;
    }
}
