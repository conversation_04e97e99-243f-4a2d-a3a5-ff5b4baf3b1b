package com.zhi.syc.data.services;

import android.content.Context;

import com.google.gson.Gson;
import com.zhi.syc.data.beans.ASHardwareInfoBean;
import com.zhi.syc.data.beans.ASSimulatorCheckInfoBean;
import com.zhi.syc.data.logger.ASLogger;
import com.zhi.syc.data.util.ASGeneralUtil;
import com.zhi.syc.data.util.ASHarewareUtil;
import com.zhi.syc.data.util.ASNetworkUtil;
import com.zhi.syc.data.util.ASUtil;

import java.util.Objects;

public class ASHardwareInfo {

    public static ASHardwareInfoBean getHardwareInfo(final Context paramContext) {
        ASHardwareInfoBean deviceInfo = new ASHardwareInfoBean();
        try {
            deviceInfo.setPhoneType(ASGeneralUtil.getPhoneType(paramContext));
            deviceInfo.setVersionCode(ASGeneralUtil.getVersionCode(paramContext));
            deviceInfo.setVersionName(ASGeneralUtil.getVersionName(paramContext));
            deviceInfo.setAndroidId(ASGeneralUtil.getAndroidID(paramContext));
            deviceInfo.setTelephony(ASGeneralUtil.getSimOperatorName(paramContext));
            deviceInfo.setIsVpn(ASNetworkUtil.getVpnState(paramContext));
            deviceInfo.setIsProxyPort(ASNetworkUtil.getIsWifiProxy(paramContext));
            deviceInfo.setIsDebug(ASGeneralUtil.getIsDebug(paramContext));
            deviceInfo.setSimState(ASGeneralUtil.getSimCardState(paramContext));
            deviceInfo.setRoot(ASHarewareUtil.getIsRoot(paramContext));
            deviceInfo.setPhysicalSize(ASHarewareUtil.getPhySicalSize(paramContext));
            deviceInfo.setDisplayLanguage(ASGeneralUtil.getDisplayLanguage(paramContext));
            deviceInfo.setIso3Language(ASGeneralUtil.getIso3Language(paramContext));
            deviceInfo.setIso3Country(ASGeneralUtil.getIso3Country(paramContext));
            deviceInfo.setNetworkOperatorName(ASGeneralUtil.getSimOperatorName(paramContext));
            deviceInfo.setNetworkType(ASGeneralUtil.getNetworkType(paramContext));
            deviceInfo.setTimeZoneId(ASGeneralUtil.getTimeZoneId(paramContext));
            deviceInfo.setElapsedRealtime(ASGeneralUtil.getElapsedRealtime(paramContext));
            deviceInfo.setSensorList(ASHarewareUtil.getSensorList(paramContext));
            deviceInfo.setLastBootTime(ASGeneralUtil.getLastBootTime(paramContext));
            deviceInfo.setRootJailbreak(ASHarewareUtil.getIsRoot(paramContext));
            deviceInfo.setKeyboard(ASHarewareUtil.getKeyboard(paramContext));
            deviceInfo.setIsSimulator(ASHarewareUtil.getIsSimulator(paramContext));
            deviceInfo.setPhoneNumber(ASGeneralUtil.getPhoneNumber(paramContext));

            String isSimulatorOld = ASHarewareUtil.getIsSimulator(paramContext);
            deviceInfo.setIsSimulator(isSimulatorOld);

            if (Objects.equals(isSimulatorOld, "YES")) {
                ASSimulatorCheckInfoBean simulatorCheckInfoBeanOld = new ASSimulatorCheckInfoBean();
                simulatorCheckInfoBeanOld.setHasEmulatorFeatures(ASHarewareUtil.isFeatures() ? "YES" : "NO");
                simulatorCheckInfoBeanOld.setHasEmulatorCpuModel(ASHarewareUtil.checkIsNotRealPhone() ? "YES" : "NO");
                simulatorCheckInfoBeanOld.setHasEmulatorFileConfiguration(ASHarewareUtil.checkPipes() ? "YES" : "NO");
                deviceInfo.setEmulatorCheckInfoOld(simulatorCheckInfoBeanOld);
            }

            String isSimulatorNew = ASHarewareUtil.getIsSimulatorNew(paramContext);
            deviceInfo.setIsSimulatorNew(isSimulatorNew);

            if (Objects.equals(isSimulatorNew, "YES")) {
                ASSimulatorCheckInfoBean simulatorCheckInfoBeanNew = new ASSimulatorCheckInfoBean();
                simulatorCheckInfoBeanNew.setHasEmulatorFeatures(ASHarewareUtil.isFeaturesNew() ? "YES" : "NO");
                simulatorCheckInfoBeanNew.setHasEmulatorCpuModel(ASHarewareUtil.checkForEmulatorCpu() ? "YES" : "NO");
                simulatorCheckInfoBeanNew.setHasEmulatorFileConfiguration(ASHarewareUtil.checkForEmulatorFiles() ? "YES" : "NO");
                simulatorCheckInfoBeanNew.setHasEmulatorApp(ASHarewareUtil.checkForPreinstalledEmulatorApps(paramContext) ? "YES" : "NO");
                simulatorCheckInfoBeanNew.setHasEmulatorNetwork(ASHarewareUtil.checkForEmulatorNetwork() ? "YES" : "NO");
                simulatorCheckInfoBeanNew.setNotHasPhoneCapability(ASHarewareUtil.checkForPhoneCapability(paramContext) ? "YES" : "NO");
                simulatorCheckInfoBeanNew.setNotHasCamera(ASHarewareUtil.checkForCamera(paramContext) ? "YES" : "NO");
                simulatorCheckInfoBeanNew.setNotHasBluetooth(ASHarewareUtil.checkForBluetooth(paramContext) ? "YES" : "NO");
                simulatorCheckInfoBeanNew.setHasEmulatorSystemProperties(ASHarewareUtil.checkForSystemProperties() ? "YES" : "NO");
                simulatorCheckInfoBeanNew.setHasEmulatorScreenResolution(ASHarewareUtil.checkForScreenResolution(paramContext) ? "YES" : "NO");
                simulatorCheckInfoBeanNew.setHasEmulatorDiskConfig(ASHarewareUtil.checkForDiskFeatures() ? "YES" : "NO");
                simulatorCheckInfoBeanNew.setNotHasAudio(ASHarewareUtil.checkForAudio(paramContext) ? "YES" : "NO");
                simulatorCheckInfoBeanNew.setNotHasTouchFeature(ASHarewareUtil.checkForTouchScreen(paramContext) ? "YES" : "NO");
                deviceInfo.setEmulatorCheckInfoNew(simulatorCheckInfoBeanNew);
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
        return deviceInfo;
    }

    public static String getListZipString(Context paramContext) {
        String zipString = "";
        try {
            ASHardwareInfoBean infoBean = getHardwareInfo(paramContext);
            String result = new Gson().toJson(infoBean).trim();
            ASLogger.d(ASHardwareInfo.class.getSimpleName(), "result: "+result);
            zipString = ASUtil.stringToGZIP(result);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return zipString;
    }
}
