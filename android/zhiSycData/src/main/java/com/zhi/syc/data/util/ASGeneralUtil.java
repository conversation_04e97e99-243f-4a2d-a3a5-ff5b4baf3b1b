package com.zhi.syc.data.util;

import android.content.Context;
import android.content.pm.ApplicationInfo;
import android.content.pm.FeatureInfo;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.hardware.biometrics.BiometricManager;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.os.Build;
import android.os.SystemClock;
import android.provider.Settings;
import android.telephony.TelephonyManager;
import android.text.TextUtils;
import android.util.DisplayMetrics;

import androidx.annotation.RequiresApi;

import java.util.Locale;
import java.util.TimeZone;

public class ASGeneralUtil {

    public static String getAndroidID(Context paramContext) {
        String androidID = Settings.System.getString(paramContext.getContentResolver(), Settings.Secure.ANDROID_ID);
        if (TextUtils.isEmpty(androidID)) {
            androidID = "";
        }
        return androidID;
    }

    public static String getPhoneType(Context paramContext) {
        String valueStr = "";
        try {
            TelephonyManager telephonyManager = (TelephonyManager) paramContext.getSystemService(Context.TELEPHONY_SERVICE);
            if (telephonyManager != null) {
                int type = telephonyManager.getPhoneType();
                switch (type) {
                    case TelephonyManager.PHONE_TYPE_NONE: {
                        break;
                    }
                    case TelephonyManager.PHONE_TYPE_GSM: {
                        valueStr = "GSM";
                        break;
                    }
                    case TelephonyManager.PHONE_TYPE_CDMA: {
                        valueStr = "CDMA";
                        break;
                    }
                    case TelephonyManager.PHONE_TYPE_SIP: {
                        valueStr = "SIP";
                        break;
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return valueStr;
    }

    public static String getLanguage(Context paramContext) {
        String str = "";
        try {
            String language = Locale.getDefault().getLanguage();
            if (language != null) {
                str = language;
            }
        } catch (Exception exception) {
            exception.printStackTrace();
        }
        return str;
    }

    public static String getDisplayLanguage(Context paramContext) {
        String str = "";
        try {
            String language = Locale.getDefault().getDisplayLanguage();
            if (language != null) {
                str = language;
            }
        } catch (Exception exception) {
            exception.printStackTrace();
        }
        return str;
    }

    public static String getIso3Language(Context paramContext) {
        String str = "";
        try {
            String language = Locale.getDefault().getISO3Language();
            if (language != null) {
                str = language;
            }
        } catch (Exception exception) {
            exception.printStackTrace();
        }
        return str;
    }

    public static String getIso3Country(Context paramContext) {
        String str = "";
        try {
            String language = Locale.getDefault().getISO3Country();
            if (language != null) {
                str = language;
            }
//            Locale.getDefault().getLanguage()       ---> en
//            Locale.getDefault().getISO3Language()   ---> eng
//            Locale.getDefault().getCountry()        ---> US
//            Locale.getDefault().getISO3Country()    ---> USA
//            Locale.getDefault().getDisplayCountry() ---> United States
//            Locale.getDefault().getDisplayName()    ---> English (United States)
//            Locale.getDefault().toString()          ---> en_US
//            Locale.getDefault().getDisplayLanguage()---> English

        } catch (Exception exception) {
            exception.printStackTrace();
        }
        return str;
    }

    // public static String getImei(Context context) {
    //     String imei = "";
    //     try {
    //         TelephonyManager telephonyManager = (TelephonyManager) context.getSystemService(Context.TELEPHONY_SERVICE);
    //         if (telephonyManager != null) {
    //             if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
    //                 imei = telephonyManager.getImei();
    //                 // 使用IMEI进行后续操作
    //             } else {
    //                 imei = telephonyManager.getDeviceId();
    //                 // 使用IMEI进行后续操作
    //             }
    //         }
    //     } catch (Exception e) {
    //         e.printStackTrace();
    //     }
    //     return imei;
    // }

    public static String getTimeZoneId(Context context) {
        String tzid = "";
        try {
            TimeZone tz = TimeZone.getDefault();
            tzid = tz.getID();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return tzid;
    }

    public static String getNetworkType(Context paramContext) {
        String networkType = "";
        try {
            NetworkInfo network = ((ConnectivityManager) paramContext.getSystemService(Context.CONNECTIVITY_SERVICE)).getActiveNetworkInfo();
            if (network != null && network.isAvailable()
                    && network.isConnected()) {
                int type = network.getType();
                if (type == ConnectivityManager.TYPE_WIFI) {
                    networkType = "WIFI";
                } else if (type == ConnectivityManager.TYPE_MOBILE) {
                    networkType = "MOBILE";
                }
            }
//            else {
//                //网络不可用
//            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return networkType;
    }

    public static String getSimOperatorName(Context paramContext) {
        String simName = "";
        try {
            TelephonyManager telephonyManager = (TelephonyManager) paramContext.getSystemService(Context.TELEPHONY_SERVICE);
            if (telephonyManager != null) {
                simName = ASUtil.safeString(telephonyManager.getNetworkOperatorName());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return simName;
    }

    public static String getIsDebug(Context context) {
        String isDebugStr = "NO";
        try {
            boolean isDebug = context.getApplicationInfo() != null && (context.getApplicationInfo().flags & ApplicationInfo.FLAG_DEBUGGABLE) != 0;
            if (isDebug) {
                isDebugStr = "YES";
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return isDebugStr;
    }

    public static String getElapsedRealtime(Context context) {
        String timeStr = "";
        try {
            timeStr = ASUtil.safeString(String.valueOf(SystemClock.elapsedRealtime()));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return timeStr;
    }

    public static String getLastBootTime(Context context) {
        String timeStr = "";
        try {
            ;
            timeStr = ASUtil.safeString(String.valueOf((System.currentTimeMillis() - SystemClock.elapsedRealtime())));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return timeStr;
    }

    public static String getVersionName(Context paramContext) {
        String str = "1.0";
        try {
            PackageManager pm = paramContext.getPackageManager();
            PackageInfo pi = pm.getPackageInfo(paramContext.getPackageName(), PackageManager.GET_ACTIVITIES);
            if (pi != null) {
                if (!TextUtils.isEmpty(pi.versionName)) {
                    str = pi.versionName;
                }
            }
        } catch (Exception exception) {
            exception.printStackTrace();
        }
        return str;
    }

    public static String getVersionCode(Context paramContext) {
        String str = "1";
        try {
            PackageManager pm = paramContext.getPackageManager();
            PackageInfo pi = pm.getPackageInfo(paramContext.getPackageName(), PackageManager.GET_ACTIVITIES);
            if (pi != null) {
                str = String.valueOf(pi.versionCode);
            }
        } catch (Exception exception) {
            exception.printStackTrace();
        }
        return str;
    }

    public static String getSimCardState(Context paramContext) {
        try {
            TelephonyManager telephonyManager = (TelephonyManager) paramContext.getSystemService(Context.TELEPHONY_SERVICE);
            if (telephonyManager == null) {
                return "NO_SIM";
            }
            int simstate = telephonyManager.getSimState();
            switch (simstate) {
                case TelephonyManager.SIM_STATE_ABSENT: {
                    //无卡
                    return "NO_SIM";
                }
                case TelephonyManager.SIM_STATE_UNKNOWN: {
                    //未知
                    return "UNKNOWN";
                }
                case TelephonyManager.SIM_STATE_NETWORK_LOCKED: {
                    //存在，但网络受限
                    return "NETWORK_LOCKED";
                }
                case TelephonyManager.SIM_STATE_PIN_REQUIRED: {
                    //存在，但需要PIN解锁
                    return "PIN_LOCKED";
                }
                case TelephonyManager.SIM_STATE_PUK_REQUIRED: {
                    //存在，但需要PUK解锁
                    return "PUK_LOCKED";
                }
                case TelephonyManager.SIM_STATE_READY: {
                    //存在，卡良好
                    return "YES";
                }
                case TelephonyManager.SIM_STATE_NOT_READY: {
                    //存在，卡未准备
                    return "NOT_READY";
                }
                case TelephonyManager.SIM_STATE_PERM_DISABLED: {
                    //存在，但卡已被长期停用
                    return "DISABLED";
                }
                case TelephonyManager.SIM_STATE_CARD_IO_ERROR: {
                    //存在，但卡有问题
                    return "PRESENT_BUT_FAULT";
                }
                case TelephonyManager.SIM_STATE_CARD_RESTRICTED: {
                    //运营商限制
                    return "CARRIER_RESTRICTIONS";
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return "NO_SIM";
    }

    public static String getPhoneNumber(Context paramContext) {
        return "";
    }

    public static String getArea(Context paramContext) {
        try {
            return Locale.getDefault().getISO3Country();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    public static String getCpuModel(Context paramContext) {
        String cpuStr = "";
        try {
            cpuStr = Build.SUPPORTED_ABIS[0];
        } catch (Exception e) {
            e.printStackTrace();
        }
        return cpuStr;
    }

    public static String getDeviceHeight(Context paramContext) {
        String heightPixels = "";
        try {
            DisplayMetrics displayMetrics = paramContext.getResources().getDisplayMetrics();
            if (displayMetrics != null) {
                heightPixels = String.valueOf(displayMetrics.heightPixels);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return heightPixels;
    }

    public static String getDeviceWidth(Context paramContext) {
        String widthPixels = "";
        try {
            DisplayMetrics displayMetrics = paramContext.getResources().getDisplayMetrics();
            if (displayMetrics != null) {
                widthPixels = String.valueOf(displayMetrics.widthPixels);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return widthPixels;
    }

//    public static long getSystemUptime(Context paramContext) {
//        long systemUptime = 0L;
//        try {
//            systemUptime =  SystemClock.elapsedRealtime();
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        return systemUptime;
//    }

    public static int getBootCount(Context paramContext) {
        int bootCount = 0;
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                bootCount =  Settings.Global.getInt(paramContext.getContentResolver(), Settings.Global.BOOT_COUNT);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return bootCount;
    }

//    public static int getDeviceFingerprintCount(Context paramContext) {
//        int fingerprintCount = 0;
//        try {
//            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
//                BiometricManager biometricManager = BiometricManager.from(paramContext);
//                if (biometricManager.canAuthenticate() == BiometricManager.BIOMETRIC_SUCCESS) {
//                    return biometricManager.getEnrolledFingerprints().size();
//                }
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        return fingerprintCount;
//    }
}
