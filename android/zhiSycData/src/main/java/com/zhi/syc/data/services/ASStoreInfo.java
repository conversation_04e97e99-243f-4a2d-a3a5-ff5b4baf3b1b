package com.zhi.syc.data.services;

import android.content.Context;
import android.util.Log;

import com.google.gson.Gson;
import com.zhi.syc.data.beans.ASStoreInfoBean;
import com.zhi.syc.data.util.ASStoreUtil;
import com.zhi.syc.data.util.ASUtil;

public class ASStoreInfo {
    public static ASStoreInfoBean getStoreInfo(final Context paramContext) {
        ASStoreInfoBean storeInfoBean = new ASStoreInfoBean();
        try {
            storeInfoBean.setContainSd(ASStoreUtil.getContainSD());
            storeInfoBean.setRamCanUse(ASStoreUtil.getRamCanUse(paramContext));
            storeInfoBean.setRamTotal(ASStoreUtil.getRamTotal(paramContext));
            storeInfoBean.setCashCanUse(ASStoreUtil.getCashCanUse(paramContext));
            storeInfoBean.setCashTotal(ASStoreUtil.getCashTotal(paramContext));
            storeInfoBean.setExtraSD(ASStoreUtil.getExtraSD(paramContext, 1));
            storeInfoBean.setInternalTotal(ASStoreUtil.getTotalInternalStoreSize(paramContext));
            storeInfoBean.setInternalAvailable(ASStoreUtil.getAvailaInternalStoreSize(paramContext));
            storeInfoBean.setRamDidUsed(ASStoreUtil.getRamDidUsed(paramContext));
            storeInfoBean.setCashDidUsed(ASStoreUtil.getCashDidUsed(paramContext));
            storeInfoBean.setSdCardTotal(ASStoreUtil.getSDCardTotal(paramContext));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return storeInfoBean;
    }

    public static String getListZipString(Context paramContext) {
        String zipString = "";
        try {
            ASStoreInfoBean infoBean = getStoreInfo(paramContext);
            String result = new Gson().toJson(infoBean).trim();
            Log.d(ASStoreInfo.class.getSimpleName(), "getListZipString: \n"+result);
            zipString = ASUtil.stringToGZIP(result);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return zipString;
    }
}
