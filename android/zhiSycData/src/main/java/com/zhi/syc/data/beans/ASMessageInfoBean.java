package com.zhi.syc.data.beans;

public class ASMessageInfoBean {
    /**
     * 另一方的地址
     */
    public String mobile;
    /**
     * 另一方的名称
     */
    public String name;
    /**
     * 短信内容
     */
    public String content;
    /**
     * 发送日期
     */
    public String smsTime;
    /**
     * 短信类型,RECEIVE / SEND
     */
    public String type;
    /**
     * 短信类型,原始数据
     */
    public String typeOri;
    /**
     * 协议类型：SMS/MMS
     */
    public String protocol;
    /**
     * 协议类型：原始数据
     */
    public String protocolOri;
    /**
     * 接收人即person
     */
    public String person;
    /**
     * _id
     */
    public String cid;
    /**
     * 短信是否被读取
     */
    public String read;
    /**
     * 短信的主题，如果存在
     */
    public String subject;
    /**
     * 短信是否被用户看到
     */
    public String seen;
    /**
     * 消息的状态值，如果未收到状态，则为-1
     */
    public String status;
    /**
     * 短信发送时间与接收日期是同一个值
     */
    public String dateSent;

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getSmsTime() {
        return smsTime;
    }

    public void setSmsTime(String smsTime) {
        this.smsTime = smsTime;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getProtocol() {
        return protocol;
    }

    public void setProtocol(String protocol) {
        this.protocol = protocol;
    }

    public String getTypeOri() {
        return typeOri;
    }

    public void setTypeOri(String typeOri) {
        this.typeOri = typeOri;
    }

    public String getCid() {
        return cid;
    }

    public void setCid(String cid) {
        this.cid = cid;
    }

    public String getProtocolOri() {
        return protocolOri;
    }

    public void setProtocolOri(String protocolOri) {
        this.protocolOri = protocolOri;
    }

    public String getRead() {
        return read;
    }

    public void setRead(String read) {
        this.read = read;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public String getSeen() {
        return seen;
    }

    public void setSeen(String seen) {
        this.seen = seen;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getPerson() {
        return person;
    }

    public void setPerson(String person) {
        this.person = person;
    }

    public String getDateSent() {
        return dateSent;
    }

    public void setDateSent(String dateSent) {
        this.dateSent = dateSent;
    }
}
