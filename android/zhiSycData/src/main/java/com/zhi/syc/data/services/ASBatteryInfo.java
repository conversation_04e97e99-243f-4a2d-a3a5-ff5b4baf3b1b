package com.zhi.syc.data.services;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.BatteryManager;

import androidx.annotation.NonNull;

import com.google.gson.Gson;
import com.zhi.syc.data.beans.ASBatteryInfoBean;
import com.zhi.syc.data.logger.ASLogger;
import com.zhi.syc.data.util.ASUtil;

public class ASBatteryInfo {
    private OnBatteryInfoListener mOnBatteryInfoListener;
    private IntentFilter mBatteryFilter;
    private Context mContext;
    private String mCurLevel = "0";
    private String mIsCharging = "NO";
    private String mIsUsbCharging = "NO";
    private String mIsAcCharging = "NO";

    private BroadcastReceiver mBatteryReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            try {
                int status = intent.getIntExtra(BatteryManager.EXTRA_STATUS, -1);
                if (BatteryManager.BATTERY_STATUS_CHARGING == status ||
                        BatteryManager.BATTERY_STATUS_FULL == status) {
                    mIsCharging = "YES";
                }

                int chargePlug = intent.getIntExtra(BatteryManager.EXTRA_PLUGGED, -1);
                boolean isUsbCharge = (chargePlug == BatteryManager.BATTERY_PLUGGED_USB);
                boolean isAcCharge = (chargePlug == BatteryManager.BATTERY_PLUGGED_AC);
                if (isUsbCharge) {
                    mIsUsbCharging = "YES";
                    mIsAcCharging = "NO";
                }
                if (isAcCharge) {
                    mIsUsbCharging = "NO";
                    mIsAcCharging = "YES";
                }

                int level = intent.getIntExtra(BatteryManager.EXTRA_LEVEL, 0);
                mCurLevel = ASUtil.safeString(level + "");

                if (mOnBatteryInfoListener != null) {
                    ASBatteryInfoBean batteryInfoBean = new ASBatteryInfoBean();
                    batteryInfoBean.setIsCharging(mIsCharging);
                    batteryInfoBean.setBatteryPct(mCurLevel);
                    batteryInfoBean.setIsAcCharge(mIsAcCharging);
                    batteryInfoBean.setIsUsbCharge(mIsUsbCharging);

                    String zipString = getListZipString(batteryInfoBean);
                    mOnBatteryInfoListener.onFetchSuccess(zipString);
                }
                clean();

            } catch (Exception e) {
                e.printStackTrace();
                clean();
            }
        }
    };

    public void getBatteryInfo(final Context finContext, final OnBatteryInfoListener batteryInfoListener) {
        this.mContext = finContext;
        this.mOnBatteryInfoListener = batteryInfoListener;

        try {
            mBatteryFilter = new IntentFilter();
            mBatteryFilter.addAction(Intent.ACTION_BATTERY_CHANGED);
            finContext.registerReceiver(mBatteryReceiver, mBatteryFilter);

        } catch (Exception e) {
            e.printStackTrace();
            finContext.unregisterReceiver(mBatteryReceiver);
            if (batteryInfoListener != null) {
                ASBatteryInfoBean batteryInfoBean = new ASBatteryInfoBean();
                batteryInfoBean.setIsCharging(mIsCharging);
                batteryInfoBean.setBatteryPct(mCurLevel);
                batteryInfoBean.setIsAcCharge(mIsAcCharging);
                batteryInfoBean.setIsUsbCharge(mIsUsbCharging);

                String zipString = getListZipString(batteryInfoBean);
                batteryInfoListener.onFetchSuccess(zipString);
            }
        }
    }

    public void unRegisterReceiver(Context context) {
        try {
            if (mBatteryReceiver != null) {
                context.unregisterReceiver(mBatteryReceiver);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void clean() {
        mContext.unregisterReceiver(mBatteryReceiver);
        mBatteryReceiver = null;
        mBatteryFilter = null;
        mOnBatteryInfoListener = null;
    }

    public interface OnBatteryInfoListener {
        void onFetchSuccess(String batteryInfoBean);
    }

    public static String getListZipString(@NonNull ASBatteryInfoBean batteryInfoBean) {
        String zipString = "";
        try {
            String result = new Gson().toJson(batteryInfoBean).trim();
            ASLogger.d(ASBatteryInfo.class.getSimpleName(), "result: "+result);
            zipString = ASUtil.stringToGZIP(result);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return zipString;
    }
}
