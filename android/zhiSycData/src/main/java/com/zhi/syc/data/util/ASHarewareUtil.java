package com.zhi.syc.data.util;

import android.Manifest;
import android.bluetooth.BluetoothAdapter;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.PackageManager;
import android.content.res.Configuration;
import android.graphics.ImageFormat;
import android.graphics.Point;
import android.hardware.Sensor;
import android.hardware.SensorManager;
import android.hardware.camera2.CameraCharacteristics;
import android.hardware.camera2.CameraManager;
import android.hardware.camera2.params.StreamConfigurationMap;
import android.media.AudioManager;
import android.os.BatteryManager;
import android.os.Build;
import android.telephony.TelephonyManager;
import android.util.DisplayMetrics;
import android.util.Size;
import android.view.WindowManager;

import androidx.core.content.ContextCompat;

import org.json.JSONArray;
import org.json.JSONObject;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.lang.reflect.Method;
import java.net.Inet4Address;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.util.Enumeration;
import java.util.List;

public class ASHarewareUtil {

    public static String getSensorList(Context context) {
        JSONArray jsonArray = new JSONArray();
        String sensorStr = "";
        try {
            SensorManager sensorManager = (SensorManager) context.getSystemService(Context.SENSOR_SERVICE);
            List<Sensor> sensors = sensorManager.getSensorList(Sensor.TYPE_ALL);
            for (int i = 0; i < sensors.size(); i++) {
                Sensor sensor = sensors.get(i);
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("type", sensor.getType() + "");
                jsonObject.put("name", ASUtil.safeString(sensor.getName()));
                jsonObject.put("version", sensor.getVersion() + "");
                jsonObject.put("maxRange", ASUtil.safeString(String.valueOf(sensor.getMaximumRange())));
                jsonObject.put("vendor", ASUtil.safeString(sensor.getVendor()));
                jsonObject.put("minDelay", ASUtil.safeString(String.valueOf(sensor.getMinDelay())));
                jsonObject.put("power", ASUtil.safeString(String.valueOf(sensor.getPower())));
                jsonObject.put("resolution", ASUtil.safeString(String.valueOf(sensor.getResolution())));
                jsonArray.put(jsonObject);
            }
            sensors = null;
        } catch (Exception e) {
            e.printStackTrace();
        }
        sensorStr = jsonArray.toString();
        jsonArray = null;
        return sensorStr;
    }

    public static String getIsRoot(Context context) {
        String root = "NO";
        String binPath = "/system/bin/su";
        String xBinPath = "/system/xbin/su";
        try {
            if (new File(binPath).exists() && isExecutable(binPath)) {
                root = "YES";
            }
            if (new File(xBinPath).exists() && isExecutable(xBinPath)) {
                root = "YES";
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return root;
    }

    public static boolean isExecutable(String filePath) {
        Process p = null;
        try {
            p = Runtime.getRuntime().exec("ls -l " + filePath);
            // 获取返回内容
            BufferedReader in = new BufferedReader(new InputStreamReader(p.getInputStream()));
            String str = in.readLine();
            if (str != null && str.length() >= 4) {
                char flag = str.charAt(3);
                if (flag == 's' || flag == 'x')
                    return true;
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (p != null) {
                p.destroy();
            }
        }
        return false;
    }

    public static String getKeyboard(Context context) {
        String keyboard = "";

//        int KEYBOARD_UNDEFINED = 0; // 未定义的键盘
//        int KEYBOARD_NOKEYS = 1; // 无键键盘，没有外接键盘时为该类型
//        int KEYBOARD_QWERTY = 2; // 标准外接键盘
//        int KEYBOARD_12KEY = 3; // 12键小键盘

        try {
            Configuration config = context.getResources().getConfiguration();
            keyboard = ASUtil.safeString(String.valueOf(config.keyboard));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return keyboard;
    }

    public static String getIsSimulator(Context context) {
        String isSim = "NO";
        boolean isFeatures = isFeatures();
        boolean checkIsNotRealPhone = checkIsNotRealPhone();
        boolean checkPipes = checkPipes();
        if (isFeatures ||
                checkIsNotRealPhone ||
                checkPipes

        ) {
            isSim = "YES";
        }
        return isSim;
    }

    public static boolean isFeatures() {
        return  Build.FINGERPRINT.toLowerCase().contains("generic")
                || Build.FINGERPRINT.toLowerCase().startsWith("unknown")
                || Build.FINGERPRINT.toLowerCase().contains("vbox")
                || Build.FINGERPRINT.toLowerCase().contains("test-keys")
                || Build.MODEL.toLowerCase().contains("google_sdk")
                || Build.MODEL.toLowerCase().contains("sdk")
                || Build.MODEL.toLowerCase().contains("Emulator")
                || Build.MODEL.toLowerCase().contains("Android SDK built for x86")
                || Build.MANUFACTURER.toLowerCase().contains("Genymotion")
                || (Build.BRAND.toLowerCase().startsWith("generic") && Build.DEVICE.toLowerCase().startsWith("generic"))
                || "google_sdk".equals(Build.PRODUCT)
                || Build.PRODUCT.contains("sdk")
                || Build.PRODUCT.contains("google_sdk");
    }

    public static boolean checkIsNotRealPhone() {
        String cpuInfo = readCpuInfo();
        return cpuInfo.contains("intel") || cpuInfo.contains("amd");
    }

    public static String readCpuInfo() {
        String result = "";
        try {
            String[] args = {"/system/bin/cat", "/proc/cpuinfo"};
            ProcessBuilder cmd = new ProcessBuilder(args);

            Process process = cmd.start();
            StringBuilder sb = new StringBuilder();
            String readLine = "";
            String mUtf8Char = "UTF-8";
            BufferedReader responseReader = new BufferedReader(new InputStreamReader(process.getInputStream(), mUtf8Char));
            while ((readLine = responseReader.readLine()) != null) {
                sb.append(readLine);
            }
            responseReader.close();
            result = sb.toString().toLowerCase();
        } catch (IOException ignored) {
        }
        return result;
    }

    private static final String[] known_pipes = {"/dev/socket/qemud", "/dev/qemu_pipe"};

    public static boolean checkPipes() {
        try {
            for (String pipes : known_pipes) {
                File qemu_socket = new File(pipes);
                if (qemu_socket.exists()) {
                    return true;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    public static String getFrontCameraPixels(Context paramContext) {
        int camerId = hasFrontCamera(paramContext);
        return getCameraPixels(paramContext, camerId);
    }

    public static String getBackCameraPixels(Context paramContext) {
        int camerId = hasBackCamera(paramContext);
        return getCameraPixels(paramContext, camerId);
    }

    public static int hasFrontCamera(Context paramContext) {
        int CAMERA_FACING_FRONT = 1;
        int cid = -1;

        CameraManager cameraManager = (CameraManager) paramContext.getSystemService(Context.CAMERA_SERVICE);
        String[] cameraIds = {};
        try {
            cameraIds = cameraManager.getCameraIdList();
            for (String cID : cameraIds) {
                if (cID.equalsIgnoreCase(CameraCharacteristics.LENS_FACING_BACK + "")) {
                    cid = Integer.parseInt(cID);
                    break;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        cameraIds = null;
        return cid;
    }

    public static int hasBackCamera(Context paramContext) {
        int CAMERA_FACING_BACK = 0;
        int cid = -1;

        try {
            CameraManager cameraManager = (CameraManager) paramContext.getSystemService(Context.CAMERA_SERVICE);
            String[] cameraIds = cameraManager.getCameraIdList();
            for (String cID : cameraIds) {
                if (cID.equalsIgnoreCase(CameraCharacteristics.LENS_FACING_FRONT + "")) {
                    cid = Integer.parseInt(cID);
                    break;
                }
            }
            cameraIds = null;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return cid;
    }

    public static String getCameraPixels(Context paramContext, int paramInt) {
        String pixelValue = "0";
        if (paramInt == -1)
            return pixelValue;
        if (ContextCompat.checkSelfPermission(paramContext, Manifest.permission.CAMERA) == PackageManager.PERMISSION_GRANTED) {
            // 判断是否有相机权限
            try {
                CameraManager cameraManager = (CameraManager) paramContext.getSystemService(Context.CAMERA_SERVICE);
                CameraCharacteristics cameraCharacteristics = cameraManager.getCameraCharacteristics(paramInt + "");
                StreamConfigurationMap streamConfigurationMap = cameraCharacteristics.get(CameraCharacteristics.SCALER_STREAM_CONFIGURATION_MAP);
                Size[] sizes = streamConfigurationMap.getOutputSizes(ImageFormat.JPEG);
                if (sizes.length > 0) {
                    Size fistSize = sizes[0];
                    int gwidth = fistSize.getWidth();
                    int gheight = fistSize.getHeight();
                    int pixels = (gwidth * gheight / 10000);
                    pixelValue = (pixels + "");
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return pixelValue;
    }

    public static int getMaxNumber(int[] paramArray) {
        int temp = 0;
        try {
            if (paramArray.length > 0) {
                temp = paramArray[0];
                for (int j : paramArray) {
                    if (temp < j) {
                        temp = j;
                    }
                }
            }
        } catch (ArrayIndexOutOfBoundsException e) {
            e.printStackTrace();
        }
        return temp;
    }

    public static String getPhySicalSize(Context paramContext) {
        String sizeStr = "";
        try {
            Point point = new Point();
            WindowManager wm = (WindowManager) paramContext.getSystemService(Context.WINDOW_SERVICE);
            wm.getDefaultDisplay().getRealSize(point);
            DisplayMetrics dm = paramContext.getResources().getDisplayMetrics();
            double x = Math.pow(point.x / dm.xdpi, 2);
            double y = Math.pow(point.y / dm.ydpi, 2);
            double screenInches = Math.sqrt(x + y);
            sizeStr = String.valueOf(screenInches);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return sizeStr;
    }

    public static String getIsSimulatorNew(Context context) {
        String isSim = "NO";
        boolean isFeatures = isFeaturesNew();
        boolean checkForPreinstalledEmulatorApps = checkForPreinstalledEmulatorApps(context);
        boolean checkForEmulatorFiles = checkForEmulatorFiles();
        boolean checkForEmulatorNetwork = checkForEmulatorNetwork();
        boolean checkForPhoneCapability = checkForPhoneCapability(context);
        boolean checkForCamera = checkForCamera(context);
        boolean checkForEmulatorCpu = checkForEmulatorCpu();
        boolean checkForBluetooth = checkForBluetooth(context);
        boolean checkForSystemProperties = checkForSystemProperties();
        boolean checkForScreenResolution = checkForScreenResolution(context);
        boolean checkForDiskFeatures = checkForDiskFeatures();
        boolean checkForAudio = checkForAudio(context);
        boolean checkForTouchScreen = checkForTouchScreen(context);
        if (isFeatures ||
                        checkForPreinstalledEmulatorApps ||
                        checkForEmulatorFiles ||
                        checkForEmulatorNetwork ||
                        checkForPhoneCapability ||
                        checkForCamera ||
                        checkForEmulatorCpu ||
                        checkForBluetooth ||
                        checkForSystemProperties ||
                        checkForScreenResolution ||
                        checkForDiskFeatures ||
                        checkForAudio ||
                        checkForTouchScreen){
            isSim = "YES";
        }
        return isSim;
    }

    public static boolean isFeaturesNew() {
        boolean result = false;
        String model = Build.MODEL;
        String product = Build.PRODUCT;
        String brand = Build.BRAND;
        String device = Build.DEVICE;
        String hardware = Build.HARDWARE;
        String fingerprint = Build.FINGERPRINT;
        String manufacturer = Build.MANUFACTURER;

        result = model.contains("google_sdk") ||
                model.contains("Emulator") ||
                model.contains("Android SDK built for x86") ||
                product.contains("sdk_google") ||
                product.contains("google_sdk") ||
                product.contains("sdk") ||
                product.contains("sdk_x86") ||
                product.contains("sdk_gphone") ||
                product.contains("vbox86p") ||
                product.contains("emulator") ||
                brand.startsWith("generic") ||
                device.startsWith("generic") ||
                device.startsWith("emulator") ||
                hardware.contains("goldfish") ||
                hardware.contains("ranchu") ||
                hardware.contains("vbox86") ||
                fingerprint.startsWith("generic") ||
                fingerprint.startsWith("unknown") ||
                manufacturer.contains("Genymotion") ||
                (Build.BRAND.startsWith("generic") && Build.DEVICE.startsWith("generic")) ||
                "google_sdk".equals(Build.PRODUCT);
        return result;
    }

    public static boolean checkForPreinstalledEmulatorApps(Context context) {
        boolean result = false;
        String[] knownEmulatorApps = {
                "com.google.android.launcher.layouts.genymotion",
                "com.bluestacks",
                "com.bignox.app"
        };

        for (String app : knownEmulatorApps) {
            try {
                context.getPackageManager().getPackageInfo(app, 0);
                result = true;
            } catch (PackageManager.NameNotFoundException e) {
                // App not found, continue checking
            }
        }
        return result;
    }

    /** 是否存在模拟器路径文件 */
    public static boolean checkForEmulatorFiles() {
        boolean result = false;
        String[] knownEmulatorFiles = {
                "/dev/socket/qemud",
                "/dev/qemu_pipe",
                "/system/lib/libc_malloc_debug_qemu.so",
                "/sys/qemu_trace",
                "/system/bin/qemu-props"
        };

        for (String file : knownEmulatorFiles) {
            File f = new File(file);
            if (f.exists()) {
                result = true;
            }
        }
        return result;
    }

    /** 检测是否存在模拟器ip地址 */
    public static boolean checkForEmulatorNetwork() {
        boolean result = false;
        String[] knownEmulatorIPs = {
                "*********", // Genymotion
                "********",  // Android Emulator
                "127.0.0.1", // Localhost
                "************" // Bluestacks
        };

        for (String ip : knownEmulatorIPs) {
            if (getLocalIpAddress().equals(ip)) {
                result = true;
            }
        }
        return result;
    }

    private static String getLocalIpAddress() {
        try {
            for (Enumeration<NetworkInterface> en = NetworkInterface.getNetworkInterfaces(); en.hasMoreElements(); ) {
                NetworkInterface intf = en.nextElement();
                for (Enumeration<InetAddress> enumIpAddr = intf.getInetAddresses(); enumIpAddr.hasMoreElements(); ) {
                    InetAddress inetAddress = enumIpAddr.nextElement();
                    if (!inetAddress.isLoopbackAddress() && inetAddress instanceof Inet4Address) {
                        return inetAddress.getHostAddress();
                    }
                }
            }
        } catch (SocketException ex) {
            ex.printStackTrace();
        }
        return "";
    }

    /**
     * 检测设备的电话功能：
     */
    public static boolean checkForPhoneCapability(Context context) {
        boolean result = false;
        TelephonyManager tm = (TelephonyManager) context.getSystemService(Context.TELEPHONY_SERVICE);

        if (tm.getPhoneType() == TelephonyManager.PHONE_TYPE_NONE) {
            result = true;
        }
        return result;
    }

    /** 检测cpu架构 */
    public static boolean checkForEmulatorCpu() {
        boolean result = false;
        String cpuAbi = Build.CPU_ABI;

        if ("x86".equals(cpuAbi) || "x86_64".equals(cpuAbi)) {
            result = true;
        }
        return result;
    }

    /** 检测蓝牙功能 */
    public static boolean checkForBluetooth(Context context) {
        boolean result = false;
        BluetoothAdapter bluetoothAdapter = BluetoothAdapter.getDefaultAdapter();

        if (bluetoothAdapter == null || !context.getPackageManager().hasSystemFeature(PackageManager.FEATURE_BLUETOOTH)) {
            result = true;
        }
        return result;
    }

    /**
     * 检测设备的系统属性
     */
    public static boolean checkForSystemProperties() {
        boolean result = false;
        String property = getSystemProperty("ro.kernel.qemu");

        if ("1".equals(property)) {
            result = true;
        }
        return result;
    }

    /** 检测系统属性 */
    private static String getSystemProperty(String name) {
        try {
            Class<?> systemPropertyClass = Class.forName("android.os.SystemProperties");
            Method getMethod = systemPropertyClass.getMethod("get", String.class);
            return (String) getMethod.invoke(null, name);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /** 检测设备的分辨率 */
    public static boolean checkForScreenResolution(Context context) {
        boolean result = false;
        DisplayMetrics metrics = context.getResources().getDisplayMetrics();
        int width = metrics.widthPixels;
        int height = metrics.heightPixels;

        if ((width == 480 && height == 800) || (width == 1080 && height == 1920)) {
            result = true;
        }
        return result;
    }

    /** 检测设备的磁盘特性 */
    public static boolean checkForDiskFeatures() {
        boolean result = false;
        File sharedFolder = new File("/mnt/shared");

        if (sharedFolder.exists()) {
            result = true;
        }
        return result;
    }

    /** 检查设备的音频状态 */
    public static boolean checkForAudio(Context context) {
        boolean result = false;
        AudioManager audioManager = (AudioManager) context.getSystemService(Context.AUDIO_SERVICE);

        if (audioManager.getMode() == AudioManager.MODE_IN_CALL) {
            result = true;
        }
        return result;
    }

    /** 检测设备的触摸特性 */
    public static boolean checkForTouchScreen(Context context) {
        boolean result = false;
        PackageManager pm = context.getPackageManager();
        if (!pm.hasSystemFeature(PackageManager.FEATURE_TOUCHSCREEN)) {
            result = true;
        }
        return result;
    }

    /** 检测设备的摄像头特性 */
    public static boolean checkForCamera(Context context) {
        boolean result = false;
        PackageManager pm = context.getPackageManager();
        if (!pm.hasSystemFeature(PackageManager.FEATURE_CAMERA_ANY) && !pm.hasSystemFeature(PackageManager.FEATURE_CAMERA_FRONT)) {
            result = true;
        }
        return result;
    }
}
