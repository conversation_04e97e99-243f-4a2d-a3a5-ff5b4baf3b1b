package com.zhi.syc.data.beans;

import com.zhi.syc.data.ASBuilder;

public class ASBatteryInfoBean {
    //**********电池信息**********
    /**
     * 是否正在充电
     *
     * @return
     */
    public String isCharging;
    /**
     * 电池百分比
     *
     * @return
     */
    public String batteryPct;
    /**
     * 是否 USB 充电
     *
     * @return
     */
    public String isUsbCharge;
    /**
     * 是否交流充电
     *
     * @return
     */
    public String isAcCharge;
    public String getIsCharging() {
        return isCharging;
    }

    public void setIsCharging(String isCharging) {
        this.isCharging = isCharging;
    }

    public String getBatteryPct() {
        return batteryPct;
    }

    public void setBatteryPct(String batteryPct) {
        this.batteryPct = batteryPct;
    }

    public String getIsUsbCharge() {
        return isUsbCharge;
    }

    public void setIsUsbCharge(String isUsbCharge) {
        this.isUsbCharge = isUsbCharge;
    }

    public String getIsAcCharge() {
        return isAcCharge;
    }

    public void setIsAcCharge(String isAcCharge) {
        this.isAcCharge = isAcCharge;
    }
}
