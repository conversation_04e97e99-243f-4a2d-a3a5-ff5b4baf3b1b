module.exports = {
  presets: ['module:@react-native/babel-preset'],
    plugins: [
    [
      'module-resolver',
      {
        root: ['./src'],
        alias: {
          '@/baseConfig': './src/baseConfig.ts',
          '@/utils': './src/utils/_export_.ts',
          '@/types': './src/types/_export_.ts',
          '@/enums': './src/enums/_export_.ts',
          '@/server': './src/server/_export_.ts',
          '@/routes': './src/routes/_export_.ts',
          '@/pages': './src/pages/_export_.ts',
          '@/managers': './src/managers/_export_.ts',
          '@/localStorage': './src/localStorage/_export_.ts',
          "@/hooks": "./src/hooks/_export_.ts",
          '@/modals': './src/modals/_export_.ts',
          '@/themes': './src/themes/_export_.ts',
          '@/components': './src/components/_export_.ts',
          '@/config': './src/config/_export_.tsx',
          '@/i18n': './src/i18n/_export_.ts',
          '@/assets': './src/assets',
          "@/image": './src/assets/NoroPrestaPlus/image',
          '@/nativeComponents': './src/nativeComponents/_export_.ts',
          '@/dataModel': './src/dataModel/_export_.ts',
          '@/native': './src/native/_export_.ts',
          "@/trackEvent": "./src/trackEvent/_export_.ts"
        },
      },
    ],
    'react-native-reanimated/plugin',
    ['@babel/plugin-proposal-decorators', {legacy: true}],
    '@babel/plugin-transform-optional-chaining',
    '@babel/plugin-proposal-nullish-coalescing-operator',
    '@babel/plugin-transform-export-namespace-from',
    ['@babel/plugin-proposal-class-properties', { loose: true }],
  ],
};