/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react/react-in-jsx-scope */

import { Modals } from '@/modals';

import { LogCenterContainer } from '@/components';
import { HitPointEnumsSpace } from '@/enums';
import ModalManagerProvider from './src/context/ModalManagerProvider';
import { useApplicationStateChangeHandle } from '@/hooks';
import { Router } from '@/routes';
import { log, TrackEvent } from '@/utils';
import React, { useCallback, useEffect, useRef } from 'react';
import { Platform, StatusBar, UIManager } from 'react-native';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import SplashScreen from 'react-native-splash-screen';
import { AdjustTools } from './src/business/_export_';
import setupForApp from './src/setup';
import ToastContainer from './src/toast';
import { uploadLogRecord } from './src/logCenter';
// import {CaptureProtection} from 'react-native-capture-protection';

if (Platform.OS === 'android') {
  if (UIManager.setLayoutAnimationEnabledExperimental) {
    UIManager.setLayoutAnimationEnabledExperimental(true);
  }
}

const { adjustForSetup, adjustDestory, AdjustEventPointTools } = AdjustTools;

function App() {
  // React.useEffect(() => {
  //   /** 禁用屏幕录制 */
  //   CaptureProtection.preventScreenRecord(true);
  //   /** 禁用屏幕截屏 */
  //   CaptureProtection.preventScreenshot();
  //   /** 禁用切换到后台 */
  //   CaptureProtection.preventBackground();
  // }, []);

  var [isInited, setIsInited] = React.useState(false);
  var inInitedRef = useRef<boolean>(false);

  const uploadLog = () => {
    log.info('上传埋点数据');
    TrackEvent.uploadEventLog();
    log.info('上传日志记录');
    uploadLogRecord();
  };

  useApplicationStateChangeHandle(
    () => {
      console.info('useApplicationStateChangeHandle active');
      uploadLog();
      if (inInitedRef.current) {
        TrackEvent.trackCommonEvent(
          {
            p: HitPointEnumsSpace.EPageKey.P_APP,
            e: HitPointEnumsSpace.EEventKey.E_APP_RESUME,
          },
          '1',
        );
      }
    },
    () => {
      console.info('useApplicationStateChangeHandle active');
      uploadLog();
      TrackEvent.trackCommonEvent(
        {
          p: HitPointEnumsSpace.EPageKey.P_APP,
          e: HitPointEnumsSpace.EEventKey.E_APP_STOP,
        },
        '1',
      );
    },
  );

  const init = useCallback(async () => {
    try {
      // 获取系统配置
      await setupForApp();
    } catch (error) {
      log.error('init fail', error);
    }
    // 启动 adjsut
    adjustForSetup();

    AdjustEventPointTools.trackEventOfFirstLaunch();

    uploadLog();

    //初始化一些配置
    setIsInited(true);
    inInitedRef.current = true;
  }, []);

  useEffect(() => {
    init();
    return () => {
      adjustDestory();
      SplashScreen.show();
    };
  }, []);

  // const {applicationTheme, darkMode} = useThemeManager().value;

  // const barStyle: StatusBarStyle = useMemo<StatusBarStyle>(
  //   () => (darkMode ? 'light-content' : 'dark-content'),
  //   [darkMode],
  // );

  if (!isInited) {
    return <></>;
  }

  return (
    <>
      <SafeAreaProvider>
        <ModalManagerProvider>
          <>
            <StatusBar
              translucent
              // barStyle={barStyle}
              barStyle={'light-content'}
              // backgroundColor={applicationTheme["background-color-0"]}
              backgroundColor={'#0000006C'}
            />
            <Modals />
            <Router />
            <LogCenterContainer />
            <ToastContainer />
          </>
        </ModalManagerProvider>
      </SafeAreaProvider>
    </>
  );
}

export default App;

// export default App;
