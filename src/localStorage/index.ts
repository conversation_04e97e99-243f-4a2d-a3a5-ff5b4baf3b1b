/**
 * TODO: 暂时使用async-storage,后续再替换成mmkv
 */

export { LOCAL_KEY } from './localKey';
import { KVManagerInstance } from '@/managers';
import { log } from '@/utils';
import { MMKVInstance, MMKVLoader } from 'react-native-mmkv-storage';
import { BaseConfig } from '../baseConfig';
import { ELocalKey } from './localKey';
export interface ILocalCacheData extends Record<ELocalKey, any> {
  [ELocalKey.TOKEN_STRING]: string;
  [ELocalKey.USER_ID_STRING]: string;
  [ELocalKey.FLOW_CODE_STRING]: string;
  [ELocalKey.PHONE_NUMBER_STRING]: string;
  [ELocalKey.LOCAL_STRING]: string;
  [ELocalKey.MODE_STRING]: string;
  [ELocalKey.APPLY_ORDER_ID_STRING]: string;
  [ELocalKey.READ_PERMISSION_BOOLEAN]: boolean;
  [ELocalKey.VERSION_UPDATE_STATUS_FLAG_INT]: number;
  [ELocalKey.READ_GUIDE_BOOLEAN]: boolean;
  [ELocalKey.ORDER_CHECK_REVIEW_GP_BOOLEAN]: boolean;
  [ELocalKey.REPAY_GP_REVIEW_BOOLEAN]: boolean;
  [ELocalKey.FCM_TOKEN_STRING]: string;
}

export const localCacheForSetup = () => {
  try {
    const { setBoolean, setInt, setString, getBoolean } = KVManagerInstance.action;
    log.info('fix KVstorage cache fix verify');
    if (getBoolean(ELocalKey.FIX_CACHE_BOOLEAN) != true && BaseConfig.appVersionName == '1.3.1') {
      log.info('fix KVstorage cache fix start');
      // todo 未修复的状态下才需要修复
      const _OLD_MMKV_: MMKVInstance = new MMKVLoader().withEncryption().initialize();

      const oldKVStoreData = {
        [ELocalKey.TOKEN_STRING]: _OLD_MMKV_.getString(ELocalKey.TOKEN_STRING) || '',
        [ELocalKey.USER_ID_STRING]: _OLD_MMKV_.getString(ELocalKey.USER_ID_STRING) || '',
        [ELocalKey.FLOW_CODE_STRING]: _OLD_MMKV_.getString(ELocalKey.FLOW_CODE_STRING) || '',
        [ELocalKey.PHONE_NUMBER_STRING]: _OLD_MMKV_.getString(ELocalKey.PHONE_NUMBER_STRING) || '',
        [ELocalKey.READ_PERMISSION_BOOLEAN]:
          _OLD_MMKV_.getBool(ELocalKey.READ_PERMISSION_BOOLEAN) || false,
      };

      setString(ELocalKey.TOKEN_STRING, oldKVStoreData[ELocalKey.TOKEN_STRING]);
      setString(ELocalKey.USER_ID_STRING, oldKVStoreData[ELocalKey.USER_ID_STRING]);
      setString(ELocalKey.PHONE_NUMBER_STRING, oldKVStoreData[ELocalKey.PHONE_NUMBER_STRING]);
      setBoolean(
        ELocalKey.READ_PERMISSION_BOOLEAN,
        oldKVStoreData[ELocalKey.READ_PERMISSION_BOOLEAN],
      );
      setBoolean(ELocalKey.FIX_CACHE_BOOLEAN, true);
      log.info('fix KVstorage cache fix done');
    }
  } catch (error: any) {
    log.error('fix KVstorage cache error', {
      error: error,
    });
  }
};
