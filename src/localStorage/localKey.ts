// TODO 键名加环境、产品名称
export const LOCAL_KEY = {
  TOKEN_STRING: 'token',
  USER_ID_STRING: 'userId',
  FLOW_CODE_STRING: 'flowCode',
  PHONE_NUMBER_STRING: 'phoneNumber',
  LOCAL_STRING: 'local',
  MODE_STRING: 'mode',
};

export enum ELocalKey {
  FIX_CACHE_BOOLEAN = 'FIX_CACHE_BOOLEAN',
  TOKEN_STRING = 'token',
  USER_ID_STRING = 'userId',
  FLOW_CODE_STRING = 'flowCode',
  PHONE_NUMBER_STRING = 'phoneNumber',
  LOCAL_STRING = 'local',
  MODE_STRING = 'mode',
  /** 是否设置密码 */
  IS_SET_PASSWORD = 'isSetPassword',
  /** 是否跳过设置密码 */
  IS_SKIP_SET_PASSWORD = 'isSkipSetPassword',
  /** 申请订单 ID */
  APPLY_ORDER_ID_STRING = 'applyOrderId',
  /** 权限申请页预览状态 */
  READ_PERMISSION_BOOLEAN = 'readPermission',
  /** 版本更新标识 非强制更新提醒标识 */
  VERSION_UPDATE_STATUS_FLAG_INT = 'versionUptateStatsuFlag',
  /** guide 引导页预览状态 */
  READ_GUIDE_BOOLEAN = 'readGuide',
  /** GP 评分永久关闭标识 */
  GP_CLOSED = 'GPReviewClosed',
  /** 申请成功页 GP 评分的状态 */
  ORDER_CHECK_REVIEW_GP_BOOLEAN = 'orderCheckGPReview',
  /** 放款成功页 GP 评分的状态 */
  REPAY_GP_REVIEW_BOOLEAN = 'loanSuccessGPReview',
  /** fcm token */
  FCM_TOKEN_STRING = 'fcmToken',
  /** sms code count record */
  SMS_CODE_COUNT_RECORD = 'smsCodeCountRecord',
  /** sms code leave count */
  SMS_CODE_COUNT_LEAVE = 'smsCodeCountLeave',
  /** 复贷提示用户修改手机号弹窗 - 打开时间记录 */
  RELOAN_RPOMPT_MODIFY_MOBILE_TIMESTAMP = 'reloanPromptModifyMobileTimestamp',
  /** 复贷提示用户修改手机号弹窗 - 贷款订单号 */
  RELOAN_RPOMPT_MODIFY_MOBILE_APPLY_ORDER_ID = 'reloanPromptModifyMobileApplyOrderId',
  /** 复贷提示用户修改手机号弹窗 - 贷款次数 */
  RELOAN_RPOMPT_MODIFY_MOBILE_LOAN_TIMES = 'reloanPromptModifyMobileLoanTimes',
  /** 提示并确认过app更新的版本 */
  PROMPT_COMFIRM_APP_UPDATE_VERSION = 'promptComfirmAppUpdateVersion',
  /** clabe补件是否相同 */
  CLABE_SUPPLY_UPDATED = 'clabeSupplyUpdated',
  /** otp比对是否两次异常 */
  OTP_VERIFY_SKIP = 'otpVerifySkip',
  /** otp比对失败时间记录 */
  OTP_VERIFY_ERROR_TIME = 'otpVerifyErrorTime',
  /** 进件跳过GPBind页 */
  SKIP_GP_BIND_PAGE = 'skip_gp_bind_page',
  /** 取消过的申请单 */
  CANCEL_APPLY_LIST = 'cancelApplyList',
}
