/** native advance.ai 活体识别模块 */

import { NativeModules } from 'react-native';
// 原生方法
const ContactPicker = NativeModules.ContactPicker;

/** 事件订阅的方式 */
export enum EContactPickerEventType {
  /** 开始选择联系人 */
  CONTRACT_GET_START = 'CONTRACT_GET_START',
  /** 联系人选择成功 */
  CONTRACT_GET_SUCCESS = 'CONTRACT_GET_SUCCESS',
  /**  联系人选择失败 */
  CONTRACT_GET_FAIL = 'CONTRACT_GET_FAIL',
}
export interface IContactPickerData {
  eventType: EContactPickerEventType;
  name?: string;
  number?: string;
}

// 订阅事件
// 例子：
// useEffect(() => {
// 	const eventEmitter = new NativeEventEmitter(NativeModules.NativeMethod);
// 	let eventListener = eventEmitter.addListener(ENativeEventEmitterEventName.CONTACT_PICKER_EVENT_NAME_SPACE, event => {
// 		console.log(event.eventProperty) // "someValue"
// 	});

// 	// Removes the listener once unmounted
// 	return () => {
// 		eventListener.remove();
// 	};
// }, []);

/**
 * 选取用户联系人
 */
export const pickContact = () => ContactPicker.pC();
