/** native advance.ai 活体识别模块 */

import { NativeModules } from 'react-native';
// 原生方法
const GmailPicker = NativeModules.GmailPicker;

/** 事件订阅的方式 */
export enum EGmailPickerEventType {
  /** 开始选择联系人 */
  GMAIL_GET_START = 'GMAIL_GET_START',
  /** 联系人选择成功 */
  GMAIL_GET_SUCCESS = 'GMAIL_GET_SUCCESS',
  /**  联系人选择失败 */
  GMAIL_GET_FAIL = 'GMAIL_GET_FAIL',
}

export interface IGmailPickerData {
  eventType: EGmailPickerEventType;
  email?: string;
}

// 订阅事件
// 例子：
// useEffect(() => {
// 	const eventEmitter = new NativeEventEmitter(NativeModules.NativeMethod);
// 	let eventListener = eventEmitter.addListener(ENativeEventEmitterEventName.CONTACT_PICKER_EVENT_NAME_SPACE, event => {
// 		console.log(event.eventProperty) // "someValue"
// 	});

// 	// Removes the listener once unmounted
// 	return () => {
// 		eventListener.remove();
// 	};
// }, []);

/**
 * 选取用户联系人
 */
export const pickGmail = () => GmailPicker.pG();
