/** native 设备数据采集上报 */
import { KVManager, UserInfoManager } from '@/managers';
import { log } from '@/utils';
import { NativeModules } from 'react-native';

const NativeMethod = NativeModules.NativeMethod;

export enum EEventType {
  LAUNCH = 'launch',
  IN_APPLY = 'in_apply',
  PRE_APPLY = 'pre_apply',
  APPLY = 'apply',
  REPAYMENT = 'repayment',
}

/** 一天毫秒数 */
const ONE_DAY = 3600000 * 24;
/** 一小时毫秒数 */
const ONE_HOUR = 3600000;
/** 一分钟毫秒数 */
const ONE_MIN = 60000;

const CALENDAR_REPORT_CACHE_KEY = 'calendarReportDeviceData';

const REPAYMENT_REPORT_CACHE_KEY = 'repaymentReportDeviceData';

const LAUNCH_REPORT_CACHE_KEY = 'launchReportDeviceData';

const IN_APPLY_REPORT_CACHE_KEY = 'inApplyReportDeviceData';

/** 获取还款场景上报数据的缓存key */
const getRepaymentReportCacheKey = () => {
  return `${UserInfoManager.context.userModel.userId}_${REPAYMENT_REPORT_CACHE_KEY}`;
};

/** 获取日历数据上报数据的缓存key */
const getCalendarReportCacheKey = () => {
  return `${UserInfoManager.context.userModel.userId}_${CALENDAR_REPORT_CACHE_KEY}`;
};

/** 获取启动上报场景的缓存key */
const getLaunchReportCacheKey = () => {
  return `${UserInfoManager.context.userModel.userId}_${LAUNCH_REPORT_CACHE_KEY}`;
};

/** 获取千万申请场景的缓存key */
const getInApplyReportCacheKey = () => {
  return `${UserInfoManager.context.userModel.userId}_${IN_APPLY_REPORT_CACHE_KEY}`;
};

/** 上报的数据控制 */
enum EReportData {
  /** 只上报设备应用列表 */
  APPLIST = 'APPLIST',
  CALENDAR = 'CALENDAR',
  /** 只上报设备短信数据列表 */
  MESSAGE = 'MESSAGE',
  /** 同时上报设备应用列表和短信列表数据 */
  APPLIST_MESSAGE = 'APPLIST_MESSAGE',
  /** 同时上报设备应用列表和短信列表数据 */
  APPLIST_MESSAGE_CALENDAR = 'APPLIST_MESSAGE_CALENDAR',
}

/** 启动 */
export const setup = (baseUrl: string) => NativeMethod.initASDeviceLib(baseUrl);

/** 启动页场景上报 上报有1个小时的缓存时长 */
export const launchReportDeviceData = async (token: string, applyOrderId: string = '') => {
  let launchReportCacheKey = getLaunchReportCacheKey();
  const lastReportDateTime: number = Number(
    KVManager.action.getString(launchReportCacheKey) || '0',
  );
  const nowDateTime: number = Date.now();
  if (nowDateTime - lastReportDateTime >= ONE_DAY) {
    try {
      await NativeMethod.fDeviceData(token, EReportData.APPLIST, applyOrderId, EEventType.LAUNCH);
      await KVManager.action.setString(launchReportCacheKey, String(nowDateTime));
    } catch (e) {
      log.error('# launchReportDeviceData', e);
    }
  }
};

/** 主页场景上报 上报有1分钟的缓存时长 */
export const inApplyReportDeviceData = async (token: string, applyOrderId: string = '') => {
  // todo 在这里补充一分钟缓存的相关逻辑代码 采用 MMKV
  let inApplyReportCacheKey = getInApplyReportCacheKey();
  const lastReportDateTime: number = Number(
    KVManager.action.getString(inApplyReportCacheKey) || '0',
  );
  const nowDateTime: number = Date.now();
  if (nowDateTime - lastReportDateTime >= ONE_MIN) {
    try {
      await NativeMethod.fDeviceData(
        token,
        EReportData.APPLIST_MESSAGE,
        applyOrderId,
        EEventType.IN_APPLY,
      );
      await KVManager.action.setString(inApplyReportCacheKey, String(nowDateTime));
    } catch (e) {
      log.error('# inApplyReportDeviceData', e);
    }
  }
};

/** 提交申请订单成功上报 */
export const applyReportDeviceData = async (token: string, applyOrderId: string) => {
  try {
    await NativeMethod.fDeviceData(
      token,
      EReportData.APPLIST_MESSAGE,
      applyOrderId,
      EEventType.APPLY,
    );
  } catch (e) {
    log.error('# applyReportDeviceData', e);
  }
};

/** 提交预先申请订单成功上报 */
export const preApplyReportDeviceData = async (token: string, applyOrderId: string) => {
  try {
    await NativeMethod.fDeviceData(
      token,
      EReportData.APPLIST_MESSAGE,
      applyOrderId,
      EEventType.PRE_APPLY,
    );
  } catch (e) {
    log.error('# applyReportDeviceData', e);
  }
};

/** 还款页面上报包含日历的数据 */
/** 提交申请订单成功上报 */
export const repaymentReportDeviceData = async (token: string, applyOrderId: string) => {
  let repaymentReportCacheKey = getRepaymentReportCacheKey();
  const lastReportDateTime: number = Number(
    KVManager.action.getString(repaymentReportCacheKey) || '0',
  );
  const nowDateTime: number = Date.now();
  if (nowDateTime - lastReportDateTime >= ONE_DAY) {
    try {
      await NativeMethod.fDeviceData(
        token,
        EReportData.APPLIST_MESSAGE_CALENDAR,
        applyOrderId,
        EEventType.REPAYMENT,
      );
      await KVManager.action.setString(repaymentReportCacheKey, String(nowDateTime));
    } catch (e) {
      log.error('# repaymentReportDeviceData', e);
    }
  }
};

/** 日历数据在还款页单独上报 */
export const calendarReportDeviceData = async (token: string, applyOrderId: string) => {
  let calendarReportCacheKey = getCalendarReportCacheKey();
  const lastReportDateTime: number = Number(
    KVManager.action.getString(calendarReportCacheKey) || '0',
  );
  const nowDateTime: number = Date.now();
  if (nowDateTime - lastReportDateTime >= ONE_DAY) {
    try {
      await NativeMethod.fDeviceData(
        token,
        EReportData.CALENDAR,
        applyOrderId,
        EEventType.REPAYMENT,
      );
      await KVManager.action.setString(calendarReportCacheKey, String(nowDateTime));
    } catch (e) {
      log.error('# repaymentReportDeviceData', e);
    }
  }
};
