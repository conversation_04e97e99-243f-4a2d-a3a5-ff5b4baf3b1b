import { NativeModules } from 'react-native';

const IntentData = NativeModules.IntentData;

export enum ELaunchMethod {
  /** 点击 icon 启动 */
  ICON_LAUNCH = 'icon_launch',
  /** 深度链接启动 */
  DEEOLINK_LANUNCH = 'deeplink_launch',
  /** push 启动 **/
  PUSH_LAUNCH = 'push_launch',
}

export enum EDeepLinkingAction {
  /** 打开营销页 */
  OPEN_MARKET = 'openMarket',
}

export interface IIntentData {
  launchMethod: ELaunchMethod;
  data?: string;
}

/** 获取intent的屏幕参数 */
export const getLaunchMethod = (): Promise<IIntentData> => IntentData.gLM();

/** 清理intent的屏幕参数 */
export const clearIntentData = (): Promise<IIntentData> => IntentData.cID();
