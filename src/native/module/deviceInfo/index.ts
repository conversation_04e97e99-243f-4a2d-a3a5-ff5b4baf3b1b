/**
 * @alias deviceInfo
 * <AUTHOR>
 */
import { NativeModules } from 'react-native';

const NativeMethod = NativeModules.NativeMethod;

/** 获取 谷歌广告ID  */
export const getGaid = (): Promise<string> => NativeMethod.gGaid();
/** 获取谷歌广告ID 在平台上是否可用 */
export const getIsLimitAdTrackingEnabled = (): Promise<boolean> =>
  NativeMethod.gIsLimitAdTrackingEnabled();
/** 获取appId 可以通过其他的数据通过逻辑来判断 */
export const getAppId = (): Promise<string> => NativeMethod.gAppId();
/** 获取系统版本code */
export const getSdkVersionCode = (): Promise<number> => NativeMethod.gSdkVersionCode();
/** 获取版本code */
export const getVersionCode = (): Promise<number> => NativeMethod.gVersionCode();
/** 获取版本名 */
export const getVersionName = (): Promise<string> => NativeMethod.gVersionName();
/** 获取 AndroidId */
export const getAndroidID = (): Promise<string> => NativeMethod.gAndroidID();
/** 获取构建类型 debug 或者 release */
export const getBulidType = (): Promise<string> => NativeMethod.gBulidType();
/** 获取包名 */
export const getPackageName = (): Promise<string> => NativeMethod.gPackageName();
/** 获取 ApplicationId  */
export const getApplicationId = (): Promise<string> => NativeMethod.gApplicationId();
/** 获取构建环境  noropresta_test 或者  noropresta_prod 可以通过这个来区分是那款产品，测试环境还是生产环境 */
export const getFlavor = (): Promise<string> => NativeMethod.gFlavor();
/** 获取 app的谷歌play商店的地址 */
export const getPlayStoreUrl = (): Promise<string> => NativeMethod.gPlayStoreUrl();
/** 当前的构建模式 debug 或者 非 debug */
export const getDebug = (): Promise<boolean> => NativeMethod.gDebug();
/** 是否采用 hermes 新引擎 */
export const getIsHermesEnabled = (): Promise<boolean> => NativeMethod.gIsHermesEnabled();
/** 是否采用 JSI 新架构模式 */
export const getIsNewArchitectrueEnabled = (): Promise<boolean> =>
  NativeMethod.gIsNewArchitectrueEnabled();
/** GPServer 是否可用 */
export const getGPServerEnabled = (): Promise<boolean> => NativeMethod.gGPServerEnabled();
