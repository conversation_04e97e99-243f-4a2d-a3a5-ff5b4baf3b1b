/** native 活体识别模块 */
import { NativeModules } from 'react-native';
// 原生方法
const FaceLiveVerification = NativeModules.FaceLiveVerification;

/** 事件订阅的方式 */
export enum EFaceLiveEventType {
  /** face 活体预检测 */
  FACE_LIVE_PRE_DETECT = 'FACE_LIVE_PRE_DETECT',
  /** face 活体预检测失败 */
  FACE_LIVE_PRE_DETECT_STATE_FAIL = 'FACE_LIVE_PRE_DETECT_STATE_FAIL',
  /** face 活体在开始前 */
  FACE_LIVE_PRE_START = 'FACE_LIVE_PRE_START',
  /** face 活体在开始完成 */
  FACE_LIVE_PRE_FINISH = 'FACE_LIVE_PRE_FINISH',
  /** face 活体在开始完成之后发生错误
   * 可能失败的原因 bizToken 不匹配
   */
  FACE_LIVE_PRE_FINISH_STATE_FAIL = 'FACE_LIVE_PRE_FINISH_STATE_FAIL',
  /** face 活体在开始完成之后成功 */
  // FACE_LIVE_PRE_FINISH_STATE_SUCCESS = "FACE_LIVE_PRE_FINISH_STATE_SUCCESS",
  /** 开始活体检测 */
  FACE_LIVE_START_DETECT = 'FACE_LIVE_START_DETECT',
  /** face 活体在执行完成之后, 因为用户行为导致的失败，例如用户取消、人脸移出等 */
  FACE_LIVE_START_DETECT_STATE_FAIL = 'FACE_LIVE_START_DETECT_STATE_FAIL',
  /** face 活体在执行完成之后, 因为token失效、sdk版本过低导致的错误 */
  FACE_LIVE_START_DETECT_STATE_ERROR = 'FACE_LIVE_START_DETECT_STATE_ERROR',
  /** face 活体在执行完成之后成功
   * 在此阶段接受活体验证的数据，以及活体的原始数据文件地址。
   */
  FACE_LIVE_START_DETECT_STATE_SUCCESS = 'FACE_LIVE_END_FINISH_STATE_SUCCESS',
}
export interface IFaceEventData {
  eventType: EFaceLiveEventType;
  faceLivenessFilePath?: string;
  megliveData?: string;
  mBizToken?: string;
}

// 订阅事件
// 例子：
// useEffect(() => {
// 	const eventEmitter = new NativeEventEmitter(NativeModules.NativeMethod);
// 	let eventListener = eventEmitter.addListener(ENativeEventEmitterEventName.FaceLiveEventNameSpace, event => {
// 		console.log(event.eventProperty) // "someValue"
// 	});

// 	// Removes the listener once unmounted
// 	return () => {
// 		eventListener.remove();
// 	};
// }, []);

/** 启动活体检测 */
export const startFaceLiveVerification = (bizToken: string) => FaceLiveVerification.sFLV(bizToken);
