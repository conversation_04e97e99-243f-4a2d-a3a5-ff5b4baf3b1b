/** native 缓存模块 */

import { EL<PERSON>al<PERSON><PERSON> } from '@/localStorage';
import { NativeModules } from 'react-native';

const NativeMethod = NativeModules.NativeMethod;

/** 原生端的缓存 key */
enum ENATIVE_LOCAL_KEY {
  /** token */
  TOKEN_STRING = 'TOKEN',
  /** userId */
  USER_ID_STRING = 'USER_ID',
  /** appOrderId */
  APPLY_ORDER_ID_STRING = 'APPLY_ORDER_ID',
  /** mobile */
  MOBILE_STRING = 'MOBILE',
  /** read permission */
  READ_PERMISSION_BOOLEAN = 'READ_PERMISSION',
  /** 版本更新状态标识 */
  VERSION_UPDATE_STATUS_FLAG_INT = 'VERSION_UPDATE_STATUS_FLAG',
  /** 放款成功还款页 GP 评分状态 */
  REPAY_GP_REVIEW_BOOLEAN = 'REPAY_GP_REVIEW_BOOLEAN',
  /** 提交申请订单成功 GP 评分状态 */
  ORDER_REVIEW_GP_BOOLEAN = 'ORDER_REVIEW_GP_BOOLEAN',
  /** FCM token */
  FCM_TOKEN_STRING = 'FCM_TOKEN',
  /**  首次启动 用于引导页的展示 */
  // FIRST_LAUNCH_BOOLEAN = 'FIRST_LAUNCH',
}

const locakKeyMapping: Record<ENATIVE_LOCAL_KEY, ELocalKey> = {
  [ENATIVE_LOCAL_KEY.TOKEN_STRING]: ELocalKey.TOKEN_STRING,
  [ENATIVE_LOCAL_KEY.USER_ID_STRING]: ELocalKey.USER_ID_STRING,
  [ENATIVE_LOCAL_KEY.APPLY_ORDER_ID_STRING]: ELocalKey.APPLY_ORDER_ID_STRING,
  [ENATIVE_LOCAL_KEY.MOBILE_STRING]: ELocalKey.PHONE_NUMBER_STRING,
  [ENATIVE_LOCAL_KEY.READ_PERMISSION_BOOLEAN]: ELocalKey.READ_PERMISSION_BOOLEAN,
  [ENATIVE_LOCAL_KEY.VERSION_UPDATE_STATUS_FLAG_INT]: ELocalKey.VERSION_UPDATE_STATUS_FLAG_INT,
  [ENATIVE_LOCAL_KEY.REPAY_GP_REVIEW_BOOLEAN]: ELocalKey.REPAY_GP_REVIEW_BOOLEAN,
  [ENATIVE_LOCAL_KEY.ORDER_REVIEW_GP_BOOLEAN]: ELocalKey.ORDER_CHECK_REVIEW_GP_BOOLEAN,
  [ENATIVE_LOCAL_KEY.FCM_TOKEN_STRING]: ELocalKey.FCM_TOKEN_STRING,
  // [NATIVE_LOCAL_KEY.FIRST_LAUNCH]: ''
};

// const NATIVE_CACHE_KEY_PREX_INT: string = 'localCacheFixForInt_';
// const NATIVE_CACHE_KEY_PREX_STRING: string = 'localCacheFixForString_';
// const NATIVE_CACHE_KEY_PREX_BOOLEAN: string = 'localCacheFixForBoolean_';

/** 获取缓存 string */
export const getCacheString = (k: string): Promise<string> => NativeMethod.gCacheString(k);
/** 设置缓存 string */
export const setCacheString = (k: string, v: string): Promise<boolean> =>
  NativeMethod.sCacheString(k, v);
/** 设置缓存 int */
export const getCacheInt = (k: string): Promise<number> => NativeMethod.gCacheInt(k);
/** 获取缓存 int */
export const setCacheInt = (k: string, v: number): Promise<boolean> => NativeMethod.sCacheInt(k, v);
/** 获取缓存 boolean */
export const getCacheBoolean = (k: string): Promise<boolean> => NativeMethod.gCacheBoolean(k);
/** 获取缓存 boolean */
export const setCacheBoolean = (k: string, v: boolean): Promise<boolean> =>
  NativeMethod.sCacheInt(k, v);

// // native 缓存修复。读取原生端的缓存，存入 MMKV
// const localCacheFixForString = async (e: string, debug: boolean = false) => {
//   // 启动事件缓存
//   // 先判断是否已经修复过
//   const fixCacheKey = `${NATIVE_CACHE_KEY_PREX_STRING}${e}`;
//   const cacheKey = e;
//   const fixCacheFlag = KVManagerInstance.action.getBoolean(fixCacheKey);
//   let nativeValue: string = '';
//   if (!fixCacheFlag) {
//     try {
//       nativeValue = await getCacheString(cacheKey);
//       KVManagerInstance.action.setString(
//         locakKeyMapping[cacheKey as ENATIVE_LOCAL_KEY],
//         nativeValue,
//       );
//       KVManagerInstance.action.setBoolean(fixCacheKey, true);
//     } catch (e) {
//       log.error(
//         `# localCacheFixForString fixCacheKey=${fixCacheKey};cacheKey=${cacheKey}`,
//         e,
//       );
//     }
//   }
//   if (debug) {
//     log.debug(
//       `# localCacheFixForString fixCacheKey=${fixCacheKey} value=${fixCacheFlag} cacheKey=${cacheKey} value=${nativeValue}`,
//     );
//   }
// };

// const localCacheFixForInt = async (e: string, debug: boolean = false) => {
//   // 启动事件缓存
//   // 先判断是否已经修复过
//   const fixCacheKey = `${NATIVE_CACHE_KEY_PREX_INT}${e}`;
//   const cacheKey = e;
//   const fixCacheFlag = KVManagerInstance.action.getBoolean(fixCacheKey);
//   let nativeValue: string = '';
//   if (!fixCacheFlag) {
//     try {
//       const nativeValue = await getCacheInt(cacheKey);
//       KVManagerInstance.action.setInt(
//         locakKeyMapping[cacheKey as ENATIVE_LOCAL_KEY],
//         nativeValue,
//       );
//       KVManagerInstance.action.setBoolean(fixCacheKey, true);
//     } catch (e) {
//       log.error(`fixCacheKey=${fixCacheKey};cacheKey=${cacheKey}`, e);
//     }
//   }
//   if (debug) {
//     log.debug(
//       `fixCacheKey=${fixCacheKey} value=${fixCacheFlag} cacheKey=${cacheKey} value=${nativeValue}`,
//     );
//   }
// };

// const localCacheFixForBoolean = async (e: string, debug: boolean = false) => {
//   // 启动事件缓存
//   // 先判断是否已经修复过
//   const fixCacheKey = `${NATIVE_CACHE_KEY_PREX_BOOLEAN}${e}`;
//   const cacheKey = e;
//   const fixCacheFlag = KVManagerInstance.action.getBoolean(fixCacheKey);
//   let nativeValue: string = '';
//   if (!fixCacheFlag) {
//     try {
//       const nativeValue = await getCacheBoolean(cacheKey);
//       KVManagerInstance.action.setBoolean(
//         locakKeyMapping[cacheKey as ENATIVE_LOCAL_KEY],
//         nativeValue,
//       );
//       KVManagerInstance.action.setBoolean(fixCacheKey, true);
//     } catch (error) {
//       log.error(`fixCacheKey=${fixCacheKey};cacheKey=${cacheKey}`, error);
//     }
//   }
//   if (debug) {
//     log.debug(
//       `fixCacheKey=${fixCacheKey} value=${fixCacheFlag} cacheKey=${cacheKey} value=${nativeValue}`,
//     );
//   }
// };

// /** @description native 缓存数据修复 */
// export const localCaheFix = async (debug: boolean = false) => {
//   await localCacheFixForString(ENATIVE_LOCAL_KEY.TOKEN_STRING, debug);
//   await localCacheFixForString(ENATIVE_LOCAL_KEY.APPLY_ORDER_ID_STRING, debug);
//   await localCacheFixForString(ENATIVE_LOCAL_KEY.MOBILE_STRING, debug);
//   await localCacheFixForString(ENATIVE_LOCAL_KEY.USER_ID_STRING, debug);
//   await localCacheFixForString(ENATIVE_LOCAL_KEY.FCM_TOKEN_STRING, debug);
//   await localCacheFixForBoolean(
//     ENATIVE_LOCAL_KEY.ORDER_REVIEW_GP_BOOLEAN,
//     debug,
//   );
//   await localCacheFixForBoolean(
//     ENATIVE_LOCAL_KEY.REPAY_GP_REVIEW_BOOLEAN,
//     debug,
//   );
//   await localCacheFixForBoolean(
//     ENATIVE_LOCAL_KEY.READ_PERMISSION_BOOLEAN,
//     debug,
//   );
//   await localCacheFixForInt(
//     ENATIVE_LOCAL_KEY.VERSION_UPDATE_STATUS_FLAG_INT,
//     debug,
//   );
// };
