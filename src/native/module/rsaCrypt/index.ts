import { NativeModules } from 'react-native';

const NativeMethod = NativeModules.NativeMethod;
/** 公钥加密 */
export const rsaEncryptByPubKey = (plaintext: string, publicKey: string): Promise<string> =>
  NativeMethod.rsaEBPubKey(plaintext, publicKey);

/** 公钥解密 */
export const rsaDecryptByPubKey = (plaintext: string, publicKey: string): Promise<string> =>
  NativeMethod.rsaDBPubKey(plaintext, publicKey);

/** 私钥加密 */
export const rsaEncryptByPreKey = (plaintext: string, publicKey: string): Promise<string> =>
  NativeMethod.rsaEBPreKey(plaintext, publicKey);

/** 私钥解密 */
export const rsaDecryptByPreKey = (plaintext: string, publicKey: string): Promise<string> =>
  NativeMethod.rsaDBPreKey(plaintext, publicKey);
