/** 错误日志 */
import { BaseEnumsSpace } from '@/enums';
import { EventPointRequestSpace, fetchUploadErrorLog } from '@/server';
import RNFS from 'react-native-fs';
import { NativeModules } from '../native/_export_';
import { getPlatformPathDir } from './fileUtils';
import log from './log';

export const { recordLog, clearLog, getLogFilePath } = NativeModules.errorLog;

/** 上传错误日志文件 */
export const uploadErrorLog = async (debug: boolean = false) => {
  try {
    const logFilePath: string = await getLogFilePath();
    const logFileAbsolutePath: string = getPlatformPathDir(logFilePath);
    debug && log.debug(`# errorLogFilePath = ${logFileAbsolutePath}`);
    const errorLogData: string = await RNFS.readFile(logFileAbsolutePath, 'utf8');
    debug &&
      log.debug(`# errorLogData `, {
        errorLogData,
      });
    if (errorLogData != '') {
      // 上传文件
      const errorLogDataInfo: EventPointRequestSpace.LogRecordType[] = JSON.parse(errorLogData);
      const uploadEventLog: EventPointRequestSpace.RequestUploadErrorLogType = {
        logList: errorLogDataInfo,
      };
      const reponseData = await fetchUploadErrorLog(uploadEventLog);
      if (reponseData.code === BaseEnumsSpace.ENetworkStatus.SUCCESS) {
        // 日志上传成功 清除日志
        log.info('log record upload success!');
        clearLog();
      } else {
        debug && log.debug('log record upload fail!');
      }

      if (errorLogDataInfo.length >= 100) {
        clearLog();
      }
    }
  } catch (e) {
    log.error('log record upload fail!', e);
  }
};
