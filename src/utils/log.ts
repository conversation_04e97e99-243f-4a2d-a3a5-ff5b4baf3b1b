import { logForNamespace } from '../logCenter';
import { recordLog } from './errorLog';

const defaultLogger = logForNamespace('default');

export default {
  /**
   * 设定命名空间，生成一个 logger 实例
   */
  generateInstanceForNamespace: (namespace: string) => {
    const logger = logForNamespace(namespace);
    const error = logger.error;
    // 重写日志方法将所有的错误日志，记录到内部错误日志收集系统
    // 和 FireBase 平台
    logger.error = (...args: any[]) => {
      error(...args);
      // 错误日志描述
      let errorLogDes: string = args[0];
      // 错误日志详情
      let errorLogInfo = args[1];
      try {
        if (errorLogInfo) {
          // 记录到自己内部的错误日志系统
          recordLog(errorLogDes + JSON.stringify(errorLogInfo));
        } else {
          // 记录到自己内部的错误日志系统
          recordLog(errorLogDes + JSON.stringify(errorLogInfo));
        }
      } catch (e) {
        defaultLogger.debug('# append error fail', e);
      }
    };
    return logger;
  },
  /**
   * 使用默认命名空间打印内容
   * 默认命名空间为 'default'
   */
  info: defaultLogger.info,
  warn: defaultLogger.warn,
  /**
   * @description 第二个参数为错误日志详情。
   *
   */
  error: (...args: any[]) => {
    defaultLogger.error(...args);

    let errorLogDes: string = args[0];

    let errorLogInfo = args[1];

    try {
      if (errorLogInfo) {
        // 记录到自己内部的错误日志系统
        recordLog(errorLogDes + JSON.stringify(errorLogInfo));
      } else {
        // 记录到自己内部的错误日志系统
        recordLog(errorLogDes + JSON.stringify(errorLogInfo));
      }
    } catch (e) {
      defaultLogger.debug('# append error fail', e);
    }
  },
  debug: defaultLogger.debug,
};
