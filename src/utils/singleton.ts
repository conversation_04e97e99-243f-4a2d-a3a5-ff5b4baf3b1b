type TCache = Map<string, any>;
type TKey = string;
type TValue = Map<string, any> | Array<any> | any;

/**
 * @private
 * get cache instance from global variable
 */
function getRNCCacheInstance(): TCache {
  //@ts-ignore
  if (!(global as any).__rnc_cache__) {
    //@ts-ignore
    (global as any).__rnc_cache__ = new Map<string, any>() as TCache;
  }
  //@ts-ignore
  return (global as any).__rnc_cache__ as TCache;
}

/**
 * register object in cache map
 * @param key cache key
 * @param value cache value
 */
export function register(key: TKey, value: TValue) {
  const cache: TCache = getRNCCacheInstance();
  if (!cache.has(key)) {
    cache.set(key, value);
  }
}

/**
 * get cache instance value by cache key
 * @param key cache key
 * @returns cache value
 */
export function get<T>(key: TKey): T {
  const cache: TCache = getRNCCacheInstance();
  return cache.get(key) as T;
}

/**
 * get cache instance value by cache key, if cache value not exist, register it
 * @param key cache key
 * @param value cache value (instance)
 * @returns cache value
 */
export function getOrRegister<T>(key: TKey, value: T): T {
  register(key, value);
  return get(key) as T;
}

export default {
  register,
  get,
  getOrRegister,
};
