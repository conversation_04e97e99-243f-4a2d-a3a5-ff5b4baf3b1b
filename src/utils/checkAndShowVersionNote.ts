/**
 * @description 检查当前app是否是更新的，是更新的则显示更新信息弹窗
 */

import { BaseConfig } from '@/baseConfig';
import { KVManager, ModalList, modalDataStoreInstance } from '@/managers';

// 如果KVManager中没有latestVersion，存储当前版本
// 如果KVManager中latestVersion小于当前版本，存储当前版本，显示更新信息弹窗
// 如果KVManager中latestVersion大于等于当前版本，不显示更新信息弹窗
export const checkAndShowVersionNote = () => {
  const whitelist = [57];
  const localLatestVersion = KVManager.action.getInt('latestVersion');
  const curVersion = BaseConfig.appVersionCode;
  if (whitelist.includes(curVersion)) {
    KVManager.action.setInt('latestVersion', curVersion);
    return;
  }

  if (!!localLatestVersion) {
    if (curVersion > localLatestVersion) {
      modalDataStoreInstance.openModal({
        key: ModalList.VERSION_NOTE,
      });
      KVManager.action.setInt('latestVersion', curVersion);
    }
  } else if (curVersion === 55) {
    // 特异性处理，55版本新增该功能，显示更新信息弹窗
    modalDataStoreInstance.openModal({
      key: ModalList.VERSION_NOTE,
    });
    KVManager.action.setInt('latestVersion', curVersion);
  } else {
    KVManager.action.setInt('latestVersion', curVersion);
  }
};
