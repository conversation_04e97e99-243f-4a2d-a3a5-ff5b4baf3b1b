/**
 * navigation functions
 */
import { BaseInfoManager, UserInfoManager } from '@/managers';
import { HomeTabScreenList, RouterConfig, WalletTabScreenList, UserTabScreenList } from '@/routes';
import { CommonActions, StackActions } from '@react-navigation/native';
import { BackHandler } from 'react-native';
import { RootStackParamList, navigationRef } from './navigation';

export function setParams(params: any) {
  // @ts-ignore
  navigationRef.setParams(params);
}

export function navigate<T extends keyof RootStackParamList>(
  name: T,
  params?: RootStackParamList[T],
) {
  if (navigationRef.isReady()) {
    let nextRouteName: string;
    let nextRouteParams;
    // @ts-ignore 在这里处理屏幕导航嵌套问题
    if (HomeTabScreenList.includes(name as RouterConfig)) {
      nextRouteName = RouterConfig.HOME_TABS;
      nextRouteParams = {
        screen: RouterConfig.HOME_TAB,
        params: {
          screen: name,
          params,
        },
      };
    } else if (WalletTabScreenList.includes(name as RouterConfig)) {
      nextRouteName = RouterConfig.HOME_TABS;
      nextRouteParams = {
        screen: RouterConfig.WALLET_TAB,
        params: {
          screen: name,
          params,
        },
      };
    } else if (UserTabScreenList.includes(name as RouterConfig)) {
      nextRouteName = RouterConfig.HOME_TABS;
      nextRouteParams = {
        screen: RouterConfig.USER_TAB,
        params: {
          screen: name,
          params,
        },
      };
    } else {
      nextRouteName = name;
      nextRouteParams = params;
    }
    //@ts-ignore
    navigationRef.navigate(nextRouteName, nextRouteParams);
  }
}

/** 重置导航堆栈，并且推入一个新的屏幕 */
export function resetRouteNavigate<T extends keyof RootStackParamList>(
  name: T,
  params?: RootStackParamList[T],
) {
  if (navigationRef.isReady()) {
    let nextRouteName: string;
    let nextRouteParams;
    // @ts-ignore 在这里处理屏幕导航嵌套问题
    if (HomeTabScreenList.includes(name as RouterConfig)) {
      nextRouteName = RouterConfig.HOME_TABS;
      nextRouteParams = {
        screen: RouterConfig.HOME_TAB,
        params: {
          screen: name,
          params,
          merge: true,
        },
      };
    } else if (WalletTabScreenList.includes(name as RouterConfig)) {
      nextRouteName = RouterConfig.HOME_TABS;
      nextRouteParams = {
        screen: RouterConfig.WALLET_TAB,
        params: {
          screen: name,
          params,
          merge: true,
        },
      };
    } else if (UserTabScreenList.includes(name as RouterConfig)) {
      nextRouteName = RouterConfig.HOME_TABS;
      nextRouteParams = {
        screen: RouterConfig.USER_TAB,
        params: {
          screen: name,
          params,
        },
      };
    } else {
      nextRouteName = name;
      nextRouteParams = params;
    }
    // routeLog.debug('# resetRouteNavigate state', {
    //   RootState: navigationRef.getRootState(),
    //   State: navigationRef.getState(),
    //   route: navigationRef.getCurrentRoute(),
    // });
    if (navigationRef.getCurrentRoute()?.name !== name) {
      navigationRef.dispatch(
        CommonActions.reset({
          index: 0,
          routes: [
            {
              name: nextRouteName,
              //@ts-ignore
              params: nextRouteParams,
            },
          ],
        }),
      );
    }
  }
}

/** 重置导航堆栈，但是回保留第一个 route 并且推入一个新的屏幕。支持返回 */
export function resetRouteNavigateCanGoback<T extends keyof RootStackParamList>(
  name: T,
  params: RootStackParamList[T] = undefined,
  baseRouterName: RouterConfig | undefined = undefined,
) {
  if (navigationRef.isReady()) {
    if (navigationRef.getCurrentRoute()?.name !== name) {
      const rootState = navigationRef.getRootState();
      const state = navigationRef.getState();
      if (baseRouterName === RouterConfig.HOME_TABS) {
        const routes = [
          {
            name: RouterConfig.HOME_TABS,
            params: {
              screen: RouterConfig.HOME_TAB,
              params: {
                screen: RouterConfig.HOME_SCREEN,
              },
            },
          },
          {
            name: name,
            //@ts-ignore
            params: params,
          },
        ];

        navigationRef.dispatch(
          CommonActions.reset({
            index: 1,
            //@ts-ignore
            routes,
          }),
        );
      } else if (state?.routes?.length === 1 || rootState?.routes?.length === 1) {
        // @ts-ignore
        navigate(name, params);
      } else if (state?.routes?.length > 1 || rootState?.routes?.length > 1) {
        const routes = [];
        if (state?.routes?.length > 1) {
          routes.push(state?.routes[0]);
        } else if (rootState?.routes?.length > 1) {
          routes.push(rootState?.routes[0]);
        }
        routes.push({
          name: name,
          //@ts-ignore
          params: params,
        });
        // log.debug('# resetRouteNavigateCanGoback state', {
        //   RootState: rootState,
        //   State: state,
        //   route: navigationRef.getCurrentRoute(),
        // });
        navigationRef.dispatch(
          CommonActions.reset({
            index: 1,
            //@ts-ignore
            routes,
          }),
        );
      }
    }
  }
}

export function navigationGoBack() {
  if (navigationRef.isReady() && navigationRef.canGoBack()) {
    // 可以返回的页面直接返回
    navigationRef.goBack();
  } else {
    // 没办法返回的页面，直接退出app
    BackHandler.exitApp();
  }
}

export function toLoginOrPermissionActivity(): boolean {
  // if (!BaseInfoManager.context.baseModel.agreePermissions) {
  //   resetRouteNavigate(RouterConfig.PERMISSION_AGREE as any);
  //   return true;
  // }
  if (!UserInfoManager.context.userModel.isLogined) {
    resetRouteNavigate(RouterConfig.ENTER_PHONE_NUMBER as any, {
      phoneNumber: '',
    });
    return true;
  }
  return false;
}

export async function toNext(routerName: string, params?: any): Promise<boolean> {
  navigate(routerName as any, params);
  return true;
}

/** 没有申请订单的新户，路由调整逻辑 */
const noHasApplyIdNewUserApplyRouteStateFlow = (curPageName: string = '') => {
  switch (curPageName) {
    case RouterConfig.HOME_SCREEN:
      navigate(RouterConfig.BASIC_INFO as any);
      break;
    case RouterConfig.BASIC_INFO:
      if (UserInfoManager.context.userModel.isNeedAccoutBind) {
        navigate(RouterConfig.PROCESS_ACCOUNT_BINDING as any);
      } else if (UserInfoManager.context.userModel.isFirstLoanQuestion) {
        // 基本信息输入
        navigate(RouterConfig.PROCESS_QUESTIONNAIRE as any);
      } else {
        navigate(RouterConfig.OCR_INFO as any);
      }
      break;
    case RouterConfig.PROCESS_ACCOUNT_BINDING:
      if (UserInfoManager.context.userModel.isFirstLoanQuestion) {
        // 基本信息输入
        navigate(RouterConfig.PROCESS_QUESTIONNAIRE as any);
      } else {
        navigate(RouterConfig.OCR_INFO as any);
      }
      break;
    case RouterConfig.PROCESS_QUESTIONNAIRE:
      navigate(RouterConfig.OCR_INFO as any);
      break;
    case RouterConfig.OCR_INFO:
      let routerName = RouterConfig.TAKE_PHOTO;
      if (BaseInfoManager.context.baseModel.isAccUser) {
        routerName = RouterConfig.TAKE_PHOTO_RESULT;
      }
      // OCR录入curp信息页面
      navigate(routerName as any);
      break;
    case RouterConfig.TAKE_PHOTO:
    case RouterConfig.TAKE_PHOTO_RESULT:
      /** 如果是通过滑块的方式加入 */
      if (BaseInfoManager.context.baseModel.isMultiPeriodSwitch) {
        navigate(RouterConfig.FIRST_LOAN_PRODUCT_SELECT as any);
      } else if (BaseInfoManager.context.baseModel.firstSelectAmountSlideSwitch) {
        navigate(RouterConfig.SELECT_AMOUNT_SLIDER as any);
      } else {
        navigate(RouterConfig.SELECT_AMOUNT as any);
      }
      break;
    case RouterConfig.FIRST_LOAN_PRODUCT_SELECT:
      navigate(RouterConfig.MULTI_PERIOD_FIRST_LOAN_PAGE as any);
      break;
    case RouterConfig.SELECT_AMOUNT:
    case RouterConfig.SELECT_AMOUNT_SLIDER:
    case RouterConfig.MULTI_PERIOD_FIRST_LOAN_PAGE:
      navigate(RouterConfig.CLABE_BASIC_INFO as any);
      break;
    default:
      resetRouteNavigate(RouterConfig.HOME_TABS as any);
      break;
  }
};

/**
 *
 * @param isForcedSupply 强制补件场景是需要resetRouter
 * @returns
 */
export async function toDataVerifyStateActivity(isForcedSupply: boolean = false): Promise<boolean> {
  if (!UserInfoManager.context.userModel.isLogined) {
    return false;
  }
  if (UserInfoManager.context.userModel.isNeedOtp) {
    // otp补件
    resetRouteNavigate(RouterConfig.SUPPLY_OTP as any);
    return true;
  }
  console.log(
    'toDataVerifyStateActivity BASIC_INFO',
    UserInfoManager.context.userModel.isNeedBasic ||
      UserInfoManager.context.userModel.isNeedWork ||
      UserInfoManager.context.userModel.isNeedContact,
  );

  if (
    UserInfoManager.context.userModel.isNeedBasic ||
    UserInfoManager.context.userModel.isNeedWork ||
    UserInfoManager.context.userModel.isNeedContact
  ) {
    // 基本信息输入
    resetRouteNavigateCanGoback(
      RouterConfig.BASIC_INFO as any,
      undefined,
      isForcedSupply ? RouterConfig.HOME_TABS : undefined,
    );
    return true;
  }

  // 第三方账户绑定
  if (UserInfoManager.context.userModel.isNeedAccoutBind) {
    // 基本信息输入
    resetRouteNavigateCanGoback(
      RouterConfig.PROCESS_ACCOUNT_BINDING as any,
      undefined,
      isForcedSupply ? RouterConfig.HOME_TABS : undefined,
    );
    return true;
  }

  // 新户动态问券
  if (UserInfoManager.context.userModel.isNeedRKQuestion) {
    // 基本信息输入
    resetRouteNavigateCanGoback(
      RouterConfig.PROCESS_QUESTIONNAIRE as any,
      undefined,
      isForcedSupply ? RouterConfig.HOME_TABS : undefined,
    );
    return true;
  }

  // 进件页
  if (UserInfoManager.context.userModel.isNeedCurp) {
    if (UserInfoManager.context.userModel.isOcrSceneError) {
      // 系统维护页面
      resetRouteNavigateCanGoback(RouterConfig.MAINTAINER as any);
      return true;
    } else {
      // OCR录入curp信息页面
      resetRouteNavigateCanGoback(
        RouterConfig.OCR_INFO as any,
        undefined,
        isForcedSupply ? RouterConfig.HOME_TABS : undefined,
      );
      return true;
    }
  }

  if (
    UserInfoManager.context.userModel.isNeedSelfie &&
    UserInfoManager.context.userModel.isUserTypeNew
  ) {
    // 新户需要自拍照
    let routerName = RouterConfig.TAKE_PHOTO;
    if (BaseInfoManager.context.baseModel.isAccUser) {
      routerName = RouterConfig.TAKE_PHOTO_RESULT;
    }
    resetRouteNavigateCanGoback(
      routerName as any,
      undefined,
      isForcedSupply ? RouterConfig.HOME_TABS : undefined,
    );
    return true;
  }

  if (
    UserInfoManager.context.userModel.isNeedFace &&
    UserInfoManager.context.userModel.isUserTypeOld
  ) {
    // 老户需要人脸识别
    resetRouteNavigateCanGoback(
      RouterConfig.LIVE_RECOGNITION as any,
      undefined,
      isForcedSupply ? RouterConfig.HOME_TABS : undefined,
    );
    return true;
  }

  // 选择额度页面
  if (
    UserInfoManager.context.userModel.isNeedDirectPayment &&
    UserInfoManager.context.userModel.isUserTypeNew
  ) {
    if (BaseInfoManager.context.baseModel.isMultiPeriodSwitch) {
      resetRouteNavigateCanGoback(
        RouterConfig.FIRST_LOAN_PRODUCT_SELECT as any,
        undefined,
        isForcedSupply ? RouterConfig.HOME_TABS : undefined,
      );
    } else if (BaseInfoManager.context.baseModel.firstSelectAmountSlideSwitch) {
      resetRouteNavigateCanGoback(
        RouterConfig.SELECT_AMOUNT_SLIDER as any,
        undefined,
        isForcedSupply ? RouterConfig.HOME_TABS : undefined,
      );
    } else {
      resetRouteNavigateCanGoback(
        RouterConfig.SELECT_AMOUNT as any,
        undefined,
        isForcedSupply ? RouterConfig.HOME_TABS : undefined,
      );
    }
    return true;
  }

  if (UserInfoManager.context.userModel.isNeedBindCard) {
    // 正常review 补件场景
    // 绑定clabe卡片
    if (UserInfoManager.context.userModel.isLoanFailNeedSupply) {
      resetRouteNavigate(RouterConfig.CLABE_BASIC_INFO as any);
    } else {
      resetRouteNavigateCanGoback(
        RouterConfig.CLABE_BASIC_INFO as any,
        undefined,
        isForcedSupply ? RouterConfig.HOME_TABS : undefined,
      );
    }
    return true;
  }
  return false;
}

export async function toLoanOrRepayStateActivity(): Promise<boolean> {
  if (!UserInfoManager.context.userModel.isLogined) {
    return false;
  }

  if (UserInfoManager.context.userModel.inEntryPermissionLocking) {
    resetRouteNavigate(RouterConfig.REJECT as any);
    // 打开拒绝页
    return true;
  }

  if (UserInfoManager.context.userModel.isNeedQa) {
    resetRouteNavigate(RouterConfig.RELOAN_QUESTION as any);
    return true;
  }

  if (UserInfoManager.context.userModel.isRepayNow) {
    if (UserInfoManager.context.userModel.isHasMultiOrder) {
      resetRouteNavigate(RouterConfig.MULTI_PERIOD_REPAYMENT as any);
    } else {
      resetRouteNavigate(RouterConfig.REPAYMENT as any);
    }

    // 打开还款页面
    return true;
  }

  if (UserInfoManager.context.userModel.isLoaningNow) {
    resetRouteNavigate(RouterConfig.WAIT_PAYMENT as any);
    // 打开放款中页面
    return true;
  }

  if (UserInfoManager.context.userModel.isUseCreditFaceExpired) {
    resetRouteNavigate(RouterConfig.FACE_EXPIRED as any);
    // 打开拒绝页面
    return true;
  }

  if (
    UserInfoManager.context.userModel.isCreditWait ||
    UserInfoManager.context.userModel.isCreditSuccessWaitFace
  ) {
    // 跳转到用户的授信（接受贷款）页面
    if (UserInfoManager.context.userModel.isHasMultiOrder) {
      resetRouteNavigate(RouterConfig.MULTI_PERIOD_COMFIRM_LOAN as any);
    } else {
      resetRouteNavigate(RouterConfig.COMFIRM_LOAN as any);
    }
    return true;
  }

  if (UserInfoManager.context.userModel.isReviewingNow) {
    resetRouteNavigate(RouterConfig.WAIT_CHECK as any);
    // 打开审核中页面
    return true;
  }

  if (
    UserInfoManager.context.userModel.isUserTypeOld &&
    UserInfoManager.context.userModel.isNeedOtpVerify &&
    !UserInfoManager.context.userModel.isHasActiveOrder
  ) {
    resetRouteNavigate(RouterConfig.RE_LOAN_OTP as any);
    // 打开otp验证页面
    return true;
  }

  return false;
}

export async function nextToTopRouter(curPageName: string = '') {
  if (curPageName === '') {
    curPageName = navigationRef.getCurrentRoute()?.name || '';
  }

  if (UserInfoManager.context.userModel.isNoApplyIdNewUser) {
    // 新户没有申请id
    noHasApplyIdNewUserApplyRouteStateFlow(curPageName);
    return true;
  }

  // 常用页面跳转行为
  const CommonPageHandlers = {
    // 跳转登录页
    loginCheck: async () => {
      const res = await toLoginOrPermissionActivity();
      console.log('nextToTopRouter loginCheck', curPageName, res);
      if (res) return true;
    },
    // 强制补件场景是需要resetRouter
    dataVerify: async () => {
      const res = await toDataVerifyStateActivity();
      console.log('nextToTopRouter dataVerify', curPageName, res);
      if (res) return true;
    },
    // 根据用户状态，跳转贷款或还款相关页面
    loanOrRepay: async () => {
      const res = await toLoanOrRepayStateActivity();
      console.log('nextToTopRouter loanOrRepay', curPageName, res);
      if (res) return true;
    },
    // 跳转首页
    defalut2Home: () => {
      console.log(
        'nextToTopRouter defalut2Home',
        curPageName,
        BaseInfoManager.context.baseModel.isMultiPeriodSwitch,
        UserInfoManager.context.userModel.isUserTypeOld,
      );
      if (
        BaseInfoManager.context.baseModel.isMultiPeriodSwitch &&
        UserInfoManager.context.userModel.isUserTypeOld
      ) {
        return resetRouteNavigate(RouterConfig.RELOAN_PRODUCT_SELECT as any);
      } else {
        return resetRouteNavigate(RouterConfig.HOME_SCREEN as any);
      }
    },
  };
  /**
   * 不同页面的跳转行为处理队列：部分页面会在 loginCheck 之后有单独的特殊拦截逻辑 handlerAfterLoginCheck 承接
   * 部分页面有 dataVerify 流程，在handlerAfterLoginCheck或者loginCheck流程后
   * RE_LOAN_OTP页面没有loanOrRepay拦截处理，其他页面都有这三个流程
   */
  let handlerQueue: Function[] = [
    CommonPageHandlers.loginCheck,
    CommonPageHandlers.loanOrRepay,
    CommonPageHandlers.defalut2Home,
  ];

  // 在 loginCheckHandler 后增加自定义的handler
  let handlerAfterLoginCheck = null;

  // 包含dataVerify的页面路由
  const dataVerifyRouters = [
    RouterConfig.HOME_SCREEN,
    RouterConfig.SUPPLY_OTP,
    RouterConfig.BASIC_INFO,
    RouterConfig.PROCESS_ACCOUNT_BINDING,
    RouterConfig.OCR_INFO,
    RouterConfig.FIRST_LOAN_PRODUCT_SELECT,
    RouterConfig.SELECT_AMOUNT,
    RouterConfig.SELECT_AMOUNT_SLIDER,
    RouterConfig.MULTI_PERIOD_FIRST_LOAN_PAGE,
    RouterConfig.TAKE_PHOTO,
    RouterConfig.TAKE_PHOTO_RESULT,
    RouterConfig.CLABE_BASIC_INFO,
    RouterConfig.PROCESS_QUESTIONNAIRE,
  ];

  // RE_LOAN_OTP 删除loanOrRepay流程
  if (curPageName === RouterConfig.RE_LOAN_OTP) {
    handlerQueue.splice(1, 1);
  }

  if (dataVerifyRouters.includes(curPageName as RouterConfig)) {
    handlerQueue.splice(1, 0, CommonPageHandlers.dataVerify);
  }

  // 页面处理策略
  switch (curPageName) {
    case RouterConfig.HOME_TAB:
    case RouterConfig.SPLASH:
    case RouterConfig.SPLASH_ACTIVITY:
    case RouterConfig.PERMISSION_AGREE:
    case RouterConfig.ENTER_PWD:
    case RouterConfig.SET_PWD:
      // 如果当前页面是审核账户，则跳转首页
      handlerAfterLoginCheck = async () => {
        if (UserInfoManager.context.userModel.isSupplyUnCompleted) {
          if (await toDataVerifyStateActivity(true)) {
            return true;
          }
        }
      };
      break;
    case RouterConfig.WAIT_CHECK:
      handlerAfterLoginCheck = async () => {
        if (UserInfoManager.context.userModel.isSupplyUnCompleted) {
          if (await toDataVerifyStateActivity(true)) {
            return true;
          }
        }
        if (UserInfoManager.context.userModel.isReviewingNow) {
          return true;
        }
      };
      break;
    case RouterConfig.SUPPLY_OTP:
      handlerAfterLoginCheck = () => {
        if (UserInfoManager.context.userModel.isNeedOtp) {
          return true;
        }
      };
      break;
    case RouterConfig.BASIC_INFO:
      handlerAfterLoginCheck = () => {
        if (UserInfoManager.context.userModel.isNeedBasic) {
          return true;
        }

        if (UserInfoManager.context.userModel.isNeedWork) {
          return true;
        }

        if (UserInfoManager.context.userModel.isNeedContact) {
          return true;
        }
      };
      break;
    case RouterConfig.PROCESS_ACCOUNT_BINDING:
      break;
    case RouterConfig.OCR_INFO:
      handlerAfterLoginCheck = () => {
        if (UserInfoManager.context.userModel.isNeedCurp) {
          return true;
        }
      };
      break;
    case RouterConfig.FIRST_LOAN_PRODUCT_SELECT:
    case RouterConfig.SELECT_AMOUNT:
    case RouterConfig.SELECT_AMOUNT_SLIDER:
    case RouterConfig.MULTI_PERIOD_FIRST_LOAN_PAGE:
      handlerAfterLoginCheck = () => {
        if (
          UserInfoManager.context.userModel.isNeedSelectAmountStatus &&
          UserInfoManager.context.userModel.isUserTypeNew &&
          !UserInfoManager.context.userModel.isNeedSupply &&
          BaseInfoManager.context.baseModel.isSkipConfirmAcceptCreditSwitch
        ) {
          return true;
        }
      };
      break;
    case RouterConfig.TAKE_PHOTO:
    case RouterConfig.TAKE_PHOTO_RESULT:
      handlerAfterLoginCheck = () => {
        if (UserInfoManager.context.userModel.isNeedSelfie) {
          return true;
        }
      };
      break;
    case RouterConfig.CLABE_BASIC_INFO:
      handlerAfterLoginCheck = () => {
        if (UserInfoManager.context.userModel.isNeedBindCard) {
          return true;
        }
      };
      break;
    case RouterConfig.PROCESS_QUESTIONNAIRE:
      handlerAfterLoginCheck = () => {
        if (UserInfoManager.context.userModel.isNeedRKQuestion) {
          return true;
        }
      };
      break;
    case RouterConfig.RE_LOAN_OTP:
      handlerAfterLoginCheck = () => {
        if (
          UserInfoManager.context.userModel.isUserTypeOld &&
          UserInfoManager.context.userModel.isNeedOtpVerify &&
          !UserInfoManager.context.userModel.isHasActiveOrder
        ) {
          return true;
        }
      };
      break;
    case RouterConfig.REJECT:
      handlerAfterLoginCheck = () => {
        if (UserInfoManager.context.userModel.inEntryPermissionLocking) {
          return true;
        }
      };
      break;
    case RouterConfig.FACE_EXPIRED:
      handlerAfterLoginCheck = () => {
        if (
          UserInfoManager.context.userModel.isNeedFace ||
          UserInfoManager.context.userModel.isCreditSuccessWaitFace
        ) {
          return true;
        }
      };
      break;
    case RouterConfig.WAIT_PAYMENT:
      handlerAfterLoginCheck = async () => {
        if (UserInfoManager.context.userModel.isSupplyUnCompleted) {
          if (await toDataVerifyStateActivity(true)) {
            return true;
          }
        }

        if (UserInfoManager.context.userModel.isLoaningNow) {
          return true;
        }
      };
      break;
    case RouterConfig.REPAYMENT:
    case RouterConfig.MULTI_PERIOD_REPAYMENT:
      handlerAfterLoginCheck = () => {
        if (UserInfoManager.context.userModel.isRepayNow) {
          return true;
        }
      };
      break;
  }

  // 在 loginCheckHandler 后增加自定义的handler
  if (handlerAfterLoginCheck) {
    handlerQueue.splice(1, 0, handlerAfterLoginCheck);
  }

  console.log('nextTopRouter handlerQueue', handlerQueue, 'curPageName', curPageName);

  // 已执行 handlerQueue 的跳转
  let isHandlerQueueExec = false;

  for (const handler of handlerQueue) {
    // 如果handler返回true则中断执行后续handler
    if (await handler()) {
      isHandlerQueueExec = true;
      break;
    }
  }

  if (isHandlerQueueExec) {
    return;
  }

  await CommonPageHandlers.loginCheck();
  await CommonPageHandlers.loanOrRepay();
  await CommonPageHandlers.defalut2Home();
}

/** 替换当前路由 */
export function replace<T extends string>(name: T, params?: any) {
  if (navigationRef.isReady()) {
    navigationRef.dispatch(StackActions.replace(name, params));
  }
}

export default {
  setParams,
  navigate,
  replace,
  navigationGoBack,
  nextToTopRouter,
  resetRouteNavigate,
  resetRouteNavigateCanGoback,
};
