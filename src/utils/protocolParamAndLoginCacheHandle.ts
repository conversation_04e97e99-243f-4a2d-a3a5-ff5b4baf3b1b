import { UserInfoManager } from '@/managers';
import { BaseConfig } from '@/baseConfig';
import analytics from '@react-native-firebase/analytics';
/** 获取协议参数 */
export const getProtocolParam = async () => {
  return {
    token: UserInfoManager.context.userModel.token,
    userId: UserInfoManager.context.userModel.userId,
    advertId: BaseConfig.gaid,
    androidId: BaseConfig.androidId,
    appId: BaseConfig.appId,
    appVersion: String(BaseConfig.appVersionCode),
    clientType: 'ANDROID',
    deviceId: BaseConfig.androidId,
    isLimitAdTrackingEnabled: BaseConfig.isLimitAdTrackingEnabled ? '1' : '0',
    applicationId: BaseConfig.applicationId,
    product: BaseConfig.appId,
    fcmToken: BaseConfig.fcmToken,
    applyOrderId:
      UserInfoManager.context.userModel.preApplyOrderId ||
      UserInfoManager.context.userModel.applyOrderId,
    firebaseAppInstanceId: await analytics().getAppInstanceId(),
    // 活体通过 mock 数据
    // "mockFaceIdCode": "1000"
  };
};

export const getProtocolHeader = async () => {
  const headers = new Headers();
  headers.append('token', UserInfoManager.context.userModel.token);
  headers.append('userId', UserInfoManager.context.userModel.userId);
  headers.append('advertId', BaseConfig.gaid);
  headers.append('androidId', BaseConfig.androidId);
  headers.append('appId', BaseConfig.appId);
  headers.append('appVersion', String(BaseConfig.appVersionCode));
  headers.append('clientType', 'ANDROID');
  headers.append('deviceId', BaseConfig.androidId);
  headers.append('applicationId', BaseConfig.applicationId);
  headers.append('isLimitAdTrackingEnabled', BaseConfig.isLimitAdTrackingEnabled ? '1' : '0');
  headers.append('product', BaseConfig.appId);
  headers.append('fcmToken', BaseConfig.fcmToken);
  headers.append(
    'applyOrderId',
    UserInfoManager.context.userModel.preApplyOrderId ||
      UserInfoManager.context.userModel.applyOrderId,
  );
  headers.append('firebaseAppInstanceId', (await analytics().getAppInstanceId()) || '');
  // 活体通过 mock 数据
  // headers.append('mockFaceIdCode', '1000')
  return headers;
};

/** 清理登录缓存状态 */
export const clearLoginCache = () => {
  UserInfoManager.clearAll();
};
