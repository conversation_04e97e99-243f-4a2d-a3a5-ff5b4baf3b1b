import { AppState, AppStateStatus } from 'react-native';
import { BehaviorSubject, Subject } from 'rxjs';
import { Lazy } from './lazy';

export enum ApplicationState {
  active = 'active',
  inactive = 'inactive',
  background = 'background',
}

let appStateCache: AppStateStatus;

export const applicationState = new Lazy(() => {
  const subject = new Subject<ApplicationState | null>();

  AppState.addEventListener('change', nextAppState => {
    if (appStateCache?.match?.(/inactive|background/) && nextAppState === 'active') {
      subject.next(ApplicationState.active);
    }
    if (appStateCache === 'active' && nextAppState.match(/inactive|background/)) {
      subject.next(ApplicationState.inactive);
    }

    appStateCache = nextAppState;
  });

  return {
    subject,
  };
});
