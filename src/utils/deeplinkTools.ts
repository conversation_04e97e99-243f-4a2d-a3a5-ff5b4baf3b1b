import { UserInfoManager } from '@/managers';
import { BaseConfig } from '@/baseConfig';

enum EAction {
  /** 打开营销活动 */
  OPEN_MARKET = 'openMarket',
  /** 跳转页面 */
  JUMP = 'jump',
}

enum EPage {
  /** 邀新活动页 */
  INVITE = 'invite',
}

/** 是否需要深度链接后进行操作 */
export const hasDeepLinkAction = (data: string | undefined) => {
  if (!data) {
    return false;
  }

  if (decodeURIComponent(data).includes(BaseConfig.deeplinkScheme)) {
    return true;
  }

  return false;
};

/** 是否需要深度链接后进行操作 */
export const deepLinkAction = async (data: string | undefined) => {
  if (!data) {
    return false;
  }

  const params = getParamsFromUrl(decodeURIComponent(data));
  if (!params) {
    return false;
  }

  const { action, page } = params;

  switch (action) {
    case EAction.OPEN_MARKET:
      switch (page) {
        case EPage.INVITE:
          if (!UserInfoManager.context.userModel.isLogined) {
            return false;
          }

          return await UserInfoManager.getInviteDataAndNavigateLand({});
        // 扩展其它营销活动页
      }
      return false;
    case EAction.JUMP:
      switch (
        page
        // 跳转其它页面
      ) {
      }
      return false;
  }
  return false;
};

const getParamsFromUrl = (url: string): { action: string; page: string } | null => {
  const pattern = /(?:\?|&)([^&=]+)=([^&]*)/g;
  const params: { [key: string]: string } = {};
  let match;

  while ((match = pattern.exec(url))) {
    params[match[1]] = match[2];
  }

  if (params['action'] && (params['market'] || params['page'])) {
    return {
      action: params['action'],
      page: params['market'] || params['page'],
    };
  } else {
    return null;
  }
};
