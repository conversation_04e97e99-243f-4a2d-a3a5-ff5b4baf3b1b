import { useEffect, useState } from 'react';
import { BehaviorSubject } from 'rxjs';
import KVManager from '../managers/kv/index';
interface DeveloperModeState {
  open: boolean;
}

const defaultState: DeveloperModeState = { open: __DEV__ };

interface DeveloperModeAction {
  turnOn: () => void;
  turnOff: () => void;
}

interface Context {
  state: DeveloperModeState;
  action: DeveloperModeAction;
}

const storageKeyForState = 'developerMode:state';

class DeveloperMode {
  context: Context;
  messageCenter: BehaviorSubject<Context>;
  constructor() {
    const state =
      { ...defaultState, ...KVManager.action.getMap(storageKeyForState) } || defaultState;
    this.context = {
      state,
      action: {
        turnOff: this.turnOff,
        turnOn: this.turnOn,
      },
    };
    this.messageCenter = new BehaviorSubject<Context>(this.context);
  }

  turnOn = () => {
    this.context.state.open = true;

    this.boardcastChange();
  };

  turnOff = () => {
    this.context.state.open = false;
    this.boardcastChange();
  };

  boardcastChange = () => {
    KVManager.action.setMap(storageKeyForState, this.context.state);
    this.messageCenter.next({ ...this.context });
  };

  get isOpen() {
    return this.context.state.open;
  }

  get isClose() {
    return !this.context.state.open;
  }
}

export const developerMode = new DeveloperMode();

export const useDeveloperMode = () => {
  const [context, setContext] = useState<Context>(developerMode.context);
  useEffect(() => {
    const subject = developerMode.messageCenter.subscribe(state => {
      setContext(state);
    });
    return () => {
      subject.unsubscribe && subject.unsubscribe();
    };
  }, []);
  return context;
};
