import { Platform } from 'react-native';
import RNFS from 'react-native-fs';
// import RNShare from 'react-native-share';
import { logForNamespace } from '../logCenter/logConfig';
import logDBOperation from '../logCenter/logDBOperation';
import { unescapeSlashes } from '../logCenter/logDetail';
import { EExportFileType, ISqlLogInfo, II18nTextInfo } from '../logCenter/type';
export * from './fileUtils';

const log = logForNamespace('loggerManager/logFile');
// import {
//   getBrand,
//   getDeviceId,
//   getBuildNumber,
//   getBundleId,
//   getTags,
//   getVersion,
// } from 'react-native-device-info';
import dayjs from 'dayjs';
import { createFile, getPlatformPathDir } from './fileUtils';
/**
 * 将日志数据生成CSV文件导出
 */
export const parseLogDataListToCsv = (logDataList: ISqlLogInfo[]) => {
  let str = '';
  let index = 0;
  //1、 先生成标题
  str += '"order","id","time","timeString","level","namespace","content","additionalData"\r\n';
  logDataList.forEach((logDataListItem: ISqlLogInfo) => {
    const { additionalData, id, time, level, timeString, namespace, content } = logDataListItem;

    let timeDescription = '';
    if (time) {
      if (time - Date.now() < 3600 * 1000) {
        timeDescription = dayjs(time).fromNow() + ' | ' + new Date(time).toLocaleTimeString();
      } else {
        timeDescription = new Date(time).toLocaleTimeString();
      }
    }

    let additionalDataString = additionalData;

    try {
      additionalDataString = JSON.stringify(
        JSON.parse(unescapeSlashes(additionalData)),
        null,
        '  ',
      ).replace(/"+/gim, "'");
    } catch (_) {}
    index++;
    str += `"${index}","${id}","${timeDescription}","${time} | ${timeString}","${level}","${namespace}","${content}","${additionalDataString}"\r\n`;
  });
  return str;
};

/**
 * 将文本数据生成CSV文件导出
 */
export const parseI18nTextDataListToCsv = (i18nTextDataList: II18nTextInfo[]) => {
  let str = '';
  let index = 0;
  //1、 先生成标题
  str += '"index", "country", "moduleKey", "i18nKey", "content"\r\n';
  i18nTextDataList.forEach((i18nTextDataListItem: II18nTextInfo) => {
    const { country, moduleKey, i18nKey, content } = i18nTextDataListItem;
    index++;
    str += `"${index}","${country}","${moduleKey}","${i18nKey}","${content}"\r\n`;
  });
  return str;
};

/**
 * 将日志数据生成CSV文件导出
 */
export const parseLogDataListToText = (logDataList: ISqlLogInfo[]) => {
  let str = '';
  str += 'order  id  time  timeString  level  namespace  content  additionalData\r\n';
  let index = 0;
  //1、 先生成标题
  logDataList.forEach((logDataListItem: ISqlLogInfo) => {
    const { additionalData, id, time, timeString, namespace, content } = logDataListItem;

    let timeDescription = '';
    if (time) {
      if (time - Date.now() < 3600 * 1000) {
        // @ts-ignore
        timeDescription = dayjs(time).fromNow() + ' | ' + new Date(time).toLocaleTimeString();
      } else {
        timeDescription = new Date(time).toLocaleTimeString();
      }
    }

    let additionalDataString = additionalData;

    try {
      additionalDataString = JSON.stringify(
        JSON.parse(unescapeSlashes(additionalData)),
        null,
        '  ',
      );
    } catch (_) {}
    index++;
    str += `${index}   ${id}   ${timeDescription}   ${time} | ${timeString}   ${namespace}   ${content}   ${additionalDataString}r\n`;
  });
  return str;
};

const LOCAL_FOLDER_PATH = RNFS.DocumentDirectoryPath;

export const parseI18nToCsvToShare = async (i18nTextData: II18nTextInfo[]) => {
  const contentString = parseI18nTextDataListToCsv(i18nTextData);
  const filePath = await createFile(contentString, EExportFileType.csv);
  if (filePath) {
    await shareFile(filePath);
  } else {
    log.error('log file not share');
  }
};

export const parseLogToSqliteToShare = async (logData: ISqlLogInfo[]) => {
  // const contentString = parseLogDataListToCsv(logData);
  // const filePath = await createFile(contentString, EExportFileType.csv);
  // if (filePath) {
  //   await shareFile(filePath);
  // } else {
  //   log.error('log file not share');
  // }

  await shareFile(getPlatformPathDir(RNFS.DocumentDirectoryPath + '/' + 'eventCenter.sqlite'));
};

export const parseLogToTextToShare = async (logData: ISqlLogInfo[]) => {
  const contentString = parseLogDataListToText(logData);
  const filePath = await createFile(contentString, EExportFileType.text);
  if (filePath) {
    await shareFile(filePath);
  } else {
    log.error('log file not share');
  }
};

export const shareFile = async (file_path: string) => {
  try {
    // await RNShare.open({
    //   title: 'logFile',
    //   url: file_path,
    // });
    // log.info(`log file share result`, {
    //   result: result,
    // });
  } catch (error: any) {
    // log.error(`log file share fail`, {
    //   error: error,
    // });
  }
};

export const findAllLogRecordAndParseLogDataListToCsvToUpload = async () => {
  const result = await logDBOperation.query({
    type: 'data',
    desc: true,
  });
  // @ts-ignore
  await parseLogToCsvToUpload(result.rows._array);
};

export const findAllLogRecordAndParseLogDataListToTextToUpload = async () => {
  const result = await logDBOperation.query({
    type: 'data',
    desc: true,
  });
  // @ts-ignore
  await parseLogToTextToUpload(result.rows._array);
};
