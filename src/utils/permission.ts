import { PermissionsAndroid } from 'react-native';
import { BaseConfig } from '../../src/baseConfig';
import { isPushNotificationAvailable } from '../native/module/push';

/** 启动权限 */
export const LaunchPermissionList = [
  /** 短信权限 */
  PermissionsAndroid.PERMISSIONS.READ_SMS,
  /** 初略定位权限 */
  PermissionsAndroid.PERMISSIONS.ACCESS_COARSE_LOCATION,
  /** 读取手机的设备状态 */
  // PermissionsAndroid.PERMISSIONS.READ_PHONE_STATE,
];

/** 全部权限 */
export const AllPermissionList = [
  /** 初略定位权限 */
  PermissionsAndroid.PERMISSIONS.ACCESS_COARSE_LOCATION,
  /** 相机权限 */
  PermissionsAndroid.PERMISSIONS.CAMERA,
  /** 短信权限 */
  PermissionsAndroid.PERMISSIONS.READ_SMS,
  /** 读取手机的设备状态 */
  // PermissionsAndroid.PERMISSIONS.READ_PHONE_STATE,
];

/** 相机权限 */
export const CameraPermissionList = [
  /** 相机权限 */
  PermissionsAndroid.PERMISSIONS.CAMERA,
];

/** 通知权限 */
export const PushNotificationPermissionList = [PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS];

/** 日历权限 */
export const CalendarPermissionList = [
  // PermissionsAndroid.PERMISSIONS.READ_CALENDAR,
  // PermissionsAndroid.PERMISSIONS.WRITE_CALENDAR,
];

/** 权限申请结果 */
export enum EPermissionStatus {
  GRANTED = 'granted',
  DENIED = 'denied',
  NEVER_ASK_AGAIN = 'never_ask_again',
}

export const requestPermission = {
  /** 相机权限 */
  CAMEAR: () => {
    return PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.CAMERA);
  },
  ACCESS_COARSE_LOCATION: () => {
    return PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.ACCESS_COARSE_LOCATION);
  },
  READ_SMS: () => {
    return PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.READ_SMS);
  },
  // READ_PHONE_STATE: () => {
  //   return PermissionsAndroid.request(
  //     PermissionsAndroid.PERMISSIONS.READ_PHONE_STATE,
  //   );
  // },
  POST_NOTIFICATIONS: () => {
    if (BaseConfig.androidSdkCode >= 33) {
      return PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS);
    } else {
      return EPermissionStatus.GRANTED;
    }
  },
};

/** 是否包含启动权限 */
export const isHasLaunchPermission = async () => {
  /** 权限确认结果 */
  const result = await Promise.all([
    PermissionsAndroid.check(LaunchPermissionList[0]),
    PermissionsAndroid.check(LaunchPermissionList[1]),
    // PermissionsAndroid.check(LaunchPermissionList[2]),
    // PermissionsAndroid.check(LaunchPermissionList[3]),
  ]);
  /** 如果包含 false 说明其中有权限没有同意 */
  return !result.includes(false);
};

/** 是否包含所有权限 */
export const isHasAllPermission = async () => {
  /** 权限确认结果 */
  const result = await Promise.all([
    PermissionsAndroid.check(AllPermissionList[0]),
    PermissionsAndroid.check(AllPermissionList[1]),
    PermissionsAndroid.check(AllPermissionList[2]),
    // PermissionsAndroid.check(LaunchPermissionList[3]),
  ]);
  const pustNotifyPermission = await isHasPushNotification();
  console.log('result ===', result, pustNotifyPermission);

  /** 如果包含 false 说明其中有权限没有同意 */
  return ![...result, pustNotifyPermission].includes(false);
};

/** 是否包含相机权限 */
export const isHasCameraPermission = async () => {
  /** 权限确认结果 */
  const result = await Promise.all([PermissionsAndroid.check(CameraPermissionList[0])]);
  /** 如果包含 false 说明启动有启动权限没有申请 */
  return !result.includes(false);
};

/** 是否包含通知权限 */
export const isHasPushNotification = async () => {
  /** 权限确认结果 */
  const result = await isPushNotificationAvailable();
  /** 如果包含 false 说明启动有启动权限没有申请 */
  return result;
};

/** 是否拥有日历权限 */
export const isHasCalendarPermission = async () => {
  /** 权限确认结果 */
  const result = await Promise.all([
    PermissionsAndroid.check(CalendarPermissionList[0]),
    PermissionsAndroid.check(CalendarPermissionList[1]),
  ]);
  /** 如果包含 false 说明启动有启动权限没有申请 */
  return !result.includes(false);
};

/** 安装特定的格式返回权限申请结果 */
const getrequestPermissionsResult = (result: any) => {
  const keys = Object.keys(result);
  /** 同意权限列表 */
  const grantedPermissionList = [];
  /** 拒绝权限列表 */
  const deniedPermissionList = [];
  /** 拒绝并且不在询问权限列表 */
  const neverAskPermissionList = [];
  for (let i = 0; i < keys.length; i++) {
    const permission = keys[i];
    const status = result[permission];
    switch (status) {
      case EPermissionStatus.GRANTED:
        grantedPermissionList.push(permission);
        break;
      case EPermissionStatus.DENIED:
        deniedPermissionList.push(permission);
        break;
      case EPermissionStatus.NEVER_ASK_AGAIN:
        neverAskPermissionList.push(permission);
        break;
    }
  }

  return {
    status: grantedPermissionList.length === keys.length,
    grantedPermissionList,
    deniedPermissionList,
    neverAskPermissionList,
  };
};

/**
 * 申请启动权限
 * 如果权限被拒绝，会返回被拒绝的权限列表
 */
export const requestLaunchPermission = async () => {
  let launchPermissionList = LaunchPermissionList;
  if (BaseConfig.androidSdkCode >= 33) {
    launchPermissionList = launchPermissionList.concat(PushNotificationPermissionList);
  }
  const requestResult = await PermissionsAndroid.requestMultiple(
    launchPermissionList.filter(permission => permission),
  );
  return getrequestPermissionsResult(requestResult);
};

/**
 * 申请所有权限
 * 如果权限被拒绝，会返回被拒绝的权限列表
 * */
export const requestAllPermission = async () => {
  let allPermissionList = AllPermissionList;
  if (BaseConfig.androidSdkCode >= 33) {
    allPermissionList = [...PushNotificationPermissionList, ...allPermissionList];
  }
  const requestResult = await PermissionsAndroid.requestMultiple(
    allPermissionList.filter(permission => permission),
  );
  return getrequestPermissionsResult(requestResult);
};

/**
 * 申请相机权限
 * 如果权限被拒绝，会返回被拒绝的权限列表
 */
export const requestCameraPermission = async () => {
  const requestResult = await PermissionsAndroid.requestMultiple(CameraPermissionList);
  return getrequestPermissionsResult(requestResult);
};

/**
 * 申请通知权限
 * 如果权限被拒绝，会返回被拒绝的权限列表
 */
export const requestPushNotifitionPermission = async () => {
  if (BaseConfig.androidSdkCode >= 33) {
    const requestResult = await PermissionsAndroid.requestMultiple(PushNotificationPermissionList);
    return getrequestPermissionsResult(requestResult);
  } else {
    return {
      status: false,
      grantedPermissionList: [],
      deniedPermissionList: [],
      neverAskPermissionList: PushNotificationPermissionList,
    };
  }
};

/**
 * 申请日历权限
 * 如果权限被拒绝，会返回被拒绝的权限列表
 */
export const requestCalendarPermission = async () => {
  const requestResult = await PermissionsAndroid.requestMultiple(CalendarPermissionList);
  return getrequestPermissionsResult(requestResult);
  // if (BaseConfig.androidSdkCode >= 33) {
  //   const requestResult = await PermissionsAndroid.requestMultiple(
  //     CalendarPermissionList,
  //   );
  //   return getrequestPermissionsResult(requestResult);
  // } else {
  //   return {
  //     status: true,
  //     grantedPermissionList: CalendarPermissionList,
  //     deniedPermissionList: [],
  //     neverAskPermissionList: [],
  //   };
  // }
};
