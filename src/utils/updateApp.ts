import { BaseEnumsSpace } from '@/enums';
import { ELocalKey } from '@/localStorage';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, ModalList, modalDataStoreInstance } from '@/managers';
import { fetchAppVersion } from '@/server';
import { jumpCurrentAppGooglePlayStore } from '@/utils';
import { BaseConfig } from '../baseConfig';

/** 获取app版本并进行更新 */
export const onGetAppVersionAndUpdate = async (
  onCancelCallback: () => void = () => {},
): Promise<boolean> => {
  const result = await fetchAppVersion();
  /** 异常场景处理 */
  if (result?.code === BaseEnumsSpace.ENetworkStatus.SUCCESS) {
    let res = onUpdateApp(
      Number(result?.data?.gpVersion),
      result?.data?.isFocus,
      result?.data?.content,
      onCancelCallback,
    );

    return res;
  }
  onCancelCallback();
  return false;
};

/** 更新app, 需要更新的话阻塞后续逻辑 */
export const onUpdateApp = (
  gpVersion: number = 0,
  isFocus: string = 'NO',
  content: string = '',
  onCancelCallback: () => void = () => {},
): boolean => {
  if (gpVersion > BaseConfig.appVersionCode) {
    let needFocus = isFocus === 'YES';
    let needUpdate = false;
    let updateVersion = Number(gpVersion);
    let updateVersionCacheKey = ELocalKey.PROMPT_COMFIRM_APP_UPDATE_VERSION;
    // 读取版本更新缓存
    let cacheUpdateVersion: number = KVManager.action.getInt(updateVersionCacheKey) || 0;
    if (updateVersion) {
      // 线上版本大于本地缓存版本相同
      if (updateVersion > BaseConfig.appVersionCode && cacheUpdateVersion != updateVersion) {
        needUpdate = true;
      }

      // 线上版本和本地缓存版本相同,但是强制更新状态为YES
      if (needFocus) {
        needUpdate = true;
      }
    }

    if (needUpdate || needFocus) {
      // 启动弹框
      modalDataStoreInstance.openModal({
        key: ModalList.UPDATE,
        imageKey: '_forceUpdate',
        content,
        confirmBtnName: 'btnString.OK',
        cancelBtnName: 'btnString.close',
        isFocus: needFocus,
        confirmBtnCallback: () => {
          // 写入版本更新缓存
          KVManager.action.setInt(updateVersionCacheKey, updateVersion);
          jumpCurrentAppGooglePlayStore();
        },
        cancelBtnCallback: () => {
          // 写入版本更新缓存
          KVManager.action.setInt(updateVersionCacheKey, updateVersion);
          onCancelCallback();
        },
      });
      return false;
    } else {
      onCancelCallback();
      return true;
    }
  } else {
    onCancelCallback();
    return true;
  }
};
