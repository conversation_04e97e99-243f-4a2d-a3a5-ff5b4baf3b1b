import { replace } from 'lodash';

/**
 * 去除字符串中的所有空格
 */
export const removeAllSpaces = (str: string): string => {
  return replace(str, /\s+/g, '');
};

/**
 * 墨西哥格式的日期字符串转为 Date
 */
export const convertToDate = (mexicoDateString: string): Date => {
  const [day, month, year] = mexicoDateString.split('/');
  return new Date(`${year}-${month}-${day}`);
};

/**
 * 墨西哥格式的日期字符串转为 YYYY-MM-DD
 */
export const convertToDateString = (mexicoDateString: string | Date): string => {
  if (mexicoDateString instanceof Date) {
    mexicoDateString = mexicoDateString.toLocaleDateString('es-MX');
  }
  const [day, month, year] = mexicoDateString.split('/');
  return `${year}-${month}-${day}`;
};

/**
 * 邮箱脱敏
 */
export const maskEmail = (email: string): string => {
  if (!email || !email.includes('@')) return email;
  const [prefix, domain] = email.split('@');
  if (prefix.length <= 2) return email;
  const firstChar = prefix.charAt(0);
  const lastTwoChars = prefix.slice(-2);
  const maskedPrefix = `${firstChar}${Array(prefix.length - 3)
    .fill('*')
    .join('')}${lastTwoChars}`;
  return `${maskedPrefix}@${domain}`;
};

/**
 * CURP 脱敏
 */
export const maskCurp = (curp: string): string => {
  if (!curp) return curp;
  return '**********' + curp.slice(-4);
};

/**
 * 姓名脱敏
 */
export const maskName = (name: string): string => {
  if (!name) return name;
  if (name.length <= 2) return name;
  const firstChar = name.charAt(0);
  const lastTwoChars = name.slice(-2);
  const maskedName = `${firstChar}${Array(name.length - 3)
    .fill('*')
    .join('')}${lastTwoChars}`;
  return maskedName;
};
