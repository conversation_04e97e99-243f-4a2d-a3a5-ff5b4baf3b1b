import { useThemeManager } from '@/managers';
import { useCallback, useMemo } from 'react';
import { StyleProp } from 'react-native';

export type BasicStyleType<T> = {
  padding?: string;
  margin?: string;
  width?: number | string;
  height?: number | string;
  style?: StyleProp<T>;
};

export const generateStyle = <T>(props: Partial<BasicStyleType<T>>) => {
  const {
    padding = '0 0 0 0',
    margin = '0 0 0 0',
    width = 'auto',
    height = 'auto',
    style = {},
  } = props;
  const marginArr = margin?.split(' ') || [0, 0, 0, 0];
  const paddingArr = padding?.split(' ') || [0, 0, 0, 0];
  /** 对于以数组形式传递的参数 需要进行转换处理，不然无法正确传递 */
  // @ts-ignore
  const decStyle =
    Object.prototype.toString.call(style) === '[object Array]'
      ? // @ts-ignore
        Object.assign({}, ...style)
      : style;
  return {
    paddingTop: Number(paddingArr[0]),
    paddingRight: Number(paddingArr[1]),
    paddingBottom: Number(paddingArr[2]),
    paddingLeft: Number(paddingArr[3]),
    marginTop: Number(marginArr[0]),
    marginRight: Number(marginArr[1]),
    marginBottom: Number(marginArr[2]),
    marginLeft: Number(marginArr[3]),
    width,
    height,
    ...decStyle,
  };
};

type StyleObject = {
  [key: string]: any;
};

/** @description 对于特定的包含颜色的单个style做特殊处理 直接返回处理后的 style 对象 */
export const useProcessColorStyles = (...args: Array<StyleObject | StyleObject[]>) => {
  const applicationTheme = useThemeManager().value.applicationTheme;
  const translateColor = useCallback(
    (value: any) => {
      const cusColor = applicationTheme[String(value)];
      return cusColor || value;
    },
    [applicationTheme],
  );
  return useMemo(() => {
    const flattened = args.flat();
    const cusStyles: StyleObject = flattened.reduce((acc, obj) => Object.assign(acc, obj), {});
    const processedStyles: StyleObject = {};
    for (const [key, value] of Object.entries(cusStyles)) {
      if (key.toLowerCase().includes('color')) {
        processedStyles[key] = translateColor(value);
      } else {
        processedStyles[key] = value;
      }
    }
    return processedStyles;
  }, [applicationTheme, args, translateColor]);
};

/** @description  对于这个由 StyleSheet.create() 生成的这样的数据格式做特殊处理 */
export const useCustomStyleSheet = (themeStyles: any) => {
  const applicationTheme = useThemeManager().value.applicationTheme;
  const translateColor = useCallback(
    (value: any) => {
      const cusColor = applicationTheme[String(value)];
      return cusColor || value;
    },
    [applicationTheme],
  );
  const styles = useMemo(() => {
    return traverseData(themeStyles, translateColor);
  }, [applicationTheme, translateColor, themeStyles]);
  return styles;
};

function traverseData(
  data: Record<string, StyleObject>,
  callback: (value: any) => any,
): Record<string, StyleObject> {
  const result: Record<string, StyleObject> = {};
  for (const key in data) {
    if (Object.prototype.hasOwnProperty.call(data, key)) {
      const value = data[key];
      if (Object.prototype.toString.call(value) === '[object Object]') {
        result[key] = traverseData(value as Record<string, StyleObject>, callback);
      } else {
        if (key.toLowerCase().includes('color')) {
          result[key] = callback(value);
        } else {
          result[key] = value;
        }
      }
    }
  }
  return result;
}
