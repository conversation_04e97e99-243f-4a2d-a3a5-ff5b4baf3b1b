/** 16进制色值混合 */
export function blendColors(color1: string, color2: string) {
  // 解析颜色值并获取 RGBA 分量
  const rgba1 = parseColor(color1);
  const rgba2 = parseColor(color2);

  // 颜色混合
  const blendedColor = {
    r: Math.round((rgba1.r * (255 - rgba2.a) + rgba2.r * rgba2.a) / 255),
    g: Math.round((rgba1.g * (255 - rgba2.a) + rgba2.g * rgba2.a) / 255),
    b: Math.round((rgba1.b * (255 - rgba2.a) + rgba2.b * rgba2.a) / 255),
    a: rgba1.a, // 使用第一个颜色的透明度
  };

  // 转换为十六进制颜色值
  const blendedHex = rgbToHex(blendedColor.r, blendedColor.g, blendedColor.b);

  return blendedHex;
}

function parseColor(color: string) {
  const hex = color.replace('#', '');
  const r = parseInt(hex.substr(0, 2), 16);
  const g = parseInt(hex.substr(2, 2), 16);
  const b = parseInt(hex.substr(4, 2), 16);
  const a = parseInt(hex.substr(6, 2), 16);

  return { r, g, b, a };
}

function rgbToHex(r: number, g: number, b: number) {
  const componentToHex = (c: number) => {
    const hex = c.toString(16);
    return hex.length === 1 ? '0' + hex : hex;
  };

  const hex = '#' + componentToHex(r) + componentToHex(g) + componentToHex(b);
  return hex;
}
