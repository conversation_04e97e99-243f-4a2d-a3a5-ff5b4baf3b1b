import _ from 'lodash';

export const checkTextLength = (text: string, length: number) => {
  if (isString(text)) {
    return text.length === length;
  } else {
    return false;
  }
};

export const isString = (string: string) => {
  return _.isString(string);
};

export const isEqualLength = (string: string, length: number): boolean => {
  if (!string) return false;
  return string?.length === length;
};

export const isMoreThanLength = (string: string, length: number): boolean => {
  if (!string) return false;
  return string?.length >= length;
};

export const isEqual = (string: string, otherString: string): boolean => {
  if (!string) return false;
  return string === otherString;
};

export const isStringExist = (string: string) => {
  if (!string) return false;
  return string.length > 0;
};

/** 数字输入框校验格式 */
export const isNumberInput = (str: string) => {
  return /^(?!0\d)\d+(\.\d{1,2})?$/.test(str);
};

/** 数字输入框校验格式 */
export const isInviteCodeInput = (str: string) => {
  return /^[a-zA-Z0-9]{0,6}$/.test(str);
};

export const isInviteCodeExist = (string: string) => {
  return string.length === 0 || string.length === 6;
};

/** 姓名校验，不能包含数字，符号，表情 */
export const isNameValid = (name: string) => {
  return /^[a-zA-Z\u00C0-\u017F\s]+$/.test(name);
};

/** 手机号格式校验 */
export const verifyPhoneNumber = (p: string = ''): boolean => {
  let verifyResult = false;
  let formattedPhoneNumber = p.replace(/\s/g, '');
  if (formattedPhoneNumber.length > 11) {
    if (formattedPhoneNumber.startsWith('+52')) {
      formattedPhoneNumber = formattedPhoneNumber.replace('+52', '');
    } else if (formattedPhoneNumber.startsWith('52')) {
      formattedPhoneNumber = formattedPhoneNumber.replace('52', '');
    }
    verifyResult = /^(1[1-9]\d{9}|[1-9]\d{9})$/.test(formattedPhoneNumber);
  } else if (formattedPhoneNumber.length === 11) {
    verifyResult = /^1[1-9]\d{9}$/.test(formattedPhoneNumber);
  } else if (formattedPhoneNumber.length === 10) {
    verifyResult = /^[1-9]\d{9}$/.test(formattedPhoneNumber);
  } else {
    verifyResult = false;
  }
  return verifyResult;
};

/** clabe号18位校验 */
export function verifyClabe(clabe: string): boolean {
  if (clabe === null || clabe.length !== 18) {
    return false;
  }
  const bigAccount: string = String(clabe).substring(0, 17);
  /** 三方校验规则 */
  const reference: string = '*****************';
  const account: string[] = Array.from(bigAccount);
  const ponderation: string[] = Array.from(reference);
  /** step1: 将帐户的每个数字乘以相应的加权系数 */
  const step1: number[] = account.map((value, index) => {
    const a: number = parseInt(value);
    const b: number = parseInt(ponderation[index]);
    return a * b;
  });
  /** step2: 对步骤1中获得的每个结果取模10 */
  const step2: number[] = step1.map(value => value % 10);
  /** step3: 将步骤2结果进行累加 */
  const step3: number = step2.reduce((accumulator, value) => accumulator + value, 0);
  /** step4: 将步骤3结果进行模10 */
  const step4: number = step3 % 10;
  /** step5: 将10减去步骤4结果 */
  const step5: number = 10 - step4;
  /** step6: 将步骤5结果进行模10 */
  const step6: number = step5 % 10;
  return bigAccount + step6 === String(clabe);
}

/** 邮箱格式校验 */
export const verifyEmail = (email: string = ''): boolean => {
  const regex = /^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$/;
  return regex.test(email);
};

/** 年龄校验 */
export const verifyAge = (age: string = ''): boolean => {
  const ageNumber = Number(age);
  return Number.isInteger(ageNumber) && ageNumber > 0 && ageNumber <= 99;
};

/** 校验数据是否不为null和undefined */
export const itNotNull = <T>(data: T): Promise<T> | Promise<never> => {
  if (!_.isNil(data)) {
    return new Promise<T>(resolve => {
      // Handle the case where data is not null
      // For example, you might want to resolve the promise with the data
      resolve(data);
    });
  } else {
    // Handle the case where data is null
    // You might want to reject the promise or handle it differently
    return Promise.reject(new Error('Data is null'));
  }
};
/** curp 校验 */
/**
 * 校验墨西哥身份证号码 CURP 是否合法。
 * 注意传的身份证先去掉空格。
 * 目前校验的项目有：
 * 1、长度必须 18 位
 * 2、姓氏校验（具体校验不做了，要求前四位必须为字母）
 * 3、生日校验（5-10 共 6 位，格式为 YYMMDD），必须是合法的日期。
 * 4、性别校验（第 11 位只能是 H 或 M）
 * 5、出生州校验（第 12、13 位必须是合法的州缩写）
 * 6、名字校验（具体校验不做了，要求 14-16 共 3 位必须为字母）
 * 7、防重序号校验（第 17 位，2000 年以前出生的必须是 0-9 的数字，2000 年及以后的必须是 A-Z 的字母）
 * 8、最后一位校验位校验。根据前面 17 位计算得出。
 *
 * @param curp 墨西哥身份证号码 CURP
 * @returns 是否合法
 */
export const validateCurp = (curp: string): boolean => {
  curp = curp.toUpperCase();

  const pattern =
    /^[A-Z]{4}\d{2}((0[1-9])|(1[0-2]))((0[1-9])|([1-2][0-9])|(3[0-1]))[H|M][A-Z]{5}[A-Z0-9][0-9]$/;

  if (!pattern.test(curp)) {
    return false;
  }

  const birthdayYear = curp.substring(4, 6); // 生日-年
  const birthday = curp.substring(6, 10); // 生日-月日
  const state = curp.substring(11, 13); // 州
  const index = curp.substring(16, 17); // 防重序号
  const dv = curp.substring(17, 18); // 校验位

  // 校验位不合法
  if (dv !== ultdig(curp.substring(0, 17))) {
    console.log('校验位不合法', dv, ultdig(curp.substring(0, 17)));
    return false;
  }

  // 出生州不合法
  if (!states.includes(state)) {
    console.log('出生州不合法', state);
    return false;
  }

  const now = new Date();
  const currentYear = now.getFullYear();
  let y = parseInt(birthdayYear, 10);

  let patternIndex: RegExp;
  if (y <= currentYear - 2000) {
    y += 2000;
    patternIndex = /^[A-Z]$/;
  } else {
    y += 1900;
    patternIndex = /^[0-9]$/;
  }

  // 防重序号不合法
  if (!patternIndex.test(index)) {
    console.log('防重序号不合法', index);
    return false;
  }

  // 出生年月不合法
  const fullBirthday = `${y}${birthday}`;
  try {
    const date = new Date(
      `${fullBirthday.substring(0, 4)}-${fullBirthday.substring(4, 6)}-${fullBirthday.substring(
        6,
        8,
      )}`,
    );
    if (isNaN(date.getTime())) {
      console.log('出生年月不合法', fullBirthday);
      return false;
    }
  } catch (e) {
    console.error('Failed to validate birthday:', e);
    return false;
  }

  return true;
};
/**
 * 计算字符对应的值
 *
 * @param i 字符
 * @returns 对应的值
 */
function tabla(i: string): number {
  const code = i.charCodeAt(0);
  if (code >= 48 && code <= 57) return code - 48; // 0-9
  if (code >= 65 && code <= 78) return code - 55; // A-N
  if (code >= 79 && code <= 90) return code - 54; // O-Z
  return 0;
}
/**
 * 校验位的计算
 *
 * @param curp_17 CURP 前 17 位
 * @returns 校验位
 */
function ultdig(curp_17: string): string {
  let dv = 0;
  for (let i = 0; i < curp_17.length; i++) {
    const c = tabla(curp_17.charAt(i));
    dv += c * (18 - i);
  }
  dv %= 10;
  return dv === 0 ? '0' : (10 - dv).toString();
}
// 合法的州缩写列表
const states = [
  'AS',
  'BC',
  'BS',
  'CC',
  'CS',
  'CH',
  'CL',
  'CM',
  'DF',
  'DG',
  'GT',
  'GR',
  'HG',
  'JC',
  'MC',
  'MN',
  'MS',
  'NT',
  'NL',
  'OC',
  'PL',
  'QT',
  'QR',
  'SP',
  'SL',
  'SR',
  'TC',
  'TL',
  'TS',
  'VZ',
  'YN',
  'ZS',
];
