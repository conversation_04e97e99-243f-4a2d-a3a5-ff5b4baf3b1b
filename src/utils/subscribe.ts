import { Subject, filter, map, pairwise, defaultIfEmpty, startWith, Observable } from 'rxjs';
import _ from 'lodash';

export const generateSubForSpecificChange = <T, K>(config: {
  subject: Subject<T>;
  filter: (data: T) => K;
  /**
   * 订阅源默认值，针对 config.subject 类型不是 BehaviorSubject 的场景
   */
  defaultData?: K;
}) => {
  const defaultValue: T = (config.subject as any).value
    ? (config.subject as any).value
    : config.defaultData
    ? // @ts-ignore
      config.filter(config.defaultData)
    : undefined;
  const defaultFilteredData = defaultValue !== undefined ? config.filter(defaultValue) : undefined;

  const sub = config.subject.pipe(
    startWith(undefined),
    map((data: T | undefined) => {
      if (data === undefined) {
        return undefined;
      }
      return config.filter(data);
    }),
    pairwise(),
    filter(([prev, next]) => {
      return !_.isEqual(prev, next);
    }),
    defaultIfEmpty([defaultFilteredData, defaultFilteredData]),
  ) as Observable<[K | undefined, K]>;
  return sub;
};

export default {
  generateSubForSpecificChange,
};
