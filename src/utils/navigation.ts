import { createNavigationContainerRef } from '@react-navigation/native';
import { NavigationState, Route } from '@react-navigation/routers';
import { cloneDeep, merge } from 'lodash';
import { BehaviorSubject } from 'rxjs';
import { Lazy } from './lazy';
import log from './log';
import Singleton from './singleton';

const routeLog = log.generateInstanceForNamespace('route');

type Page = Route<string, { [key: string]: any } | undefined> | undefined;

export type RootStackParamList = {
  dev: unknown;
  _page_: unknown;
};

let currentPageCache: Page;

export const navigation = new Lazy(() => {
  const subjectForState = new BehaviorSubject<NavigationState | undefined>(undefined);

  const subjectForPage = new BehaviorSubject<Page>(undefined);

  return {
    subjectForPage,
    subjectForState,
    get currentPageCache() {
      return currentPageCache;
    },
  };
});

export const navigationRef = Singleton.getOrRegister(
  'navigationRef',
  createNavigationContainerRef(),
);

/**
 * 更新 navigationStateSubject 页面路由变化订阅
 * 此函数将自动填充默认页面的参数
 * @param _state
 * @returns
 */
export function updateNavigationState(_state: NavigationState | undefined) {
  // log.debug(`# updateNavigationState`, _state)
  if (!_state) {
    return;
  }
  const state = cloneDeep(_state);
  state.routes[0] = merge({}, state.routes[0], {
    params: {},
  });
  navigation.instance.subjectForState.next(state);
}

export function updateNavigationPage(_page: Page, isRootPage: boolean) {
  // log.debug(`# updateNavigationPage isRootPage=${isRootPage}`, _page);
  if (!_page) {
    return;
  }
  const page = isRootPage ? merge(cloneDeep(_page), { params: {} }) : cloneDeep(_page);
  // console.log('# updateNavigationPage', page);
  currentPageCache = cloneDeep(page);
  navigation.instance.subjectForPage.next(page);
  // state.routes[0] = _.merge({}, state.routes[0], {
  //   params: {},
  // });
  // navigationPageSubject.next(state.routes[0].name);
}
