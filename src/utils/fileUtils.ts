import { Platform } from 'react-native';
import RNFS from 'react-native-fs';
import { EExportFileType } from '../logCenter/type';
import { LOCAL_FILE_CACHE_FOLDER_PATH } from '@/config';
import { logForNamespace } from '../logCenter';

const log = logForNamespace('loggerManager/logFile');

export const LOCAL_FOLDER_PATH = RNFS.DocumentDirectoryPath;

export function getPlatformPathDir(absolutePath: string | null, diffPlatform: boolean = true) {
  if (!diffPlatform) {
    return `${absolutePath}`;
  }
  switch (Platform.OS) {
    case 'ios':
      return `${absolutePath}`;
    case 'android':
    default:
      return `file://${absolutePath}`;
  }
}

export const existsFileFunc = async (file_path: string) => await RNFS.exists(file_path);

function getAbsoultePathInDocumentDir(relativePath: string | null, diffPlatform: boolean = true) {
  if (!diffPlatform) {
    return `${LOCAL_FOLDER_PATH}${relativePath}`;
  }
  switch (Platform.OS) {
    case 'ios':
      return `${LOCAL_FOLDER_PATH}${relativePath}`;
    case 'android':
    default:
      return `file://${LOCAL_FOLDER_PATH}${relativePath}`;
  }
}

const createFolderByRelativePath = async (_folderPath: string) => {
  const folderPath = getAbsoultePathInDocumentDir(_folderPath);
  try {
    const existsFile = await existsFileFunc(folderPath);
    if (!existsFile) {
      await RNFS.mkdir(folderPath);
    }
  } catch (error) {
    console.error(error);
  }
};

// 将csv字符串保存为 csv 文件
export const createFile = async (
  contentString: string,
  fileType: EExportFileType = EExportFileType.text,
) => {
  // 创建存储日志文件的文件夹
  await createFolderByRelativePath(LOCAL_FILE_CACHE_FOLDER_PATH);
  const file_path =
    getAbsoultePathInDocumentDir(LOCAL_FILE_CACHE_FOLDER_PATH) +
    Date.now() +
    '_log' +
    '.' +
    fileType;
  let writeResult = false;
  await RNFS.writeFile(file_path, contentString, 'utf8')
    .then(() => {
      writeResult = true;
    })
    .catch(error => {
      __DEV__ && console.log('write error ====', error);
      writeResult = false;
    });
  if (writeResult) {
    log.info(`log file create successful filePath=${file_path}`);
    return file_path;
  } else {
    log.error(`log file create fail filePath=${file_path}`);
    return '';
  }
};

const getCachesDirectoryPath = (path: string) => {
  const CACHE_PATH = RNFS.CachesDirectoryPath;
  switch (Platform.OS) {
    case 'ios':
      return `${CACHE_PATH}${path}`;
    case 'android':
    default:
      return `file://${CACHE_PATH}${path}`;
  }
};
const createCachesDirectoryPath = async (_folderPath: string) => {
  const folderPath = getCachesDirectoryPath(_folderPath);
  try {
    const existsFile = await existsFileFunc(folderPath);
    if (!existsFile) {
      await RNFS.mkdir(folderPath);
    }
  } catch (error) {
    console.error(error);
  }
};
export const downloadFile = async (params: {
  fileUrl: string;
  catalogue: string;
  fileName: string;
  callback?: (localFilePath: string) => void;
}) => {
  const { fileUrl, catalogue, fileName, callback } = params;
  if (!fileUrl) {
    return
  }
  const suffixArr = fileUrl.split('.');
  const ext = suffixArr[suffixArr.length - 1].split('?')[0].toLowerCase();
  await createCachesDirectoryPath(catalogue);
  const cacheLocalFilePath = getCachesDirectoryPath(catalogue) + `/${fileName}.${ext}`;
  const localFilePath = getAbsoultePathInDocumentDir(catalogue) + `/${fileName}.${ext}`;
  try {
    const fileExists = await RNFS.exists(localFilePath);
    if (fileExists) {
      return callback?.(localFilePath);
    }
  } catch (error) {
    console.error(`fileExists error: ${error}`);
  }
  callback?.('');
  const options = {
    fromUrl: fileUrl,
    toFile: cacheLocalFilePath,
    background: true,
    cache: false,
    begin: (response: any) => {
      // @ts-ignore
      const totalSize = response.contentLength / (1024 * 1024).toFixed(2);
      console.log(`totalSize: ${totalSize} MB`);
    },
    progress: (response: any) => {
      const progress = (response.bytesWritten / response.contentLength) * 100;
      console.log(`progress: ${progress.toFixed(2)}%`);
    },
    progressDivider: 10,
  };
  try {
    const result = await RNFS.downloadFile(options).promise;
    if (result.statusCode === 200) {
      console.log('download success');
      try {
        await createFolderByRelativePath(catalogue);
        // 移动文件
        await RNFS.moveFile(cacheLocalFilePath, localFilePath);
        console.log(`file moved success: ${localFilePath}`);
      } catch (error) {
        console.log(`file moved error:`, error);
      }
      return;
    }
    console.log(`download fail：HTTP status ${result.statusCode}, URL: ${fileUrl}`);
  } catch (err) {
    console.log(`download file ${fileUrl} to ${cacheLocalFilePath} error:`, err);
  }
};

/**
 * 清理 DocumentDirectoryPath 目录下的文件
 * @param {number} catalogue - 目录
 */
export const deleteFile = async (catalogue: string) => {
  const directoryPath = RNFS.DocumentDirectoryPath + catalogue;
  if (!catalogue) {
    return;
  }
  const items = await RNFS.readdir(directoryPath);

  for (const item of items) {
    const itemPath = `${directoryPath}/${item}`;
    try {
      await RNFS.unlink(itemPath);
      console.log(`delete success: ${itemPath}`);
    } catch (error) {
      console.log(`delete error:`, error);
    }
  }
};
