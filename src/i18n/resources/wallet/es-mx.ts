export default {
  title: 'Saldo de Mi Billetera',
  detail: 'Detalles',
  withdraw: 'Retirar',
  channel_fee: 'Tarifa de canal',
  recive_total: 'Total recibido',
  reward_amount: 'Recompensa',
  invite_number: 'Invitados',
  withdraw_title: 'Retirar',
  default_bank_card_title: 'Confirma tu cuenta bancaria',
  default_bank_name: 'Nombre del banco',
  default_bank_clabe: 'Número de CLABE',
  default_bank_desc: 'Contacta al servicio al cliente para modificar la cuenta bancaria receptora.',
  default_bank_error:
    'Debes solicitar un préstamo primero y completar la información de tu tarjeta bancaria.',
  withdraw_confirm: 'Confirmar',
  confirm_modal_tips: 'Se cobrará una tarifa de canal de {{ amount }} \n por cada transacción.',
  confirm_modal_ok: 'Continuar',
  confirm_modal_cancel: 'Cancelar',
  success_modal_tips: 'Su solicitud ha sido enviada. La \n estamos procesando.',
  warn_modal_tips:
    'Tienes un préstamo pendiente \n actualmente. Realiza el pago \n antes de hacer un retiro.',
  warn_modal_ok: 'Volver',
  balance_display: 'Saldo de Cuenta {{ amount }}',
  low_min_display: 'El monto mínimo por retiro es de {{ amount }}',
  not_multiple:
    'El monto solicitado para el retiro debe ser un múltiplo de {{ amount }}, como por ejemplo {{ amount }}, {{ doubleAmount}}, {{ thirdAmount}}, etc.',
  not_enough: 'Sin saldo suficiente',
  flowTitle: 'Detalles',
  flowDetail: 'Detalles de consumo',
  noData: 'No hay  movimientos',
  award_balance: 'Saldo Cashback VIP',
  invite_balance: 'Saldo de Invitaciones',
  cumulative_summary: 'Resumen Acumulado',
  card_invite_award: 'Invita y Gana',
  card_invite_number: 'Invitados',
  card_cashback_award: 'Cashback VIP',
  card_cashback_number: 'Pagos válidos',
  withdraw_unable_tips:
    'Al acumular {{ amount }} o más, podrás transferir lo a tu cuenta bancaria. ',
  couponTitle: 'Saldo canjeable',
};
