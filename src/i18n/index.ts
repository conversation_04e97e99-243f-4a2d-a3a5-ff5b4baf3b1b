/**
 * Multi-language support
 *
 * @module i18n
 */
import { default as _i18n } from 'i18next';
// import _i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import { getLocales } from 'react-native-localize';
import { ITranslationModule } from './typs';

// hooks
export { useNameSpace } from './useNameSpace';

// locals
import btn from './resources/button';
import feeback from './resources/feeback';
import guide from './resources/guide';
import hotLine from './resources/hotLine';
import loanConfirm from './resources/loanConfrim';
import loanContract from './resources/loanContract';
import modal from './resources/modal';
import tab from './resources/tab';
import waitCheck from './resources/waitCheck';

/** @description 还款页面 */
import repayment from './resources/repayment';
/** @description 基础信息 */
import basicInfo from './resources/basicInfo';
/** @description 动态问券 */
import rkQustion from './resources/rkQustion';
/** @description 直接放款 */
import directPayment from './resources/directPayment';
/** @description clabe */
import clabe from './resources/clabe';
/** @description 问题 */
import faq from './resources/faq';
/** @description 主页 */
import home from './resources/home';
/** @description 活体 */
import liveRecognition from './resources/liveRecognition';
/** @description 登录 */
import login from './resources/login';
/** @description 我的 */
import my from './resources/my';
/** @description 身份证识别 */
import ocr from './resources/ocr';
/** @description 短信验证 */
import otp from './resources/otp';
/** @description 权限确认页 */
import permissionAgree from './resources/permissionAgree';
/** @description 权限拒绝页 */
import permissionRefuse from './resources/permissionRefuse';
/** @description 隐私协议 */
import privacy from './resources/privacy';
/** 设置密码 */
import pwd from './resources/pwd';
/** @description 设置 */
import setting from './resources/setting';
/** @description 说明 */
import supply from './resources/supply';
/** @description 拍照 */
import takePhoto from './resources/takePhoto';
/** @description 等待风控 */
import waitRisk from './resources/waitRisk';
/** @description 谷歌内审 */
import adPermission from './resources/adPermission';
/** @description 表单 */
import form from './resources/form';
/** @description 通用信息 */
import message from './resources/message';
/** @description 增信 */
import increaseCredit from './resources/increaseCredit';
/** @description 第三方账户绑定 */
import accountBinding from './resources/accountBinding';
/** @description 复贷问题 */
import reloanQuestion from './resources/reloanQuestion';
/** @description 通知消息 */
import messageCenter from './resources/messageCenter';
/** @description 贷款订单列表 */
import loanRecord from './resources/loanRecord';
/** 优惠券列表 */
import couponList from './resources/couponList';
/** @description 优惠券 */
import coupon from './resources/coupon';
/** @description 用信拒绝问卷 */
import creditRefuseQuestion from './resources/creditRefuseQuestion';
/** @description 用信拒绝确认 */
import creditRefuseConfirm from './resources/creditRefuseConfirm';
/** @description 钱包 */
import wallet from './resources/wallet';
/** @description vip */
import vip from './resources/vip';
/** @description 溢缴款 */
import excessPayment from './resources/excessPayment';
/** @description 自动代扣 */
import autoWithhold from './resources/autoWithhold';
/** 展期 */
import ext from './resources/ext';
/** 产品选择页 */
import productSelect from './resources/productSelect';
/** 分期 */
import multiPeriod from './resources/multiPeriod';
import validateBasicInfo from './resources/validateBasicInfo';
import verification from './resources/verification';
import welcome from './resources/welcome';
import redeemCoupons from './resources/redeemCoupons';

const allLanguages = [{ key: 'es-MX', value: 'esMX' }];
// 所有语言资源文件
const allResources = {
  btn,
  feeback,
  guide,
  hotLine,
  loanConfirm,
  loanContract,
  modal,
  tab,
  waitCheck,
  repayment,
  basicInfo,
  rkQustion,
  directPayment,
  clabe,
  faq,
  home,
  liveRecognition,
  login,
  my,
  ocr,
  otp,
  permissionAgree,
  permissionRefuse,
  privacy,
  pwd,
  setting,
  supply,
  takePhoto,
  waitRisk,
  adPermission,
  form,
  message,
  increaseCredit,
  accountBinding,
  reloanQuestion,
  messageCenter,
  loanRecord,
  couponList,
  coupon,
  creditRefuseQuestion,
  creditRefuseConfirm,
  wallet,
  vip,
  excessPayment,
  autoWithhold,
  ext,
  productSelect,
  multiPeriod,
  validateBasicInfo,
  verification,
  welcome,
  redeemCoupons,
};

type ResourceKeys = keyof typeof allResources;
type StringsObjType = {
  [K in ResourceKeys as `${K & string}String`]: {
    [SubKey in keyof (typeof allResources)[K]['esMX']]: `${K & string}String.${SubKey & string}`;
  };
};
// @ts-ignore
let StringsObj: StringsObjType = {};
const defaultLanguage = 'esMX';
Object.keys(allResources).forEach(key => {
  // @ts-ignore
  Object.keys(allResources[key][defaultLanguage]).forEach(resourceKey => {
    // @ts-ignore
    if (!StringsObj[`${key}String`]) {
      // @ts-ignore
      StringsObj[`${key}String`] = {} as any;
    }
    // @ts-ignore
    StringsObj[`${key}String`][resourceKey] = `${key}String.${resourceKey}`;
  });
});

let resources: { [key: string]: { translation: ITranslationModule } } = {};
allLanguages.forEach(language => {
  let translationObj: ITranslationModule = {};
  Object.keys(allResources).forEach(key => {
    // @ts-ignore
    translationObj[`${key}String`] = allResources[key][language.value];
  });
  resources[language.key] = {
    translation: translationObj,
  };
});

/**
 * Register business language module in sub business packages.
 * Please call this function before `setup`
 *
 * @param moduleName
 * @param translationModule
 */
export function registerTranslationModule(
  moduleName: string,
  translationModule: ITranslationModule,
) {
  for (const language of Object.keys(translationModule)) {
    resources[language] = {
      translation: {
        ...resources[language].translation,
        [moduleName]: translationModule[language],
      },
    };
  }
}

/**
 * Please call this function in App.tsx
 */
export function setup() {
  const language = getLocales()[0].languageCode;
  __DEV__ && console.log('#language', language);
  _i18n.use(initReactI18next).init({
    lng: language,
    fallbackLng: 'es-MX',
    resources,
    compatibilityJSON: 'v3',
    debug: false,
  });

  // AppState.addEventListener('focus', () => {
  //   _i18n.changeLanguage(getLocales()[0].languageCode);
  // });
}

export function changeLanguage(languageCode: string) {
  _i18n.changeLanguage(languageCode);
}

export const i18n = _i18n;
export const instance = _i18n;

export const i18nResources = resources;
export const Strings: StringsObjType = StringsObj;

export default {
  instance: _i18n,
  setup,
  changeLanguage,
  registerTranslationModule,
  Strings,
};
