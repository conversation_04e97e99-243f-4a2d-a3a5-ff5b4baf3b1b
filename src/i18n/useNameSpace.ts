import { useTranslation } from 'react-i18next';

export const useNameSpace = (namespace: string = '', options?: { [key: string]: any }) => {
  const { t, i18n } = useTranslation(undefined, options);
  const translateFunction = (key: string = '', params?: Record<string, any>) => {
    let keyString = key;
    if (!key.includes('.') && namespace) {
      keyString = `${namespace}.${key}`;
    }

    if (!i18n.exists(keyString)) {
      return '';
    }
    return t(keyString, params);
  };

  return {
    t: translateFunction,
    i18n,
  };
};

export default useNameSpace;
