// 证件枚举值
export namespace EvidenceEnumsSpace {
  export enum ECard {
    CREDIT_CARD = 0, // tarjeta de crédito 信用卡
    CLABE_CARD = 1, // CLABE 储蓄卡
  }

  // 拍照枚举类型
  export enum ETakePicture {
    FRONT = 'front', // 前面
    BACK = 'back', // 背面
    FACE = 'face', // 人脸
  }

  // 地址级别类型
  export type AddressType = {
    city: string; // 市
    state: string; // 州
    municipality: string; // 区
    suburb: string; // 街道
  };

  // 租房状态（住宅相关属性）
  export enum EResidence {
    isRent = 1, // Arrendamiento 是否租房
    isLiveWithParents = 2, // Vive con tus padres 是否与父母同住
    isMortgage = 3, // Casa hipotecaria 是否抵押房屋
    employerProvides = 3, // El empleador proporciona 雇主提供
  }

  // 贷款用途
  export enum ELoanPurpose {
    support = 0, // sustento 支持（援助/赡养）
    consumerPurchases = 1, // compras del consumidor 作为消费者购买
    education = 2, // Educación 教育
    vacationTravel = 3, // Viajes de vacaciones 假期旅行
    getMarry = 4, // Casarse 结婚
    doctor = 5, // Médico 医疗
    decor = 6, // Decoración 装修
    religiousBelief = 7, // Creencia religiosa 宗教信仰
    fertility = 8, // Fertilidad 生育
    multifunctionalUse = 9, // Uso multifuncional 多功能用途
    multifunctionUse = 10, // Multifunction use 多功能用途
    others = 11, // Others 其他
  }

  // 收入来源
  export enum ESourceIncome {
    own = 0, // Propio 自己
    parents = 1, // Padres 父母
    couple = 2, // Pareja 夫妻
    relative = 3, // Pariente 亲戚
  }

  /**
   * 收入范围
   *
   * MXN 墨西哥比索
   * 1 USD = 17.6 MXN(2023.5.29)
   * 1 RMB = 2.49 MXN(2023.5.29)
   */
  export enum ESalary {
    ZRRO_TO_THREE_THOUSAND = 0, // 0-3,000 MXN
    THREE_THOUSAND_TO_SIX_THOUSAND = 1, // 3,000-6,000 MXN
    SIX_THOUSAND_TO_NINE_THOUSAND = 2, // 6,000-9,000 MXN
    NINE_THOUSAND_TO_TWELVE_THOUSAND = 3, // 9,000-12,000 MXN
    TWELVE_THOUSAND_TO_FIFTHTEEN_THOUSAND = 4, // 12,000-15,000 MXN
    FIFTHTEEN__THOUSAND_TO_MORE = 5, // 15,000+MXN
  }

  // 性别
  export enum EGender {
    MALE, // Masculino
    FEMALE, // Femenino
  }

  // 婚姻状态
  export enum EMarital {
    single = 0, // Soltero
    married = 1, // Casado
    divorced = 2, // Divorciado 离婚
    widower = 3, // Viudo 丧偶
  }

  // 子女状态
  export enum EChildrenNumber {
    ZERO = 0, // 0 hijo
    ONE = 1,
    TWO = 2,
    THREE = 3,
    THREE_MORE = 4, // 3+ hijo
  }

  // 教育程度
  export enum EEduLevel {
    noStudies = 0, // Sin estudios 无
    primaryEducation = 1, // Educación primaria 小学教育
    secondaryEducation = 2, // Educación secundaria 中学教育
    technicalProfessionalBaccalaureate = 3, // Bachillerato Profesional Técnico 职业文凭或技术学院
    universityTechnologicalInstitute = 4, // Universidad Instituto Tecnológico 大学或技术学院
    specializationMasterDoctorate = 5, // Especialización Maestría Doctorado 研究生或博士学位
  }

  // 联系人关系
  export enum EContactRelation {
    famililar = 0, // Familiares 亲属
    friends = 1, // Amigos 朋友
    colleagues = 2, // 同事
    others = 3, // 其他
  }

  export enum EBankAccount {
    CREDIT_CARD = 0, // tarjeta de crédito 信用卡
    CLABE_CARD = 1, // CLABE 储蓄卡
  }
}
