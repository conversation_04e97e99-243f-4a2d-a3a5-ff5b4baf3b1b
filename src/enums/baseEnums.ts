export namespace BaseEnumsSpace {
  export enum EMode {
    Dark = 'dark',
    Light = 'light',
  }

  export enum ELocal {
    ES_MX = 'es-MX',
    EN = 'en',
  }

  /**
   * @enum
   * TAB app主页
   * PAGE 页面栈结构
   */
  export enum EFlowCode {
    TAB = '10',
    PAGE = '20',
  }

  export enum ENetworkStatus {
    NETWORK_ERROR = -1,
    SUCCESS = 0,
    SERVER_ERROR = 500,
    TIMEOUT = 504,
  }

  /** banner位置枚举 */
  export enum EBannerLocationType {
    /** 卡包页面 */
    WALLET = '1',
    /** 我的页面 */
    MINE = '2',
    // /** 等待打款页 */
    // WAIT_PAYMENT = '3',
    /** 等待审核页 */
    WAIT_CHECK = '4',
    /** 复贷申请页 */
    RELOAN_HOME = '5',
  }

  /** banner位置枚举 */
  export enum EQuestionLocationType {
    FIRST_LOAN = '0',
    /** 复贷申请页 */
    RELOAN = '1',
  }

  /** banner活动类型 */
  export enum EBannerActivityType {
    /** 打开facebook账号 */
    OPEN_FACEBOOK = 'OPEN_FACEBOOK',
    /** 打开谷歌绑定页面 */
    BING_GOOGLE = 'GP_BIND',
    /** 打开谷歌绑定页面埋点 */
    BING_GOOGLE_TRACK = 'BING_GOOGLE',
    /** 邀请新户 */
    INVITE_NEW_USER = 'INVITE_NEW_USER',
    /** 导流 */
    DIVERT = 'DIVERT',
    /** 内部打开的活动页 */
    INTERNAL_H5 = 'INTERNAL_H5',
    /** 外部打开的H5 页面 */
    EXTERNAL_H5 = 'EXTERNAL_H5',
  }

  /** 复贷问卷枚举 */
  export enum EReloanQuestion {
    /** 您当前在贷现金贷的总笔数是 */
    LOAN_COUNT = 4,
    /** 您当前在贷现金贷的总金额有 */
    LOAN_AMOUNT = 5,
    /** 您当前在贷现金贷的最长期限为 */
    LOAN_LONGEST_LIMIT = 6,
    /** 您当前使用了以下哪些信贷产品 */
    USE_LOAN_PRODUCTS = 7,
    /** 您每月信用卡消费金额为 */
    CREDIT_CARD_CONSUMPOTION = 8,
    /** 当前您的消费分期贷款中，最长期限是？ */
    INSTALLMENT_PRODUCT_LONGEST_LIMIT = 9,
  }

  // 启动页资源类型
  export enum LaunchScreenSourceType {
    IMAGE = 'image',
    VIDEO = 'video',
    GIF = 'gif',
  }

  // ocr 前后卡片
  export enum CardType {
    frontCard = 'frontCard',
    backCard = 'backCard',
  }
}
