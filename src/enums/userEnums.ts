// 用户枚举值
export namespace UserEnumsSpace {
  /** 维护页类型 */
  export enum EMaintenanceType {
    /** OCR */
    OCR = 'OCR',
    /** FACE */
    FACE = 'FACE',
  }

  /** 状态类型 */
  export enum EStatusType {
    /** 是 */
    YES = 'YES',

    /** 否 */
    NO = 'NO',
  }

  /** 用户类型 */
  export enum EUserType {
    /** 新客 */
    NEW = 'NEW',

    /** 老客 */
    OLD = 'OLD',
  }

  /** 借款配置类型 */
  export enum ELoanConfigStatus {
    /** 配置不可用 */
    DISABLE,

    /** 配置能用 */
    ENABLE,
  }

  /** 还款渠道 */
  export enum ERepayChannel {
    /** 线下 */
    LIQUIDO_PAYCASH = 'LIQUIDO_PAYCASH',

    /** 线下  */
    OPENPAY_STORE = 'OPENPAY_STORE',

    /** 线上 */
    LIQUIDO_SPEI = 'LIQUIDO_SPEI',

    /** 线上 */
    STP_CLABE = 'STP_CLABE',
  }

  /** 申请状态 */
  export enum EApply {
    /**
     * 创建
     */
    CREATED = 'CREATED',

    /**
     * 打回
     */
    BACKTO = 'BACKTO',

    /**
     * 审核中
     */
    SYS_CHECK = 'SYS_CHECK',

    /**
     * 拒绝
     */
    SYS_DENIED = 'SYS_DENIED',

    /**
     * 机审通过
     */
    SYS_APROVAL = 'SYS_APROVAL',

    /**
     * 人审通过
     */
    MANU_APROVAL = 'MANU_APROVAL',

    /**
     * 人审拒绝
     */
    MANU_DENIED = 'MANU_DENIED',

    /**
     * 电审拒绝
     */
    DS_DENIED = 'DS_DENIED',

    /**
     * 电审通过
     */
    DS_APROVAL = 'DS_APROVAL',

    /**
     * 订单通过
     */
    SUCCEEDED = 'SUCCEEDED',

    /**
     * 订单取消
     */
    CANCEL = 'CANCEL',
  }

  /** 用户确认授信（接受贷款）状态 */
  export enum ECredit {
    /**
     * 等待
     */
    WAIT = 'WAIT',
    /**
     * 接受
     */
    ACCEPT = 'ACCEPT',
    /**
     * 失效
     */
    EXPIRED = 'EXPIRED',
  }

  /** 订单状态 */
  export enum EOrder {
    /**
     * 未知
     */
    NO = 'NO',

    /**
     * 创建
     */
    CREATED = 'CREATED',

    /**
     * 拒绝
     */
    DENIED = 'DENIED',

    /**
     * 取消
     */
    CANCEL = 'CANCEL',

    /**
     * 放款中
     */
    LENDING = 'LENDING',

    /**
     * 待还款，未逾期
     */
    LOANED = 'LOANED',

    /**
     * 待还款，逾期
     */
    OVERDUE = 'OVERDUE',

    /**
     * 结清
     */
    SETTLE = 'SETTLE',

    /**
     * 逾期结清
     */
    OVERDUE_SETTLE = 'OVERDUE_SETTLE',

    /**
     * 手动结清
     */
    ANNUL_SETTLE = 'ANNUL_SETTLE',

    /**
     * 逾期手动结清
     */
    OVERDUE_ANNUL_SETTLE = 'OVERDUE_ANNUL_SETTLE',

    /**
     * 坏账
     */
    BAD_DEBT = 'BAD_DEBT',
  }

  /** 登录状态 */
  export enum ELoginStatus {
    /**
     * LOGIN
     */
    LOGIN = 'LOGIN',

    /**
     * REGISTER
     */
    REGISTER = 'REGISTER',

    /**
     * SETPWD
     */
    SETPWD = 'SETPWD',
  }

  /** 登录方式 */
  export enum ELoginType {
    /**
     * sms
     */
    SMS = 'sms',

    /**
     * password
     */
    PWD = 'pwd',

    AT_LOGIN_CHECK_MOBILE = 'AT_LOGIN_CHECK_MOBILE',
  }

  /** 评分的场景 */
  export enum SceneType {
    APPLY,
    LOAN,
  }

  /** 补件状态 */
  export enum ESupplyType {
    /**
     * 取消
     */
    CANCEL = 'CANCEL',

    /**
     * 转帐失败
     */
    TRANSFER_FAILED = 'TRANSFER_FAILED',

    /**
     * 确认用信超时
     */
    ACCEPT_EXPIRED = 'ACCEPT_EXPIRED',

    /**
     * 活体超时
     */
    FACE_EXPIRED = 'FACE_EXPIRED',

    /**
     * 活体拒绝
     */
    FACE_DENIED = 'FACE_DENIED',

    /**
     * 风控拒绝
     */
    RISK_DENIED = 'RISK_DENIED',

    /**
     * 电核拒绝
     */
    DS_DENIED = 'DS_DENIED',

    /** clabe补件, 再次尝试放款 */
    RETRY_TRANSFER = 'RETRY_TRANSFER',
  }

  /** 用户确认授信后 需要人脸核验，人脸核验状态 */
  export enum UseCreditStatus {
    /**
     * 等待人脸
     */
    WAIT_FACE = 'WAIT_FACE',
    /**
     * 人脸成功
     */
    FACE_SUCCEEDED = 'FACE_SUCCEEDED',
    /**
     * 人脸拒绝
     */
    FACE_DENIED = 'FACE_DENIED',
    /**
     * 人脸失效
     */
    EXPIRED = 'EXPIRED',
  }

  /** OTP短信类型 */
  export enum ReLoanOptSmsType {
    SMS_MOBILE = 'SMS_MOBILE',
    SMS_VOICE_MOBILE = 'SMS_VOICE_MOBILE',
  }

  /** 跳过活体的场景 */
  export enum ESkipLiveScene {
    /** 调用成功 0 **/
    SUCCESS = '0',
    /** 纯客户端异常 1 **/
    CLIENT_ERROR = '1',
    /** app获取token异常 2 **/
    CLIENT_GET_TOKEN_ERROR = '2',
    /** app获取token正常,服务端获取token异常,服务端调用,前端刷新 3 **/
    SERVER_GET_TOKEN_ERROR = '3',
    /** app调用sdk校验token异常 4 **/
    CLIENT_CALL_FACEID_ERROR = '4',
    /** app调用verify接口异常 5 **/
    CLIENT_VERIFY_ERROR = '5',
    /** app调用verify接口正常，服务端调用token异常,服务端调用,前端刷新 6 **/
    SERVER_VERIFY_ERROR = '6',
  }

  /** 优惠券类型 */
  export enum ECouponsType {
    /** 提额券 */
    INCREASE = 'INCREASE',
    /** 减免券 */
    DEDUCTION = 'DEDUCTION',
    /** 新的提额券 */
    CREDIT = 'CREDIT',
  }

  /** 优惠券状态 */
  export enum ECouponsStatus {
    /** 可用 */
    AVAILABLE = 'AVAILABLE',
    /** 不可用 */
    UNAVAILABLE = 'UNAVAILABLE',
    /** 过期 */
    EXPIRED = 'EXPIRED',
    /** 核销 */
    HX = 'HX',
    /** 作废 */
    VOIDED = 'VOIDED',
    /** 绑定 */
    BINDING = 'BINDING',
  }

  /** 优惠券来源 */
  export enum ECouponsDistributeSceneStatus {
    /** 增信 */
    ENHANCE_CREDIT = 'ENHANCE_CREDIT',
    /** 风控通过 */
    RK_APPROVAL = 'RK_APPROVAL',
    /** 立即还款 */
    REPAY_ON_TIME = 'REPAY_ON_TIME',
    /** 活动中被邀请 */
    BE_INVITED = 'BE_INVITED',
    /** VIP */
    VIP_REDEMPTION = 'VIP_REDEMPTION',
    /** 挽留 */
    VOLUNTARY_REJECT = 'VOLUNTARY_REJECT',
  }
  export enum OtpChannel {
    /** whatsapp */
    WHATS_APP = 'whats_app',
    /** 短信 */
    SMS = 'sms_mobile',
    /** 语音 */
    VOICE_PHONE = 'sms_voice_mobile',
  }
  export enum BizType {
    /** 登录 */
    LOGIN = 'LOGIN',
    /** 修改手机号 */
    CHANGE_MOBILE = 'CHANGE_MOBILE',
    /** 复贷 */
    RELOAN = 'RELOAN',
    /** 首贷 */
    FIRST_LOAN_VERIFY = 'FIRST_LOAN_VERIFY',
  }
}
