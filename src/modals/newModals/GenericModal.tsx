import { BaseModal, Text } from '@/components';
import { memo } from 'react';

interface Props {
  id: string;
  closeModal: (id: string) => void;
}
const GenericModal = (props: Props) => {
  const { id, closeModal } = props;

  return (
    <BaseModal
      key={id}
      children={<Text textContent="12345" />}
      visible={true}
      onClose={() => {
        closeModal(id);
      }}
    />
  );
};

export default memo(GenericModal);
