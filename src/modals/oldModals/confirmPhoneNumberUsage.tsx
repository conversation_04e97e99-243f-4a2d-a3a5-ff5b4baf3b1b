/* eslint-disable react-native/no-inline-styles */
import { CommonModal, Text, View } from '@/components';
import { useSubscribeFilter } from '@/hooks';
import { ModalContextType, ModalList, modalDataStoreInstance } from '@/managers';
import _ from 'lodash';
import React, { useMemo } from 'react';

export const ConfirmPhoneNumberUsage = (): React.ReactElement => {
  const modalInfo = useSubscribeFilter({
    subject: modalDataStoreInstance.messageCenter,
    filter: (subject: ModalContextType) => {
      const maxLength = subject.modalList.length;
      return subject.modalList[maxLength - 1];
    },
  });

  const visible = useMemo(() => {
    return modalInfo?.key === ModalList.COMFIRM_PHONE_NUMBER_USAGE;
  }, [modalInfo]);

  const i18nKey = useMemo(() => {
    return modalInfo?.i18nKey;
  }, [modalInfo]);

  const confirmBtnName = useMemo(() => {
    return modalInfo?.confirmBtnName;
  }, [modalInfo]);

  const cancelBtnName = useMemo(() => {
    return modalInfo?.cancelBtnName;
  }, [modalInfo]);

  const handleBackdrop = _.debounce(() => {
    modalInfo?.isBackdropClose && modalDataStoreInstance.closeModal(modalInfo?.id);
  }, 100);

  const handleConfirmBtn = _.debounce(() => {
    modalInfo?.confirmBtnCallback && modalInfo?.confirmBtnCallback();
    modalDataStoreInstance.closeModal(modalInfo?.id);
  }, 100);

  const handleCloseBtn = _.debounce(() => {
    modalInfo?.cancelBtnCallback && modalInfo?.cancelBtnCallback();
    modalDataStoreInstance.closeModal(modalInfo?.id);
  }, 100);

  const extra = useMemo(() => {
    return modalInfo?.extra;
  }, [modalInfo]);

  return (
    <CommonModal
      visible={visible}
      i18nKey={i18nKey}
      confirmBtnName={confirmBtnName}
      confirmCallback={handleConfirmBtn}
      cancelBtnName={cancelBtnName}
      cancelCallback={handleCloseBtn}
      onBackdropPress={handleBackdrop}>
      <View
        margin="12 12 0 12"
        padding="8 12 8 12"
        style={{
          backgroundColor: 'background-color-100',
        }}>
        <Text
          isCenter={true}
          category="h3"
          style={{
            color: 'text-color-800',
          }}
          textContent={extra?.phoneNumber}
        />
      </View>
    </CommonModal>
  );
};
