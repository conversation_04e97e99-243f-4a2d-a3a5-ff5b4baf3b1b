/* eslint-disable react-native/no-inline-styles */
import { Image, CommonModal, Text, View } from '@/components';
import { useSubscribeFilter } from '@/hooks';
import { CodePushContextType, CodePushManager } from '@/managers';
import React, { useMemo } from 'react';

type TCodePushStateType = {
  process: number;
  visible: boolean;
  countdown: number;
};

export const HotUpdateModal = (): React.ReactElement => {
  const { process, visible, countdown } = useSubscribeFilter({
    subject: CodePushManager.messageCenter,
    filter: (subject: CodePushContextType) => {
      const { process, visible, countdown } = subject.codePushState;
      return { process, visible, countdown };
    },
    defaultData: CodePushManager.context.codePushState,
  }) as TCodePushStateType;

  const $processBar = useMemo(() => {
    return (
      <View
        margin="32 0 0 0"
        width={245}
        height={8}
        style={{
          overflow: 'hidden',
          borderRadius: 5,
          backgroundColor: 'fill-color-500',
        }}>
        <View
          width={245}
          height={8}
          style={{
            borderRadius: 5,
            backgroundColor: 'primary-color-500',
            transform: [{ translateX: -245 * (1 - process) }],
          }}
        />
      </View>
    );
  }, [process]);

  const $processLabel = useMemo(() => {
    if (countdown > 0) {
      return (
        <Text margin="32 0 0 0" category="p1" isCenter={true}>
          <Text i18nKey="modalString.installTipStart" />
          <Text status="danger" textContent={`${countdown}`} />
          <Text i18nKey="modalString.installTipEnd" />
        </Text>
      );
    } else {
      return (
        <Text margin="32 0 0 0" category="p1" isCenter={true}>
          <Text i18nKey="modalString.downloading" />
          <Text textContent={`${(process * 100).toFixed(2)}%`} />
        </Text>
      );
    }
  }, [process, countdown]);

  return (
    <CommonModal hasLinearGradient={false} width={311} visible={visible}>
      <View width={311} margin="0 0 48 0" layoutStrategy="flexColumnStartCenter">
        <Image name="_hotUpdateModal" />
        <View margin="12 12 12 12" layoutStrategy="flexColumnStartCenter">
          <Text category="h3" isCenter={true} i18nKey="modalString.hotUpdateTitle" />
          <Text
            margin="16 0 0 0"
            category="p1"
            isCenter={true}
            i18nKey="modalString.hotUpdateTip"
          />
          {$processBar}
          {$processLabel}
        </View>
      </View>
    </CommonModal>
  );
};
