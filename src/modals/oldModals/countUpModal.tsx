import { CommonModal, Image, Text, View, Button, ImageBackground } from '@/components';
import { useSubscribeFilter } from '@/hooks';
import React, { useMemo, useEffect, useRef, useState } from 'react';
import BackgroundTimer from "react-native-background-timer"
import { Strings } from "@/i18n"
import { ImageNames } from "@/config"
import { Colors } from "@/themes"
import { ModalContextType, ModalList, modalDataStoreInstance } from '@/managers';
import Animated, { useSharedValue, runOnJS, cancelAnimation, useAnimatedStyle, withTiming, Easing } from 'react-native-reanimated';
import { useSetState } from "ahooks"

interface State {
    isTimeoutVisible: boolean;
    isBottomIconCloseVisible?: boolean;
}
export const CountUpModal = (): React.ReactElement => {
    const [countUp, setCountUp] = useState<number>(0)
    const [state, setState] = useSetState<State>({
        isTimeoutVisible: false,
        isBottomIconCloseVisible: false
    })
    const { isTimeoutVisible, isBottomIconCloseVisible } = state;
    const modalInfo = useSubscribeFilter({
        subject: modalDataStoreInstance.messageCenter,
        filter: (subject: ModalContextType) => {
            const maxLength = subject.modalList.length;
            return subject.modalList[maxLength - 1];
        },
    });
    const timerRef = useRef<number>(0);
    const rotate = useSharedValue(0);
    const visible = useMemo(() => {
        return modalInfo?.key === ModalList.COUNT_UP;
    }, [modalInfo]);
    const i18nKey = useMemo(() => {
        return modalInfo?.i18nKey;
    }, [modalInfo]);
    const timeoutI18nKey = useMemo(() => {
        return modalInfo?.titleKey;
    }, [modalInfo]);
    useEffect(() => {
        let cancelled = false;
        const animate = () => {
            if (cancelled) return;
            rotate.value = withTiming(
                rotate.value + 360,
                {
                    duration: 1000,
                    easing: Easing.linear,
                },
                (isFinished) => {
                    if (isFinished && !cancelled) {
                        runOnJS(animate)();
                    }
                }
            );
        };
        if (visible) {
            animate();
        }
        return () => {
            cancelled = true;
            cancelAnimation(rotate);
        };
    }, [visible, modalInfo?.id]);
    useEffect(() => {
        if (visible) {
            clearTimer()
            setCountUp(0);
            timerRef.current = BackgroundTimer.setInterval(() => {
                setCountUp(prev => {
                    if (prev >= 60) {
                        clearTimer();
                        setState({ isTimeoutVisible: true, isBottomIconCloseVisible: false });
                        return 0;
                    }
                    if (prev >= 15) {
                        setState({ isBottomIconCloseVisible: true });
                    }
                    return prev + 1;
                });
            }, 1000);
        } else {
            clearTimer()
        }
        return () => {
            clearTimer()
        };
    }, [visible, modalInfo?.id]);
    const clearTimer = () => {
        BackgroundTimer.clearInterval(timerRef.current);
        timerRef.current = 0
    }
    const handleRetry = () => {
        modalInfo?.confirmBtnCallback?.()
        setState({ isTimeoutVisible: false, isBottomIconCloseVisible: false })
    }
    const handelClose = () => {
        modalDataStoreInstance.closeModal(modalInfo?.id);
        clearTimer();
    }
    const animatedStyle = useAnimatedStyle(() => ({
        transform: [{ rotate: `${rotate.value % 360}deg` }],
    }));
    const renderLoading = () => {
        return (
            <>
                <Text
                    i18nKey={i18nKey}
                    category='h4'
                    isCenter
                />
                <Animated.View style={[animatedStyle, { marginTop: 32 }]}>
                    <Image name={ImageNames._ocrLoadingIcon} />
                </Animated.View>
                <View style={{ width: 64, height: 64, position: "absolute", top: 105 }}
                    layoutStrategy="flexRowCenterCenter"
                >
                    <Text>
                        <Text bold="bold" style={{ fontSize: 20, color: Colors.PRIMARY_COLOR_600 }}>{countUp}</Text>
                        <Text bold="bold" style={{ fontSize: 14, color: Colors.PRIMARY_COLOR_600 }}>s</Text>
                    </Text>
                </View>
            </>
        )
    }
    const renderTimeout = () => {
        return (
            <>
                <Image name='_ocrTimeoutInfoIcon' />
                <Text isCenter category='h4' margin='16 0 0 0' i18nKey={timeoutI18nKey} />
                <Button
                    margin='32 0 0 0'
                    onPress={handleRetry}
                    textI18nKey={Strings.btnString.reSend}
                />
            </>
        )
    }
    return (
        <CommonModal
            visible={visible}
            isBottomIconColse={isBottomIconCloseVisible}
            cancelCallback={handelClose}
        >
            <ImageBackground name='_ocrCountUpModalBg'>
                <View padding="24 32 24 32" layoutStrategy="flexColumnCenterCenter">
                    {isTimeoutVisible ? renderTimeout() : renderLoading()}
                </View>
            </ImageBackground>
        </CommonModal>
    );
};
