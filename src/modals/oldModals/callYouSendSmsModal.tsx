/* eslint-disable react-native/no-inline-styles */
import React, { useMemo } from 'react';
import _ from 'lodash';
import { modalDataStoreInstance, ModalContextType, ModalList } from '@/managers';
import { useSubscribeFilter } from '@/hooks';
import { CommonModal } from '@/components';

export const CallYouSendSmsModal = (): React.ReactElement => {
  const modalInfo = useSubscribeFilter({
    subject: modalDataStoreInstance.messageCenter,
    filter: (subject: ModalContextType) => {
      const maxLength = subject.modalList.length;
      return subject.modalList[maxLength - 1];
    },
  });

  const visible = useMemo(() => {
    return modalInfo?.key === ModalList.CALL_YOU_SEND_SMS;
  }, [modalInfo]);

  const i18nKey = useMemo(() => {
    return modalInfo?.i18nKey;
  }, [modalInfo]);

  const confirmBtnName = useMemo(() => {
    return modalInfo?.confirmBtnName;
  }, [modalInfo]);

  const cancelBtnName = useMemo(() => {
    return modalInfo?.cancelBtnName;
  }, [modalInfo]);

  const handleBackdrop = _.debounce(() => {
    modalInfo?.isBackdropClose && modalDataStoreInstance.closeModal(modalInfo?.id);
  }, 100);

  const handleCloseBtn = _.debounce(() => {
    modalInfo?.cancelBtnCallback && modalInfo?.cancelBtnCallback();
    modalDataStoreInstance.closeModal(modalInfo?.id);
  }, 100);

  const handleConfirmBtn = _.debounce(() => {
    modalInfo?.confirmBtnCallback && modalInfo?.confirmBtnCallback();
    modalDataStoreInstance.closeModal(modalInfo?.id);
  }, 100);

  return (
    <CommonModal
      hasLinearGradient={false}
      visible={visible}
      imageKey="_phoneIcon"
      i18nKey={i18nKey}
      confirmBtnName={confirmBtnName}
      confirmCallback={handleConfirmBtn}
      cancelBtnName={cancelBtnName}
      cancelCallback={handleCloseBtn}
      onBackdropPress={handleBackdrop}
    />
  );
};
