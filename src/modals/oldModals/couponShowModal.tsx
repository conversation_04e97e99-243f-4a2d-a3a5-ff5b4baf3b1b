/* eslint-disable react-native/no-inline-styles */
import {
  CommonModal,
  Text,
  View,
  Image,
  ImageBackground,
  Button,
  LinearGradient,
} from '@/components';
import { useSubscribeFilter } from '@/hooks';
import { ModalContextType, ModalList, modalDataStoreInstance } from '@/managers';
import _ from 'lodash';
import React, { useMemo } from 'react';
import { ScrollView, TouchableOpacity } from 'react-native';
import { useNameSpace, Strings } from '../../i18n';
import { fixPixel, needFixPixel } from '@/utils';
import { UserVOSpace } from '@/types';
import { Colors } from '@/themes';
import CouponItem from '../../pages/components/coupon/CouponItem';

export const CouponShowModal = (): React.ReactElement => {
  const modalInfo = useSubscribeFilter({
    subject: modalDataStoreInstance.messageCenter,
    filter: (subject: ModalContextType) => {
      const maxLength = subject.modalList.length;
      return subject.modalList[maxLength - 1];
    },
  });

  const visible = useMemo(() => {
    return modalInfo?.key === ModalList.COUPON_SHOW;
  }, [modalInfo]);

  const onHandleClick = _.debounce(() => {
    modalInfo?.confirmBtnCallback && modalInfo?.confirmBtnCallback();
    modalDataStoreInstance.closeModal(modalInfo?.id);
  }, 100);

  // const handleBackdrop = _.debounce(() => {
  //   modalDataStoreInstance.closeModal(modalInfo?.id);
  // }, 100);
  /** 优惠券列表 */
  const $couponList = useMemo(() => {
    return (
      <>
        {modalInfo?.extra?.data?.map?.((couponItem: UserVOSpace.CouponsItem) => {
          return (
            <CouponItem
              key={couponItem.serialNumber}
              data={couponItem}
              height={70}
              leftTextSize={25}
              leftWidth={80}
              isShowDescription={false}
            />
          );
        })}
      </>
    );
  }, [modalInfo]);

  /** 优惠券数量 */
  const couponIsMoreThanOne = useMemo(() => {
    return modalInfo?.extra?.data?.length > 1;
  }, [modalInfo]);

  return (
    <CommonModal
      hasLinearGradient={false}
      visible={visible}
      style={{ backgroundColor: 'transparent' }}>
      <ImageBackground name={couponIsMoreThanOne ? '_couponModalOutBg' : '_couponModalOutSmallBg'}>
        <View layoutStrategy="flexColumnCenterCenter" padding="20 0 0 10">
          <Image name="_couponModalTitle" />
          <ImageBackground
            name={couponIsMoreThanOne ? '_couponModalInnerBg' : '_couponModalInnerSmallBg'}
            style={{ marginTop: 7 }}>
            <Text margin="20 0 10 0" i18nKey={Strings.modalString.couponModalTitle} isCenter />
            <ScrollView
              style={{
                marginTop: 6,
                marginBottom: 6,
                paddingHorizontal: 20,
              }}
              fadingEdgeLength={10}
              keyboardShouldPersistTaps="always">
              <View
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-around',
                }}>
                {$couponList}
              </View>
            </ScrollView>
            <View layoutStrategy="flexRowCenterCenter" margin="10 0 16 0">
              <TouchableOpacity onPress={onHandleClick} activeOpacity={0.8}>
                <ImageBackground name="_couponModalBtn">
                  <View layoutStrategy="flexRowCenterCenter" style={{ height: '100%' }}>
                    <Text i18nKey={Strings.btnString.OK} style={{ color: Colors.TEXT_COLOR_0 }} />
                  </View>
                </ImageBackground>
              </TouchableOpacity>
            </View>
          </ImageBackground>
        </View>
      </ImageBackground>
    </CommonModal>
  );
};

interface ICouponCard {
  /** 优惠券唯一编号 */
  serialNumber?: string;
  /** 金额 */
  amount?: string;
  /** 最终有效期 */
  expirationDate?: string;
}
/** 优惠券卡片 */
const CouponCard = (props: ICouponCard) => {
  const t = useNameSpace().t;
  const { serialNumber, amount = '-', expirationDate = '----.--.--' } = props;
  return (
    <View
      margin="4 0 4 0"
      layoutStrategy="flexRowStartCenter"
      cardType="baseType"
      style={{
        width: fixPixel({ size: 263 }),
        height: fixPixel({ size: 75 }),
        backgroundColor: '#fff',
      }}>
      <View
        style={{
          height: '100%',
          borderBottomLeftRadius: 6,
          borderTopLeftRadius: 6,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-around',
          width: 80,
          backgroundColor: 'secondary-color-500',
        }}>
        <Text>
          <Text
            status="control"
            bold="bold"
            category={needFixPixel() ? 'h2' : 'p1'}
            textContent={`$`}
          />
          <Text
            status="control"
            bold="bold"
            category={needFixPixel() ? 'h2' : 'p1'}
            textContent={`${Number(amount).toFixed(0)}`}
          />
        </Text>
      </View>
      <View
        margin="0 1 0 0"
        style={{
          width: 2,
          height: '100%',
          borderColor: 'line-color-200',
          borderRightWidth: 1,
          borderStyle: 'dashed',
        }}></View>
      <View
        padding="12 0 12 0"
        style={{
          height: '100%',
          borderBottomRightRadius: 6,
          borderTopRightRadius: 6,
          flex: 1,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-around',
          backgroundColor: 'secondary-color-200',
        }}>
        <View>
          <Text
            style={{
              color: 'text-color-800',
            }}
            category="p2"
            i18nKey="modalString.couponShowDisplay"
          />
        </View>
        <View padding="4 4 4 4">
          <Text
            style={{
              color: 'text-color-800',
            }}
            category={needFixPixel() ? 'c1' : 'c2'}
            textContent={t('modalString.couponShowLastDate', {
              expirationDate,
            })}
          />
        </View>
      </View>
    </View>
  );
};
