/* eslint-disable react-native/no-inline-styles */
import { LoadingModal as _LoadingModal, View } from '@/components';
import { useSubscribeFilter } from '@/hooks';
import { BaseInfoManager, useThemeManager } from '@/managers';
import React from 'react';
import { ActivityIndicator, Dimensions } from 'react-native';
import { BaseInfoContextType } from '../../managers/baseInfo';

export const LoadingModal = (): React.ReactElement => {
  const visible =
    useSubscribeFilter({
      subject: BaseInfoManager.messageCenter,
      filter: (subject: BaseInfoContextType) => {
        return subject.baseModel.loadingModal.visible;
      },
    }) || false;

  const applicationTheme = useThemeManager().value.applicationTheme;

  return (
    <_LoadingModal visible={visible}>
      <View width={Dimensions.get('window').width} height="100%">
        <View
          padding="16 16 16 16"
          style={{
            backgroundColor: 'fill-color-0',
            position: 'absolute',
            top: Dimensions.get('window').height * 0.5,
            left: Dimensions.get('window').width * 0.5,
            borderRadius: 16,
            transform: [{ translateY: -38 }, { translateX: -38 }],
          }}>
          <ActivityIndicator
            animating
            style={{
              width: 44,
              height: 44,
            }}
            color={applicationTheme['primary-color-600']}
            size={'large'}
          />
        </View>
      </View>
    </_LoadingModal>
  );
};
