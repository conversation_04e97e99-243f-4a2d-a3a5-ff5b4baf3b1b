/* eslint-disable react-native/no-inline-styles */
import { Divider, CommonModal, Text, View } from '@/components';
import { useSubscribeFilter } from '@/hooks';
import { ModalContextType, ModalList, modalDataStoreInstance } from '@/managers';
import _ from 'lodash';
import React, { useMemo } from 'react';
export const LoanDetailModal = (): React.ReactElement => {
  const handleBackdrop = _.debounce(() => {
    modalDataStoreInstance.closeModal(modalInfo?.id);
  }, 100);

  const modalInfo = useSubscribeFilter({
    subject: modalDataStoreInstance.messageCenter,
    filter: (subject: ModalContextType) => {
      const maxLength = subject.modalList.length;
      return subject.modalList[maxLength - 1];
    },
  });

  const visible = useMemo(() => {
    return modalInfo?.key === ModalList.LOAN_DETAIL;
  }, [modalInfo]);

  const handleConfirmBtn = _.debounce(() => {}, 100);

  return (
    <CommonModal
      titleKey={'repaymentString.detailTitle'}
      visible={visible}
      hasLinearGradient={false}
      confirmBtnName={'btnString.OK'}
      confirmCallback={handleConfirmBtn}
      onBackdropPress={handleBackdrop}>
      <>
        <View margin="16 16 16 16" layoutStrategy="flexRowBetweenCenter">
          <Text
            category="p2"
            style={{
              color: 'text-color-600',
            }}
            i18nKey={'repaymentString.detailItem1'}
          />
          <Text category="p2" textContent={'$450'} />
        </View>
        <View margin="16 16 16 16" layoutStrategy="flexRowBetweenCenter">
          <Text
            category="p2"
            style={{
              color: 'text-color-600',
            }}
            i18nKey={'repaymentString.detailItem2'}
          />
          <Text category="p2" textContent={'$0'} />
        </View>
        <View margin="16 16 16 16" layoutStrategy="flexRowBetweenCenter">
          <Text
            category="p2"
            style={{
              color: 'text-color-600',
            }}
            i18nKey={'repaymentString.detailItem3'}
          />
          <Text category="p2" textContent={'$64'} />
        </View>
        <View margin="16 16 16 16" layoutStrategy="flexRowBetweenCenter">
          <Text
            category="p2"
            style={{
              color: 'text-color-600',
            }}
            i18nKey={'repaymentString.detailItem4'}
          />
          <Text category="p2" textContent={'$290'} />
        </View>
        <View margin="16 16 16 16" layoutStrategy="flexRowBetweenCenter">
          <Text
            category="p2"
            style={{
              color: 'text-color-600',
            }}
            i18nKey={'repaymentString.detailItem5'}
          />
          <Text category="p2" textContent={'$0'} />
        </View>
        <Divider margin="0 16 0 16" />
        <View margin="16 16 16 16" layoutStrategy="flexRowBetweenCenter">
          <Text
            category="p2"
            style={{
              color: 'text-color-600',
            }}
            i18nKey={'repaymentString.detailItem6'}
          />
          <Text category="p2" textContent={'$0'} />
        </View>
      </>
    </CommonModal>
  );
};
