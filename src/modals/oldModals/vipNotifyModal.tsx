/* eslint-disable react-native/no-inline-styles */
import { CommonModal as _CommonModal, Button, Text, View } from '@/components';
import { useSubscribeFilter } from '@/hooks';
import { useNameSpace } from '@/i18n';
import {
  BaseInfoManager,
  ModalContextType,
  ModalList,
  UserInfoManager,
  modalDataStoreInstance,
} from '@/managers';
import { trackCommonEvent } from '@/trackEvent';
import { UserVOSpace } from '@/types';
import _ from 'lodash';
import { memo, useCallback, useMemo } from 'react';
import { HitPointEnumsSpace } from '@/enums';

export const VipNotifyModal = (): React.ReactElement => {
  const modalInfo = useSubscribeFilter({
    subject: modalDataStoreInstance.messageCenter,
    filter: (subject: ModalContextType) => {
      const maxLength = subject.modalList.length;
      return subject.modalList[maxLength - 1];
    },
  });

  const t = useNameSpace().t;

  const visible = useMemo(() => {
    return modalInfo?.key === ModalList.VIP_NOTIFY;
  }, [modalInfo]);

  const { type, level, rate } = useMemo(() => {
    return (
      (modalInfo?.extra as UserVOSpace.VipPopDataItem) || {
        type: 'UPGRADE',
        level: 2,
        rate: '20%',
      }
    );
  }, [modalInfo]);

  const i18nKey = useMemo(() => {
    return modalInfo?.i18nKey;
  }, [modalInfo]);

  const titleKey = useMemo(() => {
    switch (type) {
      case 'MILD_OVERDUE_LAUNCHED':
      case 'VIP1_LAUNCHED':
        return 'vipString.vipGetFuncModalTitle';
      case 'UPGRADE':
        return 'vipString.vipLevelUpModalTitle';
      case 'VIP0_LAUNCHED':
        return 'vipString.vipNewFuncModalTitle';
      default:
        return '';
    }
  }, [type]);

  const onTrack = useCallback(
    (content: string) => {
      switch (type) {
        case 'VIP1_LAUNCHED':
        case 'VIP0_LAUNCHED':
          trackCommonEvent(
            {
              p: HitPointEnumsSpace.EPageKey.P_APP,
              e: HitPointEnumsSpace.EEventKey.BTN_VIP_NEW_FUNC_NOTIFY,
            },
            content,
          );
          break;
        case 'UPGRADE':
        case 'MILD_OVERDUE_LAUNCHED':
        default:
          trackCommonEvent(
            {
              p: HitPointEnumsSpace.EPageKey.P_HOMEPAGE,
              e: HitPointEnumsSpace.EEventKey.BTN_VIP_LEVEL_UP_NOTIFY,
            },
            content,
          );
      }
    },
    [type],
  );

  const handleConfirmBtn = _.debounce(() => {
    onTrack('1');
    UserInfoManager.getVipConfigAndNavigate({});
    modalDataStoreInstance.closeModal(modalInfo?.id);
  }, 100);

  const handleBackdrop = _.debounce(() => {
    onTrack('0');
    modalDataStoreInstance.closeModal(modalInfo?.id);
  }, 100);

  const $content = useMemo(() => {
    switch (type) {
      case 'MILD_OVERDUE_LAUNCHED':
        return (
          <Text>
            <Text category="p2" i18nKey="vipString.vipResumeModalTipStart" />
            <Text status="danger" category="p2" textContent={String('VIP' + level)} />
            <Text category="p2" i18nKey="vipString.vipResumeModalTipEnd" />
          </Text>
        );
      case 'UPGRADE':
        return (
          <Text>
            <Text category="p1" textContent={t('vipString.vipUpToModalTipStart')} />
            <Text status="danger" category="p1" textContent={rate} />
            <Text category="p1" textContent={t('vipString.vipUpToModalTipEnd')} />
            <Text status="danger" category="h3" textContent={String('VIP' + level)} />
            <Text category="p1" textContent={'!'} />
          </Text>
        );
      case 'VIP0_LAUNCHED':
        return (
          <Text>
            <Text category="p2" i18nKey="vipString.vipNewFuncModalTip" />
          </Text>
        );
      case 'VIP1_LAUNCHED':
        return (
          <Text>
            <Text category="p2" i18nKey="vipString.vipNewFuncToLevelModalTipStart" />
            <Text status="danger" category="p2" textContent={String('VIP' + level)} />
            <Text
              category="p2"
              textContent={t('vipString.vipNewFuncToLevelModalTipEnd', {
                rate,
              })}
            />
          </Text>
        );
    }
  }, [type, level, rate, t]);

  return (
    <_CommonModal
      visible={visible}
      titleKey={titleKey}
      i18nKey={i18nKey}
      // content={content}
      imageKey={'_modalVipIcon'}
      isBottomIconColse={true}
      // buttonType={buttonType}
      hasLinearGradient={false}
      // children={children}
      // confirmBtnName={confirmBtnName}
      // confirmCallback={handleConfirmBtn}
      // cancelBtnName={cancelBtnName}
      cancelCallback={handleBackdrop}
      onBackdropPress={handleBackdrop}>
      <View margin="12 24 12 24">
        {$content}
        <Button
          margin="12 0 12 0"
          onPress={handleConfirmBtn}
          status="tertiary"
          textI18nKey="btnString.viewMore"
        />
        <Text
          category="c1"
          isCenter
          style={{ color: 'text-color-600' }}
          textContent={t('vipString.vipModalRightDescription')}></Text>
      </View>
    </_CommonModal>
  );
};

/** 通用modal */
export default memo(VipNotifyModal);
