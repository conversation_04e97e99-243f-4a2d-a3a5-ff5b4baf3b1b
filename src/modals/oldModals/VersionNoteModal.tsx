/**
 * @description 当前版本更新内容弹窗
 * 用户版本更新后展示该弹窗，只展示一次
 */
import { CommonModal, Image, Layouts, LinearGradient, Text, View } from '@/components';
import { useSubscribeFilter } from '@/hooks';
import { ModalContextType, ModalList, modalDataStoreInstance } from '@/managers';
import _ from 'lodash';
import React, { useMemo } from 'react';
import { Strings } from '@/i18n';
import { ImageNames, VERSION_NOTE } from '@/config';
import { Colors } from '@/themes';

export const VersionNoteModal = (): React.ReactElement => {
  const VERSION_NOTES = [
    'Pago flexible: ahora puedes elegir la fecha de pago que más te convenga. Tú decides cuándo pagar, ¡más flexibilidad y comodidad para ti!',
  ];

  const modalInfo = useSubscribeFilter({
    subject: modalDataStoreInstance.messageCenter,
    filter: (subject: ModalContextType) => {
      const maxLength = subject.modalList.length;
      return subject.modalList[maxLength - 1];
    },
  });

  const visible = useMemo(() => {
    return modalInfo?.key === ModalList.VERSION_NOTE;
  }, [modalInfo]);
  const handleBackdrop = _.debounce(() => {
    modalInfo?.isBackdropClose && modalDataStoreInstance.closeModal(modalInfo?.id);
  }, 100);

  const handleConfirmBtn = _.debounce(() => {
    modalInfo?.confirmBtnCallback && modalInfo?.confirmBtnCallback();
    if (!modalInfo?.isFocus) {
      modalDataStoreInstance.closeModal(modalInfo?.id);
    }
  }, 100);

  return (
    <CommonModal
      hasLinearGradient={false}
      visible={visible}
      confirmBtnName={Strings.btnString.agree}
      confirmCallback={handleConfirmBtn}
      style={{ paddingBottom: 8 }}
      onBackdropPress={handleBackdrop}>
      <View
        style={{
          display: 'flex',
          alignItems: 'center',
        }}>
        <Image margin="0 0 0 0" name={ImageNames._versionNoteHeader} />
        <View margin="0 0 0 0" padding="0 16 0 16">
          <View
            margin="8 0 16 0"
            style={{ flexDirection: 'row', justifyContent: 'center', alignItems: 'flex-end' }}>
            <Image
              style={{ position: 'absolute', bottom: -4 }}
              name={ImageNames._versionNoteTitleBottom}
            />
            <Text>
              <Text
                textContent="¡Nueva función disponible!"
                bold="bold"
                style={{
                  fontSize: 16,
                  color: Colors.PRIMARY_COLOR_700,
                }}
              />
              {/* <Text */}
              {/*   textContent="Actualizaciones!" */}
              {/*   bold="bold" */}
              {/*   category="h3" */}
              {/*   style={{ color: Colors.PRIMARY_COLOR_700 }} */}
              {/* /> */}
            </Text>
          </View>

          {VERSION_NOTES.map(note => (
            <Text
              key={note}
              margin="0 0 0 0"
              textContent={`- ${note}`}
              isCenter={false}
              category="p1"
              style={{
                color: Colors.PRIMARY_COLOR_600,
                fontSize: 14,
              }}
            />
          ))}
        </View>
      </View>
    </CommonModal>
  );
};
