/* eslint-disable react-native/no-inline-styles */
import { CommonModal } from '@/components';
import { useSubscribeFilter } from '@/hooks';
import { ModalContextType, modalDataStoreInstance, ModalList } from '@/managers';
import _ from 'lodash';
import React, { useMemo } from 'react';

export const LoanPreFacedModal = (): React.ReactElement => {
  const modalInfo = useSubscribeFilter({
    subject: modalDataStoreInstance.messageCenter,
    filter: (subject: ModalContextType) => {
      const maxLength = subject.modalList.length;
      return subject.modalList[maxLength - 1];
    },
  });

  const visible = useMemo(() => {
    return modalInfo?.key === ModalList.LOAN_PRE_FACED;
  }, [modalInfo]);

  const handleBackdrop = _.debounce(() => {
    modalInfo?.isBackdropClose && modalDataStoreInstance.closeModal(modalInfo?.id);
  }, 100);

  const handleCloseBtn = _.debounce(() => {
    modalDataStoreInstance.closeModal(modalInfo?.id);
  }, 100);

  const handleConfirmBtn = _.debounce(() => {}, 100);

  return (
    <CommonModal
      visible={visible}
      content={'modalString.loanPreFaced'}
      confirmBtnName={'btnString.OK'}
      confirmCallback={handleConfirmBtn}
      cancelBtnName={'btnString.No'}
      cancelCallback={handleCloseBtn}
      onBackdropPress={handleBackdrop}
    />
  );
};
