/* eslint-disable react-native/no-inline-styles */
import { CommonModal, Text, View, Image, ActionSheet, Divider, Button } from '@/components';
import { useSubscribeFilter } from '@/hooks';
import { ModalContextType, ModalList, modalDataStoreInstance } from '@/managers';
import _ from 'lodash';
import React, { useCallback, useMemo, useState } from 'react';
import { Dimensions } from 'react-native';
import { useNameSpace } from '../../i18n';
import { ScrollView } from 'react-native';
import { nav, TrackEvent } from '@/utils';
import { RouterConfig } from '@/routes';
import { HitPointEnumsSpace } from '@/enums';

export const PermissionApplicationModal = (): React.ReactElement => {
  const t = useNameSpace().t;
  const modalInfo = useSubscribeFilter({
    subject: modalDataStoreInstance.messageCenter,
    filter: (subject: ModalContextType) => {
      // const maxLength = subject.modalList.length;
      let modals = subject.modalList.filter(
        modalInfo => modalInfo.key === ModalList.PERMISSION_APPLICATION,
      );
      return modals.length === 1 ? modals[0] : undefined;
    },
  });

  const visible = useMemo(() => {
    return modalInfo?.key === ModalList.PERMISSION_APPLICATION;
  }, [modalInfo]);

  const handleBackdrop = _.debounce(() => {
    modalDataStoreInstance.closeModal(modalInfo?.id);
  }, 100);

  const handleAgreePermission = useCallback(async () => {
    modalInfo?.confirmBtnCallback && modalInfo?.confirmBtnCallback();

    modalDataStoreInstance.closeModal(modalInfo?.id);
  }, [modalInfo?.id]);

  const handleRejectPermission = useCallback(async () => {
    // 申请权限
    modalInfo?.cancelBtnCallback && modalInfo?.cancelBtnCallback();
    // modalDataStoreInstance.closeModal(modalInfo?.id);
  }, [modalInfo?.id]);

  const [height, setHeight] = useState(
    Dimensions.get('window').height > 800 ? 600 : Dimensions.get('window').height * 0.7,
  );

  const handleOpenPrivacyNotice = useCallback(() => {
    modalDataStoreInstance.closeModal(modalInfo?.id);
    TrackEvent.trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_HOMEPAGE,
        e: HitPointEnumsSpace.EEventKey.BTN_PRIVACY_DISCLOSURE_VIEW_PLLICY,
      },
      '1',
    );
    nav.navigate(RouterConfig.PERMISSION_PRIVACY as any);
  }, [modalInfo?.id]);

  return (
    <ActionSheet visible={visible} height={height} onClose={handleBackdrop}>
      <View
        onLayout={e => {
          // setHeight(e.nativeEvent.layout.height);
        }}
        style={{
          borderTopLeftRadius: 8,
          borderTopRightRadius: 8,
          width: Dimensions.get('window').width,
          backgroundColor: 'background-color-0',
        }}>
        <View
          padding="16 16 16 16"
          style={{ backgroundColor: 'primary-color-500' }}
          layoutStrategy="flexRowCenterCenter">
          <Image margin="0 6 0 0" name="_permissionDetail" />
          <Text status="control" i18nKey="permissionAgreeString.modal_title" />
        </View>
        <ScrollView style={{ height: height }}>
          <Divider
            style={{ height: 16, backgroundColor: 'background-color-100' }}
            margin="0 0 0 0"
          />
          <View padding="16 16 16 16">
            <Text i18nKey="permissionAgreeString.peso_per_tips" />
            <View margin="8 0 0 0" layoutStrategy="flexRowBetweenCenter">
              <Text
                category="p2"
                i18nKey="permissionAgreeString.peso_per_tips_des"
                style={{ color: 'text-color-600' }}
              />
            </View>
          </View>
          <Divider
            style={{ height: 16, backgroundColor: 'background-color-100' }}
            margin="0 0 0 0"
          />

          <View padding="16 16 16 16">
            <View margin="8 0 0 0" layoutStrategy="flexRowStartCenter">
              <Image margin="0 6 0 0" name="_permissionLocation" />
              <Text i18nKey="permissionAgreeString.peso_per_location" />
            </View>
            <View margin="8 0 0 0" layoutStrategy="flexRowBetweenCenter">
              <Text
                category="p2"
                i18nKey="permissionAgreeString.peso_per_location_des"
                style={{ color: 'text-color-600' }}
              />
            </View>
            <Divider margin="16 0 8 0" />
            <View margin="8 0 0 0" layoutStrategy="flexRowStartCenter">
              <Image margin="0 6 0 0" name="_permissionNotify" />
              <Text i18nKey="permissionAgreeString.peso_per_notify" />
            </View>
            <View margin="8 0 0 0" layoutStrategy="flexRowBetweenCenter">
              <Text
                category="p2"
                i18nKey="permissionAgreeString.peso_per_notify_des"
                style={{ color: 'text-color-600' }}
              />
            </View>
            <Divider margin="16 0 8 0" />
            <View margin="8 0 0 0" layoutStrategy="flexRowStartCenter">
              <Image margin="0 6 0 0" name="_permissionCamera" />
              <Text i18nKey="permissionAgreeString.peso_per_camera" />
            </View>
            <View margin="8 0 0 0" layoutStrategy="flexRowBetweenCenter">
              <Text
                category="p2"
                i18nKey="permissionAgreeString.peso_per_camera_des"
                style={{ color: 'text-color-600' }}
              />
            </View>
            <Divider margin="16 0 8 0" />
            <View margin="8 0 0 0" layoutStrategy="flexRowStartCenter">
              <Image margin="0 6 0 0" name="_permissionMessage" />
              <Text i18nKey="permissionAgreeString.peso_per_sms" />
            </View>
            <View margin="8 0 0 0" layoutStrategy="flexRowBetweenCenter">
              <Text
                category="p2"
                i18nKey="permissionAgreeString.peso_per_sms_des"
                style={{ color: 'text-color-600' }}
              />
            </View>
          </View>
        </ScrollView>
        <Divider style={{ height: 22, backgroundColor: 'background-color-100' }} margin="0 0 0 0" />
        <View margin="8 0 0 0" layoutStrategy="flexColumnStartCenter">
          <Text>
            <Text
              category="p2"
              i18nKey={'loginString.readPrivacyNoticeTabOne'}
              style={{
                color: 'text-color-800',
              }}
            />
            <Text
              onPress={handleOpenPrivacyNotice}
              category="p2"
              i18nKey={'loginString.readPrivacyNoticeTabTwo'}
              style={{
                color: 'primary-color-500',
                textDecorationLine: 'underline',
              }}
            />
          </Text>
          <View layoutStrategy={'flexRowBetweenCenter'} margin="8 16 16 16">
            <Button
              appearance="filled"
              status="primary"
              onPress={handleAgreePermission}
              style={{ flex: 1 }}
              textI18nKey={'btnString.proximo'}
            />
          </View>
        </View>
      </View>
    </ActionSheet>
  );
};
