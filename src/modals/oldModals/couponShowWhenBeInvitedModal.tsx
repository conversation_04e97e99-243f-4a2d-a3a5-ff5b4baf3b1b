/* eslint-disable react-native/no-inline-styles */
import { CommonModal, Text, View, Image, ImageBackground, Button } from '@/components';
import { useSubscribeFilter } from '@/hooks';
import { ModalContextType, ModalList, modalDataStoreInstance } from '@/managers';
import _ from 'lodash';
import React, { useMemo } from 'react';
import { TouchableOpacity, ScrollView, Dimensions } from 'react-native';
import Animated, {
  useSharedValue,
  withTiming,
  useAnimatedStyle,
  withRepeat,
  Easing,
} from 'react-native-reanimated';
import { useNameSpace } from '../../i18n';
import { fixPixel, needFixPixel } from '@/utils';
import { UserVOSpace } from '@/types';
import RenderHtml from 'react-native-render-html';

export const CouponShowWhenBeInvitedModal = (): React.ReactElement => {
  const t = useNameSpace().t;
  const modalInfo = useSubscribeFilter({
    subject: modalDataStoreInstance.messageCenter,
    filter: (subject: ModalContextType) => {
      const maxLength = subject.modalList.length;
      return subject.modalList[maxLength - 1];
    },
  });

  const visible = useMemo(() => {
    return modalInfo?.key === ModalList.COUPON_SHOW_WHEN_BE_INVITED;
  }, [modalInfo]);

  const onHandleClick = _.debounce(() => {
    modalInfo?.confirmBtnCallback && modalInfo?.confirmBtnCallback();
    modalDataStoreInstance.closeModal(modalInfo?.id);
  }, 100);

  const handleBackdrop = _.debounce(() => {
    modalDataStoreInstance.closeModal(modalInfo?.id);
  }, 100);

  /** 优惠券列表 */
  const $couponList = useMemo(() => {
    return (
      <>
        {modalInfo?.extra?.data?.map?.((couponItem: UserVOSpace.CouponsItem) => {
          return (
            <CouponCard
              key={couponItem.serialNumber}
              amount={couponItem.amount}
              expirationDate={couponItem.expirationDate}
            />
          );
        })}
      </>
    );
  }, [modalInfo]);

  const tips = useMemo(() => {
    return modalInfo?.extra?.tips || '';
  }, [modalInfo]);

  return (
    <CommonModal hasLinearGradient={false} visible={visible}>
      <View
        layoutStrategy="flexColumnStartCenter"
        style={{
          backgroundColor: 'secondary-color-500',
          borderColor: 'secondary-color-100',
          borderWidth: 2,
          borderRadius: 12,
          height: fixPixel({ size: 379 }),
        }}>
        <View
          margin="4 0 0 0"
          style={{
            display: 'flex',
            alignItems: 'center',
          }}>
          <Image name="_couponTitle" />
        </View>
        <ImageBackground
          name={'_couponBg'}
          style={{
            position: 'absolute',
            bottom: 0,
          }}>
          <View layoutStrategy="flexColumnStartCenter">
            <RenderHtml
              contentWidth={Dimensions.get('window').width}
              source={{
                html:
                  tips ||
                  '<p style="margin: 32px 24px 0 24px;fontSize: 16px; height: 100px; lineHeight: 24px; fontWeight: `normal`; color: #000000d9; text-align: center;">no data</p>',
              }}
            />
            <ScrollView
              style={{
                marginTop: 12,
                marginBottom: 3,
                height: fixPixel({ size: 110 }),
              }}
              fadingEdgeLength={10}
              keyboardShouldPersistTaps="always">
              <View
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-around',
                }}>
                {$couponList}
              </View>
            </ScrollView>
            <Button
              width={'80%'}
              status="secondary"
              onPress={onHandleClick}
              textI18nKey="btnString.accept"
            />
          </View>
        </ImageBackground>
      </View>
    </CommonModal>
  );
};

interface ICouponCard {
  /** 优惠券唯一编号 */
  serialNumber?: string;
  /** 金额 */
  amount?: string;
  /** 最终有效期 */
  expirationDate?: string;
}
/** 优惠券卡片 */
const CouponCard = (props: ICouponCard) => {
  const t = useNameSpace().t;
  const { serialNumber, amount = '-', expirationDate = '----.--.--' } = props;
  return (
    <View
      margin="4 0 4 0"
      layoutStrategy="flexRowStartCenter"
      cardType="baseType"
      style={{
        width: fixPixel({ size: 263 }),
        height: fixPixel({ size: 75 }),
        backgroundColor: '#fff',
      }}>
      <View
        style={{
          height: '100%',
          borderBottomLeftRadius: 6,
          borderTopLeftRadius: 6,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-around',
          width: 80,
          backgroundColor: 'secondary-color-500',
        }}>
        <Text>
          <Text
            status="control"
            bold="bold"
            category={needFixPixel() ? 'h2' : 'p1'}
            textContent={`$`}
          />
          <Text
            status="control"
            bold="bold"
            category={needFixPixel() ? 'h2' : 'p1'}
            textContent={`${Number(amount).toFixed(0)}`}
          />
        </Text>
      </View>
      <View
        margin="0 1 0 0"
        style={{
          width: 2,
          height: '100%',
          borderColor: 'line-color-200',
          borderRightWidth: 1,
          borderStyle: 'dashed',
        }}></View>
      <View
        padding="12 0 12 0"
        style={{
          height: '100%',
          borderBottomRightRadius: 6,
          borderTopRightRadius: 6,
          flex: 1,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-around',
          backgroundColor: 'secondary-color-200',
        }}>
        <View>
          <Text
            style={{
              color: 'text-color-800',
            }}
            category="p2"
            i18nKey="modalString.couponShowDisplay"
          />
        </View>
        <View padding="4 4 4 4">
          <Text
            style={{
              color: 'text-color-800',
            }}
            category={needFixPixel() ? 'c1' : 'c2'}
            textContent={t('modalString.couponShowLastDate', {
              expirationDate,
            })}
          />
        </View>
      </View>
    </View>
  );
};
