/**
 * @description 取消申请后回到复贷首页提示重新选择额度
 */
/* eslint-disable react-native/no-inline-styles */
import React, { useMemo } from 'react';
import _ from 'lodash';
import { modalDataStoreInstance, ModalContextType, ModalList } from '@/managers';
import { useSubscribeFilter } from '@/hooks';
import { Button, CommonModal, Image, LinearGradient, Text, View } from '@/components';
import { Colors } from '@/themes';
import { Strings } from '@/i18n';
import { Dimensions } from 'react-native';
import { ImageNames } from '@/config';

export const RemindReloanModal = (): React.ReactElement => {
  const modalInfo = useSubscribeFilter({
    subject: modalDataStoreInstance.messageCenter,
    filter: (subject: ModalContextType) => {
      const maxLength = subject.modalList.length;
      return subject.modalList[maxLength - 1];
    },
  });

  const visible = useMemo(() => {
    return modalInfo?.key === ModalList.REMIND_RELOAN;
  }, [modalInfo]);

  const handleBackdrop = _.debounce(() => {
    modalDataStoreInstance.closeModal(modalInfo?.id);
  }, 100);

  const handleConfirmBtn = _.debounce(() => {
    modalInfo?.confirmBtnCallback && modalInfo?.confirmBtnCallback();
    modalDataStoreInstance.closeModal(modalInfo?.id);
  }, 100);

  const ButtonGroup = () => {
    return (
      <View layoutStrategy={'flexColumnStartCenter'} margin="16 16 16 16">
        <Button
          width={'90%'}
          appearance="filled"
          status="primary"
          onPress={handleConfirmBtn}
          textI18nKey={Strings.btnString.agree}
        />
      </View>
    );
  };

  return (
    <CommonModal
      hasLinearGradient={false}
      visible={visible}
      isBottomIconColse={true}
      onBackdropPress={handleBackdrop}
      cancelCallback={handleBackdrop}
      style={{
        backgroundColor: 'transparent',
      }}>
      <View style={{ backgroundColor: 'transparent' }}>
        <Image
          style={{
            zIndex: 2,
            alignSelf: 'center',
            backgroundColor: 'transparent',
            top: 40,
            right: -8,
          }}
          name={ImageNames._loanRemindHeader}
        />
        <View
          style={{
            borderRadius: 16,
            borderWidth: 1,
            borderColor: Colors.FILL_COLOR_0,
            backgroundColor: '#FFF',
            overflow: 'hidden',
            paddingBottom: 12,
          }}>
          <LinearGradient
            style={{ alignItems: 'center', height: 48 }}
            start={{ x: 0, y: 0 }}
            end={{ x: 0, y: 1 }}
            colors={['#DBE4FF', '#FFFFFF']}
          />
          <Text
            padding="0 16 0 16"
            margin="0 0 0 0"
            category="p1"
            style={{ color: Colors.TEXT_COLOR_800, alignSelf: 'center', textAlign: 'center' }}
            i18nKey={Strings.homeString.remindReselectAmount}
            isCenter
          />
          <ButtonGroup />
        </View>
      </View>
    </CommonModal>
  );
};
