/* eslint-disable react-native/no-inline-styles */
import { CommonModal, Text, View, Image, Card, Divider } from '@/components';
import { useSubscribeFilter } from '@/hooks';
import { BaseInfoManager, ModalContextType, ModalList, modalDataStoreInstance } from '@/managers';
import { Toast } from '@/nativeComponents';
import Clipboard from '@react-native-clipboard/clipboard';
import _ from 'lodash';
import React, { memo, useCallback, useMemo } from 'react';
import { Linking, TouchableOpacity } from 'react-native';
import { BaseInfoContextType } from 'src/managers/baseInfo';
import { useNameSpace } from '../../i18n';
import { UserVOSpace, EContractConfigActionType } from '@/types';

export const HotlineModal = (): React.ReactElement => {
  const t = useNameSpace().t;
  const modalInfo = useSubscribeFilter({
    subject: modalDataStoreInstance.messageCenter,
    filter: (subject: ModalContextType) => {
      const maxLength = subject.modalList.length;
      return subject.modalList[maxLength - 1];
    },
  });

  const visible = useMemo(() => {
    return modalInfo?.key === ModalList.HOT_LINE;
  }, [modalInfo]);

  const contactConfig = useSubscribeFilter({
    subject: BaseInfoManager.messageCenter,
    filter: (subject: BaseInfoContextType) => {
      return subject.baseModel.contactConfig;
    },
  });

  const handleBackdrop = _.debounce(() => {
    modalDataStoreInstance.closeModal(modalInfo?.id);
  }, 100);

  const handleAction = useCallback(async (item: UserVOSpace.ContractConfigItemType) => {
    switch (item.actionType) {
      case EContractConfigActionType.CALL:
        Linking.openURL(`tel:${item.value}`);
        break;
      case EContractConfigActionType.COPY:
        Clipboard.setString(item.value || '');
        Toast(t('messageString.copy_success'));
        break;
    }
  }, []);

  return (
    <CommonModal
      hasLinearGradient={false}
      visible={visible}
      isBottomIconColse={true}
      cancelCallback={handleBackdrop}
      onBackdropPress={handleBackdrop}>
      <View>
        <View margin={'24 0 16 0'} layoutStrategy="flexRowCenterCenter">
          <Image name="_hotlineModalTop" />
          <Text
            i18nKey={'modalString.hotLineTitle'}
            isCenter={true}
            category="h3"
            margin="0 0 0 8"
            style={{
              color: 'text-color-800',
            }}
          />
        </View>
        <Divider margin="0 16 0 16" />
        <View layoutStrategy="flexColumnCenterCenter" margin="8 12 0 12" padding="0 12 0 12">
          <Text
            category="p2"
            i18nKey={'hotLineString.limitTitle'}
            style={{
              color: 'text-color-600',
            }}
          />
          <Text
            category="p2"
            textContent={contactConfig?.tips || ''}
            style={{
              color: 'text-color-800',
            }}
          />
        </View>
        <Card
          padding="0 0 0 0"
          margin="20 16 24 16"
          style={{
            backgroundColor: 'background-color-0',
            overflow: 'hidden',
            //@ts-ignore
            paddingBottom: contactConfig?.contactList?.length > 0 ? 8 : 0,
          }}>
          {contactConfig?.contactList?.map((item, index) => {
            return (
              <ContactItemView
                key={index + item.title + item.value}
                item={item}
                handleAction={() => handleAction(item)}
              />
            );
          })}
        </Card>
      </View>
    </CommonModal>
  );
};

interface CallViewProp {
  mobile: string;
  handlePhoneCall: () => void;
}
const CallView = memo(function CallView({ mobile, handlePhoneCall }: CallViewProp) {
  return (
    <View
      margin="12 12 12 12"
      style={{
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'flex-end',
      }}>
      <View margin="0 12 0 12">
        <Text
          category="p2"
          i18nKey={'hotLineString.phoneNumber'}
          style={{
            color: 'text-color-600',
          }}
        />
        <Text
          category="p2"
          textContent={mobile}
          style={{
            color: 'text-color-800',
          }}
        />
      </View>
      <TouchableOpacity onPress={handlePhoneCall}>
        <Image name="_callIcon" />
      </TouchableOpacity>
    </View>
  );
});

interface CopyEmailViewProp {
  email: string;
  handleCopyEmail: () => void;
}
const ContactItemView = memo(function ContactItemView({
  item,
  handleAction,
}: {
  item: UserVOSpace.ContractConfigItemType;
  handleAction: () => void;
}) {
  const actionIcon =
    item.actionType === EContractConfigActionType.CALL ? '_hotlinePhoneIcon' : '_copyIcon';
  return (
    <View
      margin="8 0 0 0"
      padding="4 16 4 16"
      style={{
        backgroundColor: 'info-color-100',
        borderRadius: 4,
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'flex-end',
      }}>
      <View margin="0 0 0 0">
        <Text
          category="p2"
          textContent={item.title}
          style={{
            color: 'text-color-600',
          }}
        />
        <Text
          category="p2"
          textContent={item.value}
          style={{
            color: 'text-color-800',
          }}
        />
      </View>
      <TouchableOpacity onPress={handleAction}>
        <Image name={actionIcon} />
      </TouchableOpacity>
    </View>
  );
});
