/* eslint-disable react-native/no-inline-styles */
import { CommonModal } from '@/components';
import { useSubscribeFilter } from '@/hooks';
import { ModalContextType, modalDataStoreInstance, ModalList } from '@/managers';
import _ from 'lodash';
import React, { useMemo } from 'react';

export const ReciveOtpByCallModal = (): React.ReactElement => {
  const modalInfo = useSubscribeFilter({
    subject: modalDataStoreInstance.messageCenter,
    filter: (subject: ModalContextType) => {
      const maxLength = subject.modalList.length;
      return subject.modalList[maxLength - 1];
    },
  });

  const visible = useMemo(() => {
    return modalInfo?.key === ModalList.RECIVE_OTP_BY_CALL;
  }, [modalInfo]);

  const i18nKey = useMemo(() => {
    return modalInfo?.i18nKey;
  }, [modalInfo]);

  const handleBackdrop = _.debounce(() => {
    modalDataStoreInstance.closeModal(modalInfo?.id);
  }, 100);

  const handleConfirmBtn = _.debounce(() => {
    modalInfo?.confirmBtnCallback && modalInfo?.confirmBtnCallback();
    modalDataStoreInstance.closeModal(modalInfo?.id);
  }, 100);

  const confirmBtnName = useMemo(() => {
    return modalInfo?.confirmBtnName;
  }, [modalInfo]);

  return (
    <CommonModal
      hasLinearGradient={false}
      visible={visible}
      imageKey="_loginRightIcon"
      i18nKey={i18nKey}
      confirmBtnName={confirmBtnName}
      confirmCallback={handleConfirmBtn}
      onBackdropPress={handleBackdrop}
    />
  );
};
