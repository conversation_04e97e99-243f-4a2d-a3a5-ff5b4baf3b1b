import { ActionSheet, Button, CommonModal, Image, LinearGradient, Text, View } from '@/components';
import { ImageNames } from '@/config';
import { useSubscribeFilter } from '@/hooks';
import { Strings } from '@/i18n';
import { ModalContextType, ModalList, UserInfoManager, modalDataStoreInstance } from '@/managers';
import { Colors } from '@/themes';
import { EWithholderTipType } from '@/types';
import _ from 'lodash';
import React, { useMemo } from 'react';

export const WithholderTipModal = (): React.ReactElement => {
  const modalInfo = useSubscribeFilter({
    subject: modalDataStoreInstance.messageCenter,
    filter: (subject: ModalContextType) => {
      const maxLength = subject.modalList.length;
      return subject.modalList[maxLength - 1];
    },
  });

  // 关闭或打开
  const tipType: EWithholderTipType = useMemo(() => {
    return modalInfo?.extra || EWithholderTipType.OPEN;
  }, [modalInfo]);

  const title = useMemo(() => {
    if (tipType === EWithholderTipType.OPEN) {
      if (UserInfoManager.context.userModel.isFirstOpenWithhold) {
        return Strings.autoWithholdString.openWithholdTitle;
      }
      return Strings.autoWithholdString.openAgainWithholdTitle;
    } else {
      return Strings.autoWithholdString.closeWithholdTitle;
    }
  }, [tipType, UserInfoManager.context.userModel.isFirstOpenWithhold]);

  const content = useMemo(() => {
    if (tipType === EWithholderTipType.OPEN) {
      if (UserInfoManager.context.userModel.isFirstOpenWithhold) {
        return Strings.autoWithholdString.openWithholdContent;
      }
      return Strings.autoWithholdString.openAgainWithholdContent;
    } else {
      return Strings.autoWithholdString.closeWithholdContent;
    }
  }, [tipType, UserInfoManager.context.userModel.isFirstOpenWithhold]);

  const confirmBtnName = useMemo(() => {
    if (tipType === EWithholderTipType.OPEN) {
      if (UserInfoManager.context.userModel.isFirstOpenWithhold) {
        return Strings.btnString.OK;
      }
      return Strings.btnString.activar;
    } else {
      return Strings.btnString.inactivar;
    }
  }, [tipType, UserInfoManager.context.userModel.isFirstOpenWithhold]);

  const cancelBtnName = useMemo(() => {
    if (tipType === EWithholderTipType.OPEN) {
      if (UserInfoManager.context.userModel.isFirstOpenWithhold) {
        return Strings.btnString.No;
      }
      return Strings.btnString.cancel;
    } else {
      return Strings.btnString.cancel;
    }
  }, [tipType, UserInfoManager.context.userModel.isFirstOpenWithhold]);

  const visible = useMemo(() => {
    return modalInfo?.key === ModalList.WITHHOLDER_TIP;
  }, [modalInfo]);

  const handleBackdrop = _.debounce(() => {
    modalDataStoreInstance.closeModal(modalInfo?.id);
  }, 100);

  const handleConfirmBtn = _.debounce(() => {
    modalInfo?.confirmBtnCallback && modalInfo?.confirmBtnCallback();
    if (!modalInfo?.isFocus) {
      modalDataStoreInstance.closeModal(modalInfo?.id);
    }
  }, 100);

  const handleCancelBtn = useMemo(() => {
    return modalInfo?.isFocus
      ? undefined
      : _.debounce(() => {
        modalDataStoreInstance.closeModal(modalInfo?.id);
        modalInfo?.cancelBtnCallback && modalInfo.cancelBtnCallback();
      }, 100);
  }, [modalInfo]);

  return (
    <CommonModal
      visible={visible}
      cancelBtnName={Strings.btnString.No}
      confirmBtnName={Strings.btnString.Si}
      isBottomIconColse
      cancelCallback={handleCancelBtn}
      confirmCallback={handleConfirmBtn}
      style={{ paddingBottom: 14 }}
      onBackdropPress={handleBackdrop}
    >
      <View padding='16 16 14 16'>
        <View style={{ alignSelf: 'center', alignItems: 'center' }}>
          <Image name={ImageNames._withholdTip} />
          <Text
            margin='12 0 0 0'
            category="h3"
            style={{
              color: Colors.PRIMARY_COLOR_600,
              fontWeight: '700',
              textAlign: 'center',
            }}
            i18nKey={title}
          />
        </View>

        <Text
          margin="16 0 0 0 "
          category={tipType === EWithholderTipType.OPEN && UserInfoManager.context.userModel.isFirstOpenWithhold ? "p2" : "h4"}
          style={{ color: Colors.TEXT_COLOR_700, textAlign: 'center' }}
          i18nKey={content}
        />

        {tipType === EWithholderTipType.OPEN ? (
          <Text
            margin="16 0 0 0 "
            category="c2"
            style={{
              color: Colors.TEXT_COLOR_600, textAlign: 'center',
              fontStyle: 'italic',
            }}
            i18nKey={Strings.autoWithholdString.openWithholdNote}
          />
        ) : null}
      </View>
    </CommonModal>
  );
};
