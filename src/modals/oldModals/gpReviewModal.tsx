/* eslint-disable react-native/no-inline-styles */
import { Button, Image, Input, CommonModal, Text, View } from '@/components';
import { useSubscribeFilter } from '@/hooks';
import { ModalContextType, ModalList, modalDataStoreInstance } from '@/managers';
import _ from 'lodash';
import React, { useCallback, useMemo } from 'react';
import { TouchableOpacity } from 'react-native';

interface Props {
  delay?: boolean;
}
/**
 * @description
 * 触发时机, 审核中页, 还款页
 */
export const GpReviewModal = (props: Props): React.ReactElement => {
  const { delay } = props;
  const modalInfo = useSubscribeFilter({
    subject: modalDataStoreInstance.messageCenter,
    filter: (subject: ModalContextType) => {
      const maxLength = subject.modalList.length;
      return subject.modalList[maxLength - 1];
    },
  });

  const visible = useMemo(() => {
    return modalInfo?.key === ModalList.GP_REVIEW && !delay;
  }, [modalInfo, delay]);

  const [score, setScore] = React.useState<number>(0);
  const [comment, setComment] = React.useState<string>('');

  const handleBackdrop = _.debounce(() => {}, 100);

  const handleCloseBtn = _.debounce(() => {
    modalDataStoreInstance.closeModal(modalInfo?.id);
  }, 100);

  const handleConfirmBtn = _.debounce(() => {
    if (comment.length > 100 || score === 0) {
      return;
    }
    modalInfo?.confirmBtnCallback && modalInfo?.confirmBtnCallback(score, comment);
    modalDataStoreInstance.closeModal(modalInfo?.id);
  }, 100);

  /** 选择评分 */
  const handleSelectStar = useCallback(
    _.debounce((score: number) => {
      setScore(score);
    }, 100),
    [],
  );

  /** 进行评论 */
  const onChangeComment = useCallback(
    (description: string) => {
      setComment(description);
    },
    [comment],
  );

  const inputLimitContent = useMemo(() => {
    return `${comment.length}/100`;
  }, [comment]);

  /** 评论输入框 */
  const InputView = (
    <View margin="0 12 8 12">
      <Input
        placeholderKey={'basicInfoString.textareaPlaceholder'}
        value={comment}
        validateConfig={[
          {
            condition: comment.length <= 100,
            info: '',
            status: 'danger',
          },
        ]}
        onChangeText={onChangeComment}
      />
      <Text
        margin="4 0 0 0"
        style={{
          color: 'text-color-600',
          textAlign: 'right',
        }}
        textContent={inputLimitContent}
      />
    </View>
  );

  return (
    <CommonModal visible={visible} onBackdropPress={handleBackdrop}>
      <View
        style={{
          backgroundColor: 'background-color-0',
          borderRadius: 16,
        }}>
        <Text
          margin={'12 12 0 12'}
          category="p1"
          i18nKey={'modalString.praiseContent'}
          style={{
            color: 'text-color-800',
            lineHeight: 28,
          }}
          isCenter={true}
        />
        <View layoutStrategy="flexRowBetweenCenter" margin="20 16 20 16">
          <TouchableOpacity
            onPress={() => {
              handleSelectStar(1);
            }}>
            <Image name={score >= 1 ? '_star' : '_greyStar'} />
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => {
              handleSelectStar(2);
            }}>
            <Image name={score >= 2 ? '_star' : '_greyStar'} />
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => {
              handleSelectStar(3);
            }}>
            <Image name={score >= 3 ? '_star' : '_greyStar'} />
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => {
              handleSelectStar(4);
            }}>
            <Image name={score >= 4 ? '_star' : '_greyStar'} />
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => {
              handleSelectStar(5);
            }}>
            <Image name={score >= 5 ? '_star' : '_greyStar'} />
          </TouchableOpacity>
        </View>
        {score <= 3 && score > 0 && InputView}
        <View layoutStrategy={'flexRowBetweenCenter'} margin="16 16 16 16">
          <Button
            appearance="outline"
            margin={'0 10 0 0'}
            onPress={handleCloseBtn}
            style={{ flex: 1 }}
            textI18nKey={'btnString.No'}
          />
          <Button
            appearance="filled"
            status="primary"
            onPress={handleConfirmBtn}
            style={{ flex: 1 }}
            textI18nKey={'btnString.OK'}
          />
        </View>
      </View>
    </CommonModal>
  );
};
