/* eslint-disable react-native/no-inline-styles */
import { CommonModal as _CommonModal } from '@/components';
import { useSubscribeFilter } from '@/hooks';
import { ModalContextType, ModalList, modalDataStoreInstance } from '@/managers';
import _ from 'lodash';
import { memo, useMemo } from 'react';

export const CommonModal = (): React.ReactElement => {
  const modalInfo = useSubscribeFilter({
    subject: modalDataStoreInstance.messageCenter,
    filter: (subject: ModalContextType) => {
      const maxLength = subject.modalList.length;
      return subject.modalList[maxLength - 1];
    },
  });

  const visible = useMemo(() => {
    return modalInfo?.key === ModalList.INFO_PROMPT_CONFIRM;
  }, [modalInfo]);

  const i18nKey = useMemo(() => {
    return modalInfo?.i18nKey;
  }, [modalInfo]);

  const topImageKey = useMemo(() => {
    return modalInfo?.topImageKey;
  }, [modalInfo]);

  const titleKey = useMemo(() => {
    return modalInfo?.titleKey;
  }, [modalInfo]);

  const content = useMemo(() => {
    return modalInfo?.content;
  }, [modalInfo]);

  const titleStyle = useMemo(() => {
    return modalInfo?.titleStyle;
  }, [modalInfo]);

  const titleStatus = useMemo(() => {
    return modalInfo?.titleStatus;
  }, [modalInfo]);

  const imageKey = useMemo(() => {
    return modalInfo?.imageKey;
  }, [modalInfo]);

  const hasLinearGradient = useMemo(() => {
    return modalInfo?.hasLinearGradient;
  }, [modalInfo]);

  const confirmBtnName = useMemo(() => {
    return modalInfo?.confirmBtnName;
  }, [modalInfo]);

  const cancelBtnName = useMemo(() => {
    return modalInfo?.cancelBtnName;
  }, [modalInfo]);

  const handleClose = _.debounce(() => {
    modalDataStoreInstance.closeModal(modalInfo?.id);
  }, 100);

  const handleBackdrop = _.debounce(() => {
    modalInfo?.isBackdropClose && modalDataStoreInstance.closeModal(modalInfo?.id);
  }, 100);

  const handleConfirmBtn = _.debounce(() => {
    modalInfo?.confirmBtnCallback && modalInfo?.confirmBtnCallback();
    modalDataStoreInstance.closeModal(modalInfo?.id);
  }, 100);

  const handleCloseBtn = _.debounce(() => {
    modalInfo?.cancelBtnCallback && modalInfo?.cancelBtnCallback();
    modalDataStoreInstance.closeModal(modalInfo?.id);
  }, 100);

  const buttonType = useMemo(() => {
    return modalInfo?.buttonType;
  }, [modalInfo]);

  const isBottomIconColse = useMemo(() => {
    return modalInfo?.isBottomIconColse;
  }, [modalInfo]);

  const isTopIconColse = useMemo(() => {
    return modalInfo?.isTopIconColse;
  }, [modalInfo]);

  const children = useMemo(() => {
    return modalInfo?.children;
  }, [modalInfo]);

  return (
    <_CommonModal
      visible={visible}
      topImageKey={topImageKey}
      titleKey={titleKey}
      titleStatus={titleStatus}
      titleStyle={titleStyle}
      i18nKey={i18nKey}
      content={content}
      imageKey={imageKey}
      isTopIconColse={isTopIconColse}
      isBottomIconColse={isBottomIconColse}
      buttonType={buttonType}
      hasLinearGradient={hasLinearGradient}
      children={children}
      confirmBtnName={confirmBtnName}
      confirmCallback={handleConfirmBtn}
      cancelBtnName={cancelBtnName}
      cancelCallback={handleCloseBtn}
      onBackdropPress={handleBackdrop}
    />
  );
};

/** 通用modal */
export default memo(CommonModal);
