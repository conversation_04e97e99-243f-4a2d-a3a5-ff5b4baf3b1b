import { CommonModal, Image, Text, View } from '@/components';
import { useSubscribeFilter } from '@/hooks';
import { ModalContextType, ModalList, modalDataStoreInstance } from '@/managers';
import _ from 'lodash';
import React, { useMemo } from 'react';

export const UpdateModal = (): React.ReactElement => {
  const modalInfo = useSubscribeFilter({
    subject: modalDataStoreInstance.messageCenter,
    filter: (subject: ModalContextType) => {
      const maxLength = subject.modalList.length;
      return subject.modalList[maxLength - 1];
    },
  });

  const visible = useMemo(() => {
    return modalInfo?.key === ModalList.UPDATE;
  }, [modalInfo]);

  const content = useMemo(() => {
    return modalInfo?.content;
  }, [modalInfo]);

  const imageKey = useMemo(() => {
    return modalInfo?.imageKey;
  }, [modalInfo]);

  const confirmBtnName = useMemo(() => {
    return modalInfo?.isFocus ? 'btnString.close' : modalInfo?.confirmBtnName;
  }, [modalInfo]);

  const cancelBtnName = useMemo(() => {
    return modalInfo?.isFocus ? undefined : modalInfo?.cancelBtnName;
  }, [modalInfo]);

  const handleBackdrop = _.debounce(() => {
    modalInfo?.isBackdropClose && modalDataStoreInstance.closeModal(modalInfo?.id);
  }, 100);

  const handleConfirmBtn = _.debounce(() => {
    modalInfo?.confirmBtnCallback && modalInfo?.confirmBtnCallback();
    if (!modalInfo?.isFocus) {
      modalDataStoreInstance.closeModal(modalInfo?.id);
    }
  }, 100);

  const handleCancelBtn = useMemo(() => {
    return modalInfo?.isFocus
      ? undefined
      : _.debounce(() => {
          modalDataStoreInstance.closeModal(modalInfo?.id);
          modalInfo?.cancelBtnCallback && modalInfo.cancelBtnCallback();
        }, 100);
  }, [modalInfo]);

  return (
    <CommonModal
      hasLinearGradient={true}
      visible={visible}
      confirmBtnName={confirmBtnName}
      cancelBtnName={cancelBtnName}
      confirmCallback={handleConfirmBtn}
      cancelCallback={handleCancelBtn}
      onBackdropPress={handleBackdrop}>
      <View
        margin="0 32 0 32"
        padding="0 16 0 16"
        style={{
          display: 'flex',
          alignItems: 'center',
        }}>
        <Image name="_forceUpdate" />
        <Text
          margin="32 0 0 0"
          i18nKey="settingString.updateTitle"
          isCenter={true}
          category="h3"
          bold="bold"
          style={{
            color: 'text-color-800',
          }}
        />
        <Text
          margin="32 0 32 0"
          textContent={content}
          isCenter={true}
          category="p1"
          style={{
            color: 'text-color-800',
          }}
        />
      </View>
    </CommonModal>
  );
};
