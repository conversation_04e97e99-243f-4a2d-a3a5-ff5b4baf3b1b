/**
 * @description 确认用信申请更改再次提醒弹窗
 */
/* eslint-disable react-native/no-inline-styles */
import React, { useMemo } from 'react';
import _ from 'lodash';
import { modalDataStoreInstance, ModalContextType, ModalList } from '@/managers';
import { useSubscribeFilter } from '@/hooks';
import { Button, CommonModal, Image, LinearGradient, Text, View } from '@/components';
import { Colors } from '@/themes';
import { Strings } from '@/i18n';
import { ImageNames } from '@/config';

export const ApplyRecheckModal = (): React.ReactElement => {
  const modalInfo = useSubscribeFilter({
    subject: modalDataStoreInstance.messageCenter,
    filter: (subject: ModalContextType) => {
      const maxLength = subject.modalList.length;
      return subject.modalList[maxLength - 1];
    },
  });

  const visible = useMemo(() => {
    return modalInfo?.key === ModalList.APPLY_RECHECK;
  }, [modalInfo]);

  const handleBackdrop = _.debounce(() => {
    modalDataStoreInstance.closeModal(modalInfo?.id);
  }, 100);

  const handleCloseBtn = _.debounce(() => {
    modalInfo?.cancelBtnCallback && modalInfo?.cancelBtnCallback();
    modalDataStoreInstance.closeModal(modalInfo?.id);
  }, 100);

  const handleConfirmBtn = _.debounce(() => {
    modalInfo?.confirmBtnCallback && modalInfo?.confirmBtnCallback();
    modalDataStoreInstance.closeModal(modalInfo?.id);
  }, 100);

  const ButtonGroup = () => {
    return (
      <View layoutStrategy={'flexColumnStartCenter'} margin="16 16 16 16">
        <Button
          width={'90%'}
          appearance="filled"
          status="primary"
          onPress={handleConfirmBtn}
          textI18nKey={Strings.btnString.acceptMyLoan}
        />
        <Button
          style={{
            paddingTop: 0,
            paddingBottom: 0,
            minHeight: undefined,
          }}
          appearance="ghost"
          margin={'24 0 0 0'}
          onPress={handleCloseBtn}>
          <Text
            category="c2"
            status="basic"
            style={{
              textDecorationLine: 'underline',
              color: 'text-color-500',
              fontSize: 12,
            }}
            bold="bold"
            i18nKey={Strings.btnString.modifyMyApply}
          />
        </Button>
      </View>
    );
  };

  return (
    <CommonModal
      hasLinearGradient={false}
      visible={visible}
      isBottomIconColse={true}
      onBackdropPress={handleBackdrop}
      cancelCallback={handleBackdrop}
      style={{ backgroundColor: 'transparent' }}
      buttonType={'vertical-special-1'}>
      <View style={{ backgroundColor: 'transparent' }}>
        <Image
          style={{
            zIndex: 2,
            alignSelf: 'center',
            backgroundColor: 'transparent',
            top: 30,
            right: 8,
          }}
          name={ImageNames._applyRecheckHeader}
        />
        <View
          style={{
            borderRadius: 16,
            borderWidth: 1,
            borderColor: Colors.FILL_COLOR_0,
            backgroundColor: '#FFF',
            overflow: 'hidden',
            paddingBottom: 12,
          }}>
          <LinearGradient
            style={{ alignItems: 'center' }}
            start={{ x: 0, y: 0 }}
            end={{ x: 0, y: 1 }}
            colors={['rgba(219, 228, 255, 1)', 'rgba(255, 255, 255, 0.8)']}>
            <Text
              margin="40 0 10 0"
              category="h4"
              style={{ color: Colors.PRIMARY_COLOR_700, fontSize: 20 }}
              i18nKey={Strings.loanConfirmString.loan_modify_recheck_title}
            />
            <Text
              padding="0 16 0 16"
              margin="16 0 0 0"
              category="p1"
              style={{
                color: Colors.TEXT_COLOR_700,
                alignSelf: 'center',
                textAlign: 'center',
              }}
              i18nKey={Strings.loanConfirmString.loan_modify_recheck_content}
            />
          </LinearGradient>
          <ButtonGroup />
        </View>
      </View>
    </CommonModal>
  );
};
