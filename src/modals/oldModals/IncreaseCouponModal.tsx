/**
 * @description 提额券弹窗
 */
import {
  CommonModal,
  Divider,
  Image,
  ImageBackground,
  Layouts,
  LinearGradient,
  Text,
  View,
} from '@/components';
import { ImageNames } from '@/config';
import { useSubscribeFilter } from '@/hooks';
import { Strings } from '@/i18n';
import { ModalContextType, modalDataStoreInstance, ModalList } from '@/managers';
import { Colors } from '@/themes';
import { UserVOSpace } from '@/types';
import _ from 'lodash';
import React, { useMemo } from 'react';
import { Pressable } from 'react-native';

/**
 * 
 * extra.data={
    useType: 'amount' | 'percent';
    value: string;
  }
 */
const IncreaseCouponModal = () => {
  // 顶部图片导致的弹窗偏移量
  const OFFSET_Y = 130 / 4;

  const modalInfo = useSubscribeFilter({
    subject: modalDataStoreInstance.messageCenter,
    filter: (subject: ModalContextType) => {
      const maxLength = subject.modalList.length;
      return subject.modalList[maxLength - 1];
    },
  });

  const visible = useMemo(() => {
    return modalInfo?.key === ModalList.INCREASE_COUPON;
  }, [modalInfo]);

  // 优惠券展示的提额金额或百分比
  const data: {
    type: 'amount' | 'percent';
    value: string;
  } = useMemo(() => {
    return modalInfo?.extra?.data;
  }, [modalInfo?.extra?.data]);

  const onPressConfirm = _.debounce(() => {
    modalInfo?.confirmBtnCallback && modalInfo?.confirmBtnCallback();
    modalDataStoreInstance.closeModal(modalInfo?.id);
  }, 100);

  const ModalContentContainer = (params: { children: React.ReactNode }) => {
    return (
      <View
        style={{
          borderTopLeftRadius: 24,
          borderTopRightRadius: 24,
          overflow: 'hidden',
        }}>
        <LinearGradient
          useAngle
          angle={19}
          angleCenter={{ x: 0.5, y: 0.5 }}
          colors={['rgba(193, 206, 255, 0.15)', 'rgba(216, 184, 254, 0.5)']}>
          <LinearGradient
            useAngle
            angle={161}
            angleCenter={{ x: 0.7, y: 0.3 }}
            colors={['#CCD7FF', '#FFE1E2']}>
            {params.children}
          </LinearGradient>
        </LinearGradient>
      </View>
    );
  };

  const CouponCard = (
    <LinearGradient
      start={{ x: 0, y: 0.5 }}
      end={{ x: 1, y: 0.5 }}
      colors={['#8098FF', '#BF99FF']}
      style={{ padding: 6, borderRadius: 8, marginTop: 22 }}>
      <ImageBackground
        style={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
          paddingLeft: 14,
          paddingRight: 14,
        }}
        name={ImageNames._increaseCouponCardBg}>
        <ImageBackground
          name={ImageNames._primaryCouponBadge}
          style={{
            position: 'absolute',
            left: -12,
            top: -6,
            justifyContent: 'center',
            alignItems: 'center',
          }}>
          <Text
            i18nKey={Strings.couponString.increaseMore}
            margin="0 0 4 0"
            style={{
              color: Colors.TEXT_COLOR_0,
              fontSize: 10,
              lineHeight: 14,
            }}
          />
        </ImageBackground>
        <Text
          i18nKey={Strings.couponString.increaseCardContent}
          style={{
            fontSize: 14,
            fontWeight: 500,
            lineHeight: 20,
            color: Colors.TERTIARY_COLOR_500,
            flex: 1,
          }}
        />

        <Divider
          direction={'vertical'}
          style={{
            backgroundColor: Colors.TERTIARY_COLOR_500,
            height: 57,
            marginLeft: 6,
            marginRight: 6,
          }}
        />
        <Text
          textContent="$"
          style={{
            display: data?.type === 'percent' ? 'none' : 'flex',
            fontSize: 16,
            color: Colors.TERTIARY_COLOR_500,
            top: 4,
          }}
        />
        <Text
          style={{
            color: Colors.TERTIARY_COLOR_500,
            fontSize: 24,
          }}
          category="h2"
          bold={'bold'}
          textContent={data?.value}
        />
        <Text
          textContent="%"
          style={{
            display: data?.type === 'percent' ? 'flex' : 'none',
            fontSize: 16,
            color: Colors.TERTIARY_COLOR_500,
            top: 4,
          }}
        />
      </ImageBackground>
    </LinearGradient>
  );

  const ButtonView = (
    <Pressable onPress={onPressConfirm}>
      <ImageBackground
        style={{ justifyContent: 'center', alignItems: 'center' }}
        margin="24 0 0 0"
        name={ImageNames._increaseCouponAgreeBtn}>
        <Text
          i18nKey={Strings.btnString.agree}
          style={{
            color: Colors.TEXT_COLOR_0,
            fontSize: 16,
            lineHeight: 24,
            fontWeight: '500',
          }}
        />
      </ImageBackground>
    </Pressable>
  );

  const ModalView = useMemo(() => {
    return (
      <>
        <Image name={ImageNames._primaryGiftModal} style={{ top: 4 }} />
        <ModalContentContainer>
          <View padding="32 40 32 40" layoutStrategy={Layouts.flexColumnStartCenter}>
            <Image name={ImageNames._primaryCheers} />
            {CouponCard}
            {ButtonView}
          </View>
        </ModalContentContainer>
      </>
    );
  }, [data]);

  return (
    <CommonModal
      visible={visible}
      onBackdropPress={onPressConfirm}
      hasLinearGradient={false}
      style={{ backgroundColor: 'transparent', borderRadius: 24, top: -OFFSET_Y }}>
      {ModalView}
    </CommonModal>
  );
};

export default IncreaseCouponModal;
