/* eslint-disable react/react-in-jsx-scope */
import { CommonModal } from './oldModals/commonModal';
import { HotUpdateModal } from './oldModals/hotUpdateModal';
import { HotlineModal } from './oldModals/hotlineModal';
import { CouponShowModal } from './oldModals/couponShowModal';
import { CouponShowWhenBeInvitedModal } from './oldModals/couponShowWhenBeInvitedModal';
import { LoadingModal } from './oldModals/loadingModal';
import { UpdateModal } from './oldModals/updateModal';
import { VipNotifyModal } from './oldModals/vipNotifyModal';
import { PermissionApplicationModal } from './oldModals/permissionApplicationModal';
import React from 'react';
import { VersionNoteModal } from './oldModals/VersionNoteModal';
import { ApplyRecheckModal, IncreaseCouponModal } from '@/modals';
import { RemindReloanModal } from './oldModals/remindReloanModal';
import { WithholderTipModal } from './oldModals/withholderTipModal';
import { CountUpModal } from './oldModals/countUpModal';

export default function App() {
  return (
    <>
      {/* 确认权限弹框 */}
      <PermissionApplicationModal />
      {/* 通用弹窗 */}
      <CommonModal />
      {/* 客服弹框 */}
      <HotlineModal />
      {/* 系统更新弹框 */}
      <UpdateModal />
      {/* loading  */}
      <LoadingModal />
      {/* 热更新进度弹窗 */}
      <HotUpdateModal />
      {/* 优惠券发放弹窗 */}
      <CouponShowModal />
      {/* 提额劵发放弹窗 */}
      <IncreaseCouponModal />
      {/* 被邀请用户优惠券发放 */}
      <CouponShowWhenBeInvitedModal />
      {/* vip 弹窗 */}
      <VipNotifyModal />
      {/* 新版本更新内容弹窗 */}
      <VersionNoteModal />
      {/* 确认用信申请更改再次提醒弹窗 */}
      <ApplyRecheckModal />
      {/* 复贷提示重新选择额度进行贷款 */}
      <RemindReloanModal />
      {/** 开关代扣提示弹窗 */}
      <WithholderTipModal />
      {/* 正计时弹窗 */}
      <CountUpModal />
    </>
  );
}
