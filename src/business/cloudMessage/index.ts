/** @description FCM 推送通知 */

import { MessageDataStoreInstance } from '@/managers';
import { uploadFcmToken } from '@/server';
import { ErrorLog, isHasPushNotification, log } from '@/utils';
import messaging, { FirebaseMessagingTypes } from '@react-native-firebase/messaging';
import { BaseConfig, setFCMToken } from '../../baseConfig';

var _fcmMessagingCache_: FirebaseMessagingTypes.Module = messaging();

const registerAppWithFCM = async (debug: boolean = false) => {
  await _fcmMessagingCache_.registerDeviceForRemoteMessages();
  _fcmMessagingCache_.onTokenRefresh(async token => {
    debug && log.debug(`设置token=${token}`);
    /** 避免重复上报阻塞启动页进入首页 */
    if (BaseConfig.fcmToken !== token) {
      setFCMToken(token);
      await uploadFcmToken();
    }
  });
};

/** @description 获取 token */
export const getToken = async (debug: boolean = false): Promise<void> => {
  await BaseConfig.geGPServerEnabled();
  try {
    if ((await isHasPushNotification()) && BaseConfig.isGPServerEnabled) {
      // 判断是否有 fcm 通知权限
      const fcmToken = await _fcmMessagingCache_.getToken();
      debug && log.debug(`设置token=${fcmToken}`);
      setFCMToken(fcmToken);
      await uploadFcmToken();
    }
  } catch (error) {
    log.error('get fcmToken error', error);
  }
};

/** @description token 更新 */
export const registerFCMTokenUpdate = () => {};

/** @description init */
export const fcmForSetup = async (debug: boolean = false) => {
  await registerAppWithFCM(debug);
  await getToken();
  // 注册消息
  async function onMessageReceived(message: any) {
    try {
      // todo 接收到通知的时候，更新未读消息的状态
      MessageDataStoreInstance.updateMessageUnReadState();
    } catch (e: any) {
      ErrorLog.recordLog(e);
    }
  }
  // debug && log.debug(`设置推送通知前台处理函数`);
  _fcmMessagingCache_.onMessage(onMessageReceived);
  // debug && log.debug(`设置推送通知后台处理函数`);
  _fcmMessagingCache_.setBackgroundMessageHandler(onMessageReceived);
};
