/** @description adjust 配置 */

import { KVManagerInstance, UserInfoManager } from '@/managers';
import { log } from '@/utils';
import { useEffect } from 'react';
import { Adjust, AdjustConfig, AdjustEvent } from 'react-native-adjust';
import { BaseConfig } from '@/baseConfig';
import analytics from '@react-native-firebase/analytics';

enum EAdjustEvent {
  /** @description 首次启动。需要增加缓存 */
  ADJUST_FIRST_LAUNCH = 'ad_first_launch',

  /** @description 注册 */
  ADJUST_REGISTER = 'ad_register',

  /** @description 第一次进入用信页面 */
  ADJUST_CUST_REVIEW = 'ad_cust_review',

  /** @description 确认用信 */
  ADJUST_ACCEPT = 'ad_accept',
}

const ADJUST_CALLBACK_PARAM_KEY_FOR_USER_ID: string = 'user_id';
const ADJUST_CALLBACK_PARAM_KEY_FOR_VERSION_CODE: string = 'version_code';

/** @description adjust 事件key值配置 */
export const AdjustEventConfig: Record<EAdjustEvent, string> = {
  [EAdjustEvent.ADJUST_FIRST_LAUNCH]: 'cdqowg',
  [EAdjustEvent.ADJUST_REGISTER]: '507vza',
  [EAdjustEvent.ADJUST_CUST_REVIEW]: 'r1267a',
  [EAdjustEvent.ADJUST_ACCEPT]: 'v1ovf7',
};

export const useAdjustForSetup = () => {
  useEffect(() => {
    // token
    const adjustToken = BaseConfig.adjustProdKey;
    // 环境
    const environment = BaseConfig.isProductionRelease
      ? AdjustConfig.EnvironmentProduction
      : AdjustConfig.EnvironmentSandbox;
    // 日志等级
    const logLevel = BaseConfig.isProductionRelease
      ? AdjustConfig.LogLevelSuppress
      : BaseConfig.debug
      ? AdjustConfig.LogLevelDebug
      : AdjustConfig.LogLevelError;
    const adjustConfig = new AdjustConfig(adjustToken, environment);
    adjustConfig.setLogLevel(logLevel); // enable more logging
    Adjust.create(adjustConfig);
    return () => {
      Adjust.componentWillUnmount();
    };
  }, []);
};

/** @description 启动 adjust */
export const adjustForSetup = async () => {
  // token
  const adjustToken = BaseConfig.adjustProdKey;
  // 环境
  const environment = BaseConfig.isProductionRelease
    ? AdjustConfig.EnvironmentProduction
    : AdjustConfig.EnvironmentSandbox;
  // 日志等级
  const logLevel = BaseConfig.isProductionRelease
    ? AdjustConfig.LogLevelSuppress
    : BaseConfig.debug
    ? AdjustConfig.LogLevelDebug
    : AdjustConfig.LogLevelError;
  const adjustConfig = new AdjustConfig(adjustToken, environment);
  adjustConfig.setLogLevel(logLevel); // enable more logging
  Adjust.create(adjustConfig);
};

export const adjustDestory = Adjust.componentWillUnmount;

const addAllParameterAndTrack = (e: string) => {
  var adjustEvent = new AdjustEvent(e);
  adjustEvent.addCallbackParameter(
    ADJUST_CALLBACK_PARAM_KEY_FOR_USER_ID,
    UserInfoManager.context.userModel.userId,
  );
  adjustEvent.addCallbackParameter(
    ADJUST_CALLBACK_PARAM_KEY_FOR_VERSION_CODE,
    BaseConfig.appVersionCode.toString(),
  );
  Adjust.trackEvent(adjustEvent);
};

/**
 * 生成 cacheKey
 */
const generateCacheKey = (eventName: string, markName: string = ''): string =>
  markName ? `${eventName}_${markName}` : `${eventName}`;

/** 通用事件打点 */
// const trackEvent = (e: string) => {
// 	addAllParameterAndTrack(e)
// }

/** 通用唯一标识打点 标识基于平台 */
const trackEventUniqueUniversal = (e: string) => {
  const cacheKey: string = generateCacheKey(e);
  if (!KVManagerInstance.action.getBoolean(cacheKey)) {
    addAllParameterAndTrack(e);
    KVManagerInstance.action.setBoolean(cacheKey, true);
  }
};

/** 用户唯一标识打点 */
// const trackEventUniqueUserId = (e: string) => {
// 	const cacheKey: string = generateCacheKey(e, userInfoDataStoreInstance.context.userId)
// 	if (!KVManagerInstance.action.getBoolean(cacheKey)) {
// 		addAllParameterAndTrack(e)
// 		KVManagerInstance.action.setBoolean(cacheKey, true)
// 	}
// }

export const AdjustEventPointTools = {
  // 首次启动app
  async trackEventOfFirstLaunch() {
    // 需要判断是否为新户，且无历史订单
    try {
      const eventName: string = AdjustEventConfig[EAdjustEvent.ADJUST_FIRST_LAUNCH];
      analytics().logEvent(EAdjustEvent.ADJUST_FIRST_LAUNCH);
      trackEventUniqueUniversal(eventName);
    } catch (error) {
      log.error('trackEventOfFirstLaunch fail ', error);
    }
  },

  // 注册
  async trackEventOfRegister() {
    // 需要判断是否为新户，且无历史订单
    try {
      const eventName: string = AdjustEventConfig[EAdjustEvent.ADJUST_REGISTER];
      analytics().logEvent(EAdjustEvent.ADJUST_REGISTER);
      trackEventUniqueUniversal(eventName);
    } catch (error) {
      log.error('trackEventOfRegister fail ', error);
    }
  },
  // 登录
  // trackEventOfLogin() {
  // login 目前先不需要先注释
  //        AdjustEventPointTools.trackEvent(AdjustEventPointTools.ADJUST_LOGIN)
  // }
  // 申请订单（新客）此处已经改为后端埋点
  // trackEventOfApply() {
  // 	trackEventUniqueUserID(AdjustEventConfig.[EAdjustEvent.ADJUST_APPLY])
  // },
  /**  首次进入确认授信（新客） */
  async trackEventOfCustReview() {
    try {
      // 需要判断 登录状态 是否为新户，且无历史订单
      if (
        UserInfoManager.context.userModel.isUserTypeNew &&
        UserInfoManager.context.userModel.loanCount <= 1
      ) {
        const eventName: string = AdjustEventConfig[EAdjustEvent.ADJUST_CUST_REVIEW];
        analytics().logEvent(EAdjustEvent.ADJUST_CUST_REVIEW);
        trackEventUniqueUniversal(eventName);
      }
    } catch (error) {
      log.error('trackEventOfCustReview fail ', error);
    }
  },
  /** 首次确认授信（新客）*/
  async trackEventOfAccept() {
    // 需要判断是否为新户，且无历史订单
    try {
      if (
        UserInfoManager.context.userModel.isUserTypeNew &&
        UserInfoManager.context.userModel.loanCount <= 1
      ) {
        const eventName: string = AdjustEventConfig[EAdjustEvent.ADJUST_ACCEPT];
        analytics().logEvent(EAdjustEvent.ADJUST_ACCEPT);
        trackEventUniqueUniversal(eventName);
      }
    } catch (error) {
      log.error('trackEventOfAccept fail ', error);
    }
  },
};
