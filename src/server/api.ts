import { BaseEnumsSpace } from '@/enums';
import { i18n } from '@/i18n';
import { modalDataStoreInstance, ModalList } from '@/managers';
import { Toast } from '@/nativeComponents';
import { RouterConfig } from '@/routes';
import { log, nav } from '@/utils';
import { BaseConfig } from '@/baseConfig';
import {
  clearLoginCache,
  getProtocolHeader,
  getProtocolParam,
} from '../utils/protocolParamAndLoginCacheHandle';
import { decrypt, encrypt, md5Sign } from './rsa';

/** 日志接口的上传数据不打印 */
const LOG_URL_CONFIG = ['/api/crypt/v6/app/logRecord', '/api/crypt/v6/app/log'];

/** 重试数据类型 */
interface IRetryType {
  /** 是否启用 */
  enabled: boolean;
  /** 重试次数 */
  limit: number;
}

/** 默认的重试数据 */
const defaultRetry: IRetryType = {
  enabled: false,
  limit: 1,
};

const requestLog = log.generateInstanceForNamespace('request');

/** 格式化查询参数 */
const formatQuerySting = (query: Record<string, any>): string => {
  const params = new URLSearchParams();
  for (const key in query) {
    params.append(key, query[key]);
  }
  const queryString = params.toString();
  return queryString ? `?${queryString}` : '';
};

class Api {
  baseUrl: string = BaseConfig.apiBaseUrl;
  private TIMEOUT: number = 20 * 1000; // 网络超时：20 秒
  private fetchWithTimeout = (
    url: string,
    options: RequestInit,
    timeout: number = this.TIMEOUT,
  ): Promise<Response> => {
    const controller = new AbortController();
    const { signal } = controller;
    const timeoutPromise = new Promise<Response>((_, reject) => {
      setTimeout(() => {
        // 先拒绝超时错误，再终止请求
        reject({
          code: BaseEnumsSpace.ENetworkStatus.TIMEOUT,
          msg: "timeout",
          data: null
        });
        controller.abort();
      }, timeout);
    });

    const fetchPromise = fetch(url, {
      ...options,
      signal,
    });

    return Promise.race([fetchPromise, timeoutPromise]);
  };
  /** get */
  get = async (path: string, query: Record<string, any> = {}, retry: IRetryType = defaultRetry) => {
    let result: any;
    const headers = await getProtocolHeader();
    headers.append('content-type', 'application/json;charset=utf-8');
    const queryString = formatQuerySting(query);
    const apiPath = path + queryString;
    let retryCount = 0;
    const sendRequest = async () => {
      try {
        requestLog.debug(`GET request send success url=${apiPath}`, {
          query,
          headers,
        });

        let resp = await this.fetchWithTimeout(BaseConfig.apiBaseUrl + apiPath, {
          headers,
          method: 'GET',
        });

        if (resp.status === 200) {
          // 成功
          result = await resp.json();
          requestLog.debug(`GET response receive success url=${apiPath}`, {
            query,
            headers,
            response: result,
          });
        } else {
          // 失败
          requestLog.error(`GET response receive fail code=${resp.status} url=${apiPath}`, {
            query,
            headers,
            response: await resp.json(),
          });
        }
      } catch (error: any) {
        requestLog.error(`GET response receive fail code=${-1} url=${apiPath}`, error);
      }
      // 判断需不需要重试
      if (!result) {
        const { enabled, limit } = retry;
        // 是否需要重试
        if (enabled) {
          // 开始重试
          if (retryCount < limit) {
            retryCount++;
            await sendRequest();
          }
        }
      }
    };
    await sendRequest();
    if (result) {
      if (result.code === 403) {
        // 退出登录
        clearLoginCache();
        nav.resetRouteNavigate(RouterConfig.ENTER_PHONE_NUMBER as any);
      }
      if (result.code !== 0) {
        requestLog.error(`GET response receive fail url=${BaseConfig.apiBaseUrl + apiPath}`, {
          query,
          headers,
          response: result,
        });
      }
    } else {
      result = {
        code: BaseEnumsSpace.ENetworkStatus.NETWORK_ERROR,
        data: null,
        msg: i18n.t('basicInfoString.network_error'),
      };
      requestLog.error(`GET response receive fail url=${BaseConfig.apiBaseUrl + apiPath}`, {
        query,
        headers,
        response: result,
      });
    }
    // 错误状态下提示错误消息
    if (result.code !== 0) {
      Toast(result.msg);
    }
    return result;
  };
  /** post */
  post = async (path: string, body: Record<string, any> = {}, retry: IRetryType = defaultRetry) => {
    let result: any;
    const protocolParam = await getProtocolParam();
    const headers = await getProtocolHeader();
    const cryptHeaders = new Headers();
    cryptHeaders.append('content-type', 'application/json;charset=utf-8');
    headers.append('content-type', 'application/json;charset=utf-8');

    // body
    let customBody: any;
    // header
    let customHeaders: any = headers;
    const apiPath = path;
    let retryCount = 0;
    const timeout = body.timeout || this.TIMEOUT;
    body.timeout && delete body.timeout;
    const sendRequest = async () => {
      try {
        if (apiPath.includes('user/buryPoint')) {
          requestLog.info(`POST request send success url=${apiPath}`, {
            body: LOG_URL_CONFIG.includes(path) ? 'Data structure discard' : body,
            headers,
          });
        } else {
          requestLog.debug(`POST request send success url=${apiPath}`, {
            body: LOG_URL_CONFIG.includes(path) ? 'Data structure discard' : body,
            headers,
          });
        }
        customBody = {
          ...body,
          ...protocolParam,
          // applyOrderId 优先使用传入的Id
          applyOrderId: body?.applyOrderId || protocolParam.applyOrderId,
        };
        // 针对于url包含 crypt 需要处理加密逻辑
        if (path.includes('/api/crypt/')) {
          if (BaseConfig.apiEncryptionEnabled) {
            // 新接口加密处理
            const encryptedData = await encrypt(customBody);
            const mdtSignData = md5Sign(encryptedData);
            customBody = {
              encryptedData,
            };
            cryptHeaders.append('timestamp', String(mdtSignData.dataNow));
            cryptHeaders.append('signature', mdtSignData.signData);
            cryptHeaders.append('needEncryption', 'YES');
            customHeaders = cryptHeaders;
          } else {
            // 新接口非加密处理
            headers.append('needEncryption', 'NO');
            customHeaders = headers;
          }
        }
        let resp = await this.fetchWithTimeout(BaseConfig.apiBaseUrl + apiPath, {
          headers: customHeaders,
          body: JSON.stringify(customBody),
          method: 'POST',
        }, timeout);
        if (resp.status === 200) {
          // 成功
          result = await resp.json();
        } else {
          // 失败

          if (!apiPath.includes('app/log') && !apiPath.includes('user/buryPoint')) {
            requestLog.error(
              `POST response receive error code=${resp.status} url=${BaseConfig.apiBaseUrl + apiPath
              }`,
              {
                body: LOG_URL_CONFIG.includes(path) ? 'Data structure discard' : body,
                headers,
                customHeaders,
              },
            );
          } else {
            requestLog.error(
              `POST response receive fail code=${resp.status} url=${BaseConfig.apiBaseUrl + apiPath
              }`,
              {
                body: LOG_URL_CONFIG.includes(path) ? 'Data structure discard' : body,
                headers,
                customHeaders,
              },
            );
          }
        }
      } catch (error: any) {
        if (error?.code === BaseEnumsSpace.ENetworkStatus.TIMEOUT) {
          result = error
        }
        if (!apiPath.includes('app/log') && !apiPath.includes('user/buryPoint')) {
          requestLog.error(
            `POST response receive error code=${-1} url=${BaseConfig.apiBaseUrl + apiPath}`,
            {
              body: LOG_URL_CONFIG.includes(path) ? 'Data structure discard' : body,
              headers,
              customHeaders,
              error,
            },
          );
        } else {
          requestLog.error(
            `POST response receive error code=${-1} url=${BaseConfig.apiBaseUrl + apiPath}`,
            {
              body: LOG_URL_CONFIG.includes(path) ? 'Data structure discard' : body,
              headers,
              customHeaders,
              error,
            },
          );
        }
      }
      // 判断需不需要重试
      if (!result) {
        const { enabled, limit } = retry;
        // 是否需要重试
        if (enabled) {
          // 开始重试
          if (retryCount < limit) {
            retryCount++;
            await sendRequest();
          }
        }
      } else {
        const { code, data } = result;
        if (
          code == BaseEnumsSpace.ENetworkStatus.SUCCESS &&
          path.includes('/api/crypt/') &&
          BaseConfig.apiEncryptionEnabled
        ) {
          result = {
            ...result,
            data: await decrypt(data),
          };
        }

        if (apiPath.includes('user/buryPoint')) {
          requestLog.info(`POST response receive success url=${apiPath}`, {
            body: LOG_URL_CONFIG.includes(path) ? 'Data structure discard' : body,
            headers,
            // customBody,
            customHeaders,
            response: result,
          });
        } else {
          requestLog.debug(`POST response receive success url=${apiPath}`, {
            body: LOG_URL_CONFIG.includes(path) ? 'Data structure discard' : body,
            headers,
            // customBody,
            customHeaders,
            response: result,
          });
        }
      }
    };
    await sendRequest();
    if (result) {
      if (Number(result.code) === 403) {
        // 退出登录
        clearLoginCache();
        nav.resetRouteNavigate(RouterConfig.ENTER_PHONE_NUMBER as any);
      }
    } else {
      result = {
        code: BaseEnumsSpace.ENetworkStatus.NETWORK_ERROR,
        data: null,
        msg: i18n.t('basicInfoString.network_error'),
      };
    }

    if (result.code !== 0) {
      // 错误状态下提示错误消息
      if (Number(result.code) === 501) {
        modalDataStoreInstance.openModal({
          key: ModalList.INFO_PROMPT_CONFIRM,
          imageKey: '_notifyRedMiddle',
          content: result.msg,
          confirmBtnName: 'btnString.OK',
          isBackdropClose: false,
        });
      } else {
        Toast(result.msg);
      }
      requestLog.error(`POST response receive fail url=${apiPath}`, {
        body: LOG_URL_CONFIG.includes(path) ? 'Data structure discard' : body,
        headers,
        customHeaders,
        response: result,
      });
    }
    return result;
  };
  /** 上传图片 */
  upload = async (
    path: string,
    formData: FormData,
    query: Record<string, any> = {},
    retry: IRetryType = defaultRetry,
  ) => {
    let result: any;
    const protocolParam = await getProtocolParam();
    const headers = await getProtocolHeader();
    const cryptHeaders = new Headers();
    cryptHeaders.set('content-type', 'multipart/form-data');
    headers.set('content-type', 'multipart/form-data');
    // body
    let customBody: any;
    // header
    let customHeaders: any = headers;
    let apiPath = path;
    let retryCount = 0;
    const timeout = query.timeout || this.TIMEOUT;
    query.timeout && delete query.timeout;

    const sendRequest = async () => {
      try {
        requestLog.debug(`POST request send success url=${apiPath}`, {
          query,
          headers,
        });

        // 针对于url包含 crypt 需要处理加密逻辑
        if (path.includes('/api/crypt/')) {
          if (BaseConfig.apiEncryptionEnabled) {
            customBody = {
              ...protocolParam,
              ...query,
            };
            // 新接口加密处理
            const encryptedData = await encrypt(customBody);
            const mdtSignData = md5Sign(encryptedData);
            formData.append('encryptedData', encryptedData);
            cryptHeaders.set('timestamp', String(mdtSignData.dataNow));
            cryptHeaders.set('signature', mdtSignData.signData);
            cryptHeaders.set('needEncryption', 'YES');
            customHeaders = cryptHeaders;
          } else {
            // 新接口非加密处理
            headers.set('needEncryption', 'NO');
            const queryString = formatQuerySting(query);
            apiPath = path + queryString;
            customHeaders = headers;
          }
        } else {
          const queryString = formatQuerySting(query);
          apiPath = path + queryString;
        }
        let resp = await this.fetchWithTimeout(BaseConfig.apiBaseUrl + apiPath, {
          headers: customHeaders,
          body: formData,
          method: 'POST',
        }, timeout);
        if (resp.status === 200) {
          // 成功
          result = await resp.json();
        } else {
          // 失败
          requestLog.debug(`POST response receive fail code=${resp.status} url=${apiPath}`, {
            query,
            headers,
            // customBody,
            customHeaders,
          });
        }
      } catch (error: any) {
        if (error?.code === BaseEnumsSpace.ENetworkStatus.TIMEOUT) {
          result = error
        }
        requestLog.error(
          `POST response receive false code=${-1} url=${BaseConfig.apiBaseUrl + apiPath}`,
          {
            query,
            headers,
            // customBody,
            customHeaders,
            error,
          },
        );
      }
      // 判断需不需要重试
      if (!result) {
        const { enabled, limit } = retry;
        // 是否需要重试
        if (enabled) {
          // 开始重试
          if (retryCount < limit) {
            retryCount++;
            await sendRequest();
          }
        }
      } else {
        const { code, data } = result;
        if (
          code == BaseEnumsSpace.ENetworkStatus.SUCCESS &&
          path.includes('/api/crypt/') &&
          BaseConfig.apiEncryptionEnabled
        ) {
          result = {
            ...result,
            data: await decrypt(data),
          };
        }

        requestLog.debug(`POST response receive success url=${apiPath}`, {
          query,
          headers,
          // customBody,
          customHeaders,
          response: result,
        });
      }
    };
    await sendRequest();
    if (result) {
      if (result.code === 403) {
        // 退出登录
        clearLoginCache();
        nav.resetRouteNavigate(RouterConfig.ENTER_PHONE_NUMBER as any);
      }
    } else {
      result = {
        code: BaseEnumsSpace.ENetworkStatus.NETWORK_ERROR,
        data: null,
        msg: i18n.t('basicInfoString.network_error'),
      };
    }
    // 错误状态下提示错误消息
    if (result.code !== 0) {
      Toast(result.msg);
      requestLog.error(`UPLOAD response receive fail url=${BaseConfig.apiBaseUrl + apiPath}`, {
        query,
        headers,
        // customBody,
        customHeaders,
        response: result,
      });
    }
    return result;
  };
}

const instance = new Api();

export default instance;
