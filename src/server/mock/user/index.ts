export const userProcessData = {
  code: 0,
  msg: '',
  data: {
    name: 'ORTEGA RIVERA CHRISTIAN YAIR',
    basic: 'NO',
    professional: 'NO',
    additional: 'NO',
    contact: 'NO',
    face: null,
    rfc: null,
    curp: 'NO',
    bindCard: 'NO',
    applyStatus: 'SYS_CHECK',
    isLoan: 'NO',
    orderStatus: '',
    isNeedSignUp: 'NO',
    updInfo: 'NO',
    focus: 'NO',
    link: '',
    appUpdatesContent: '测试环境，开始版本更新的功能，可以进行验证',
    userType: 'NEW',
    acceptCreditStatus: 'ACCEPT',
    applyOrderId: '',
    locked: 'NO',
    lockMessage: [
      'Estimado usuario, tu solicitud no fue aprobada, por favor aplica de nuevo a partir de {unlock_time}.',
    ],
    unlockTime: '2023/08/05 a las 16',
    allowApply: 'NO',
    selfie: 'NO',
    useCreditStatus: 'WAIT_FACE',
    faceExpired: 'NO',
    faceExpiredMessage: [
      'Lo sentimos, tu reconocimiento facial no ha sido aceptado y no podemos confirmar que hayas solicitado este préstamo en persona.Inténtalo de nuevo',
    ],
    otp: 'YES',
    supplyType: '',
    maintenanceInfo: [
      {
        message: 'Sistema en mantenimiento. Inténtalo de nuevo más tarde.',
        maintenance: 'OCR',
        enabled: 'YES',
      },
      {
        message: 'Sistema en mantenimiento. Inténtalo de nuevo más tarde.',
        maintenance: 'FACE',
        enabled: 'NO',
      },
    ],
  },
};

export const userProcessMockData = new Promise((resolve: any) => resolve(userProcessData));
