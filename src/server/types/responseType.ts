import { EProductType, UserEnumsSpace } from '@/enums';
import { EvidenceVOSpace, LaunchScreenSourceType, OrderVOSpace, UserVOSpace } from '@/types';
import _ from 'lodash';

// 基本类型
export type ResponseBaseType<T> = {
  code: number;
  msg: string;
  data: T;
};

export namespace EventPointResponseSpace {}

// 证件
export namespace EvidenceResponseSpace {
  /**
   * @type 银行列表
   */
  export type ResponseBankListData = ResponseBaseType<EvidenceVOSpace.BankListDataType>;

  /** 查询是否需要做人脸返回（点击申请时调用判断）*/
  export type ResponseNeedFaceApproveData = ResponseBaseType<NeedFaceApproveDataType>;

  /** 人脸是否需要重做信息数据类 */
  type NeedFaceApproveDataType = {
    result: string;
  };

  /** 查询城市pin code 列表返回*/
  export type ResponseAddressPinCodeListData = ResponseBaseType<string[]>;

  /** 查询城市列表返回 */
  export type ResponseAddressData = ResponseBaseType<EvidenceVOSpace.AddressDataType>;

  /** 查询face++ biz token返回 */
  export type ResponseFaceBizTokenData = ResponseBaseType<EvidenceVOSpace.FaceBizTokenDataType>;

  /** 验证face++ 人脸对比是否通过返回 */
  export type ResponseFaceVerifyData = ResponseBaseType<EvidenceVOSpace.FaceVerifyDataType>;

  /** 查询 advance.ai license 返回 */
  export type ResponseFaceLicenseData = ResponseBaseType<EvidenceVOSpace.FaceLicenseDataType>;
  /** 验证 advance.ai 活体检测结果 */
  export type ResponseFaceDetectData = ResponseBaseType<EvidenceVOSpace.FaceDetectDataType>;

  /** 查询已绑的银行卡列表返回 */
  export type ResponseMeBankcardData = ResponseBaseType<EvidenceVOSpace.MeBankcardDataType[]>;

  /** 获取用户的基本信息 */
  export type ResponceBasicInfoDetail = ResponseBaseType<EvidenceVOSpace.RequestBasicType>;

  /** 获取用户的联系人 */
  export type ResponceContactsInfoDetail = ResponseBaseType<EvidenceVOSpace.ContactItemType[]>;

  /** OCR 回填信息 */
  export type ResponceOCRDataInfo = ResponseBaseType<EvidenceVOSpace.OCRDataInfoType>;
  /** 保存银行卡信息 */
  export type ResponceBankAccount = ResponseBaseType<EvidenceVOSpace.RequestBankAccountType>;

  /** 获取银行卡补件信息 */
  export type ResponceSupplyBankAccount =
    ResponseBaseType<EvidenceVOSpace.MeBankcardSupplyDataType>;

  /** 风控问券列表 */
  export type ResponceRKQuestionList = ResponseBaseType<{
    qgId: number;
    questions: EvidenceVOSpace.TQuestionItem[];
  }>;
}

// 订单
export namespace OrderResponseSpace {
  /** 查询首页显示金额返回 */
  export type ResponseRootAmountData = ResponseBaseType<RooutAmountData>;

  export type RooutAmountData = {
    amount: string;
    term: string;
  };

  /** 查询复贷额度配置 */
  export type ResponseReloanConfigData = ResponseBaseType<ReloanConfigDataWithTips>;

  export type ReloanConfigDataWithTips = {
    productPriceList: ReloanConfigData[];
    tips: string;
    /** 每天服务费费率 */
    serviceFeePerDayRate: string;
  };

  export type ReloanConfigData = {
    /** 最小值 */
    minAmount: number;
    /** 最大值 */
    maxAmount: number;
    /** 默认值 */
    defaultAmount: number;
    /** 贷款期限 */
    days: number;
    /** 贷款金额步进值 */
    increment: number;
    /** 启用｜锁定 */
    enable: 'YES' | 'NO';
    /** 贷款产品类型  */
    productType?: EProductType;
    /** 还款期限 */
    repayDates?: string[];
  };

  /** 查询最后一笔订单信息返回(贷款确认页调用) */
  export type ResponseLoanLastOrderData = ResponseBaseType<OrderVOSpace.LoanLastOrderDataType>;

  /** 查询贷款详情信息返回(贷款确认页调用) */
  export type ResponseLoanDetailData = ResponseBaseType<OrderVOSpace.LoanDetailDataType>;

  /**
   * @description 查询用户默认的银行卡号返回
   *  @property cardNo
   *  @property bankName
   *  @property bankCode
   *  @property type
   *  @property isDef
   *  @property isSelf 是否为非本人clabe号
   *  @property othersFatherName 非本人父姓
   *  @property othersMotherName 非本人母姓
   *  @property othersName 非本人名字
   *  @property othersCurpNumber 非本人curp
   *  @property othersRelation 非本人关系
   * */
  export type ResponseDefaultBankCardInfo = ResponseBaseType<OrderVOSpace.DefaultBankCardInfo>;

  /** 查询贷款详情信息返回(贷款确认页调用) */
  export type ResponseLoanCreditDetailData = ResponseBaseType<OrderVOSpace.LoanDetailDataType>;

  /** 查询订单详情信息返回(放款中页调用) */
  export type ResponseOrderDetailData = ResponseBaseType<OrderVOSpace.OrderDetailDataType>;

  /** 查询贷款详情信息返回(贷款确认页调用) */
  export type ResponceApplyLoanOrder = ResponseBaseType<OrderApplyId>;

  type OrderApplyId = string;

  /** 查询还款详情信息返回(还款页调用) */
  export type ResponseBillDetailData = ResponseBaseType<OrderVOSpace.BillDetailDataType>;

  /** 查询配置的还款渠道返回(还款页调用) */
  export type ResponseRepayMethodConfigData =
    ResponseBaseType<OrderVOSpace.RaepayMethodConfigDataType>;

  /** 查询线下还款账号信息返回(线下还款页调用) */
  export type ResponseRepayOfflineData = ResponseBaseType<OrderVOSpace.RepayOfflineDataType>;

  /** 查询线上还款账号信息返回(线下还款页调用) */
  export type ResponseRepayOnlineData = ResponseBaseType<OrderVOSpace.RepayOnlineDataType>;

  /** 查询可以代扣的银行卡 */
  export type ResponseDirectDebitBankData = ResponseBaseType<OrderVOSpace.DirectDebitBankInfo>;

  /** 上传图片返回 */
  export type ResponseImageUploadData = ResponseBaseType<string>;

  /** ocr图片识别返回 */
  export type ResponseImageOcrDataType = ResponseBaseType<OrderVOSpace.ImageOcrDataType>;

  /** 查询借款配置返回 */
  export type ResponseLoanConfigData = ResponseBaseType<OrderVOSpace.LoanConfigDataType[]>;

  /** 查询banner列表返回 */
  export type ResponseBannerData = ResponseBaseType<OrderVOSpace.BannerDataType>;

  /** 获取放款页面的动态展示文案 */
  export type ResponseDisbursingPageShowTip = ResponseBaseType<OrderVOSpace.DisbursingShowTipType>;

  /** 获取还款页面的动态展示文案 */
  export type ResponseRepayPageShowTip = ResponseBaseType<OrderVOSpace.RepayShowTipType>;

  /** 预申请单合同预览 */
  export type ResponsePreApplyContract = ResponseBaseType<String>;

  /** 合同预览 */
  export type ResponseApplyContract = ResponseBaseType<String>;

  /** 首贷额度列表 */
  export type ResponseAmountConfigOnFirstLoan =
    ResponseBaseType<OrderVOSpace.AmountConfigOnFirstLoanType>;

  /** 试算接口首贷 */
  export type ResponseCalculateCostOnFirstLoan =
    ResponseBaseType<OrderVOSpace.CalculateCostOnFirstLoanType>;

  /** 试算接口复贷 */
  export type ResponseCalculateCostOnReLoan =
    ResponseBaseType<OrderVOSpace.CalculateCostOnReLoanType>;

  /** 首贷用户选择额度直接创建临时申请单 */
  export type ResponseFirstLoanPreApply = ResponseBaseType<OrderVOSpace.FirstPreLoanApplyType>;
  // 复贷可选日期范围和不可选的节假日
  export type ResponseReloanRepayDays = ResponseBaseType<OrderVOSpace.LoanRepayDays>;
}

// 用户
export namespace UserResponseSpace {
  /** 查询版本信息返回 */
  export type ResponseAppVersionData = ResponseBaseType<UserVOSpace.AppVersionDataType>;

  /** 查询app配置信息返回 */
  export type ResponseAppConfigData = ResponseBaseType<UserVOSpace.AppConfigDataType>;

  /** 登录返回 */
  export type ResponseLoginDataType = ResponseBaseType<UserVOSpace.LoginDataType>;

  /** 查询联系配置信息返回 */
  export type ResponseContactConfigData = ResponseBaseType<UserVOSpace.ContactConfigDataType>;

  /** 查询用户流程状态信息返回 */
  export type ResponseUserStateData = ResponseBaseType<UserVOSpace.BackUserStateType>;

  /** 查询问题列表返回 */
  export type ResponseFaqData = ResponseBaseType<UserVOSpace.FaqDataType[]>;

  /** 查询banner列表返回 */
  export type ResponseBannerList = ResponseBaseType<OrderVOSpace.BannerDataType[]>;

  /** 查询优惠券列表返回 */
  export type ResponseCouponsList = ResponseBaseType<UserVOSpace.CouponsItem[]>;

  /** 获取邀请code */
  export type ResponseInviteUrl = ResponseBaseType<UserVOSpace.InviteUrlType>;

  /** 获取邀请code */
  export type ResponseHasQualification = ResponseBaseType<UserVOSpace.HasQualificationType>;

  /** 当前日期是否在活动期间 */
  export type ResponseIsInviteDuring = ResponseBaseType<UserVOSpace.InviteDuringType>;

  /** 获取当前活动规则 */
  export type ResponseRichTextConfig = ResponseBaseType<string>;

  /** 卡包信息 */
  export type ResponseWalletInfo = ResponseBaseType<UserVOSpace.WalletInfo>;

  /** 查询用户当前状态 */
  export type ResponseBackUserState = ResponseBaseType<UserVOSpace.BackUserStateType>;
  export namespace LoginSpace {
    /** 登录返回 */
    export type ResponseLoginDataType = ResponseBaseType<UserVOSpace.LoginDataType>;

    export class ResponseLoginDataClass {
      public context: UserVOSpace.LoginDataType;

      constructor(context: UserVOSpace.LoginDataType) {
        this.context = context;
      }

      // 是否转注册页面
      public isRisterFlag(): boolean {
        return _.isEqual(this.context.registerFlag, UserEnumsSpace.ELoginStatus.REGISTER);
      }

      /** 是否转设置密码页 */
      public isSetPasswordFlag(): boolean {
        return _.isEqual(this.context.registerFlag, UserEnumsSpace.ELoginStatus.SETPWD);
      }
    }
  }
  export type ResponseValidateUserExist = ResponseBaseType<UserVOSpace.ValidateUserExist>;
  /** 查询兑换优惠券列表返回 */
  export type ResponseRedeemCoupons = ResponseBaseType<UserVOSpace.RedeemCoupons>;
}

// 广告、营销活动
export namespace ActivityResponseSpace {
  /** 启动页配置 */
  export type LaunchScreenConfig = {
    sourceUrl: string;
    sourceId: string;
    duration: number;
    sourceType: LaunchScreenSourceType;
    link: string;
    active: 'YES' | 'NO';
    version: number;
  };
}
