import { EvidenceEnumsSpace, HitPointEnumsSpace, UserEnumsSpace } from '@/enums';
import { ISqlLogInfo } from '../../logCenter/type';
import { ISqlEventInfo } from '@/trackEvent';
import { EvidenceVOSpace, UserVOSpace } from '@/types';
import { DATA_TYPES } from 'react-native-mmkv-storage/dist/src/utils';
import { Type } from '../../pages/my/verifyBasicInfo/constants/enum';

export namespace UserRequestSpace {
  /** 登录相关 */
  export type RequestLoginType = UserVOSpace.RequestLoginType;

  /** 修改登录手机号 */
  export type RequestModifyMobileType = UserVOSpace.RequestModifyMobileType;

  /** 用户评论 */
  export type RequestSubmitCommentType = UserVOSpace.RequestSubmitCommentType;

  /** otp类型 */
  export type RequestOtp = UserVOSpace.RequestOtp;

  /** 获取OTP */
  export type RequestOtpType = UserVOSpace.RequestOtpType;

  /** opt验证类型 */
  export type RequestVerifyOtp = {
    mobile: string;
    password: string;
  };

  /** 修改登录手机号 */
  export type RequestModifyMobile = {
    mobile: string;
    smsCode: string;
    password: string;
  };

  /**
   * @type 设备信息类型
   */
  export type RequestDeviceType = UserVOSpace.DeviceType;

  /** 条件查询优惠券 */
  export type RequestCouponsByConditions = {
    /** 优惠券类型 */
    type?: UserEnumsSpace.ECouponsType;
    /** 优惠券状态 */
    status: UserEnumsSpace.ECouponsStatus.AVAILABLE | UserEnumsSpace.ECouponsStatus.UNAVAILABLE;
    /** 用信页取最优组合 */
    optimal?: 'YES' | 'NO' | '';
  };

  /** 钱包提现 */
  export type RequestWalletBalanceWithdraw = {
    /** 提现金额 */
    amount: number;
  };

  /** 钱包流水 */
  export type RequestWalletBalanceFlow = {
    /** 提现金额 */
    index?: number;
  };
  /** 原手机号 */
  export type RequestValidateOriginalMobile = {
    originalMobile: string;
  };
  /** 验证是否是本人 */
  export type ValidateIsSelf = {
    validateChannel: Type;
    emailOTP?: string;
    password?: string;
    curp?: string;
  };
  export type RequestChangeMobile = {
    mobile: string;
    smsCode: string;
    password: string;
  };
}

// 证件
export namespace EvidenceRequestSpace {
  /**
   * @type curp身份证信息
   */
  export type RequestEvidenceType = EvidenceVOSpace.RequestCurpType;

  /**
   * @type 基本信息
   */
  export type RequestBasicInfoDetail = EvidenceVOSpace.RequestBasicType;

  /**
   * @type 工作信息
   */
  export type RequestWorkInfoDetail = EvidenceVOSpace.RequestWorkType;

  /**
   * @type 联系人信息
   */
  export type RequestContactInfo = EvidenceVOSpace.RequestContactType;

  /**
   * @type 保存银行卡信息
   * @property bankCode 编码
   * @property bankName 银行名称
   * @property cardNo 类型
   * @property type 卡号
   * @property isSelf 是否为非本人clabe号
   * @property othersFatherName 非本人父姓
   * @property othersMotherName 非本人母姓
   * @property othersName 非本人名字
   * @property othersCurpNumber 非本人curp
   * @property othersRelation 非本人关系
   */
  export type RequestBankItem = EvidenceVOSpace.BankItemDataType;

  export type RequestQuestionnaireSubmit = EvidenceVOSpace.QuestionnaireDataType;

  export type RequestEnhanceCredit = {
    /** 编码 */
    rfc: string;
    /** facebook */
    facebook: string;
    /** instagram */
    instagram: string;
    /** 社保账号 */
    nss: string;
    /** 是否需要社保账号 */
    nssRequired: UserEnumsSpace.EStatusType;
  };

  export type RequestUploadImageAndOcr = {
    /** 照片正面 */
    imageFrontUrl: string;
    /** 照片背面 */
    imageBackUrl: string;
    /** 证件类型 */
    cardType: EvidenceEnumsSpace.ETakePicture;
    /** 超时时间 */
    timeout?: number;
  };

  /**
   * @type 活体验证
   */
  export type RequestFaceVerify = EvidenceVOSpace.RequestFaceVerifyType;

  /** advance.ai 活体验证 */
  export type RequestFaceDetect = EvidenceVOSpace.RequestFaceDetectType;
  // 基本信息
  export type RequestBasicType = EvidenceVOSpace.RequestBasicType;

  // 工作信息
  export type RequestWorkType = EvidenceVOSpace.RequestWorkType;

  // 保存rfc信息
  export type RequestRfcType = EvidenceVOSpace.RequestRfcType;

  // 保存额外信息
  export type RequestAdditionalType = EvidenceVOSpace.RequestAdditionalType;

  // 保存curp信息
  export type RequestCurpType = EvidenceVOSpace.RequestCurpType;

  // 查询城市列表
  export type RequestAddressType = EvidenceVOSpace.RequestAddressType;

  // 保存人脸图片
  export type RequestFaceType = EvidenceVOSpace.RequestFaceType;
  // 对比人脸
  export type RequestFaceVerifyType = EvidenceVOSpace.RequestFaceVerifyType;
  // 跳过人脸比对
  export type RequestSkipLiveType = EvidenceVOSpace.RequestSkipLive;
  // 保存紧急联系人列表信息
  export type RequestContactType = EvidenceVOSpace.RequestContactType;

  // 保存银行卡信息
  export type RequestBankAccountType = EvidenceVOSpace.RequestBankAccountType;

  // 保存RK问卷类型
  export type RequestRKQuestionAnswerDataType = EvidenceVOSpace.RequestQuestionAnswerDataType;
}

// 打点
export namespace EventPointRequestSpace {
  // 定义日志记录类
  export type LogRecordType = {
    message: string;
    logTime: string;
    errorLevel: HitPointEnumsSpace.EErrorLevel;
  };

  /** 保存日志事件打点 */
  export type RequestLogRecordType = {
    ut: number;
    logRecords: ISqlLogInfo[];
  };

  /** 保存埋点日志打点 */
  export type RequestEventPointType = {
    ut: number;
    events: ISqlEventInfo[];
  };

  /** 保存事件打点 */
  export type RequestHitPointType = {
    type: string;
    remark?: string;
  };

  /** 错误日志信息 */
  export type RequestUploadErrorLogType = {
    logList: LogRecordType[];
  };
}

// 订单
export namespace OrderRequestSpace {
  /** 上传图片 */
  export type RequestImageUploadType = {
    /** 图片本地路径 */
    filePath: string;
    /** 图片类型 */
    cardType: string;
  };

  /** 上传图片 */
  export type RequestOcrImageUploadType = {
    /** 正面图片本地路径 */
    frontFilePath: string;
    /** 背面图片本地路径 */
    backFilePath: string;
    /** 图片类型 */
    cardType: string;
  };

  /** 确认贷款 */
  export type RequestLoanCreateType = {
    /** 配置id */
    configId: string;
    /** 金额 */
    amount: string;
  };

  /** 确认复贷 */
  export type RequestReLoanCreateType = {
    /** 天数 */
    days: number;
    /** 金额 */
    amount: number;
    /** 自动代扣的银行卡 */
    cardNo?: string;
    /** 自动代扣开关状态 */
    withholdAuthorizeStatus: string; //代扣授权状态
    /** 还款日期 */
    repayDate?: string;
    /** 选择的贷款类型 */
    repayType?: string;
  };

  /** 复贷二单增信问题 */
  export type RequestReLoanQuestionType = {
    /** 问题列表 */
    answerList: ReLoanQuestionItemType[];
  };

  export type ReLoanQuestionItemType = {
    questionId: number;
    optionKey: string; // 选项
    fillAnswer?: string; // 填写
  };

  /** 确认接收贷款 */
  export type RequestLoanApplyConfirmType = {
    /**
     * 授信
     */
    acceptCredit: string;
    /**
     * 借款订单
     */
    applyOrderId: string;
  };

  /** 计算借款额度信息 */
  export type RequestLoanCalculationType = {
    /** 配置id */
    configId: string;
    /** 金额 */
    amount: string;
  };

  /** 计算借款额度信息 */
  export type RequestLoanCreditCalculationType = {
    /** 订单ID */
    applyOrderId: string;
    /** 优惠券码 */
    couponSerialNumber?: string;
    /** 选中的金额 */
    selectedAmount: string;
    /** 选中的天数 */
    selectedDays: string;
    /** 自动代扣开关 */
    withholdAuthorizeStatus: string;
  };

  /** 查询banner */
  export type RequestBannerType = {
    /** 位置 */
    location: string;
  };

  /** 复贷合同 */
  export type RequestContractOnReloanType = {
    /** 选择额度 */
    selectedAmount: string;
    /** 选择天数 */
    selectedDays: string;
    /** 还款期限 */
    repayDate?: string;
    /** 预申请单号 */
    applyOrderId: string;
    /** 优惠券编号 */
    couponSerialNumber: string;
    /** 首贷YES 复贷NO */
    firstApply: 'YES' | 'NO';
    /** 开通自动代扣的卡号 */
    cardNo?: string;
    /** 是否开通自动代扣 */
    withholdAuthorizeStatus?: string;
  };

  /** 合同预览 */
  export type RequestContractType = {
    /** 申请订单号 */
    applyOrderId?: string;
    /** 优惠券编号 */
    couponSerialNumber?: string;
    /** 选择额度 */
    selectedAmount?: string;
    /** 选中的天数 */
    selectedDays?: string;
  };

  /** 试算接口首贷 */
  export type RequestCalculateCostOnFirstLoanType = {
    /** 选择额度 */
    selectedAmount: string;
    /** 选择天数 */
    selectedDays: string;
    /** 账期 */
    selectedPeriods: string;
    /** 优惠券 */
    couponSerialNumber?: string;
    /** 还款日期 */
    repayDate?: string;
  };

  /** 试算接口复贷 */
  export type RequestCalculateCostOnReLoanType = {
    /** 选择额度 */
    selectedAmount: string;
    /** 选择天数 */
    selectedDays: string;
    /** 优惠券 */
    couponSerialNumber?: string;
    /** 还款日期 */
    repayDate?: string;
  };

  /** 首贷用户选择额度直接创建临时申请单 */
  export type RequestCancelApplyType = {
    /** 申请单ID */
    applyOrderId: string;
  };

  /** 首贷用户选择额度直接创建临时申请单 */
  export type RequestFirstPreLoanApplyType = {
    /** 选择额度 */
    amount: string;
    /** 天数 */
    days?: string;
    /** 期数 */
    productPeriods?: number;
    /** 产品类型 */
    productType: string;
    cardNo: string;
    /** 是否开通自动代扣 */
    withholdAuthorizeStatus: string;
  };

  /** 首贷用户选择额度直接创建临时申请单 */
  export type RequestRepaymentBindCouponType = {
    /** 优惠券码 */
    couponSN: string;
  };

  /** 用户提交用信挽留的结果 */
  export type RequstRetainConfrimType = {
    acceptRetain: 'ACCEPT' | 'REJECT';
  };

  export type RequestCreditRefuseReasonType = {
    optionReason: 'unsatisfied_amount' | 'unsatisfied_fee' | 'other';
    inputReason: string;
  };
}
