import { BaseInfoManager, UserInfoManager } from '@/managers';
import { OrderVOSpace, AppDefaultConfig } from '@/types';
import { getProtocolParam } from '../../../utils/protocolParamAndLoginCacheHandle';
import Api from '../../api';
import { OrderRequestSpace } from '../../types/requestType';
import { OrderResponseSpace, ResponseBaseType } from '../../types/responseType';
import { EProductType } from '@/enums';

/**
 * 获取首页展示信息
 * @description @GET("/api/crypt/v6/homepageAmount")
 * @param params
 * @returns
 */
export const fetchHomePageAmount = async (): Promise<OrderResponseSpace.ResponseRootAmountData> => {
  const { appId, appVersion } = await getProtocolParam();
  const resp = await Api.post('/api/crypt/v6/homepageAmount', {
    appId,
    appVersion,
  });
  return resp;
};

/**
 * 获取复贷额度配置信息
 * @description @GET("/api/v8/reLoan/amountConfig")
 * @param params
 * @returns
 */
export const fetchReLoanAmountConfig =
  async (): Promise<OrderResponseSpace.ResponseReloanConfigData> => {
    const resp = await Api.post('/api/crypt/v8/reLoan/amountConfig');
    return resp;
  };

/**
 * 获取最后一笔订单信息
 * @description @POST("/api/crypt/v6/lastOrder")
 */
export const fetchLastOrder = async (): Promise<OrderResponseSpace.ResponseLoanLastOrderData> => {
  const resp = await Api.post('/api/crypt/v6/lastOrder');
  return resp;
};

/**
 * 获取贷款配置信息
 * @description @POST("/api/v1/getLoanConfig")
 */
export const fetchLoanConfig = async (): Promise<OrderResponseSpace.ResponseLoanConfigData> => {
  const resp = await Api.post('/api/crypt/v6/getLoanConfig');
  return resp;
};

/**
 * 获取默认的银行卡号
 * @description @POST("/api/crypt/v6/user/default/bankcard")
 */
export const fetchDefaultBankCardInfo =
  async (): Promise<OrderResponseSpace.ResponseDefaultBankCardInfo> => {
    const resp = await Api.post('/api/crypt/v6/user/default/bankcard');
    return resp;
  };

/**
 * 获取贷款详情计算信息
 * @description @POST("/api/crypt/v6/getLoanCalculation")
 */
export const fetchLoanCalculation = async (
  params: Partial<OrderRequestSpace.RequestLoanCalculationType>,
): Promise<OrderResponseSpace.ResponseLoanDetailData> => {
  const resp = await Api.post('/api/crypt/v6/getLoanCalculation', params);
  return resp;
};

/**
 * 获取贷款详情计算信息 用户授信页（用户接受贷款页）
 * @description @POST("/api/crypt/v6/loanApply/cost/calculate")
 */
export const fetchLoanCreditCalculation = async (
  params: Partial<OrderRequestSpace.RequestLoanCreditCalculationType>,
): Promise<OrderResponseSpace.ResponseLoanDetailData> => {
  const resp = await Api.post('/api/crypt/v6/loanApply/cost/calculate', params);
  return resp;
};

/**
 * 获取订单信息
 * @description @POST("/api/crypt/v6/loanOrderInfo")
 **/
export const fetchOrderDetail = async (): Promise<OrderResponseSpace.ResponseOrderDetailData> => {
  const resp = await Api.post('/api/crypt/v6/loanOrderInfo');
  return resp;
};

/** 复贷首页获取支持开通自动代扣的银行卡 */
export const fetchReLoanAutoWithholdBankCard = async (): Promise<
  ResponseBaseType<OrderVOSpace.ReloanHomeWithholdBankCardInfo>
> => {
  const resp = await Api.post('/api/crypt/v6/reLoan/getCanOpenWithholdCard');
  return resp;
};

/**
 * 确认贷款
 * @description @POST("/api/crypt/v6/loanApply")
 */
export const confirmApply = async (
  params: Partial<OrderRequestSpace.RequestLoanCreateType> = {},
): Promise<OrderResponseSpace.ResponceApplyLoanOrder> => {
  const resp = await Api.post('/api/crypt/v6/loanApply', params);
  return resp;
};

/**
 * 复贷-确认贷款
 * @description @POST("/api/crypt/v6/reLoan/applyy")
 */
export const confirmReLoanApply = async (
  params: Partial<OrderRequestSpace.RequestReLoanCreateType> = {},
): Promise<OrderResponseSpace.ResponceApplyLoanOrder> => {
  const resp = await Api.post('/api/crypt/v6/reLoan/apply', params);
  return resp;
};

/**
 * 首代取消补件后主动提交申请
 * @description  @POST("/api/crypt/v6/firstLoanApply")
 **/
export const firstCancelConfirmApply =
  async (): Promise<OrderResponseSpace.ResponceApplyLoanOrder> => {
    const resp = await Api.post('/api/crypt/v6/firstLoanApply');
    return resp;
  };

/**
 * 首代取消补件后主动提交申请
 * @description  @POST("/api/crypt/v6/questionnaire/submit")
 **/
export const reLoanQuestionSubmit = async (
  params: Partial<OrderRequestSpace.RequestReLoanQuestionType> = {},
): Promise<ResponseBaseType<any>> => {
  const resp = await Api.post('/api/crypt/v6/questionnaire/submit', params);
  return resp;
};

/**
 * 用户确认授信，收款
 * @description @POST("/api/crypt/v6/loanApply/confirm")
 **/
export const confirmApplyConfirm = async (
  params: Partial<OrderRequestSpace.RequestContractType>,
): Promise<ResponseBaseType<any>> => {
  const resp = await Api.post('/api/crypt/v6/loanApply/confirm', params);
  return resp;
};

/**
 * 分期确认用信
 * @description @POST("/api/crypt/v6/installment/loanApply/confirm")
 */
export const multiPeriodConfirmApplyConfirm = async (params: {
  withholdAuthorizeStatus: string;
  cardNo: string;
}): Promise<ResponseBaseType<any>> => {
  const resp = await Api.post('/api/crypt/v6/installment/loanApply/confirm', params);
  return resp;
};

/**
 * 贷款签约
 * @description @POST("/api/crypt/v6/loanSignUp")
 **/
export const loanSignUp = async (): Promise<ResponseBaseType<any>> => {
  const resp = await Api.post('/api/crypt/v6/loanSignUp');
  return resp;
};

/**
 *  用户确认解锁禁止贷款状态
 * @description @POST("/api/crypt/v6/user/confirmUnlock")
 **/
export const confirmUnlock = async (): Promise<ResponseBaseType<any>> => {
  const resp = await Api.post('/api/crypt/v6/user/confirmUnlock');
  return resp;
};

/**
 * 获取还款详情信息
 * @description @POST("/api/crypt/v6/repaymentInfo")
 **/
export const fetchRepayDetail = async (): Promise<OrderResponseSpace.ResponseBillDetailData> => {
  const resp = await Api.post('/api/crypt/v6/repaymentInfo', {
    applyOrderId: UserInfoManager.context.userModel.applyOrderId,
  });
  return resp;
};

/**
 * 获取分期还款详情信息
 * @description @POST("/api/crypt/v6/repaymentInfo")
 **/
export const fetchMultiPeriodRepayDetail = async (): Promise<
  ResponseBaseType<OrderVOSpace.MultiPeriodBillDetailDataType>
> => {
  const resp = await Api.post('/api/crypt/v6/installment/repaymentInfo', {
    applyOrderId: UserInfoManager.context.userModel.applyOrderId,
  });
  return resp;
};

/** 获取线上还款信息-所有渠道汇总的接口 */
export const fetchOnlineRepayMethodDetailAllChannel =
  async (): Promise<OrderResponseSpace.ResponseRepayOnlineData> => {
    const resp = await Api.post('/api/crypt/v6/repayment/online');
    return resp;
  };

/** 获取线下还款信息-所有渠道汇总的接口 */
export const fetchOfflineRepayMethodDetailAllChannel = async (params?: {
  expectedRepayAmount: string;
}): Promise<OrderResponseSpace.ResponseRepayOfflineData> => {
  const resp = await Api.post('/api/crypt/v6/repayment/offline', params);
  return resp;
};

/**
 * 获取放款之前页面的展示文案
 * @description @POST("/api/crypt/v6/wiper-list/before-loan")
 */
export const fetchDisbursingPageShowTipList =
  async (): Promise<OrderResponseSpace.ResponseDisbursingPageShowTip> => {
    const resp = await Api.post('/api/crypt/v6/wiper-list/before-loan');
    return resp;
  };

/**
 * 获取放款之后页面的展示文案
 * @description @POST("/api/crypt/v6/wiper-list/after-loan")
 */
export const fetchRepayPageShowTipList =
  async (): Promise<OrderResponseSpace.ResponseRepayPageShowTip> => {
    const resp = await Api.post('/api/crypt/v6/wiper-list/after-loan');
    return resp;
  };

/**
 * 预申请单合同预览
 * @description @POST("/api/v6/preApply/contract/review")
 */
export const fetchPreApplyContract = async (
  params: Partial<OrderRequestSpace.RequestContractOnReloanType>,
): Promise<OrderResponseSpace.ResponsePreApplyContract> => {
  UserInfoManager.updatePreApplyId(params.applyOrderId || '');
  const resp = await Api.post('/api/v6/preApply/contract/review', params);
  return resp;
};

/**
 * 合同预览
 * @description @POST("/api/v4/toLoanContract/review")
 */
export const fetchApplyContract = async (
  params: Partial<OrderRequestSpace.RequestContractType>,
): Promise<OrderResponseSpace.ResponseApplyContract> => {
  UserInfoManager.updatePreApplyId(params.applyOrderId || '');
  const resp = await Api.post('/api/v4/toLoanContract/review', params);
  return resp;
};

/** 代扣合同预览 */
export const fetchWithholdContract = async (params?: {
  cardNo: string;
  bankName: string;
}): Promise<OrderResponseSpace.ResponseApplyContract> => {
  const resp = await Api.post('/api/v6/withhold/contract/view', params);
  return resp;
};

/**
 * 首贷额度列表
 *
 * v6 只返回下拉选择额度
 *
 * v7 需要支持天数可拖动选择, 增加了额外的字段
 *
 * @description @POST("/api/crypt/v7/firstLoan/productPrice")
 */
export const fetchAmountConfigOnFirstLoan =
  async (): Promise<OrderResponseSpace.ResponseAmountConfigOnFirstLoan> => {
    const resp = await Api.post('/api/crypt/v7/firstLoan/productPrice');
    return resp;
  };

/**
 * 首贷额度列表
 * *
 * v8 需要支持贷款金额可以拖动选择
 *
 * @description @POST("/api/crypt/v8/firstLoan/productPrice")
 */
export const fetchAmountConfigOnFirstLoanNew = async (params: {
  productType: EProductType;
}): Promise<ResponseBaseType<OrderVOSpace.ProductPriceType>> => {
  const resp = await Api.post('/api/crypt/v8/firstLoan/productPrice', params);
  return resp;
};

/**
 * 新用户保存产品定价
 *
 * @description @POST("/api/crypt/v6/firstLoan/saveSelectedProductPrice")
 */
export const saveFirstLoanAmountConfig = async (params: {
  amount: number;
  loanTerm: number; //账期8天
  productType: string;
  couponSerialNumber?: string; // 优惠券
  repayDate?: string; // 还款日期
}): Promise<ResponseBaseType<any>> => {
  const resp = await Api.post('/api/crypt/v6/firstLoan/saveSelectedProductPrice', params);
  return resp;
};
/**
 * 用户保存产品复贷
 *
 * @description @POST("/api/crypt/v6/firstLoan/saveSelectedProductPrice")
 */
export const saveReLoanAmountConfig = async (params: {
  amount: number;
  loanTerm: number;
  productType: string;
  couponSerialNumber?: string;
  repayDate?: string
}): Promise<ResponseBaseType<any>> => {
  const resp = await Api.post('/api/crypt/v6/reLoan/saveSelectedProductPrice', params);
  return resp;
};

/**
 * 首次贷款额度回填
 *
 * @description @POST("/api/crypt/v6/firstLoan/getSelectedProductPrice")
 */
export const getOnFirstLoanAmountConfigDataInfo = async (): Promise<
  ResponseBaseType<OrderVOSpace.FirstLoanPriceConfigInfoType>
> => {
  const resp = await Api.post('/api/crypt/v6/firstLoan/getSelectedProductPrice');
  return resp;
};

/**
 * 试算接口首贷
 * @description @POST("/api/crypt/v6/firstLoan/cost/calculate")
 */
export const fetchCalculateCostOnFirstLoan = async (
  params: Partial<OrderRequestSpace.RequestCalculateCostOnFirstLoanType>,
): Promise<OrderResponseSpace.ResponseCalculateCostOnFirstLoan> => {
  const resp = await Api.post('/api/crypt/v6/firstLoan/cost/calculate', params);
  return resp;
};

/**
 * 试算接口复贷
 * @description @POST("/api/crypt/v6/reLoan/cost/calculate")
 */
export const fetchCalculateCostOnReLoan = async (
  params: Partial<OrderRequestSpace.RequestCalculateCostOnReLoanType>,
): Promise<OrderResponseSpace.ResponseCalculateCostOnReLoan> => {
  const resp = await Api.post('/api/crypt/v6/reLoan/cost/calculate', params);
  return resp;
};

/**
 * 首贷用户选择额度直接创建临时申请单
 * @description @POST("/api/crypt/v7/firstPreLoanApply")
 */
export const fetchFirstPreLoanApply = async (
  params: Partial<OrderRequestSpace.RequestFirstPreLoanApplyType>,
): Promise<OrderResponseSpace.ResponseFirstLoanPreApply> => {
  const resp = await Api.post('/api/crypt/v7/firstPreLoanApply', params);
  return resp;
};

/**
 * 复贷分期创建申请订单
 * @description @POST("/api/crypt/v7/firstPreLoanApply")
 */
export const fetchReLoanLoanApply = async (
  params: Partial<OrderRequestSpace.RequestFirstPreLoanApplyType>,
): Promise<OrderResponseSpace.ResponseFirstLoanPreApply> => {
  const resp = await Api.post('/api/crypt/v7/reLoan/installment/apply', params);
  return resp;
};

/**
 * 取消申请单
 * @description @POST("/api/crypt/v6/cancelApply")
 */
export const fetchCancelLoanApply = async (
  params: Partial<OrderRequestSpace.RequestCancelApplyType>,
): Promise<ResponseBaseType<any>> => {
  const resp = await Api.post('/api/crypt/v6/cancelApply', params);
  return resp;
};

/** 逾期用户登记无法还款，请求减免
 * @description @POST("/api/crypt/v6/loanOrder/dunRegistration")
 */
export const fetchDunRegistration = async (): Promise<ResponseBaseType<any>> => {
  const resp = await Api.post('/api/crypt/v6/loanOrder/dunRegistration');
  return resp;
};

/**
 * 获取用户的贷款订单记录
 */

export const getLoanRecoedList = async (): Promise<
  ResponseBaseType<OrderVOSpace.LoanRecordItem[]>
> => {
  const resp = await Api.post('/api/crypt/v6/loanOrder/list');
  return resp;
};

/**
 * 还款页核销优惠券
 * @description @POST("/api/crypt/v6/loanOrder/binding/coupons")
 */
export const fetchRepayBindCoupon = async (
  params: Partial<OrderRequestSpace.RequestRepaymentBindCouponType>,
): Promise<ResponseBaseType<any>> => {
  const resp = await Api.post('/api/crypt/v6/loanOrder/binding/coupons', params);
  return resp;
};

/**
 * 确认用信挽留用户的方式
 */
export const submitCreditRefuseRetain = async (
  params: OrderRequestSpace.RequstRetainConfrimType,
): Promise<ResponseBaseType<any>> => {
  const resp = await Api.post('/api/crypt/v6/loanApply/retain/confirm', params);
  return resp;
};

/**
 * 确认用户挽留的原因
 */
export const submitCreditRefuseReason = async (
  params: OrderRequestSpace.RequestCreditRefuseReasonType,
): Promise<ResponseBaseType<OrderVOSpace.CreditRefuseReasonDataType>> => {
  const resp = await Api.post('/api/crypt/v6/loanApply/rejectReason/submit', params);
  return resp;
};

/**
 * 获取确认用信剩余时间
 */
export const fetchCreditVaildDays = async (): Promise<ResponseBaseType<number>> => {
  const resp = await Api.post('/api/crypt/v6/loanApply/credit/validDays');
  return resp;
};

/** 获取产品列表 */
export const fetchProductList = async (): Promise<ResponseBaseType<OrderVOSpace.ProductType>> => {
  const resp = await Api.post('/api/crypt/v6/product/list');
  return resp;
};

/** 首贷产品定价 */
export const fetchFirstLoanMultiPeriodProductPrice = async (): Promise<
  ResponseBaseType<OrderVOSpace.ProductPriceType>
> => {
  const resp = await Api.post('/api/crypt/v7/installment/firstLoan/productPrice');
  return resp;
};

/** 复贷产品定价 */
export const fetchReLoanMultiPeriodProductPrice = async (): Promise<
  ResponseBaseType<OrderVOSpace.ProductPriceType>
> => {
  const resp = await Api.post('/api/crypt/v7/installment/reLoan/productPrice');
  return resp;
};

/** 贷款申请分期试算 */
export const fetchMultiPeriodCalculate = async (params: {
  applyPeriods: number;
  applyAmount: number;
  applyDays: number;
}): Promise<ResponseBaseType<OrderVOSpace.MultiPeriodCalculateType>> => {
  const resp = await Api.post('/api/crypt/v6/installment/pre/cost/calculate', params);
  return resp;
};

/** 用信页分期试算 */
export const fetchMultiPeriodLoanApplyCalculate = async (params: {
  // applyPeriods: number;
  // applyAmount: number;
  // applyDays: number;
}): Promise<ResponseBaseType<OrderVOSpace.MultiPeriodCalculateConfirmLoanType>> => {
  const resp = await Api.post('/api/crypt/v6/installment/cost/calculate', params);
  return resp;
};
/** 获取线下还款信息-试算 */
export const fetchOfflineRepaymentCostCal = async (params: {
  expectedRepayAmount: string;
}): Promise<OrderResponseSpace.ResponseRepayOfflineData> => {
  const resp = await Api.post('/api/crypt/v6/repayment/offline/costCal', params);
  return resp;
};

/**
 * 准备一键还款的信息（用户发起代扣）
 * @description @POST("/api/crypt/v6/prepareDirectDebitInfo")
 */
export const fetchDirectDebitBankList =
  async (): Promise<OrderResponseSpace.ResponseDirectDebitBankData> => {
    const resp = await Api.post('/api/crypt/v6/prepareDirectDebitInfo');
    return resp;
  };

/**
 * 创建一键还款（代扣）
 * @description @POST("/api/crypt/v6/repayment/createDirectDebit")
 */
export const createDirectDebit = async (params: {
  fullRepay: 'YES' | 'NO'; //是否全额还款,YES(全额) NO(非全额)
  amount: number; //还款金额
  repayCardNo: string; //还款（代扣）卡号
}): Promise<ResponseBaseType<any>> => {
  const resp = await Api.post('/api/crypt/v6/repayment/createDirectDebit', params);
  return resp;
};

/**
 *  @description 首贷、复贷固定期限还款 获取节假日和可选日期范围
 *  @POST "/api/crypt/v6/queryHoliday"
 */
/** 获取节假日订单 */
export const fetchLoanFixedDateHoliday =
  async (): Promise<OrderResponseSpace.ResponseReloanRepayDays> => {
    const resp = await Api.post('/api/crypt/v6/queryHoliday');
    return resp;
  };
/**
 * 获取app业务默认值
 */
export const fetchAppDefaultConfig = async (): Promise<ResponseBaseType<AppDefaultConfig>> => {
  const resp = await Api.post('/api/crypt/v6/appDefaultConfig');
  return resp;
};
