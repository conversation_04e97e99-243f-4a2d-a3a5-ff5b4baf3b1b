import { BaseEnumsSpace, UserEnumsSpace } from '@/enums';
import { BaseInfoManager } from '@/managers';
import { EvidenceVOSpace, UserVOSpace } from '@/types';
import _ from 'lodash';
import { getProtocolParam } from '../../../utils/protocolParamAndLoginCacheHandle';
import Api from '../../api';
import { UserRequestSpace } from '../../types/requestType';
import {
  EvidenceResponseSpace,
  ResponseBaseType,
  UserResponseSpace,
} from '../../types/responseType';

/**
 * 登录获取token
 * @description @POST("/api/crypt/v6/user/login")
 * @param params
 * @returns
 */
export const fetchLogin = async (
  params: Partial<UserRequestSpace.RequestLoginType>,
): Promise<UserResponseSpace.ResponseLoginDataType> => {
  const resp = await Api.post('/api/crypt/v6/user/login', params);
  return resp;
};

/**
 * 注册
 * @description @POST("/api/crypt/v6/user/register")
 * @param params
 * @returns
 */
export const fetchRegister = async (
  params?: Partial<UserRequestSpace.RequestLoginType>,
): Promise<UserResponseSpace.ResponseLoginDataType> => {
  const resp = await Api.post('/api/crypt/v6/user/register', params);
  return resp;
};

/**
 * 获取Sms code
 * @description @get("/api/crypt/v6/user/sms")
 * @param params
 * @returns
 */
export const fetchSendSmsCode = async (
  params: Partial<UserRequestSpace.RequestLoginType>,
): Promise<UserResponseSpace.ResponseLoginDataType> => {
  const { appId, appVersion } = await getProtocolParam();
  const resp = await Api.post('/api/crypt/v6/user/sms', {
    mobile: params?.mobile,
    appId,
    appVersion,
  });
  return resp;
};

interface ICheckMobileResult {
  registerFlag: UserEnumsSpace.ELoginStatus;
}
/**
 * 检测手机信息,获取三种状态
 * @description @POST("/api/crypt/v6/user/checkMobile")
 * @param
 * @returns
 */
export const fetchCheckMobile = async (
  params: Partial<UserRequestSpace.RequestLoginType>,
): Promise<ResponseBaseType<ICheckMobileResult>> => {
  const resp = await Api.post('/api/crypt/v6/user/checkMobile', params);
  return resp;
};

/**
 * 设置密码
 * @description @POST("/api/crypt/v6/user/setPwd")
 * @param params
 * @returns
 */
export const fetchSetPassword = async (
  params?: Partial<UserRequestSpace.RequestLoginType>,
): Promise<UserResponseSpace.ResponseLoginDataType> => {
  const resp = await Api.post('/api/crypt/v6/user/setPwd', params);
  return resp;
};

/**
 * 获取app banner
 * @description  @GET("/api/crypt/v6/banner/getAppBanners")
 * @param params
 * @returns
 */
export const fetchGetAppBanner = async (params?: {
  location: BaseEnumsSpace.EBannerLocationType;
}): Promise<UserResponseSpace.ResponseBannerList> => {
  const { appId, appVersion } = await getProtocolParam();
  const resp = await Api.post('/api/crypt/v6/banner/getAppBanners', {
    appId,
    appVersion,
    location: params?.location,
  });
  return resp;
};

/**
 * 获取contact config
 * @description @POST("/api/crypt/v7/contactConfig")
 * @param params
 * @returns
 */
export const fetchGetContactConfig =
  async (): Promise<UserResponseSpace.ResponseContactConfigData> => {
    const resp = await Api.post('/api/crypt/v7/contactConfig');
    return resp;
  };

/**
 * 获取用户流程
 * @description @POST("/api/crypt/v6/user/process")
 * @param params
 * @returns
 */
export const fetchUserState = async (): Promise<UserResponseSpace.ResponseUserStateData> => {
  const resp = await Api.post('/api/crypt/v6/user/process');
  return resp;
};

/**
 * 创建申请单
 * @description @POST('/api/crypt/v6/firstLoanApply')
 * @param params
 * @param queryKey
 * @returns
 */
export const fetchCreateApply = async (): Promise<ResponseBaseType<string>> => {
  const resp = await Api.post('/api/crypt/v6/firstLoanApply');
  return resp;
};

/**
 * 获取墨西哥地址信息
 * @description @POST('/api/crypt/v6/getAddress')
 * @param params
 * @returns
 */
export const fetchGetAddress = async (
  params: Partial<{
    city: string;
    state: string;
    first: 'YES' | 'NO';
    municipality: string;
    zipCode: string;
  }>,
): Promise<ResponseBaseType<any>> => {
  const resp = await Api.post('/api/crypt/v6/getAddress', params);
  return resp;
};

/**
 * 获取墨西哥邮政编码列表
 * @description @POST('/api/crypt/v6/getAddressPincode')
 */
export const fetchGetPincode = async (): Promise<ResponseBaseType<any>> => {
  const resp = await Api.post('/api/crypt/v6/getAddressPincode');
  return resp;
};

/**
 * 获取App配置,人脸最大错误次数,语音验证码状态
 * @description @Get(/api/crypt/v6/appConfig)
 * @param params
 * @returns
 */
export const fetchGetAppConfig = async (): Promise<UserResponseSpace.ResponseAppConfigData> => {
  const { appId, clientType } = await getProtocolParam();
  const resp = await Api.post('/api/crypt/v6/appConfig', { appId, clientType });
  return resp;
};

/**
 * 发送验证码-忘记密码
 * @description  @GET("/api/crypt/v6/forgetPwdSms")
 * @returns
 */
export const fetchForgetPwdOtp = async (params: {
  mobile: string;
}): Promise<UserResponseSpace.ResponseLoginDataType> => {
  const { appId, clientType } = await getProtocolParam();
  const resp = await Api.post('/api/crypt/v6/forgetPwdSms', {
    appId,
    clientType,
    mobile: params?.mobile,
  });
  return resp;
};

/**
 * 重置密码
 * @description  @GET("/api/crypt/v6/resetPwd")
 * @returns
 */
export const doResetPassword = async (
  params: Partial<UserRequestSpace.RequestLoginType>,
): Promise<UserResponseSpace.ResponseLoginDataType> => {
  const resp = await Api.post('/api/crypt/v6/resetPwd', params);
  return resp;
};

/**
 * 发送验证码-修改手机号
 * @description  @GET("/api/crypt/v6/modifyMobileSms")
 * @returns
 */
export const fetchModifyMobileOtp = async (params: {
  mobile: string;
}): Promise<UserResponseSpace.ResponseLoginDataType> => {
  const { appId, appVersion } = await getProtocolParam();
  const resp = await Api.post('/api/crypt/v6/modifyMobileSms', {
    appId,
    appVersion,
    mobile: params?.mobile,
  });
  return resp;
};

/**
 * 用户评论
 * @description @POST("/api/crypt/v6/saveComment")
 * @returns
 */
export const submitComment = async (
  params: UserRequestSpace.RequestSubmitCommentType,
): Promise<ResponseBaseType<string>> => {
  const resp = await Api.post('/api/crypt/v6/saveComment', params);
  return resp;
};

/**
 * 获取常见问题配置
 * @description @GET("/api/crypt/v6/getFaqList")
 * @returns
 */
export const fetchFaqConfig = async (): Promise<UserResponseSpace.ResponseFaqData> => {
  const { appId, appVersion } = await getProtocolParam();
  const resp = await Api.post('/api/crypt/v6/getFaqList', {
    appId,
    appVersion,
  });
  return resp;
};

/**
 * 发送语音验证码
 * @description @POST("/api/crypt/v6/user/getVoiceSmsCode")
 * @returns
 */
export const fetchVoiceOtp = async (
  params: Partial<UserRequestSpace.RequestLoginType>,
): Promise<ResponseBaseType<string>> => {
  const resp = await Api.post('/api/crypt/v6/user/getVoiceSmsCode', params);
  return resp;
};

/**
 * 复贷获取短信验证码
 * @description @POST("/api/crypt/v6/reLoan/getSmsCode")
 * @returns
 */
export const fetchReLoanOtp = async (
  params: Partial<UserRequestSpace.RequestOtp>,
): Promise<UserResponseSpace.ResponseLoginDataType> => {
  const resp = await Api.post('/api/crypt/v6/reLoan/getSmsCode', params);
  return resp;
};

/**
 * 跳过OTP验证
 * @description @POST("/api/crypt/v6/user/skipVerify")
 * */
export const fetchSkipVerify = async (): Promise<ResponseBaseType<string>> => {
  const resp = await Api.post('/api/crypt/v6/user/skipVerify');
  return resp;
};

/**
 * 验证复贷短信验证码
 * @description @POST("/api/crypt/v6/reLoan/validateSmsCode")
 * @returns
 */
export const fetchVerifyReLoanOtp = async (
  params: Partial<UserRequestSpace.RequestVerifyOtp>,
): Promise<ResponseBaseType<string>> => {
  const resp = await Api.post('/api/crypt/v6/reLoan/validateSmsCode', params);
  return resp;
};

/**
 * 验证首贷短信验证码
 * @description @POST("/api/crypt/v6/firstLoan/getSmsCode")
 */
export const fetchFirstLoanOtp = async (
  params: Partial<UserRequestSpace.RequestOtp>,
): Promise<ResponseBaseType<any>> => {
  const resp = await Api.post('/api/crypt/v6/firstLoan/getSmsCode', params);
  return resp;
};

/**
 * 验证首贷短信验证码
 * @todo
 * @description @POST("/api/crypt/v6/firstLoan/validateSmsCode")
 * @returns
 */
export const fetchVerifyFirstLoanOtp = async (
  params: Partial<UserRequestSpace.RequestVerifyOtp>,
): Promise<ResponseBaseType<any>> => {
  const resp = await Api.post('/api/crypt/v6/firstLoan/validateSmsCode', params);
  return resp;
};

/**
 * 获取用户绑卡列表
 * @description @POST("/api/crypt/v6/getUserBankCards")
 * @returns
 */
export const fetchUserBankcard =
  async (): Promise<EvidenceResponseSpace.ResponseMeBankcardData> => {
    const resp = await Api.post('/api/crypt/v6/getUserBankCards');
    return resp;
  };

/**
 * 获取用户自动代扣功能开启状态
 * @description @POST("/api/crypt/v6/getUserBankCards")
 * @returns
 */
export const fetchUserWithholdAuthorizeStatus = async (): Promise<
  ResponseBaseType<EvidenceVOSpace.RespWithholdAuthorization>
> => {
  const resp = await Api.post('/api/crypt/v6/withhold/authorizeStatus');
  return resp;
};

/**
 * 修改手机号验证密码
 * @description @POST("/api/v1/user/validateBeforeChangeMobile")
 * @returns
 */
export const fetchModifyMobileVerifyPwd = async (
  params: UserRequestSpace.RequestVerifyOtp,
): Promise<ResponseBaseType<string>> => {
  const resp = await Api.post('/api/crypt/v6/user/validateBeforeChangeMobile', params);
  return resp;
};

/**
 * 修改手机号
 * @description @POST("/api/v2/user/changeMobile")
 * @returns
 */
export const doModifyMobile = async (
  params: UserRequestSpace.RequestModifyMobile,
): Promise<ResponseBaseType<string>> => {
  const resp = await Api.post('/api/crypt/v6/user/changeMobile', params);
  return resp;
};

/**
 * 发送验证码-修改手机号
 * @description @POST("/api/v1/changeMobile/smsCode")
 * @returns
 */
export const fetchOtpByModifyPwd = async (
  params: UserRequestSpace.RequestOtp,
): Promise<ResponseBaseType<string>> => {
  const resp = await Api.post('/api/crypt/v6/changeMobile/smsCode', params);
  return resp;
};

/**
 * 获取当前第三方账户的绑定状态
 * @description @POST("/api/crypt/v6/user/checkOauth")
 * @returns
 */
export const getAccountBindingState3rd = async (): Promise<
  ResponseBaseType<UserVOSpace.Account3rdBindingStateData>
> => {
  const resp = await Api.post('/api/crypt/v6/user/checkOauth');
  return resp;
};

/**
 * 绑定第三方账户
 */
export const submitAccountBinding = async (
  params: UserVOSpace.Account3rdBindingData,
): Promise<ResponseBaseType<string>> => {
  const resp = await Api.post('/api/crypt/v6/user/oauth', params);
  return resp;
};

/**
 * 获取用户的未读消息状态
 */
export const getMessageUnReadCount = _.debounce(
  async (): Promise<ResponseBaseType<UserVOSpace.UnReadMessageCount>> => {
    const resp = await Api.post('/api/crypt/v6/message/unReadCount');
    return resp;
  },
  500,
  { leading: true },
);

/**
 *  查看消息
 */
export const fetchMessageRead = async (
  params: UserVOSpace.RequestMessageType,
): Promise<ResponseBaseType<any>> => {
  const resp = await Api.post('/api/crypt/v6/message/read', params);
  return resp;
};

/**
 * 消息列表
 * @param params
 * @returns
 */
export const getMessageList = async (
  params: UserVOSpace.RequestMessageType,
): Promise<ResponseBaseType<UserVOSpace.MessageListItem[]>> => {
  const resp = await Api.post('/api/crypt/v6/message/list', params);
  return resp;
};

/**
 * 根据条件查询优惠券列表
 * @param params
 * @returns
 */
export const fetchCouponsByConditions = async (
  params: UserRequestSpace.RequestCouponsByConditions,
): Promise<UserResponseSpace.ResponseCouponsList> => {
  const resp = await Api.post('/api/crypt/v7/user/coupons', params);
  return resp;
};
/**
 * 根据条件查询优惠券所有优惠券
 * @param params
 * @returns
 */
export const fetchAllCouponsByConditions = async (
  params: UserRequestSpace.RequestCouponsByConditions,
): Promise<UserResponseSpace.ResponseCouponsList> => {
  const resp = await Api.post('/api/crypt/v6/user/allCoupons', params);
  return resp;
};

/**
 * 获取用户得到优惠券
 * @param params
 * @returns
 */
export const fetchCouponsForShow = async (params: {
  includeInvite: 'YES' | 'NO';
}): Promise<UserResponseSpace.ResponseCouponsList> => {
  const resp = await Api.post('/api/crypt/v6/user/coupons/for/show', params);
  return resp;
};

/**
 * 获取邀请code
 * @param params
 * @returns
 */
export const fetchInviteCode = async (): Promise<UserResponseSpace.ResponseInviteUrl> => {
  const resp = await Api.post('/api/crypt/v6/invite/getInviteUrl');
  return resp;
};

/**
 * 获取活动资格
 * @param params
 * @returns
 */
export const fetchHasQualification =
  async (): Promise<UserResponseSpace.ResponseHasQualification> => {
    const resp = await Api.post('/api/crypt/v6/invite/hasQualification');
    return resp;
  };

/**
 * 当前日期是否在活动期间
 * @param params
 * @returns
 */
export const fetchIsInviteDuring = async (): Promise<UserResponseSpace.ResponseIsInviteDuring> => {
  const resp = await Api.post('/api/crypt/v6/invite/isInviteDuring');
  return resp;
};

/**
 * 获取富文本配置
 * @param params
 * @returns
 */
export const fetchRichTextConfig = async (params: {
  scene: 'INVITE' | 'COUPON_USAGE' | 'COUPON_MODAL_BE_INVITED';
}): Promise<UserResponseSpace.ResponseRichTextConfig> => {
  const resp = await Api.post('/api/crypt/v6/copywriter/config', params);
  return resp;
};

/**
 * 查看卡包
 * @param params
 * @returns
 */
export const fetchUserWaletInfo = async (): Promise<UserResponseSpace.ResponseWalletInfo> => {
  const resp = await Api.post('/api/crypt/v6/wallet/myWallet');
  return resp;
};

/**
 * 提现
 * @param params
 * @returns
 */
export const fetchWalletBalanceWithdraw = async (
  params: UserRequestSpace.RequestWalletBalanceWithdraw,
): Promise<ResponseBaseType<void>> => {
  const resp = await Api.post('/api/crypt/v6/wallet/withdraw', params);
  return resp;
};

/**
 * 查看卡包
 * @param params
 * @returns
 */
export const fetchUserWaletFlow = async (
  params: UserRequestSpace.RequestWalletBalanceFlow,
): Promise<ResponseBaseType<UserVOSpace.WalletFlowData>> => {
  const resp = await Api.post('/api/crypt/v6/wallet/flow/list', params);
  return resp;
};

/**
 * 获取vip配置规则
 * @param params
 * @returns
 */
export const fetchVipRule = async (): Promise<ResponseBaseType<UserVOSpace.VipRuleType>> => {
  const resp = await Api.post('/api/crypt/v6/vip/rules');
  return resp;
};

/**
 * 查看vip升级上新弹窗
 */
export const fetchVipPopData = async (): Promise<
  ResponseBaseType<UserVOSpace.VipPopDataItem[]>
> => {
  const resp = await Api.post('/api/crypt/v6/vip/pop');
  return resp;
};
/**
 * 获取vip等级
 * @param params
 * @returns
 */
export const fetchVipLevel = async (): Promise<ResponseBaseType<UserVOSpace.VIPInfoData>> => {
  const resp = await Api.post('/api/crypt/v6/vip/myLevel');
  return resp;
};

/**
 * 获取溢缴款
 * @param params
 * @returns
 */
export const fetchExcessPaymentBalance = async (): Promise<
  ResponseBaseType<UserVOSpace.ExcessPaymentCostData>
> => {
  const resp = await Api.post('/api/crypt/v6/wallet/excessRepayBalance');
  return resp;
};

/**
 * 获取溢缴款试算结果
 * @param params
 * @returns
 */
export const fetchExcessPaymentCostData = async (): Promise<
  ResponseBaseType<UserVOSpace.ExcessPaymentCostData>
> => {
  const resp = await Api.post('/api/crypt/v6/wallet/excessRepayBalance/costCal');
  return resp;
};

/**
 * 溢缴款提现
 * @param params
 * @returns
 */
export const fetchExcessPaymentBalanceWithdraw = async (): Promise<ResponseBaseType<void>> => {
  const resp = await Api.post('/api/crypt/v6/wallet/excessRepayBalance/withdraw');
  return resp;
};
/**
 * 发送OTP验证码
 * @description @POST("/api/crypt/v6/user/getOTP")
 * @returns
 */
export const fetchOtp = async (
  params: Partial<UserRequestSpace.RequestOtpType>,
): Promise<ResponseBaseType<void>> => {
  const resp = await Api.post('/api/crypt/v6/user/getOTP', params);
  return resp;
};
/**
 * 验证基本信息
 * @description @POST("/api/crypt/v6/user/changeMobile/validateUserExist")
 * @returns
 */
export const validateUserExist = async (
  params: Partial<UserRequestSpace.RequestValidateOriginalMobile>,
): Promise<ResponseBaseType<UserVOSpace.ValidateUserExist>> => {
  const resp = await Api.post('/api/crypt/v6/user/changeMobile/validateUserExist', params);
  return resp;
};
/**
 * 修改手机号
 * @description @POST("/api/crypt/v7/user/changeMobile")
 * @returns
 */
export const fetchChangeMobile = async (
  params: UserRequestSpace.RequestChangeMobile,
): Promise<ResponseBaseType<void>> => {
  const resp = await Api.post('/api/crypt/v7/user/changeMobile', params);
  return resp;
};
/**
 * 邮箱OTP
 * @description @POST("/api/crypt/v6/user/getEmailOTP")
 * @returns
 */
export const fetchEmailOtp = async (
  params: UserRequestSpace.RequestOtpType,
): Promise<ResponseBaseType<void>> => {
  const resp = await Api.post('/api/crypt/v6/user/getEmailOTP', params);
  return resp;
};
/**
 * 验证是否是本人
 * @description @POST("/api/crypt/v6/user/changeMobile/validateIsSelf")
 * @returns
 */
export const fetchValidateIsSelf = async (
  params: UserRequestSpace.ValidateIsSelf,
): Promise<ResponseBaseType<void>> => {
  const resp = await Api.post('/api/crypt/v6/user/changeMobile/validateIsSelf', params);
  return resp;
};
/**
 * 查询优惠券列表
 * @description @POST("/api/crypt/v6/wallet/redeemableCoupons/list")
 * @returns
 */
export const fetchRedeemCouponList = async (): Promise<UserResponseSpace.ResponseRedeemCoupons> => {
  const resp = await Api.post('/api/crypt/v6/wallet/redeemableCoupons/list');
  return resp;
};
/**
 * 兑换优惠券
 * @description @POST("/api/crypt/v6/wallet/redeemCoupons")
 * @returns
 */
export const submitRedeemCoupon = async (params: {
  redeemCouponId: number;
}): Promise<ResponseBaseType<void>> => {
  const resp = await Api.post('/api/crypt/v6/wallet/redeemCoupons', params);
  return resp;
};
