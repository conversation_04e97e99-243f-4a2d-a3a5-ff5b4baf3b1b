import { OrderVOSpace } from '@/types';
import { getProtocolParam } from '../../../utils/protocolParamAndLoginCacheHandle';
import Api from '../../api';
import { EvidenceRequestSpace } from '../../types/requestType';
import {
  ActivityResponseSpace,
  EvidenceResponseSpace,
  ResponseBaseType,
  UserResponseSpace,
} from '../../types/responseType';
import { UserEnumsSpace } from '@/enums';

/**
 * 上传图片
 * @description  @POST("/api/crypt/v6/fileUpload")
 * @param params
 * @returns
 */
export const fetchUploadImage = async (
  filePath: string,
  params: { cardType: string; fileType: string; bizType?: UserEnumsSpace.BizType },
): Promise<ResponseBaseType<string>> => {
  let data: any = Object.create(null);
  const { cardType, fileType, bizType = UserEnumsSpace.BizType.FIRST_LOAN_VERIFY } = params;
  const fileName = filePath.split('/').pop();
  let mediaType = 'image/jpg';
  if (fileType?.includes('.png')) {
    mediaType = 'image/png';
  }
  if (fileType?.includes('.jpeg')) {
    mediaType = 'image/jpeg';
  }
  if (fileType?.includes('.megvii')) {
    // 通用的 mime 格式
    mediaType = 'application/octet-stream';
  }
  const file = {
    uri: filePath,
    name: fileName,
    type: mediaType,
  };

  const formData = new FormData();
  formData.append('file', file);
  const resp = Api.upload('/api/crypt/v6/fileUpload', formData, {
    cardType,
    fileType,
    bizType,
  });
  return resp;
};

/**
 * 上传自拍照
 * @description  @POST("/api/crypt/v6/user/selfie")
 * @param params
 * @returns
 */
export const fetchSelfieImage = async (filePath: string): Promise<ResponseBaseType<string>> => {
  const formData = new FormData();
  let mediaType = 'image/jpg';
  let file = null;
  if (filePath !== '') {
    const fileName = filePath.split('/').pop();
    if (fileName?.includes('.png')) {
      mediaType = 'image/png';
    }
    file = {
      uri: filePath,
      name: fileName,
      type: mediaType,
    };
  }
  formData.append('file', file);
  const resp = Api.upload('/api/crypt/v6/user/selfie', formData, {
    fileType: mediaType,
    cardType: 'face',
    timeout: 1000 * 60
  });
  return resp;
};

/**
 * 上传图片进行OCR解析
 * @description  @POST("/api/crypt/v7/nubarium/idCardOcr")
 * @param params
 * @returns
 */
export const fetchUploadImageAndOCR = async (
  params: EvidenceRequestSpace.RequestUploadImageAndOcr,
): Promise<ResponseBaseType<OrderVOSpace.ImageOcrDataV7Type>> => {
  const resp = await Api.post('/api/crypt/v7/nubarium/idCardOcr', params);
  return resp;
};

/**
 * 获取App配置,人脸最大错误次数,语音验证码状态
 * @description  @POST("/api/crypt/v6/faceid/getBizToken")
 * @param params
 * @returns
 */
export const fetchGetBizToken = async (params: {
  get_liveness_video: number;
}): Promise<EvidenceResponseSpace.ResponseFaceBizTokenData> => {
  const resp = await Api.post('/api/crypt/v6/faceid/getBizToken', params);
  return resp;
};

/**
 * 获取复贷App配置,人脸最大错误次数,语音验证码状态
 * @description  @POST("/api/crypt/v6/reLaon/faceid/getBizToken")
 * @param params
 * @returns
 */
export const fetchReloanGetBizToken = async (params: {
  get_liveness_video: number;
}): Promise<EvidenceResponseSpace.ResponseFaceBizTokenData> => {
  const resp = await Api.post('/api/crypt/v6/reLaon/faceid/getBizToken', params);
  return resp;
};

/**
 * 验证用户face
 * @description @POST("/api/crypt/v6/faceid/verify")
 */
export const faceVerify = async (
  params: EvidenceRequestSpace.RequestFaceVerify,
): Promise<EvidenceResponseSpace.ResponseFaceVerifyData> => {
  let _params = params;
  // Object.assign(_params, {
  //   mockFaceIdCode: '1000',
  // });
  const resp = await Api.post('/api/crypt/v6/faceid/verify', _params);
  return resp;
};

/**
 * 复贷验证用户face
 * @description @POST("/api/crypt/v6/reLoan/faceid/verify")
 */
export const reloanFaceVerify = async (
  params: EvidenceRequestSpace.RequestFaceVerify,
): Promise<EvidenceResponseSpace.ResponseFaceVerifyData> => {
  let _params = { ...params, bizType: UserEnumsSpace.BizType.RELOAN };
  // Object.assign(_params, {
  //   mockFaceIdCode: '1000',
  // });
  const resp = await Api.post('/api/crypt/v6/reLoan/faceid/verify', _params);
  return resp;
};

/**
 * 跳过活体识别
 * @description @POST("/api/crypt/v6/faceid/verify")
 */
export const faceSkip = async (
  params: EvidenceRequestSpace.RequestSkipLiveType,
): Promise<ResponseBaseType<string>> => {
  let _params = params;
  const resp = await Api.post('/api/crypt/v6/faceid/skip', _params);
  return resp;
};

/** 获取 advance.ai 活体 license */
export const fetchFaceLicense =
  async (): Promise<EvidenceResponseSpace.ResponseFaceLicenseData> => {
    const resp = await Api.post('/api/crypt/v6/advance/getLicense');
    return resp;
  };

/**
 * 验证 advance.ai 活体结果
 */
export const faceDetect = async (
  params: EvidenceRequestSpace.RequestFaceDetect,
): Promise<EvidenceResponseSpace.ResponseFaceDetectData> => {
  let _params = params;
  // Object.assign(_params, {
  //   mockSimilarity: '88',
  // });
  const resp = await Api.post('/api/crypt/v6/advance/liveDetect', _params);
  return resp;
};

/**
 * 获取app版本号
 * @description @GET("/api/crypt/v6/appVersion")
 */
export const fetchAppVersion = async (): Promise<UserResponseSpace.ResponseAppVersionData> => {
  const { appId, appVersion, clientType } = await getProtocolParam();
  const resp = await Api.post('/api/crypt/v6/appVersion', {
    appId,
    appVersion,
    clientType,
  });
  return resp;
};

/** 上报fcmToken */
export const uploadFcmToken = async (): Promise<ResponseBaseType<string>> => {
  const resp = await Api.post('/api/crypt/v6/user/saveFcmToken');
  return resp;
};
/**
 * 获取App配置,人脸最大错误次数,语音验证码状态
 * @description  @POST("/api/crypt/v7/faceid/getBizToken")
 * @param params
 * @returns
 */
export const fetchGetBizTokenV7 = async (params: {
  bizType: UserEnumsSpace.BizType;
}): Promise<EvidenceResponseSpace.ResponseFaceBizTokenData> => {
  const resp = await Api.post('/api/crypt/v7/faceid/getBizToken', params);
  return resp;
};
/**
 * 验证用户face
 * @description @POST("/api/crypt/v7/faceid/verify")
 */
export const faceVerifyV7 = async (
  params: EvidenceRequestSpace.RequestFaceVerify,
): Promise<EvidenceResponseSpace.ResponseFaceVerifyData> => {
  const resp = await Api.post('/api/crypt/v7/faceid/verify', params);
  return resp;
};
/**
 * 上传图片
 * @description  @POST("/api/crypt/v7/fileUpload")
 * @param params
 * @returns
 */
export const fetchUploadImageV7 = async (
  filePath: string,
  params: { cardType: string; fileType: string; bizType: UserEnumsSpace.BizType },
): Promise<ResponseBaseType<string>> => {
  let data: any = Object.create(null);
  const { cardType, fileType, bizType } = params;
  const fileName = filePath.split('/').pop();
  let mediaType = 'image/jpg';
  if (fileType?.includes('.png')) {
    mediaType = 'image/png';
  }
  if (fileType?.includes('.jpeg')) {
    mediaType = 'image/jpeg';
  }
  if (fileType?.includes('.megvii')) {
    // 通用的 mime 格式
    mediaType = 'application/octet-stream';
  }
  const file = {
    uri: filePath,
    name: fileName,
    type: mediaType,
  };

  const formData = new FormData();
  formData.append('file', file);
  const resp = Api.upload('/api/crypt/v7/fileUpload', formData, {
    cardType,
    fileType,
    bizType,
  });
  return resp;
};

/**
 * 获取启动页配置
 * @description  @POST("/api/crypt/v6/app/getLaunchScreen")
 * @param params
 * @returns
 */
export const getLaunchScreen = async (): Promise<
  ResponseBaseType<ActivityResponseSpace.LaunchScreenConfig>
> => {
  const resp = await Api.post('/api/crypt/v6/app/getLaunchScreen');
  return resp;
};
