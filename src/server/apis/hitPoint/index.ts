import Api from '../../api';
import { EventPointRequestSpace } from '../../types/requestType';
import { ResponseBaseType } from '../../types/responseType';

/** 上传埋点信息  /api/crypt/v6/user/buryPoint */
export const fetchUserEventPointUpload = async (
  params: Partial<EventPointRequestSpace.RequestEventPointType>,
): Promise<ResponseBaseType<any>> => {
  const resp = await Api.post('/api/crypt/v6/user/buryPoint', params);
  return resp;
};

/** 上传埋点信息  /api/crypt/v6/user/buryPoint */
export const fetchLogRecordUpload = async (
  params: Partial<EventPointRequestSpace.RequestLogRecordType>,
): Promise<ResponseBaseType<any>> => {
  const resp = await Api.post('/api/crypt/v6/app/logRecord', params);
  return resp;
};

/** 上传错误日志信息 /api/crypt/v6/app/log */
export const fetchUploadErrorLog = async (
  params: Partial<EventPointRequestSpace.RequestUploadErrorLogType>,
): Promise<ResponseBaseType<any>> => {
  const resp = await Api.post('/api/crypt/v6/app/log', params);
  return resp;
};
