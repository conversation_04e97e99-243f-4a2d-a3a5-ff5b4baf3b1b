import { EvidenceVOSpace, OrderVOSpace } from '@/types';
import Api from '../../api';
import { EvidenceRequestSpace } from '../../types/requestType';
import { EvidenceResponseSpace, ResponseBaseType } from '../../types/responseType';
import { BaseEnumsSpace } from '@/enums';

/**
 * 保存基本信息
 * @description @GET("/api/crypt/v6/user/basicDetail")
 * @param params
 * @returns
 */
export const fetchSaveBasicInfo = async (
  params: Partial<EvidenceRequestSpace.RequestBasicInfoDetail>,
): Promise<ResponseBaseType<any>> => {
  const resp = await Api.post('/api/crypt/v6/user/basicDetail', params);
  return resp;
};

/**
 * 获取用户基本信息
 * @description @GET("/api/crypt/v6/user/basicDetail/info")
 */
export const fetchBasicInfo = async (): Promise<EvidenceResponseSpace.ResponceBasicInfoDetail> => {
  const resp = await Api.post('/api/crypt/v6/user/basicDetail/info');
  return resp;
};

/**
 * 保存用户工作
 * @description @POST("/api/crypt/v6/user/professionalDetail")
 * @param params
 * @returns
 */
export const fetchSaveProfessionalInfo = async (
  params: Partial<EvidenceRequestSpace.RequestWorkInfoDetail>,
): Promise<ResponseBaseType<any>> => {
  const resp = await Api.post('/api/crypt/v6/user/professionalDetail', params);
  return resp;
};

/**
 * 保存联系人信息
 * @description @POST("/api/crypt/v6/user/contact")
 * @param params
 * @returns
 */
export const fetchSaveContactInfo = async (
  params: Partial<EvidenceRequestSpace.RequestContactInfo>,
): Promise<ResponseBaseType<any>> => {
  const resp = await Api.post('/api/crypt/v6/user/contact', params);
  return resp;
};

/**
 * 获取用户联系人列表信息
 * @description @GET("/api/crypt/v6/user/contact/info")
 * @returns
 */
export const fetchContact = async (): Promise<EvidenceResponseSpace.ResponceContactsInfoDetail> => {
  const resp = await Api.post('/api/crypt/v6/user/contact/info');
  return resp;
};

/**
 * 获取用户kyc回填信息
 * @description @POST("/api/crypt/v6/ocr/lastResult")
 */
export const fetchOcrInfo = async (): Promise<
  ResponseBaseType<OrderVOSpace.ImageOcrDataV7Type>
> => {
  const resp = await Api.post('/api/crypt/v6/ocr/lastResult');
  return resp;
};

/**
 * 获取自拍照的照片
 */
export const fetchSelfieInfo = async (): Promise<ResponseBaseType<string>> => {
  const resp = await Api.post('/api/crypt/v6/user/selfie/lastResult');
  return resp;
};

/**
 * 保存CURP信息
 * @description @POST("/api/crypt/v6/user/curp")
 * @param params
 * @returns
 */
export const fetchSaveCurp = async (
  params: Partial<EvidenceRequestSpace.RequestEvidenceType>,
): Promise<ResponseBaseType<any>> => {
  const resp = await Api.post('/api/crypt/v6/user/curp', params);
  return resp;
};

/**
 * 获取银行列表
 * @description @GET("/api/crypt/v6/bankList")
 * @param params
 * @returns
 */
export const fetchGetBankList = async (): Promise<EvidenceResponseSpace.ResponseBankListData> => {
  const resp = await Api.post('/api/crypt/v6/bankList');
  return resp;
};

/**
 * 保存用户的增信
 * @description @POST("/api/crypt/v6/user/enhanceCreditDetail")
 */
export const fetchEnhanceCreditDetail = async (
  params: Partial<EvidenceRequestSpace.RequestEnhanceCredit>,
): Promise<ResponseBaseType<string>> => {
  const resp = await Api.post('/api/crypt/v7/user/enhanceCreditDetail', params);
  return resp;
};

/**
 * 上传银行卡信息
 * @description  @POST("/api/crypt/v6/bankCard")
 * @param params
 * @returns
 */
export const fetchGetBankCardInfo = async (
  params: Partial<EvidenceRequestSpace.RequestBankItem>,
): Promise<ResponseBaseType<EvidenceVOSpace.RespBankCard>> => {
  const resp = await Api.post('/api/crypt/v6/bankCard', params);
  return resp;
};

/**
 * 获取银行卡补件默认文案
 * @description  @POST("/crypt/v6/user/default/bankcard")
 * @param params
 * @returns
 */
export const fetchBankCardDefaultText =
  async (): Promise<EvidenceResponseSpace.ResponceSupplyBankAccount> => {
    const resp = await Api.post('/api/crypt/v6/user/default/bankcard');
    return resp;
  };

/**
 * 银行卡补件提交信息
 * @description  @POST("/crypt/v6/user/bankcard/supply/submit")
 * @param params
 * @returns
 */
export const fetchBankCardSupplySubmit = async (
  params: Partial<EvidenceRequestSpace.RequestBankItem>,
): Promise<ResponseBaseType<EvidenceVOSpace.RespBankCard>> => {
  const resp = await Api.post('/api/crypt/v6/user/bankcard/supply/submit', params);
  return resp;
};

/**
 * 推送风控问券审核
 * @description  @POST("/crypt/v6/rkPush/questionnaire")
 * @param params
 * @returns
 */
export const fetchRKPushQuestionnaire = async (): Promise<ResponseBaseType<any>> => {
  const resp = await Api.post('/api/crypt/v6/rkPush/questionnaire');
  return resp;
};

/**
 * 查询风控审核页的状态
 * @description  @POST("/crypt/v6/rkPush/questionnaire")
 * @param params
 * @returns
 */
export const fetchRKQuestionnaireEnterLabel = async (): Promise<ResponseBaseType<any>> => {
  const resp = await Api.post('/api/crypt/v6/rk/questionnaire/enterLabel');
  return resp;
};

/**
 * 获取首贷风控问券列表
 * @description  @POST("/crypt/v6/questionGroup/list")
 * @param params
 * @returns
 */
export const fetchRKQuestionList =
  async (): Promise<EvidenceResponseSpace.ResponceRKQuestionList> => {
    const resp = await Api.post('/api/crypt/v6/questionGroup/list', {
      location: BaseEnumsSpace.EQuestionLocationType.FIRST_LOAN,
    });
    return resp;
  };

/**
 * 提交首贷问券结果
 * @description  @POST("/crypt/v7/questionnaire/submit")
 * @param params
 * @returns
 */
export const fetchRKQuestionSubmit = async (
  params: Partial<EvidenceRequestSpace.RequestRKQuestionAnswerDataType>,
): Promise<ResponseBaseType<any>> => {
  const resp = await Api.post('/api/crypt/v6/questionGroup/submit', params);
  return resp;
};

/**
 * 提交复贷问卷接口
 * @description  @POST("/api/crypt/v7/questionnaire/submit")
 * @param params
 * @returns
 */
export const fetchQuestionnaireSubmit = async (
  params: EvidenceRequestSpace.RequestQuestionnaireSubmit,
): Promise<ResponseBaseType<string>> => {
  const resp = await Api.post('/api/crypt/v7/questionnaire/submit', params);
  return resp;
};
/**
 * 获取复贷风控问券列表
 * @description  @POST("/crypt/v6/questionGroup/list")
 * @param params
 * @returns
 */
export const fetchRLoanQuestionList =
  async (): Promise<EvidenceResponseSpace.ResponceRKQuestionList> => {
    const resp = await Api.post('/api/crypt/v6/questionGroup/list', {
      location: BaseEnumsSpace.EQuestionLocationType.RELOAN,
    });
    return resp;
  };

/**
 * 提交复贷问券结果
 * @description  @POST("/crypt/v7/questionnaire/submit")
 * @param params
 * @returns
 */
export const fetchReloanQuestionSubmit = async (
  params: Partial<EvidenceRequestSpace.RequestRKQuestionAnswerDataType>,
): Promise<ResponseBaseType<any>> => {
  const resp = await Api.post('/api/crypt/v6/questionGroup/submit', params);
  return resp;
};

/**
 * @description  @POST("/api/crypt/v6/user/skipGpBind")
 * @param params
 * @returns
 */
export const fetchSkipGPBind = async (): Promise<ResponseBaseType<string>> => {
  const resp = await Api.post('/api/crypt/v6/user/skipGpBind');
  return resp;
};
/**
 * @description  @POST("/api/crypt/v6/withholdAuthorization/open")
 * @param params
 * @returns
 */
export const fetchWithholdAuthorizationOpen = async (params?: {
  cardNo: string;
}): Promise<ResponseBaseType<string>> => {
  const resp = await Api.post('/api/crypt/v6/withholdAuthorization/open', params);
  return resp;
};
/**
 * @description  @POST("/api/crypt/v6/withholdAuthorization/close")
 * @param params
 * @returns
 */
export const fetchWithholdAuthorizationClose = async (params?: {
  cardNo: string;
}): Promise<ResponseBaseType<null>> => {
  const resp = await Api.post('/api/crypt/v6/withholdAuthorization/close', params);
  return resp;
};
