import { BaseEnumsSpace } from '@/enums';
import { ResponseBaseType } from '../types/responseType';

type ApiResult<T> = {
  error: Error | null;
  data: T;
};
export async function responseHandler<T>(
  promise: Promise<ResponseBaseType<T>>,
): Promise<ApiResult<T>> {
  try {
    const response = await promise;
    if (response?.code === BaseEnumsSpace.ENetworkStatus.SUCCESS) {
      return { error: null, data: response.data || ({} as T) };
    }
    return { error: new Error(response?.msg || 'Request failed'), data: {} as T };
  } catch (error) {
    return { error: error as Error, data: {} as T };
  }
}
