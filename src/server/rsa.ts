import { Log, RSACrypt } from '@/utils';
import MD5 from 'react-native-md5';
import { BaseConfig } from '../baseConfig';
/** 公钥 */

/** 公钥加密 */
export const encrypt = async (data: Object): Promise<string> => {
  try {
    const encryptData = await RSACrypt.rsaEncryptByPubKey(
      JSON.stringify(data),
      BaseConfig.rsaPublicKey,
    );
    return encryptData;
  } catch (e) {
    Log.error(`# encrypt `, { data, error: e });
  }
  return '';
};

/** 公钥解密 */
export const decrypt = async <T>(data: string = ''): Promise<T | string> => {
  let result: T | string = '';
  try {
    const decryptData = await RSACrypt.rsaDecryptByPubKey(data || '', BaseConfig.rsaPublicKey);
    // 处理序列化后的对象、数组
    result = JSON.parse(decryptData);
  } catch (e) {
    const decryptData = await RSACrypt.rsaDecryptByPubKey(data || '', BaseConfig.rsaPublicKey);
    // 处理序列化后的字符串
    result = decryptData;
  }
  return result;
};

/** 加密数据签名 encryptedData=xxx&timestamp=1234567890123&{salt} */
export const md5Sign = (data: string): { dataNow: number; signData: string } => {
  let dataNow = Date.now();
  let signData = '';
  try {
    const signNativeData = `encryptedData=${data}&timestamp=${dataNow}&${BaseConfig.apiMd5Salt}`;
    signData = MD5.hex_md5(signNativeData);
  } catch (e) {
    Log.error(`# func md5Sign`, e);
  }
  return {
    dataNow,
    signData,
  };
};
// /** 测试公钥加密 私钥解密 */
// export const testPubKeyCryptAndPriKeyDecrypt = async () => {
//   const data = JSON.stringify(userProcessData);
//   const cryptData = await RSACrypt.rsaEncryptByPubKey(
//     data,
//     BaseConfig.rsaPublicKey,
//   );
//   const deCryptData = await RSACrypt.rsaDecryptByPreKey(
//     cryptData,
//     BaseConfig.rsaPrivatecKey,
//   );
//   console.log(
//     'data.length = ',
//     data.length,
//     'deCryptData.length',
//     deCryptData.length,
//   );
//   console.log(`data=${data}&cryptData=${cryptData}&deCryptData=${deCryptData}`);
// };

// /** 测试私钥加密 公钥解密 */
// export const testPriKeyCryptAndPubKeyDecrypt = async () => {
//   const data = JSON.stringify(userProcessData);
//   const cryptData = await RSACrypt.rsaEncryptByPreKey(
//     data,
//     BaseConfig.rsaPrivatecKey,
//   );

//   const deCryptData = await RSACrypt.rsaDecryptByPubKey(
//     cryptData,
//     BaseConfig.rsaPublicKey,
//   );
//   console.log(
//     'data.length = ',
//     data.length,
//     'deCryptData.length',
//     deCryptData.length,
//   );
//   console.log(`data=${data}&cryptData=${cryptData}&deCryptData=${deCryptData}`);
// };

// export const testEncryptMd5Sign = async () => {
//   const data = await encrypt(JSON.stringify(userProcessData));
//   return md5Sign(data);
// };
