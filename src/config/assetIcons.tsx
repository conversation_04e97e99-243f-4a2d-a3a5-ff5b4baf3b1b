import { fixPixel } from '@/utils';
import { Dimensions } from 'react-native';

type TIconObj = {
  source: any;
  height?: any;
  width?: any;
};

export const Images = {
  //  权限申请弹窗
  _permissionDetail: {
    source: require('@/image/permission/detail-icon.png'),
    height: 32,
    width: 32,
  },

  _permissionToSettingTipsIcon: {
    source: require('@/image/permission/to-setting-tip-icon.png'),
  },

  _permissionLocation: {
    source: require('@/image/permission/location-icon.png'),
    height: 18,
    width: 18,
  },

  _permissionNotify: {
    source: require('@/image/permission/notify-icon.png'),
    height: 18,
    width: 18,
  },

  _permissionCamera: {
    source: require('@/image/permission/camera-icon.png'),
    height: 18,
    width: 18,
  },

  _permissionMessage: {
    source: require('@/image/permission/message-icon.png'),
    height: 18,
    width: 18,
  },

  _modalClose: {
    source: require('@/image/Base/modal-close.png'),
    height: 24,
    width: 24,
  },
  _radioSelected: {
    source: require('@/image/Base/radio-selected.png'),
    height: 24,
    width: 24,
  },
  _radioUnSelect: {
    source: require('@/image/Base/radio-unselect.png'),
    height: 24,
    width: 24,
  },
  _arrowRight: {
    source: require('@/image/Base/arrow-right.png'),
    width: 24,
    height: 24,
  },
  _arrowBottom: {
    source: require('@/image/Base/arrow-bottom.png'),
    width: 24,
    height: 24,
  },
  _arrowTop: {
    source: require('@/image/Base/arrow-top.png'),
    width: 24,
    height: 24,
  },
  _question: {
    source: require('@/image/Base/question.png'),
    height: 16,
    width: 16,
  },
  _maintainer: {
    source: require('@/image/Base/maintainer-icon.png'),
    height: 140,
    width: 140,
  },

  _myCouponIcon: {
    source: require('@/image/My/my-coupon.png'),
    height: 34,
    width: 34,
  },
  _myRecordIcon: {
    source: require('@/image/My/my-record.png'),
    height: 34,
    width: 34,
  },
  _myWarnIcon: {
    source: require('@/image/My/my-warn-icon.png'),
    height: 20,
    width: 20,
  },
  _rightIcon: {
    source: require('@/image/My/right-icon.png'),
    height: 24,
    width: 24,
  },

  _star: {
    source: require('@/image/My/star-icon.png'),
    height: 40,
    width: 40,
  },
  _greyStar: {
    source: require('@/image/My/grey-star-icon.png'),
    height: 40,
    width: 40,
  },

  _messageWhite: {
    source: require('@/image/My/message-white-icon.png'),
    width: 24,
    height: 24,
  },

  _messageBlack: {
    source: require('@/image/My/message-black-icon.png'),
    width: 24,
    height: 24,
  },

  /** 银行卡列表页面 */
  _clabeBg: {
    source: require('@/image/My/clabe-bg.png'),
    width: '100%',
    // height: 24,
  },

  _amountUpIcon: {
    source: require('@/image/My/bind-google-amount-up-icon.png'),
    width: 24,
    height: 24,
  },

  _infoIcon: {
    source: require('@/image/Base/info.png'),
    height: 24,
    width: 24,
  },

  _infoIconYellow: {
    source: require('@/image/Base/info-yellow.png'),
    height: 24,
    width: 24,
  },

  _correctIcon: {
    source: require('@/image/Base/right.png'),
    height: 20,
    width: 20,
  },

  _rePaymentcopyIcon: {
    source: require('@/image/Repayment/copy-icon.png'),
    height: 24,
    width: 24,
  },

  _likeIcon: {
    source: require('@/image/Repayment/like-icon.png'),
    height: 24,
    width: 24,
  },

  _successIcon: {
    source: require('@/image/Repayment/success-icon.png'),
    height: 24,
    width: 24,
  },

  _speIcon: {
    source: require('@/image/Repayment/spe-icon.png'),
    height: 18,
    width: 55,
  },

  _greyRightIcon: {
    source: require('@/image/Repayment/grey-icon.png'),
    height: 16,
    width: 16,
  },

  _paynetImage: {
    source: require('@/image/Repayment/paynet-image.png'),
    height: 50,
    width: 160,
  },

  _directDebitIimit: {
    source: require('@/image/Repayment/direct-debit-limit.png'),
    width: 16,
    height: 16,
  },

  _directDebitFail: {
    source: require('@/image/Repayment/direct-debit-fail.png'),
    width: 16,
    height: 16,
  },

  _directDebitProcess: {
    source: require('@/image/Repayment/direct-debit-process.png'),
    width: 16,
    height: 16,
  },

  _vector: {
    source: require('@/image/Base/vector-icon.png'),
    height: 12,
    width: 12,
  },

  _suffix: {
    source: require('@/image/Base/suffix-icon.png'),
    height: 24,
    width: 24,
  },

  _clearIcon: {
    source: require('@/image/Base/clear-icon.png'),
    height: 20,
    width: 20,
  },

  _onlineRepayIcon: {
    source: require('@/image/Repayment/repay_online_icon.png'),
    height: 32,
    width: 32,
  },

  _baseIndeterminate: {
    source: require('@/image/Base/indeterminate-icon.png'),
    height: 20,
    width: 20,
  },

  _baseCheck: {
    source: require('@/image/Base/check-icon.png'),
    height: 20,
    width: 20,
  },

  _notify: {
    source: require('@/image/Wait/notify-icon.png'),
    height: 48,
    width: 48,
  },

  _auth: {
    source: require('@/image/Login/auth.png'),
    height: 28,
    width: 28,
  },

  _phoneIcon: {
    source: require('@/image/Login/phone.png'),
    width: 48,
    height: 48,
  },

  _loginRightIcon: {
    source: require('@/image/Login/right.png'),
    width: 48,
    height: 48,
  },

  _couponBg: {
    source: require('@/image/Component/coupon-bg.png'),
    width: fixPixel({ size: 311 }),
    height: fixPixel({ size: 338 }),
  },

  _closeEye: {
    source: require('@/image/Component/close-eye.png'),
    width: 24,
    height: 24,
  },

  _pedentContainer: {
    source: require('@/image/Component/pedent.png'),
    width: 43,
    height: 48,
  },

  _pedentClose: {
    source: require('@/image/Component/pedent-close.png'),
    width: 13,
    height: 13,
  },

  _openEye: {
    source: require('@/image/Component/open-eye.png'),
    width: 24,
    height: 24,
  },

  _hotLineWhite: {
    source: require('@/image/Component/hot-line-white.png'),
    width: 24,
    height: 24,
  },

  _hotLineBlack: {
    source: require('@/image/Component/hotline.png'),
    width: 24,
    height: 24,
  },

  _logoTitle: {
    source: require('@/image/Component/logo-title.png'),
    width: 132,
    height: 14,
  },

  _logoGreyTitle: {
    source: require('@/image/Component/logo-grey-title.png'),
    width: 132,
    height: 14,
  },

  _backIcon: {
    source: require('@/image/Component/back-icon.png'),
    width: 24,
    height: 24,
  },

  _whiteBackIcon: {
    source: require('@/image/Component/white-back-icon.png'),
    width: 24,
    height: 24,
  },

  _passwordLock: {
    source: require('@/image/Base/lock.png'),
    width: 48,
    height: 48,
  },

  _blackNotice: {
    source: require('@/image/Base/notice.png'),
    width: 20,
    height: 20,
  },

  _polygonIcon: {
    source: require('@/image/Base/polygon-icon.png'),
    width: 22,
    height: 17,
  },

  _radioUnchecked: {
    source: require('@/image/Base/radio-un-checked.png'),
    width: 24,
    height: 24,
  },

  _radioChecked: {
    source: require('@/image/Base/radio-checked.png'),
    width: 24,
    height: 24,
  },

  _fromRadioUnchecked: {
    source: require('@/image/Component/form-radioUnchecked.png'),
    width: 20,
    height: 20,
  },

  _fromRadioChecked: {
    source: require('@/image/Component/form-radioChecked.png'),
    width: 20,
    height: 20,
  },

  _fromCheckboxUnchecked: {
    source: require('@/image/Component/form-checkboxUnchecked.png'),
    width: 20,
    height: 20,
  },

  _fromCheckboxChecked: {
    source: require('@/image/Component/form-checkboxChecked.png'),
    width: 20,
    height: 20,
  },

  _whiteNotice: {
    source: require('@/image/Base/notice-white.png'),
    width: 24,
    height: 24,
  },

  _lockTipIcon: {
    source: require('@/image/Home/lock.png'),
    height: 8,
    width: 7,
  },

  _soundBlackIcon: {
    source: require('@/image/Home/sound-black-icon.png'),
    height: 20,
    width: 20,
  },

  _quickLoan: {
    source: require('@/image/Home/quick-loan.png'),
    height: 32,
    width: 32,
  },

  _lowerInterest: {
    source: require('@/image/Home/lower-interest.png'),
    height: 32,
    width: 32,
  },

  _sendProfile: {
    source: require('@/image/Home/send-profile.png'),
    height: 40,
    width: 40,
  },

  _beEvaluated: {
    source: require('@/image/Home/be-evaluated.png'),
    height: 40,
    width: 40,
  },

  _optainEvaluatedLoan: {
    source: require('@/image/Home/ontain-evaluated-loan.png'),
    height: 40,
    width: 40,
  },

  _rightTrigel: {
    source: require('@/image/Home/right-trigle.png'),
    height: 16,
    width: 16,
  },

  _homeCardLeftDecoration: {
    source: require('@/image/Home/home-card-left.png'),
    height: 6,
    width: 53,
  },
  _homeCardRightDecoration: {
    source: require('@/image/Home/home-card-right.png'),
    height: 6,
    width: 53,
  },

  _homeActive: {
    source: require('@/image/Component/home-active.png'),
    height: 24,
    width: 24,
  },

  _homeDisActive: {
    source: require('@/image/Component/home-disactive.png'),
    height: 24,
    width: 24,
  },

  _walletActive: {
    source: require('@/image/Component/wallet-active.png'),
    height: 24,
    width: 24,
  },

  _walletDisActive: {
    source: require('@/image/Component/wallet-disactive.png'),
    height: 24,
    width: 24,
  },

  _userActive: {
    source: require('@/image/Component/user-active.png'),
    height: 24,
    width: 24,
  },

  _userDisActive: {
    source: require('@/image/Component/user-disactive.png'),
    height: 24,
    width: 24,
  },

  _userIcon: {
    source: require('@/image/User/user.png'),
    height: 35,
    width: 35,
  },

  _faqIcon: {
    source: require('@/image/User/faq.png'),
    height: 24,
    width: 24,
  },

  _transhIcon: {
    source: require('@/image/User/trash.png'),
    height: 24,
    width: 24,
  },

  _cardIcon: {
    source: require('@/image/User/card.png'),
    height: 24,
    width: 24,
  },

  _hotlineIcon: {
    source: require('@/image/User/hotline.png'),
    height: 24,
    width: 24,
  },

  _privacyIcon: {
    source: require('@/image/User/privacy.png'),
    height: 24,
    width: 24,
  },

  _mobileIcon: {
    source: require('@/image/User/mobile.png'),
    height: 24,
    width: 24,
  },

  _lockIcon: {
    source: require('@/image/User/lock.png'),
    height: 24,
    width: 24,
  },

  _copyIcon: {
    source: require('@/image/User/copy.png'),
    height: 24,
    width: 24,
  },

  _hotlinePhoneIcon: {
    source: require('@/image/User/hotline-phone.png'),
    height: 24,
    width: 24,
  },

  _sound: {
    source: require('@/image/Component/sound.png'),
    height: 20,
    width: 20,
  },

  _coin: {
    source: require('@/image/Component/coin.png'),
    height: 24,
    width: 24,
  },

  _minCoin: {
    source: require('@/image/Component/min-coin.png'),
    height: 24,
    width: 24,
  },

  _maxCoin: {
    source: require('@/image/Component/max-coin.png'),
    height: 24,
    width: 24,
  },

  _oneCoinActive: {
    source: require('@/image/Component/one-coin-active.png'),
    height: 24,
    width: 24,
  },

  _twoCoinActive: {
    source: require('@/image/Component/two-coin-active.png'),
    height: 24,
    width: 24,
  },

  _threeCoinActive: {
    source: require('@/image/Component/three-coin-active.png'),
    height: 24,
    width: 24,
  },

  _oneCoin: {
    source: require('@/image/Component/one-coin.png'),
    height: 24,
    width: 24,
  },

  _twoCoin: {
    source: require('@/image/Component/two-coin.png'),
    height: 24,
    width: 24,
  },

  _threeCoin: {
    source: require('@/image/Component/three-coin.png'),
    height: 24,
    width: 24,
  },

  _fourCoin: {
    source: require('@/image/Component/four-coin.png'),
    height: 24,
    width: 24,
  },

  _twoCoinDisable: {
    source: require('@/image/Component/two-coin-disable.png'),
    height: 24,
    width: 24,
  },

  _threeCoinDisable: {
    source: require('@/image/Component/three-coin-disable.png'),
    height: 24,
    width: 24,
  },

  _fourCoinDisable: {
    source: require('@/image/Component/four-coin-disable.png'),
    height: 24,
    width: 24,
  },

  _reCycle: {
    source: require('@/image/Home/re-cycle.png'),
    height: 18,
    width: 18,
  },

  _processLine: {
    source: require('@/image/Component/process-line.png'),
    height: 1,
    width: 36,
  },

  _blueInfo: {
    source: require('@/image/Component/blue-info.png'),
    height: 14,
    width: 14,
  },

  _grayInfo: {
    source: require('@/image/Component/gray-info.png'),
    height: 12,
    width: 12,
  },

  _evidenceBasicInfo: {
    source: require('@/image/Evidence/basic_info_icon.png'),
    width: 24,
    height: 24,
  },

  _evidenceBasicReleation: {
    source: require('@/image/Evidence/basic_relation_icon.png'),
    width: 24,
    height: 24,
  },

  _evidenceFrontCard: {
    source: require('@/image/Evidence/front-card.png'),
    width: Dimensions.get('window').width / 2 - 36,
    height: (Dimensions.get('window').width / 2 - 36) * 0.57,
  },

  _evidenceCameraIcon: {
    source: require('@/image/Evidence/camera-icon.png'),
    width: 16,
    height: 16,
  },

  _evidenceCheckedIcon: {
    source: require('@/image/Evidence/checked-icon.png'),
    width: 12,
    height: 12,
  },
  _evidenceOcrPhotoSuccess: {
    source: require('@/image/Evidence/ocr_photo_success.png'),
    width: 12,
    height: 12,
  },
  _evidenceOcrErrorTip1: {
    source: require('@/image/Evidence/ocr-photo-error1.png'),
    width: 119,
    height: 82,
  },

  _evidenceOcrErrorTip2: {
    source: require('@/image/Evidence/ocr-photo-error2.png'),
    width: 119,
    height: 82,
  },

  _evidenceOcrErrorTip3: {
    source: require('@/image/Evidence/ocr-photo-error3.png'),
    width: 119,
    height: 82,
  },

  _evidenceOcrErrorTip4: {
    source: require('@/image/Evidence/ocr-photo-error4.png'),
    width: 119,
    height: 82,
  },

  _evidenceOcrCurpDes: {
    source: require('@/image/Evidence/curp-des-icon.png'),
    width: Dimensions.get('window').width,
    height: (Dimensions.get('window').width * 905) / 375,
  },

  _evidenceSelectIcon: {
    source: require('@/image/Evidence/select-icon.png'),
    width: 24,
    height: 24,
  },

  _evidenceReSelectIcon: {
    source: require('@/image/Evidence/re-select-icon.png'),
    width: 24,
    height: 24,
  },

  _evidenceBankCard: {
    source: require('@/image/Evidence/back-card.png'),
    width: Dimensions.get('window').width / 2 - 36,
    height: (Dimensions.get('window').width / 2 - 36) * 0.57,
  },

  _evidencePhoto: {
    source: require('@/image/Evidence/photo.png'),
    width: 316,
    height: 274,
  },

  _evidenceOcrPhotoFailed: {
    source: require('@/image/Evidence/ocr_photo_failed.png'),
    width: 12,
    height: 12,
  },

  _evidenceSelfieErrorIcon: {
    source: require('@/image/Evidence/selfie-error-icon.png'),
    height: 14,
    width: 14,
  },

  _evidenceSelfieErrorIcon1: {
    source: require('@/image/Evidence/selfie-error-icon1.png'),
    width: 100,
    height: 74,
  },

  _evidenceSelfieErrorIcon2: {
    source: require('@/image/Evidence/selfie-error-icon2.png'),
    width: 100,
    height: 74,
  },

  _evidenceSelfieErrorIcon3: {
    source: require('@/image/Evidence/selfie-error-icon3.png'),
    width: 100,
    height: 74,
  },

  _modalCloseIcon: {
    source: require('@/image/Modal/close.png'),
    height: 24,
    width: 24,
  },

  _modalCloseWhiteIcon: {
    source: require('@/image/Modal/modal-close-white-icon.png'),
    height: 30,
    width: 30,
  },

  _modalVipIcon: {
    source: require('@/image/Modal/modal-vip-icon.png'),
    height: 64,
    width: 64,
  },

  _modalRefuseIcon: {
    source: require('@/image/Modal/modal-refuse-icon.png'),
    width: 343,
    height: 75,
  },

  _modalCouponSelectIcon: {
    source: require('@/image/Modal/coupon-select.png'),
    height: 32,
    width: 32,
  },

  _modalCouponHelpsTitleBgIcon: {
    source: require('@/image/Modal/coupon-help-title-bg.png'),
    height: 24,
    width: 265,
  },

  _modalCry: {
    source: require('@/image/Modal/cry.png'),
    height: 48,
    width: 48,
  },

  _hotUpdateModal: {
    source: require('@/image/Modal/hot-update-logo.png'),
    height: 68,
    width: 311,
  },

  _faceModal: {
    source: require('@/image/Modal/face.png'),
    height: 58,
    width: 58,
  },

  _refreshModal: {
    source: require('@/image/Modal/refresh.png'),
    height: 24,
    width: 24,
  },

  _safeTipIcon: {
    source: require('@/image/Evidence/safe.png'),
    height: 25,
    width: 25,
  },

  _keyIcon: {
    source: require('@/image/Evidence/key-icon.png'),
    height: 25,
    width: 25,
  },

  _evidenceTopTriangleIcon: {
    source: require('@/image/Evidence/top-triangle.png'),
    width: 22,
    height: 22,
  },

  _evidenceMoreInfoIcon: {
    source: require('@/image/Evidence/more-info.png'),
    width: 16,
    height: 16,
  },

  _evidenceEmphasisIcon: {
    source: require('@/image/Evidence/emphasis.png'),
    width: 22,
    height: 22,
  },

  _evidenceWaitCheck: {
    source: require('@/image/Evidence/wait-check.png'),
    height: 96,
    width: 96,
  },

  _evidenceReject: {
    source: require('@/image/Evidence/reject.png'),
    height: 96,
    width: 96,
  },

  _withholdTopBg: {
    source: require('@/image/Evidence/withhold-top-bg.png'),
    width: Dimensions.get('window').width,
    height: 155,
  },

  /** 复贷首页和确认用信页 自动代扣卡片背景 */
  _withholdCardBg: {
    source: require('@/image/Component/withhold-card-bg.png'),
    width: '100%',
    // height: 100,
  },

  /** 自动代扣奖励卡片 */
  _withholdRewardBanner: {
    source: require('@/image/Evidence/withhold-reward.png'),
    width: '100%',
    height: 200,
  },

  _loanCreditTips: {
    source: require('@/image/Loan/tips.png'),
    height: 24,
    width: 24,
  },

  _loanCreditRefuseConfirm: {
    source: require('@/image/Loan/credit-refuse-confrim-icon.png'),
    height: 96,
    width: 96,
  },

  _loanCreditIncreaseCreditRewardBg: {
    source: require('@/image/Loan/increase-credit-reward-bg.png'),
    width: 100,
    height: 14,
  },

  _loanCreditIncreaseCreditRewardIcon: {
    source: require('@/image/Loan/increase-credit-reward-icon.png'),
    height: 42,
    width: 42,
  },

  _evidenceFaceSuccess: {
    source: require('@/image/Evidence/face-success.png'),
    height: 96,
    width: 96,
  },

  _evidenceFaceCancel: {
    source: require('@/image/Evidence/face-cancel.png'),
    height: 96,
    width: 96,
  },

  _modalDangerIcon: {
    source: require('@/image/Modal/danger.png'),
    height: 32,
    width: 32,
  },

  _newFuncIcon: {
    source: require('@/image/Base/new-func-icon.png'),
    width: 32,
    height: 14,
  },

  _callIcon: {
    source: require('@/image/Base/call.png'),
    height: 24,
    width: 24,
  },

  _detailIcon: {
    source: require('@/image/Base/detail.png'),
    height: 24,
    width: 24,
  },

  _detailWhiteIcon: {
    source: require('@/image/Base/detail-white.png'),
    height: 24,
    width: 24,
  },

  _accountBinding: {
    source: require('@/image/User/account-binding.png'),
    width: 24,
    height: 24,
  },

  _facebook: {
    source: require('@/image/AccountBinding/facebook.png'),
    width: 48,
    height: 48,
  },

  _google: {
    source: require('@/image/AccountBinding/google.png'),
    width: 73,
    height: 29,
  },

  _withdrawLeftIcon: {
    source: require('@/image/Wallet/withdraw_left.png'),
    width: 16,
    height: 16,
  },

  _withdrawInputCloseIcon: {
    source: require('@/image/Wallet/withdraw_input_close.png'),
    width: 24,
    height: 24,
  },

  _withdrawModalSuccess: {
    source: require('@/image/Wallet/withdraw_modal_success.png'),
    width: 32,
    height: 32,
  },

  _withdrawModalWarn: {
    source: require('@/image/Wallet/withdraw_modal_warn.png'),
    width: 32,
    height: 32,
  },

  _repaymentGuideIcon: {
    source: require('@/image/Repayment/repayment-guide-icon.png'),
    width: 48,
    height: 48,
  },

  _couponTitle: {
    source: require('@/image/Modal/coupon-title.png'),
    width: 243,
    height: 28,
  },

  _forceUpdate: {
    source: require('@/image/Base/update.png'),
    width: 48,
    height: 48,
  },

  // vip
  _vipIcon: {
    source: require('@/image/Vip/vip-icon.png'),
    width: 27,
    height: '100%',
  },

  _vipTipIcon: {
    source: require('@/image/Vip/vip.png'),
    height: 30,
    width: 30,
  },

  _vipEnterIcon: {
    source: require('@/image/Vip/vip-enter.png'),
    height: 34,
    width: 34,
  },

  _vipLogo: {
    source: require('@/image/Vip/vip-logo.png'),
    width: 34,
    height: 34,
  },

  _vipView: {
    source: require('@/image/Vip/vip-view.png'),
    width: 24,
    height: 24,
  },

  _excessPaymentIcon: {
    source: require('@/image/excessPayment/excess-payment.png'),
    height: 24,
    width: 24,
  },

  _epModalNormalIcon: {
    source: require('@/image/excessPayment/modal-normal.png'),
    height: 42,
    width: 42,
  },

  _epModalSuccessIcon: {
    source: require('@/image/excessPayment/modal-success.png'),
    height: 32,
    width: 32,
  },

  _epModalFailIcon: {
    source: require('@/image/excessPayment/modal-fail.png'),
    height: 32,
    width: 32,
  },

  _epModalWarningIcon: {
    source: require('@/image/excessPayment/modal-warning.png'),
    height: 32,
    width: 32,
  },

  _extAlarmIcon: {
    source: require('@/image/ext/ext-alarm.png'),
    width: 24,
    height: 24,
  },

  _extCardBgLeft: {
    source: require('@/image/ext/ext-card-bg-left.png'),
    width: 200,
    height: 80,
  },

  _extModalTopBg: {
    source: require('@/image/ext/ext-card-bg.png'),
    // width: 24,
    height: 87.5,
  },
  _extModalPayChannelBg: {
    source: require('@/image/ext/ext-modal-pay-channel-bg.png'),
    // width: 24,
    height: 44,
  },

  // 产品选择页
  /** 通知图片  */
  _rewardNotice: {
    source: require('@/image/productSelect/rewardNotice.png'),
    width: 24,
    height: 24,
  },
  /**
   * 单期产品Icon
   */
  _expressIcon: {
    source: require('@/image/productSelect/express.png'),
    width: 40,
    height: 40,
  },
  /**
   * 多期产品Icon
   */
  _flexIcon: {
    source: require('@/image/productSelect/flex.png'),
    width: 40,
    height: 40,
  },
  _flexLimit: {
    source: require('@/image/productSelect/flex-limit.png'),
    width: 57,
    height: 23,
  },

  /** 首贷产品选择 */
  /** 产品未选中 */
  _productUnSelected: {
    source: require('@/image/productSelect/product-un-selected.png'),
    width: 48,
    height: 48,
  },

  /** 产品选中 */
  _productSelected: {
    source: require('@/image/productSelect/product-selected.png'),
    width: 48,
    height: 48,
  },

  /** 滑动条图片 */
  _thumbImage: {
    source: require('@/image/productSelect/thumb-image.png'),
    width: 58,
    height: 68,
  },
  /** 三角尖头向上 */
  _triangleUpIcon: {
    source: require('@/image/Base/triangle-up.png'),
    width: 24,
    height: 24,
  },
  /** 三角尖头向下 */
  _triangleDownIcon: {
    source: require('@/image/Base/triangle-down.png'),
    width: 24,
    height: 24,
  },

  _launchIcon: {
    source: require('@/image/Component/launch.png'),
    width: 20,
    height: 20,
  },

  _evidenceCameraFaceMaskview: {
    source: require('@/image/Component/camera-face-maskview.png'),
    width: Dimensions.get('window').width - 90,
    height: Math.round(((Dimensions.get('window').width - 90) * 300) / 231),
  },

  _evidenceCameraFrontCardMaskview: {
    source: require('@/image/Evidence/front-card-line.png'),
    width: 562,
    height: 335,
  },

  _evidenceCameraBackCardMaskview: {
    source: require('@/image/Evidence/back-card-line.png'),
    width: 562,
    height: 335,
  },

  _evidenceCameraTakePhoto: {
    source: require('@/image/Component/camera-take-photo.png'),
    height: 68,
    width: 68,
  },

  _evidenceSelectAmountHeader: {
    source: require('@/image/Evidence/home_header_bg.png'),
    height: 300,
    width: Dimensions.get('window').width,
  },

  _modalIconClose: {
    source: require('@/image/Login/modal_close.png'),
    width: 24,
    height: 24,
  },
  _modalIconSms: {
    source: require('@/image/Login/modal_icon_sms.png'),
    width: 19,
    height: 15,
  },
  _modalIconVoice: {
    source: require('@/image/Login/modal_icon_phone.png'),
    width: 24,
    height: 24,
  },
  _modalIconWhatsapp: {
    source: require('@/image/Login/modal_icon_whatsapp.png'),
    width: 24,
    height: 24,
  },
  _iconCloseRound: {
    source: require('@/image/Base/icon_close_round.png'),
    width: 20,
    height: 20,
  },
  _progressNodeArrived: {
    source: require('@/image/Evidence/progress_node_arrived.png'),
    width: 14,
    height: 14,
  },
  _progressNodeCompleted: {
    source: require('@/image/Evidence/progress_node_completed.png'),
    width: 14,
    height: 14,
  },
  _progressNodeNotArrive: {
    source: require('@/image/Evidence/progress_node_not_arrive.png'),
    width: 14,
    height: 14,
  },
  _progressNodeNotCompleted: {
    source: require('@/image/Evidence/progress_node_not_completed.png'),
    width: 14,
    height: 14,
  },
  _ocrPhotoSuccess: {
    source: require('@/image/Evidence/ocr-photo-success.png'),
    width: 60,
    height: 60,
  },
  _ocrPhotoRetake: {
    source: require('@/image/Evidence/ocr-photo-retake.png'),
    width: 60,
    height: 60,
  },
  _curpHelpIcon: {
    source: require('@/image/Evidence/curp-help.png'),
    width: 343,
    height: 865,
  },
  _grayArrowRightIcon: {
    source: require('@/image/Component/gray-arrow-right-icon.png'),
    height: 14,
    width: 14,
  },
  _repaymentAccountModalHeaderBg: {
    source: require('@/image/Repayment/repayment_account_modal_header_bg.png'),
    width: 327,
    height: 178,
  },
  _welcomeBg: {
    source: require('@/image/welcome/welcome_bg.png'),
    width: Dimensions.get('window').width,
    height: Math.round((Dimensions.get('window').width * 371) / 375),
  },

  _welcomeBottom: {
    source: require('@/image/welcome/welcome_bottom.png'),
    width: Dimensions.get('window').width,
    height: Math.round((Dimensions.get('window').width * 94) / 375),
  },

  _welcomeMan: {
    source: require('@/image/welcome/welcome_man.png'),
    width: Math.round(244 * 0.95),
    height: Math.round(240 * 0.95),
  },

  _welcomeMessage1: {
    source: require('@/image/welcome/welcome_message1.png'),
    width: 40,
    height: 40,
  },

  _welcomeMessage2: {
    source: require('@/image/welcome/welcome_message2.png'),
    width: 40,
    height: 40,
  },

  _welcomeMessage3: {
    source: require('@/image/welcome/welcome_message3.png'),
    width: 40,
    height: 40,
  },

  _brandLine: {
    source: require('@/image/Brand/brand_line.png'),
    width: Dimensions.get('window').width,
    height: Math.round((Dimensions.get('window').width * 94) / 375),
  },

  _brandBottomBg: {
    source: require('@/image/Brand/brand_bottom_bg.png'),
    width: Dimensions.get('window').width,
    height: Math.round((Dimensions.get('window').width * 515) / 375),
  },

  _brandInfo: {
    source: require('@/image/Brand/brand_info.png'),
    width: 276,
    height: 32,
  },

  _brandLogo: {
    source: require('@/image/Brand/brand_logo.png'),
    width: 48,
    height: 48,
  },

  _brandPhoneNumber: {
    source: require('@/image/Brand/brand_phonenumber.png'),
    width: 327,
    height: 96,
  },
  _offlineIconEdit: {
    source: require('@/image/Loan/offline_icon_edit.png'),
    width: 17,
    height: 18,
  },
  _offlineIconRefresh: {
    source: require('@/image/Loan/offline_icon_refresh.png'),
    width: 24,
    height: 24,
  },
  _offlineQrCode: {
    source: require('@/image/Loan/offline_qrcode.png'),
    width: 235,
    height: 88,
  },
  _notifyRedMiddle: {
    source: require('@/image/Base/notify-red-middle.png'),
    width: 32,
    height: 32,
  },
  _offlineRepaymentBorderBg: {
    source: require('@/image/Repayment/offline_repayment_border_bg.png'),
    width: 251,
    height: 7,
  },
  _infoYellowMiddle: {
    source: require('@/image/Base/info-yellow-middle.png'),
    width: 23,
    height: 23,
  },
  _dropDownIcon: {
    source: require('@/image/Repayment/dropdown-icon.png'),
    width: 24,
    height: 24,
  },
  _versionNoteHeader: {
    source: require('@/image/Modal/version-note-header.png'),
    width: 295 + 32,
    height: 72,
  },
  _versionNoteTitleBottom: {
    source: require('@/image/Modal/version-note-title-bottom.png'),
    width: 290,
    height: 14,
  },
  _redeemCouponLoadFail: {
    source: require('@/image/My/redeem-coupon-load-fail.png'),
    width: 218,
    height: 188,
  },
  _basicInfoCoupon: {
    source: require('@/image/Evidence/basic-info-coupon.png'),
    width: 24,
    height: 24,
  },
  _couponVipIcon: {
    source: require('@/image/My/coupon-vip-icon.png'),
    width: 15,
    height: 19,
  },
  _couponHotIcon: {
    source: require('@/image/My/coupon-hot-icon.png'),
    width: 10,
    height: 10,
  },
  _couponHotVipIcon: {
    source: require('@/image/My/coupon-hot-vip-icon.png'),
    width: 10,
    height: 10,
  },
  _couponDeductionLeftBg: {
    source: require('@/image/My/coupon-deduction-left-bg.png'),
    width: 94,
    height: 74,
  },
  _couponDeductionLeftVipBg: {
    source: require('@/image/My/coupon-deduction-left-vip-bg.png'),
    width: 94,
    height: 74,
  },
  _couponDeductionRightBg: {
    source: require('@/image/My/coupon-deduction-right-bg.png'),
    width: 221,
    height: 74,
  },
  _couponDeductionRightVipBg: {
    source: require('@/image/My/coupon-deduction-right-vip-bg.png'),
    width: 221,
    height: 74,
  },
  _couponCreditInnerBg: {
    source: require('@/image/My/coupon-credit-inner-bg.png'),
    width: 315,
    height: 74,
  },
  _couponCreditInnerExpireBg: {
    source: require('@/image/My/coupon-credit-expire-inner-bg.png'),
    width: 315,
    height: 74,
  },
  _couponDeductionRightVipBtn: {
    source: require('@/image/My/coupon-deduction-right-vip-btn.png'),
    width: 59,
    height: 24,
  },
  _couponDeductionRightBtn: {
    source: require('@/image/My/coupon-deduction-right-btn.png'),
    width: 59,
    height: 24,
  },
  _couponDeductionRightBtnDisable: {
    source: require('@/image/My/coupon-deduction-right-btn-disable.png'),
    width: 104,
    height: 24,
  },
  _couponExpireRightBg: {
    source: require('@/image/My/coupon-expire-right-bg.png'),
    width: 221,
    height: 74,
  },

  _couponExpireIcon: {
    source: require('@/image/My/coupon-expire-icon.png'),
    width: 45,
    height: 40,
  },
  _couponExchangeSuccessBg: {
    source: require('@/image/My/coupon-exchange-success-bg.png'),
    width: 373,
    height: 362,
  },
  _couponExchangeSuccessCancelBtn: {
    source: require('@/image/My/coupon-exchange-success-cancel-btn.png'),
    width: 154,
    height: 44,
  },
  _couponExchangeSuccessSureBtn: {
    source: require('@/image/My/coupon-exchange-success-sure-btn.png'),
    width: 96,
    height: 44,
  },
  _couponExchangeSuccessTopIcon: {
    source: require('@/image/My/coupon-exchange-success-top-icon.png'),
    width: 180,
    height: 108,
  },
  _walletCouponIcon: {
    source: require('@/image/Wallet/wallet-coupon-icon.png'),
    width: 20,
    height: 20,
  },
  _walletHandleRightIcon: {
    source: require('@/image/Wallet/wallet-handle-right.png'),
    width: 20,
    height: 20,
  },
  _coinAddIcon: {
    source: require('@/image/Component/coin-add-icon.png'),
    width: 25,
    height: 22,
  },
  _primaryGiftModal: {
    source: require('@/image/Modal/primary-gift-modal.png'),
    width: 327,
    height: 129,
  },

  _primaryCheers: {
    source: require('@/image/Modal/primary-cheers.png'),
    width: 135,
    height: 24,
  },

  _primaryCouponBadge: {
    source: require('@/image/Modal/primary-coupon-badge.png'),
    width: 110,
    height: 24,
  },

  _increaseCouponCardBg: {
    source: require('@/image/Modal/increase-coupon-card-bg.png'),
    width: 222,
    height: 84,
  },
  _increaseCouponAgreeBtn: {
    source: require('@/image/Modal/increase-coupon-agree-btn.png'),
    width: 141,
    height: 48,
  },
  _increaseAmountLoanBg: {
    source: require('@/image/Loan/increase-amount-loan-bg.png'),
    // width: 74,
    height: 20,
  },

  _increaseAmountLoanInfo: {
    source: require('@/image/Loan/increase-amount-loan-info.png'),
    width: 12,
    height: 12,
  },

  _coinVipExpireIcon: {
    source: require('@/image/My/coupon-vip-expire-icon.png'),
    width: 15,
    height: 19,
  },
  _couponModalInnerBg: {
    source: require('@/image/Component/coupon-modal-inner-bg.png'),
    width: 311,
    height: 345,
  },
  _couponModalInnerSmallBg: {
    source: require('@/image/Component/coupon-modal-inner-small-bg.png'),
    width: 311,
    height: 259,
  },
  _couponModalOutBg: {
    source: require('@/image/Component/coupon-modal-out-bg.png'),
    width: 327,
    height: 406,
  },
  _couponModalOutSmallBg: {
    source: require('@/image/Component/coupon-modal-out-small-bg.png'),
    width: 327,
    height: 320,
  },
  _couponModalTitle: {
    source: require('@/image/Component/coupon-modal-title.png'),
    width: 243,
    height: 28,
  },
  _couponModalBtn: {
    source: require('@/image/Component/coupon-modal-btn.png'),
    width: 141,
    height: 48,
  },
  _mountDownIcon: {
    source: require('@/image/Base/amount-down-icon.png'),
    width: 16,
    height: 16,
  },
  _couponItemSelectedIcon: {
    source: require('@/image/My/coupon-item-selected.png'),
    width: 34,
    height: 32,
  },
  _couponModalCloseIcon: {
    source: require('@/image/Component/coupon-modal-close-icon.png'),
    width: 24,
    height: 24,
  },
  _couponModalTipIcon: {
    source: require('@/image/Component/coupon-modal-tip-icon.png'),
    width: 16,
    height: 16,
  },
  _upSuccessIcon: {
    source: require('@/image/Base/up-success-icon.png'),
    width: 16,
    height: 16,
  },

  _retainDiscountCouponBtn: {
    source: require('@/image/Modal/retain-discount-coupon-btn.png'),
    width: 186,
    height: 45,
  },

  _retainDiscountCouponHeader: {
    source: require('@/image/Modal/retain-discount-coupon-header.png'),
    width: 292,
    height: 100,
  },

  _retainIncreaseCouponBtn: {
    source: require('@/image/Modal/retain-increase-coupon-btn.png'),
    width: 186,
    height: 45,
  },

  _retainIncreaseCouponHeader: {
    source: require('@/image/Modal/retain-increase-coupon-header.png'),
    width: 257,
    height: 160,
  },
  _retainIncreaseSurprise: {
    source: require('@/image/Modal/retain-increase-superise.png'),
    width: 86,
    height: 28,
  },
  _retainDiscountSurprise: {
    source: require('@/image/Modal/retain-discount-superise.png'),
    width: 86,
    height: 28,
  },
  _couponVoidedIcon: {
    source: require('@/image/My/coupon-voided-icon.png'),
    width: 45,
    height: 40,
  },
  _couponHXIcon: {
    source: require('@/image/My/coupon-hx-icon.png'),
    width: 45,
    height: 40,
  },
  _tipGrayIcon: {
    source: require('@/image/Base/tip-gray-icon.png'),
    width: 12,
    height: 12,
  },
  _changeMobileSuccessIcon: {
    source: require('@/image/Base/success-icon.png'),
    width: 32,
    height: 32,
  },
  _dateIcon: {
    width: 24,
    height: 24,
    source: require('@/image/Component/date.png'),
  },
  _whiteInfo: {
    source: require('@/image/Evidence/white-info.png'),
    height: 12,
    width: 12,
  },
  _applyRecheckHeader: {
    source: require('@/image/Loan/apply-recheck-header.png'),
    height: 99,
    width: 131,
  },

  _happyBoy: {
    source: require('@/image/Loan/happy-boy.png'),
    height: 86,
    width: 86,
  },

  _progressBar: {
    source: require('@/image/Loan/progress-bar.png'),
    height: 24,
    width: 182,
  },
  _repayDateBg: {
    source: require('@/image/Loan/repay-date-bg.png'),
    height: 348,
    width: 311,
  },
  _repayDateModalBtn: {
    source: require('@/image/Loan/repay-date-modal-btn.png'),
    height: 48,
    width: 186,
  },
  _loanRemindHeader: {
    source: require('@/image/Loan/loan-remind-header.png'),
    height: 67.57,
    width: 60,
  },
  _withholdTip: {
    source: require('@/image/Modal/ic_withhold_tip.png'),
    width: 72,
    height: 72,
  },
  _launchScreenBg: {
    source: require('@/image/welcome/launch-screen-bg.png'),
    width: '100%',
  },
  _ocrLoadingIcon: {
    source: require('@/image/Evidence/ocr-loading-icon.png'),
    width: 64,
    height: 64,
  },
  _ocrTimeoutInfoIcon: {
    source: require('@/image/Evidence/ocr-timeout-info.png'),
    width: 32,
    height: 32,
  },
  _ocrCountUpModalBg: {
    source: require('@/image/Modal/ocr-count-up-modal-bg.png'),
    width: 327,
  },
  _addBankCardIcon: {
    source: require('@/image/Component/add-bank-card-icon.png'),
    width: 28,
    height: 28,
  },
  _personalInfoIcon: {
    source: require('@/image/My/personal-info.png'),
    width: 24,
    height: 24,
  },
  _emptyIcon: {
    source: require('@/image/My/ic_empty.png'),
    width: 218,
    height: 188,
  },
};

// Map of the key of the Images
// ImagesNames 的类型是一个对象，其中每个属性的键和值都是 Images 的 key
export const ImageNames: { [K in keyof typeof Images]: K } = Object.keys(Images).reduce(
  (acc: any, key) => {
    acc[key] = key;
    return acc;
  },
  {},
);
