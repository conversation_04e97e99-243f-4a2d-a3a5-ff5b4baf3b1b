import { useDevPage } from '@/hooks';
import {
  AccountBindingPage,
  BankCardListPage,
  BankListPage,
  BasicInfoPage,
  ClabePage,
  DevPage,
  SelectAmountPage,
  EnterPhoneNumberPage,
  EnterPwdPage,
  FaqPage,
  GetSmsCodePage,
  IncreaseCreditPage,
  LoanContractPage,
  LoanRecordPage,
  CouponListPage,
  MaintainerPage,
  MessageCenterPage,
  ModifyPwdPage,
  NewMobilePage,
  OcrPage,
  OfflinePaymentPage,
  OnlinePaymentPage,
  PermissionAgreePage,
  PermissionPrivacyPage,
  PrivacyPage,
  ProcessAccountBindingPage,
  ProcessQuestionnairePage,
  ReLoanOtpPage,
  ResetPwdPage,
  SetPwdPage,
  SplashPage,
  SupplyOtpPage,
  TakePhotoPage,
  TakePhotoResultPage,
  VerifyPwdPage,
  CreditRefuseQuestion,
  WalletFlowPage,
  CreditRefuseConfirm,
  WithdrawPage,
  LandPage,
  AutomaticWithholdPage,
  AutomaticWithholdProtocolPage,
  FirstLoanProductSelect,
  SingleTremReLoanPage,
  MultiPeriodReLoanPage,
  MultiPeriodFirstLoanPage,
  PermissionRefuseToSettingPage,
  SelfiePage,
  OCRTakePhotoPage,
  VerifyBasicInfo,
  ChangeMobile,
  RegionalSelectionPage,
  SelectAmountSliderPage,
  VerifyOriginalMobile,
  WelcomePage,
  NewMobileSmsCode,
  DirectDebitPage,
  RedeemCoupons,
  SplashActivityPage,
  PersonalInfoPage,
  OCRInfoUpdatePage,
} from '@/pages';
import { navigationRef, updateNavigationPage, updateNavigationState } from '@/utils';
import { NavigationContainer, Route } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import _ from 'lodash';
import React, { useEffect, useMemo, useRef } from 'react';
import SplashScreen from 'react-native-splash-screen';
import { BaseConfig } from '@/baseConfig';
import { HomeTabs, RouterConfig } from './useRouter';

const Stack = createNativeStackNavigator();

export default function App() {
  const routeRef = useRef<Route<string> | undefined>(null);

  useEffect(() => {
    BaseConfig.debug && SplashScreen.hide();
    // eslint-disable-next-line react-hooks/rules-of-hooks
    BaseConfig.debug && useDevPage();
  }, []);

  const defaultScreenOption: any = useMemo(() => {
    return {
      headerShown: false,
    };
  }, []);

  return (
    <NavigationContainer
      ref={navigationRef}
      onReady={async () => {
        routeRef.current = navigationRef.current?.getCurrentRoute();
        const navigationState = navigationRef.current?.getState();
        updateNavigationState(navigationState);

        const isRootPage = navigationState?.routes.length === 1 || !navigationState?.routes;

        updateNavigationPage(routeRef.current, isRootPage);
      }}
      onStateChange={async _state => {
        updateNavigationState(_state);

        const previousRoute = routeRef.current;
        const currentRoute = navigationRef.current?.getCurrentRoute();

        if (!_.isEqual(previousRoute, currentRoute)) {
          // Save the current route name for later comparison
          routeRef.current = currentRoute;

          const isRootPage = _state?.routes.length === 1;
          updateNavigationPage(currentRoute, isRootPage);
        }
      }}>
      <Stack.Navigator initialRouteName={RouterConfig.HOME_TABS}>
        <Stack.Screen
          name={RouterConfig.WELCOME_PAGE}
          component={WelcomePage}
          options={defaultScreenOption}
        />
        <Stack.Screen
          name={RouterConfig.FIRST_LOAN_PRODUCT_SELECT}
          component={FirstLoanProductSelect}
          options={defaultScreenOption}
        />
        <Stack.Screen
          name={RouterConfig.SINGLE_TERM_RELOAN_PAGE}
          component={SingleTremReLoanPage}
          options={defaultScreenOption}
        />
        <Stack.Screen
          name={RouterConfig.REGIONAL_SELECTION_PAGE}
          component={RegionalSelectionPage}
          options={defaultScreenOption}
        />

        <Stack.Screen
          name={RouterConfig.MULTI_PERIOD_RELOAN_PAGE}
          component={MultiPeriodReLoanPage}
          options={defaultScreenOption}
        />
        <Stack.Screen
          name={RouterConfig.MULTI_PERIOD_FIRST_LOAN_PAGE}
          component={MultiPeriodFirstLoanPage}
          options={defaultScreenOption}
        />
        <Stack.Screen
          name={RouterConfig.PROCESS_QUESTIONNAIRE}
          component={ProcessQuestionnairePage}
          options={defaultScreenOption}
        />
        <Stack.Screen
          name={RouterConfig.HOME_TABS}
          component={HomeTabs}
          options={defaultScreenOption}
        />
        <Stack.Screen
          name={RouterConfig.SPLASH}
          options={defaultScreenOption}
          component={SplashPage}
        />
        <Stack.Screen
          name={RouterConfig.SPLASH_ACTIVITY}
          options={defaultScreenOption}
          component={SplashActivityPage}
        />
        <Stack.Screen
          name={RouterConfig.ENTER_PHONE_NUMBER}
          options={defaultScreenOption}
          component={EnterPhoneNumberPage}
        />
        <Stack.Screen
          name={RouterConfig.ENTER_PWD}
          options={defaultScreenOption}
          component={EnterPwdPage}
        />
        <Stack.Screen
          name={RouterConfig.PRIVACY_SCREEN}
          options={defaultScreenOption}
          component={PrivacyPage}
        />

        <Stack.Screen
          name={RouterConfig.PERMISSION_REFUSE_TO_SETTING}
          options={defaultScreenOption}
          component={PermissionRefuseToSettingPage}
        />

        <Stack.Screen
          name={RouterConfig.GET_SMS_CODE}
          options={defaultScreenOption}
          component={GetSmsCodePage}
        />
        <Stack.Screen
          name={RouterConfig.TAKE_PHOTO}
          options={defaultScreenOption}
          component={TakePhotoPage}
        />
        <Stack.Screen
          name={RouterConfig.MAINTAINER}
          options={defaultScreenOption}
          component={MaintainerPage}
        />
        <Stack.Screen
          name={RouterConfig.TAKE_PHOTO_RESULT}
          options={defaultScreenOption}
          component={TakePhotoResultPage}
        />
        <Stack.Screen
          name={RouterConfig.SUPPLY_OTP}
          options={defaultScreenOption}
          component={SupplyOtpPage}
        />
        <Stack.Screen
          name={RouterConfig.OCR_INFO}
          options={defaultScreenOption}
          component={OcrPage}
        />
        <Stack.Screen
          name={RouterConfig.BASIC_INFO}
          options={defaultScreenOption}
          component={BasicInfoPage}
        />
        <Stack.Screen
          name={RouterConfig.PROCESS_ACCOUNT_BINDING}
          options={defaultScreenOption}
          component={ProcessAccountBindingPage}
        />
        <Stack.Screen
          name={RouterConfig.SELECT_AMOUNT}
          options={defaultScreenOption}
          component={SelectAmountPage}
        />
        <Stack.Screen
          name={RouterConfig.SELECT_AMOUNT_SLIDER}
          options={defaultScreenOption}
          component={SelectAmountSliderPage}
        />
        <Stack.Screen
          name={RouterConfig.CLABE_BASIC_INFO}
          options={defaultScreenOption}
          component={ClabePage}
        />
        <Stack.Screen
          name={RouterConfig.ONLINE_REPAYMENT}
          options={defaultScreenOption}
          component={OnlinePaymentPage}
        />
        <Stack.Screen
          name={RouterConfig.OFFLINE_REPAYMENT}
          options={defaultScreenOption}
          component={OfflinePaymentPage}
        />
        <Stack.Screen
          name={RouterConfig.RE_LOAN_OTP}
          options={defaultScreenOption}
          component={ReLoanOtpPage}
        />
        <Stack.Screen name={RouterConfig.DEV} options={defaultScreenOption} component={DevPage} />
        <Stack.Screen
          name={RouterConfig.SET_PWD}
          options={defaultScreenOption}
          component={SetPwdPage}
        />
        <Stack.Screen
          name={RouterConfig.PERSONAL_INFO}
          options={defaultScreenOption}
          component={PersonalInfoPage}
        />
        <Stack.Screen name={RouterConfig.FAQ} options={defaultScreenOption} component={FaqPage} />
        <Stack.Screen
          name={RouterConfig.PERMISSION_AGREE}
          options={defaultScreenOption}
          component={PermissionAgreePage}
        />
        <Stack.Screen
          name={RouterConfig.PERMISSION_PRIVACY}
          options={defaultScreenOption}
          component={PermissionPrivacyPage}
        />
        <Stack.Screen
          name={RouterConfig.LOAN_CONTRACT}
          options={defaultScreenOption}
          component={LoanContractPage}
        />
        <Stack.Screen
          name={RouterConfig.BANK_LIST}
          options={defaultScreenOption}
          component={BankListPage}
        />
        <Stack.Screen
          name={RouterConfig.BANK_CARD_LIST}
          options={defaultScreenOption}
          component={BankCardListPage}
        />
        <Stack.Screen
          name={RouterConfig.MODIFY_PWD}
          options={defaultScreenOption}
          component={ModifyPwdPage}
        />
        <Stack.Screen
          name={RouterConfig.RESET_PWD}
          options={defaultScreenOption}
          component={ResetPwdPage}
        />
        <Stack.Screen
          name={RouterConfig.NEW_MOBILE}
          options={defaultScreenOption}
          component={NewMobilePage}
        />
        <Stack.Screen
          name={RouterConfig.VERIFY_PWD}
          options={defaultScreenOption}
          component={VerifyPwdPage}
        />
        <Stack.Screen
          name={RouterConfig.INCREASE_CREDIT}
          options={defaultScreenOption}
          component={IncreaseCreditPage}
        />
        <Stack.Screen
          name={RouterConfig.ACCOUNT_BINDING}
          options={defaultScreenOption}
          component={AccountBindingPage}
        />
        <Stack.Screen
          name={RouterConfig.MESSAGE_CENTER}
          options={defaultScreenOption}
          component={MessageCenterPage}
        />
        <Stack.Screen
          name={RouterConfig.LOAN_RECORD}
          options={defaultScreenOption}
          component={LoanRecordPage}
        />
        <Stack.Screen
          name={RouterConfig.COUPON_LIST}
          options={defaultScreenOption}
          component={CouponListPage}
        />
        <Stack.Screen
          name={RouterConfig.INVITE_USER}
          options={defaultScreenOption}
          component={LandPage}
        />
        <Stack.Screen
          name={RouterConfig.VIP_RULE}
          options={defaultScreenOption}
          component={LandPage}
        />
        <Stack.Screen
          name={RouterConfig.VIP_RULE_DETAIL}
          options={defaultScreenOption}
          component={LandPage}
        />
        <Stack.Screen
          name={RouterConfig.WEBSITE}
          options={defaultScreenOption}
          component={LandPage}
        />
        <Stack.Screen
          name={RouterConfig.DELETE_USER_DATA}
          options={defaultScreenOption}
          component={LandPage}
        />
        <Stack.Screen
          name={RouterConfig.INTERNAL_H5}
          options={defaultScreenOption}
          component={LandPage}
        />
        <Stack.Screen
          name={RouterConfig.CREDIT_REFUSE_CONFRIM}
          options={defaultScreenOption}
          component={CreditRefuseConfirm}
        />
        <Stack.Screen
          name={RouterConfig.CREDIT_REFUSE_QUESTION}
          options={defaultScreenOption}
          component={CreditRefuseQuestion}
        />
        <Stack.Screen
          name={RouterConfig.WITHDRAW}
          options={defaultScreenOption}
          component={WithdrawPage}
        />
        <Stack.Screen
          name={RouterConfig.WALLET_FLOW}
          options={defaultScreenOption}
          component={WalletFlowPage}
        />
        <Stack.Screen
          name={RouterConfig.AUTOMATIC_WITHHOLD}
          options={defaultScreenOption}
          component={AutomaticWithholdPage}
        />
        <Stack.Screen
          name={RouterConfig.AUTOMATIC_WITHHOLD_PROTOCOL}
          options={defaultScreenOption}
          component={AutomaticWithholdProtocolPage}
        />
        <Stack.Screen
          name={RouterConfig.SELFIE}
          options={defaultScreenOption}
          component={SelfiePage}
        />
        <Stack.Screen
          name={RouterConfig.VERIFY_BASIC_INFO}
          options={defaultScreenOption}
          component={VerifyBasicInfo}
        />
        <Stack.Screen
          name={RouterConfig.CHANGE_MOBILE}
          options={defaultScreenOption}
          component={ChangeMobile}
        />
        <Stack.Screen
          name={RouterConfig.OCR_TAKE_PHOTO}
          options={defaultScreenOption}
          component={OCRTakePhotoPage}
        />
        <Stack.Screen
          name={RouterConfig.VERIFY_ORIGINAL_MOBILE}
          options={defaultScreenOption}
          component={VerifyOriginalMobile}
        />
        <Stack.Screen
          name={RouterConfig.NEW_MOBILE_CMS_CODE}
          options={defaultScreenOption}
          component={NewMobileSmsCode}
        />
        <Stack.Screen
          name={RouterConfig.DIRECT_DEBIT_PAGE}
          options={defaultScreenOption}
          component={DirectDebitPage}
        />
        <Stack.Screen
          name={RouterConfig.REDEEM_COUPONS}
          options={defaultScreenOption}
          component={RedeemCoupons}
        />
        <Stack.Screen
          name={RouterConfig.OCR_INFO_UPDATE}
          options={defaultScreenOption}
          component={OCRInfoUpdatePage}
        />
      </Stack.Navigator>
    </NavigationContainer>
  );
}
