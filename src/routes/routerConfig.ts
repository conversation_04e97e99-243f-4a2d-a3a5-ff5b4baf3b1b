export enum RouterConfig {
  /** 开发者页面 */
  DEV = 'devPage',
  /** HomeTabs */
  HOME_TABS = 'HomeTabs',
  /** Home tab */
  HOME_TAB = 'HomeTab',
  /** 钱包页 tab */
  WALLET_TAB = 'WalletTab',
  /** 隐私协议 tab */
  SAFETY_TAB = 'SafetyTab',
  /** 用户 tab */
  USER_TAB = 'UserTab',
  /** 首贷用户产品列表选择 */
  FIRST_LOAN_PRODUCT_SELECT = 'firstLoanProductSelectPage',
  /** 复贷用户产品列表选择 */
  RELOAN_PRODUCT_SELECT = 'reloanProductSelectPage',
  /** 复贷单期贷款页 */
  SINGLE_TERM_RELOAN_PAGE = 'singleTermReLoanPage',
  /** 复贷多期贷款页 */
  MULTI_PERIOD_RELOAN_PAGE = 'multiPeriodReLoanPage',
  /** 首贷单期贷款页 */
  SINGLE_TERM_FIRST_LOAN_PAGE = 'singleTermFirstLoanPage',
  /** 首贷多期贷款页 */
  MULTI_PERIOD_FIRST_LOAN_PAGE = 'multiPeriodFirstLoanPage',
  /** home 主页 */
  HOME_SCREEN = 'HomeStack',
  /** 隐私协议页 */
  SAFETY_SCREEN = 'SafetyStack',
  /** 登录页 */
  LOGIN_SCREEN = 'LoginStack',
  /** 隐私协议 */
  PRIVACY_SCREEN = 'PrivacyStack',
  /** 输入协议页 */
  ENTER_PHONE_NUMBER = 'EnterPhoneNumberPage',
  /** 密码登入 */
  ENTER_PWD = 'EnterPwdPage',
  /** 设置密码 */
  SET_PWD = 'SetPwdPage',
  /** 获取短信验证页 */
  GET_SMS_CODE = 'GetSmsCodePage',
  /** 基础信息页 */
  BASIC_INFO = 'BasicInfoPage',
  /** 进件流程第三方账号绑定页 */
  PROCESS_ACCOUNT_BINDING = 'ProcessAccountBindingPage',
  /** 进件流程问券页 */
  PROCESS_QUESTIONNAIRE = 'ProcessQuestionnairePage',
  /** 进件流程补充信息页 */
  PROCESS_SUPPLY_BINDING = 'ProcessAccountBindingPage',
  /** ocr 页 */
  OCR_INFO = 'OCRInfoPage',
  /** 拍照页 */
  TAKE_PHOTO = 'takePhotoPage',
  /** OTP补件页 */
  SUPPLY_OTP = 'supplyOtpPage',
  /** 拍照详情页 */
  TAKE_PHOTO_DETAIL = 'takePhotoDetailPage',
  /** 拍照结果页 */
  TAKE_PHOTO_RESULT = 'takePhotoResultPage',
  /** clabe 页面 */
  CLABE_BASIC_INFO = 'clabeBaiscInfoPage',
  /** 等待审核页 */
  WAIT_CHECK = 'waitCheckPage',
  /** 拒绝页面 */
  REJECT = 'rejectPage',
  /** 等待放款页 */
  WAIT_PAYMENT = 'waitPaymentPage',
  /** 分期确认用信 */
  MULTI_PERIOD_COMFIRM_LOAN = 'multiPeriodComfirmLoanPage',
  /** 贷款确认页 */
  COMFIRM_LOAN = 'comfirmLoanPage',
  /** 人脸识别页 */
  LIVE_RECOGNITION = 'LiveRecognitionPage',
  /** 系统繁忙页 */
  MAINTAINER = 'maintainerPage',
  /** 还款页 */
  REPAYMENT = 'repaymentPage',
  /** 分期还款页 */
  MULTI_PERIOD_REPAYMENT = 'multiPeriodRepaymentPage',
  /** 线上还款页 */
  ONLINE_REPAYMENT = 'onlinePaymentPage',
  /** 线下还款页 */
  OFFLINE_REPAYMENT = 'offlinePaymentPage',
  /** 我的页面 */
  MY = 'myPage',
  /** 消息中心 */
  MESSAGE_CENTER = 'messageCenterPage',
  /** 问题页 */
  FAQ = 'faqPage',
  /** 个人信息页 */
  PERSONAL_INFO = 'personalInfoPage',
  /** OTP页面 */
  RE_LOAN_OTP = 'reLoanOtpPage',
  /** 首贷页面 */
  FIRST_LOAN = 'firstLoanPage',
  /** 权限确认页 */
  PERMISSION_AGREE = 'permissionAgreePage',
  /** 数据安全页 */
  PERMISSION_PRIVACY = 'permissionPrivacyPage',
  /** 权限申请拒绝去设置页面 */
  PERMISSION_REFUSE_TO_SETTING = 'permissionRefuseToSettingPage',
  /** 其他页面 */
  OTHER = 'other',
  /** 启动页 */
  SPLASH = 'splashPage',
  /** 贷款合同页 */
  LOAN_CONTRACT = 'loanContractPage',
  /** 人脸超时页面 */
  FACE_EXPIRED = 'faceExpiredPage',
  /** 银行卡列表页 */
  BANK_LIST = 'bankListPage',
  /** 银行卡页 */
  BANK_CARD_LIST = 'bankCardListPage',
  /** 编辑密码页 */
  MODIFY_PWD = 'modifyPwdPage',
  /** 编辑密码页 */
  RESET_PWD = 'resetPwdPage',
  /** 新手机号输入页 */
  NEW_MOBILE = 'newMobilePage',
  /** 密码验证页 */
  VERIFY_PWD = 'verifyPwdPage',
  /** 增信页 */
  INCREASE_CREDIT = 'increaseCreditPage',
  /** 选择额度页面 */
  SELECT_AMOUNT = 'selectAmountPage',
  /** 首贷滑块选择额度页面 */
  SELECT_AMOUNT_SLIDER = 'selectAmountSlidePage',
  /** 第三方账户绑定页 */
  ACCOUNT_BINDING = 'accountBindingPage',
  /** 订单记录 */
  LOAN_RECORD = 'loanRecordPage',
  /** 复贷问卷页 */
  RELOAN_QUESTION = 'reloanQuestionPage',
  /** 优惠券列表 */
  COUPON_LIST = 'couponListPage',
  /** 钱包页面 */
  WALLET = 'walletPage',
  /** 提现页面 */
  WITHDRAW = 'withDrawPage',
  /** 钱包流水 */
  WALLET_FLOW = 'walletFlowPage',
  /** 邀新落地页 */
  INVITE_USER = 'inviteUserPage',
  /** VIP规则 */
  VIP_RULE = 'vipRulePage',
  /** VIP细则 */
  VIP_RULE_DETAIL = 'vipRuleDetailPage',
  /** 删除用户数据 */
  DELETE_USER_DATA = 'deleteUserData',
  /** 删除用户数据 */
  INTERNAL_H5 = 'internalH5',
  /** 官网页面 */
  WEBSITE = 'website',
  /** 拒绝用信问卷页 */
  CREDIT_REFUSE_QUESTION = 'creditRefuseQuestion',
  /** 用信拒绝去确认页 */
  CREDIT_REFUSE_CONFRIM = 'creditRefuseConfirm',
  /** 自动代扣签约页面 */
  AUTOMATIC_WITHHOLD = 'automaticWithhold',
  /** 自动代扣协议页面 */
  AUTOMATIC_WITHHOLD_PROTOCOL = 'automaticWithholdProtocol',
  /** 相机页 */
  SELFIE = 'SelfiePage',
  /** 验证基础信息 */
  VERIFY_BASIC_INFO = 'verifyBasicInfo',
  /** 修改手机号 */
  CHANGE_MOBILE = 'changeMobile',
  /** OCR摄像头拍照页面 */
  OCR_TAKE_PHOTO = 'ocrTakePhoto',
  /** 城市选择页 */
  REGIONAL_SELECTION_PAGE = 'regionalSelectionPage',
  /** 验证原手机号 */
  VERIFY_ORIGINAL_MOBILE = 'verifyOriginalMobile',
  /** 欢迎页 */
  WELCOME_PAGE = 'welcomePage',
  /** 修改手机号验证码 */
  NEW_MOBILE_CMS_CODE = 'newMobileSmsCode',
  /** 一键代扣 */
  DIRECT_DEBIT_PAGE = 'directDebitPage',
  /** 兑换优惠券 */
  REDEEM_COUPONS = 'redeemCoupons',
  /** 启动页活动 */
  SPLASH_ACTIVITY = 'splashActivity',
  /** OCR 信息更新 */
  OCR_INFO_UPDATE = 'ocrInfoUpdatePage',
}

/** HomeTabStack 页面列表 */
export const HomeTabScreenList: RouterConfig[] = [
  RouterConfig.RELOAN_PRODUCT_SELECT,
  RouterConfig.HOME_SCREEN,
  RouterConfig.RELOAN_QUESTION,
  RouterConfig.WAIT_CHECK,
  // RouterConfig.AD_WAIT_CHECK,
  RouterConfig.REJECT,
  RouterConfig.MULTI_PERIOD_REPAYMENT,
  RouterConfig.REPAYMENT,
  RouterConfig.WAIT_PAYMENT,
  RouterConfig.COMFIRM_LOAN,
  RouterConfig.MULTI_PERIOD_COMFIRM_LOAN,
  RouterConfig.FACE_EXPIRED,
];

/** UserTabStack 页面列表 */
export const UserTabScreenList: RouterConfig[] = [RouterConfig.MY];

/** 钱包列表 */
export const WalletTabScreenList: RouterConfig[] = [RouterConfig.WALLET];

export default RouterConfig;
