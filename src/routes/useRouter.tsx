/* eslint-disable react/no-unstable-nested-components */
/* eslint-disable react/react-in-jsx-scope */
import { BottomTabBar, Image } from '@/components';
import {
  ConfirmLoanPage,
  MultiPeriodConfirmLoanPage,
  FaceExPpiredPage,
  HomePage,
  MyPage,
  RejectPage,
  ReLoanProductSelect,
  ReloanQuestionPage,
  RepaymentPage,
  WaitCheckPage,
  WaitPaymentPage,
  WalletPage,
  MultiPeriodRepaymentPage,
} from '@/pages';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { useState } from 'react';
import { useNameSpace } from '../i18n';
import RouterConfig from './routerConfig';
import { useTheme } from '@/hooks';

const HomeStack = createNativeStackNavigator();
const WalletStack = createNativeStackNavigator();
const MyStack = createNativeStackNavigator();
const Tab = createBottomTabNavigator();

function HomeStackScreen() {
  return (
    <HomeStack.Navigator
      screenOptions={{
        animation: 'default',
        headerShown: false,
      }}
      initialRouteName={RouterConfig.COMFIRM_LOAN}>
      <HomeStack.Screen name={RouterConfig.HOME_SCREEN} component={HomePage} />
      <HomeStack.Screen name={RouterConfig.RELOAN_PRODUCT_SELECT} component={ReLoanProductSelect} />
      <HomeStack.Screen name={RouterConfig.RELOAN_QUESTION} component={ReloanQuestionPage} />
      <HomeStack.Screen name={RouterConfig.WAIT_CHECK} component={WaitCheckPage} />
      <HomeStack.Screen
        name={RouterConfig.MULTI_PERIOD_REPAYMENT}
        component={MultiPeriodRepaymentPage}
      />
      <HomeStack.Screen
        name={RouterConfig.REPAYMENT}
        options={{ headerShown: false }}
        component={RepaymentPage}
      />
      <HomeStack.Screen
        name={RouterConfig.MULTI_PERIOD_COMFIRM_LOAN}
        options={{ headerShown: false }}
        component={MultiPeriodConfirmLoanPage}
      />
      <HomeStack.Screen
        name={RouterConfig.COMFIRM_LOAN}
        options={{ headerShown: false }}
        component={ConfirmLoanPage}
      />
      <HomeStack.Screen name={RouterConfig.REJECT} component={RejectPage} />
      <HomeStack.Screen name={RouterConfig.WAIT_PAYMENT} component={WaitPaymentPage} />
      <HomeStack.Screen name={RouterConfig.FACE_EXPIRED} component={FaceExPpiredPage} />
    </HomeStack.Navigator>
  );
}

function WalletStackScreen() {
  return (
    <WalletStack.Navigator
      screenOptions={{
        animation: 'default',
        headerShown: false,
      }}>
      <WalletStack.Screen name={RouterConfig.WALLET} component={WalletPage} />
    </WalletStack.Navigator>
  );
}

function MyStackScreen() {
  return (
    <MyStack.Navigator
      screenOptions={{
        animation: 'default',
        headerShown: false,
      }}>
      <MyStack.Screen name={RouterConfig.MY} component={MyPage} />
    </MyStack.Navigator>
  );
}

function HomeTabs() {
  const bottomTabs = useBottomTab();
  const theme = useTheme();
  return (
    <Tab.Navigator
      // initialRouteName={RouterConfig.SPLASH}
      tabBar={(props: any) => (
        <BottomTabBar
          {...props}
          bottomTabs={bottomTabs}
          tabBarButton
          focusedIconColor={theme['primary-color-500']}
          iconColor={theme['icon-color']}
          navigateCallbak={() => {}}
        />
      )}
      screenOptions={{
        tabBarHideOnKeyboard: true,
        headerShown: false,
        tabBarStyle: {
          position: 'absolute',
          display: 'flex',
        },
      }}>
      <Tab.Screen name={RouterConfig.HOME_TAB} component={HomeStackScreen} />
      <Tab.Screen name={RouterConfig.WALLET_TAB} component={WalletStackScreen} />
      <Tab.Screen name={RouterConfig.USER_TAB} component={MyStackScreen} />
    </Tab.Navigator>
  );
}

function useRouter({ navigation }: any) {
  const onNavigatePage = ({ pageName, params }: { pageName: string; params?: any }) => {
    navigation.navigate(pageName, params);
  };

  return {
    onNavigatePage,
  };
}

function useBottomTab() {
  const t = useNameSpace('tabString').t;
  const [bottomTabs] = useState<any>({
    [`${RouterConfig.HOME_TAB}`]: {
      name: t('home'),
      key: RouterConfig.HOME_TAB,
      icon: (
        <Image
          name="_homeDisActive"
          style={{
            paddingBottom: 16,
          }}
        />
      ),
      activeIcon: (
        <Image
          name="_homeActive"
          style={{
            paddingBottom: 16,
          }}
        />
      ),
    },
    [`${RouterConfig.WALLET_TAB}`]: {
      name: t('wallet'),
      key: RouterConfig.WALLET_TAB,
      icon: (
        <Image
          name="_walletDisActive"
          style={{
            paddingBottom: 16,
          }}
        />
      ),
      activeIcon: (
        <Image
          name="_walletActive"
          style={{
            paddingBottom: 16,
          }}
        />
      ),
    },
    [`${RouterConfig.USER_TAB}`]: {
      name: t('user'),
      key: RouterConfig.USER_TAB,
      icon: (
        <Image
          name="_userDisActive"
          style={{
            paddingBottom: 16,
          }}
        />
      ),
      activeIcon: (
        <Image
          name="_userActive"
          style={{
            paddingBottom: 16,
          }}
        />
      ),
    },
  });

  return bottomTabs;
}

export { HomeTabs, RouterConfig, useRouter };
