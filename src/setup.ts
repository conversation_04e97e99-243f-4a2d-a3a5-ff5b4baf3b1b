/* eslint-disable no-extend-native */
import { DeviceDataReport, ErrorLog, TrackEvent, log } from '@/utils';
import { Platform, UIManager } from 'react-native';
import { BaseConfig, baseConfigForSetup } from './baseConfig';
import { fcmForSetup } from './business/cloudMessage';
import { setup as setupForI18n } from './i18n';
import logDBOperation from './logCenter/logDBOperation';

if (Platform.OS === 'android') {
  if (UIManager.setLayoutAnimationEnabledExperimental) {
    UIManager.setLayoutAnimationEnabledExperimental(true);
  }
}

// @ts-ignore
// TouchableOpacity.defaultProps = {
//   activeOpacity: 0.5,
// };

/** 自定义方法 写在此处 ts 不会提示错误*/
declare global {
  interface String {
    /** 格式化金融单位 */
    toFormatFinance(isEmptyline?: boolean): string;
    /** 格式化日期为英式写法 */
    toFormatDate(): string;
    /** 格式化天 */
    toFormatDay(): string;
    /** 格式化月 */
    toFormatMonth(): string;
    /** 格式化账期 */
    toFormatPeriod(): string;
    /** 增加电话号码前缀 */
    toPrefixPhoneNumber(): string;
    /** 格式化 clabe 号 */
    toFormatClabe(isEncrypt?: boolean): string;
  }
}

String.prototype.toFormatDay = function () {
  if (Number(this.valueOf()) > 1) {
    return `${this.valueOf()} días`;
  } else {
    return `${this.valueOf()} día`;
  }
};

String.prototype.toFormatMonth = function () {
  if (Number(this.valueOf()) > 1) {
    return `${this.valueOf()}meses`;
  } else {
    return `${this.valueOf()}mes`;
  }
};

String.prototype.toFormatPeriod = function () {
  return `${this.valueOf()}pagos`;
};

String.prototype.toFormatFinance = function (isEmptyline: boolean = true) {
  const strSplitDecimal = this.valueOf().split('.');
  const reversedStr = strSplitDecimal[0].split('').reverse().join('');
  const chunks = reversedStr.match(/.{1,3}/g);
  if (!chunks) {
    return ` ${BaseConfig.financeUnit} ${this.valueOf()}`;
  }
  const formattedStr = chunks.join(',').split('').reverse().join('');
  if (strSplitDecimal.length > 1) {
    return `${BaseConfig.financeUnit}${isEmptyline ? ' ' : ''}${formattedStr}.${
      strSplitDecimal[1]
    }`;
  } else {
    return `${BaseConfig.financeUnit}${isEmptyline ? ' ' : ''}${formattedStr}`;
  }
};

String.prototype.toFormatDate = function () {
  if (this.valueOf().length <= 1) {
    return this.valueOf();
  }
  const inputDate = new Date(this.valueOf());
  const outputDate = inputDate.toLocaleDateString('en-GB');
  const formattedDate = outputDate.replace(/-/g, '/');
  return ` ${formattedDate}`;
};

String.prototype.toPrefixPhoneNumber = function () {
  return `+52 ${this.valueOf()}`;
};

String.prototype.toFormatClabe = function (isEncrypt: boolean = false) {
  // 去掉空格和非数字字符
  let str = this.valueOf().replace(/\s+|\D/g, '');
  // 分割字符串，每四个字符一组
  const chunks = str.match(/.{1,4}/g);
  // 合并分割后的字符串数组，每个分组末尾添加一个空格
  if (isEncrypt) {
    return chunks ? '**** **** ' + str.slice(str.length - 4) : '';
  } else {
    return chunks ? chunks.join(' ').trim() : '';
  }
};

/** @description 项目启动配置 */
const info: boolean = true;
const debug: boolean = true;

export default async () => {
  // 获取系统配置和初始化
  info && log.info('基础配置初始化');
  await baseConfigForSetup(debug);
  /**
   * @todo debug & fix
   */
  info && log.info('数据上报模块初始化');
  DeviceDataReport.setup(BaseConfig.apiBaseUrl);

  // info && log.info('设备缓存修复');
  // localCacheForSetup();

  // 每次系统启动的时候 上传错误日志
  info && log.info('上报错误日志');
  ErrorLog.uploadErrorLog(debug);
  // 初始化push
  info && log.info('初始化fcmToken');
  fcmForSetup();

  /** 删除三十天以前的日志缓存 */
  logDBOperation.delete_latest_days({
    day: 7,
  });
};

/** 配置启动 */
export const setup = () => {
  setupForI18n();
};

setup();
