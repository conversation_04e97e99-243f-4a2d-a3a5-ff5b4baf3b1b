import { useEffect, useState } from 'react';
import { BehaviorSubject } from 'rxjs';
import logDBOperation from '../../logCenter/logDBOperation';
import { EFilterType } from '../../logCenter/type';
import {
  EDevicePerformanceStats,
  ILoggerManagerContext as ILoggerMangerMessageContext,
  Icondition,
} from './types';

class LoggerManager {
  /**
   * 常驻性能指标展示列表
   */
  showList: EDevicePerformanceStats[] = [
    EDevicePerformanceStats.cpu,
    EDevicePerformanceStats.ui,
    EDevicePerformanceStats.js,
    EDevicePerformanceStats.mem,
    // EDevicePerformanceStats.totalMem,
  ];

  /**
   * 视图的展示状态
   */
  showViewStatus: boolean = __DEV__;
  /**
   * logCount
   */
  logCount: number = 0;
  /**
   * 过滤的日志数据
   */
  filterText: string = '';
  /**
   * 过滤的日志类型
   */
  filterType: EFilterType = EFilterType.content;
  /**
   * modal 弹窗的展示状态
   */
  modalVisiable: boolean = false;

  messageCenter: BehaviorSubject<ILoggerMangerMessageContext>;
  action = {
    setViewPerformanceStatsList: (list: EDevicePerformanceStats[]) => {
      this.context.state.showList = list;
      this.broadcastContextChange();
    },
    openView: () => {
      // __DEV__ && console.log('#loggerManager show view');
      this.context.state.showViewStatus = true;
      this.broadcastContextChange();
    },
    closeView: () => {
      // __DEV__ && console.log('#loggerManager close view');
      this.context.state.showViewStatus = false;
      this.broadcastContextChange();
    },
    openModal: () => {
      // __DEV__ && console.log('#loggerManager open modal');
      this.context.state.modalVisiable = true;
      this.broadcastContextChange();
    },
    closeModal: () => {
      // __DEV__ && console.log('#loggerManager close modal');
      this.context.state.modalVisiable = false;
      this.broadcastContextChange();
    },
    updateLogCount: (count: number) => {
      if (count !== this.context.state.logCount) {
        this.context.state.logCount = count;
        this.broadcastContextChange();
      }
    },
    updateFilterCondition: async (condition: Icondition) => {
      const { text, type } = condition;
      this.context.state.filterText = text || '';
      this.context.state.filterType = type || EFilterType.content;
      const queryResult = await logDBOperation.query({
        type: 'count',
        filterText: this.context.state.filterText,
        filterType: this.context.state.filterType,
      });
      // query count
      if (queryResult?.rows?.length === 1 && queryResult.rows.item(0)) {
        // query success
        const count = queryResult.rows.item(0).count;
        // __DEV__ && console.log('count is', count);
        this.context.state.logCount = count;
      }
      this.broadcastContextChange();
    },
  };

  broadcastContextChange = () => {
    this.messageCenter.next({ ...this.context });
  };

  context: ILoggerMangerMessageContext = {
    action: this.action,
    state: {
      showViewStatus: this.showViewStatus,
      showList: this.showList,
      modalVisiable: this.modalVisiable,
      filterText: this.filterText,
      filterType: this.filterType,
      logCount: this.logCount,
    },
  };

  constructor() {
    this.messageCenter = new BehaviorSubject<ILoggerMangerMessageContext>(this.context);
  }
}

export const loggerManagerInstance = new LoggerManager();

export const useLoggerManager = () => {
  const [contextCache, setContextCache] = useState<ILoggerMangerMessageContext>(
    loggerManagerInstance.context,
  );

  useEffect(() => {
    const subject = loggerManagerInstance.messageCenter.subscribe(context => {
      setContextCache(context);
    });
    return () => {
      subject.unsubscribe && subject.unsubscribe();
    };
  }, []);

  return contextCache;
};
