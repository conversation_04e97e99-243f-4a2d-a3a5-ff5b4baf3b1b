import { EFilterType } from '../../logCenter/type';

export interface ILoggerManagerContext {
  action: ILoggerManagerAction;
  state: ILoggerManagerState;
}

export interface ILoggerManagerState {
  showList: EDevicePerformanceStats[];
  showViewStatus: boolean;
  modalVisiable: boolean;
  filterText: string;
  filterType: EFilterType;
  logCount: number;
}

export interface Icondition {
  text?: string;
  type?: EFilterType;
}

export interface ILoggerManagerAction {
  setViewPerformanceStatsList: (list: EDevicePerformanceStats[]) => void;
  openView: () => void;
  closeView: () => void;
  openModal: () => void;
  closeModal: () => void;
  updateFilterCondition: (condition: Icondition) => void;
  updateLogCount: (count: number) => void;
}

export enum EDevicePerformanceStats {
  cpu = 'cpu',
  ui = 'ui',
  js = 'js',
  mem = 'mem',
  totalMem = 'totalMem',
}
