export interface IKVValue {}

export interface IKVState {
  loading: boolean;
}

export interface IKVAction {
  // string
  setString: (key: string, value: string) => void;
  getString: (key: string) => string | null | undefined;
  // int
  setInt: (key: string, value: number) => void;
  getInt: (key: string) => number | null | undefined;
  // boolean
  setBoolean: (key: string, value: boolean) => void;
  getBoolean: (key: string) => boolean | null | undefined;
  // map
  setMap: (key: string, value: Record<string, any>) => void;
  getMap: (key: string) => Record<string, any> | null | undefined;
  // array
  setArray: (key: string, value: any[]) => void;
  getArray: (key: string) => any[] | null | undefined;
  // remove
  remove: (key: string) => void;
}
