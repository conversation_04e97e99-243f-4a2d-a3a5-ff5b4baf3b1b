// import { useState, useEffect, useRef } from 'react';
import { MMKVInstance, MMKVLoader } from 'react-native-mmkv-storage';
// import { BehaviorSubject } from 'rxjs';
import Singleton from '../../utils/singleton';

// types
import { log } from '@/utils';
import type { IKVAction } from './type';

export const _MMKV_: MMKVInstance = Singleton.getOrRegister(
  'MMKV',
  new MMKVLoader().withInstanceID('KVManagerStore').initialize(),
);
/**
 * manager class
 */

class KVManager {
  MMKV = _MMKV_;
  /**
   * action
   */
  action: IKVAction = {
    // string
    setString: (key: string, value: string) => {
      // log.debug(`#MMKV setString key=${key}`, {
      //   value,
      // });
      this.MMKV.setString(key, value);
    },
    getString: (key: string) => {
      const value = this.MMKV.getString(key);
      // log.debug(`#MMKV getString key=${key}`, {
      //   value,
      // });
      return value;
    },
    // int
    setInt: (key: string, value: number) => {
      // log.debug(`#MMKV setInt key=${key}`, {
      //   value,
      // });
      this.MMKV.setInt(key, value);
    },
    getInt: (key: string) => {
      const value = this.MMKV.getInt(key);
      // log.debug(`#MMKV getInt key=${key}`, {
      //   value,
      // });
      return value;
    },
    // boolean
    setBoolean: (key: string, value: boolean) => {
      // log.debug(`#MMKV setBoolean key=${key}`, {
      //   value,
      // });
      this.MMKV.setBool(key, value);
    },
    getBoolean: (key: string) => {
      const value = this.MMKV.getBool(key);
      // log.debug(`#MMKV getBoolean key=${key}`, {
      //   value,
      // });
      return value;
    },
    // map
    setMap: (key: string, value: Record<string, any>) => {
      // log.debug(`#MMKV setMap key=${key}`, {
      //   value,
      // });
      this.MMKV.setMap(key, value);
    },
    getMap: (key: string) => {
      const value: Record<string, any> | null | undefined = this.MMKV.getMap(key);
      // log.debug(`#MMKV getMap key=${key}`, {
      //   value,
      // });
      return value;
    },
    // array
    setArray: (key: string, value: any[]) => {
      // log.debug(`#MMKV setArray key=${key}`, {
      //   value,
      // });
      this.MMKV.setArray(key, value);
    },
    getArray: (key: string) => {
      const value = this.MMKV.getArray(key);
      // log.debug(`#MMKV getArray key=${key}`, {
      //   value,
      // });
      return value;
    },
    // remove
    remove: (key: string) => {
      this.MMKV.removeItem(key);
    },
  };
}

export const KVManagerInstance = Singleton.getOrRegister(
  'KVManagerInstance',
  new KVManager(),
) as KVManager;

/**
 * react hook
 */

export default KVManagerInstance;
