/**
 * @moudule Manager:User
 *
 */
import { BaseConfig } from '@/baseConfig';
import { UserModel } from '@/dataModel';
import { BaseEnumsSpace, HitPointEnumsSpace, UserEnumsSpace } from '@/enums';
import {
  BaseInfoManager,
  MessageDataStoreInstance,
  modalDataStoreInstance,
  ModalList,
} from '@/managers';
import { RouterConfig } from '@/routes';
import {
  fetchCheckMobile,
  fetchGetAppConfig,
  fetchInviteCode,
  fetchIsInviteDuring,
  fetchRichTextConfig,
  fetchUserState,
  fetchUserWaletInfo,
  fetchVipLevel,
  fetchVipRule,
  fetchVipPopData,
} from '@/server';
import { UserVOSpace } from '@/types';
import { Log, nav } from '@/utils';
import { BehaviorSubject } from 'rxjs';

export type UserInfoContextType = {
  userModel: UserModel;
};
/** 更新appConfig来源 */
export enum EUpdateAppConfigSource {
  /** 从process接口获取配置 */
  PROCESS = 'process',
  /** 从appConfig接口获取配置 */
  APP_CONFIG = 'appConfig',
  /** 异常场景 */
  NONE = '',
}
class UserInfoManager {
  constructor() {
    this.context = {
      userModel: new UserModel(),
    };
    this.messageCenter = new BehaviorSubject<UserInfoContextType>(this.context);
  }

  messageCenter: BehaviorSubject<UserInfoContextType>;
  context: UserInfoContextType;

  updateMobile(mobile: string) {
    this.context.userModel.mobile = mobile;
    this.boardcastContextChange();
  }

  updateToken(token: string) {
    this.context.userModel.token = token;
    this.boardcastContextChange();
  }

  updateUserId(userId: string) {
    this.context.userModel.userId = userId;
    this.boardcastContextChange();
  }

  updateApplyId(applyOrderId: string) {
    this.context.userModel.userState.applyOrderId = applyOrderId;
    this.boardcastContextChange();
  }

  updatePreApplyId(preApplyOrderId: string) {
    this.context.userModel.userState.preApplyOrderId = preApplyOrderId;
    this.boardcastContextChange();
  }

  clearAll() {
    this.context.userModel.userId = '';
    this.context.userModel.token = '';
    this.context.userModel.mobile = '';
    this.context.userModel.userState = {} as UserVOSpace.BackUserStateType;
    this.boardcastContextChange();
  }

  /** 更新用户状态和基础配置 */
  async updateUserState(config?: EUpdateAppConfigSource): Promise<boolean> {
    let flag: EUpdateAppConfigSource = config || EUpdateAppConfigSource.NONE;
    if (this.context.userModel.isLogined && !config) {
      flag = EUpdateAppConfigSource.PROCESS;
    } else {
      flag = EUpdateAppConfigSource.APP_CONFIG;
    }

    switch (flag) {
      case EUpdateAppConfigSource.PROCESS:
        const result = await fetchUserState();

        if (result?.code !== BaseEnumsSpace.ENetworkStatus.SUCCESS) {
          return false;
        }

        this.context.userModel.userState = result?.data;

        BaseInfoManager.updateAppConfig(result.data.appConfig);
        if (result.data.appConfig.vipSwitch === 'YES') {
          this.updateVipNotify();
        }

        this.boardcastContextChange();
        return true;
      case EUpdateAppConfigSource.APP_CONFIG:
        const appConfigResult = await fetchGetAppConfig();

        if (appConfigResult?.code !== BaseEnumsSpace.ENetworkStatus.SUCCESS) {
          return false;
        }

        BaseInfoManager.updateAppConfig(appConfigResult.data);

        this.boardcastContextChange();
        return true;
      default:
        return false;
    }
  }

  async updateVipNotify(): Promise<void> {
    const result = await fetchVipPopData();
    if (result.code === 0) {
      if (result.data.length !== 0) {
        setTimeout(() => {
          modalDataStoreInstance.openModal({
            key: ModalList.VIP_NOTIFY,
            extra: result.data[0],
          });
        }, 1000);
      }
    }
  }

  /** 启动页-核实手机号状态,根据注册状态进行跳转 */
  async checkMobileAndNavigateFlow({
    /** 启动页上报埋点 */
    buryCallback,
    /** appCenter初始化 */
    appCenterInitCallback,
  }: {
    /** 启动页上报埋点 */
    buryCallback?: () => Promise<void>;
    /** appCenter初始化 */
    appCenterInitCallback?: () => void;
  }): Promise<void> {
    const { isLogined, mobile } = this.context.userModel;
    if (!isLogined) {
      nav.nextToTopRouter(RouterConfig.SPLASH as any);
      return;
    }

    // 更新通知消息
    // MessageDataStoreInstance.updateMessageUnReadState();

    typeof buryCallback === 'function' && buryCallback();

    let result = await fetchCheckMobile({
      mobile,
    });
    await this.updateUserState();
    if (result?.code === BaseEnumsSpace.ENetworkStatus.SUCCESS) {
      typeof appCenterInitCallback === 'function' && appCenterInitCallback();
      const {
        data: { registerFlag = undefined },
      } = result;
      BaseInfoManager.updateSkipSetPasswordStatus(true);
      switch (registerFlag) {
        case UserEnumsSpace.ELoginStatus.LOGIN:
          BaseInfoManager.updateSetPasswordStatus(true);
          nav.nextToTopRouter(RouterConfig.SPLASH as any);
          break;
        case UserEnumsSpace.ELoginStatus.REGISTER:
          BaseInfoManager.updateSetPasswordStatus(false);
          nav.nextToTopRouter(RouterConfig.SPLASH as any);
          break;
        case UserEnumsSpace.ELoginStatus.SETPWD:
          BaseInfoManager.updateSetPasswordStatus(false);
          nav.nextToTopRouter(RouterConfig.SPLASH as any);
          break;
        default:
          nav.nextToTopRouter(RouterConfig.SPLASH as any);
      }
    } else {
      nav.nextToTopRouter();
    }
  }

  /** 获取邀新数据并且跳转启动页 */
  async getInviteDataAndNavigateLand(
    params: { link?: string; title?: string } = {
      link: '',
      title: '',
    },
  ) {
    const { link = `${BaseConfig.h5Url}/h5`, title = 'Información general' } = params;
    BaseInfoManager.changeLoadingModalVisible(true);
    let result = '';
    let rule = '';
    /** 活动是否在持续期间 */
    await fetchIsInviteDuring().then(async isInviteDuringResult => {
      if (
        isInviteDuringResult.code === BaseEnumsSpace.ENetworkStatus.SUCCESS &&
        isInviteDuringResult.data.isDuring
      ) {
        const { startTime = '*', endTime = '*', rewardAmount = '*' } = isInviteDuringResult.data;
        result += `startTime=${startTime}&endTime=${endTime}&awardMoney=${rewardAmount}&`;
        /** 活动相关数据(邀请码、邀请链接，或者其他活动需要展示的数据) */
        await fetchInviteCode().then(async inviteCodeResult => {
          if (inviteCodeResult.code === BaseEnumsSpace.ENetworkStatus.SUCCESS) {
            const {
              inviteCode = '*',
              inviteUrl = '*',
              couponRewardAmount = 0,
            } = inviteCodeResult.data;
            result += `adjustCode=${inviteCode}&downloadUrl=${inviteUrl}&couponRewardAmount=${couponRewardAmount}&`;
          } else {
            result = '';
          }
          /** 活动相关数据(邀请数量、奖励金额，或者其他活动需要展示的数据) */
          await fetchUserWaletInfo().then(async userWaletInfoResult => {
            if (userWaletInfoResult.code === BaseEnumsSpace.ENetworkStatus.SUCCESS) {
              const { invitedCount = 0, rewardAmount = 0 } = userWaletInfoResult.data;
              result += `inviteAmount=${invitedCount}&totalMoney=${rewardAmount}`;
              /** 活动规则 */
              await fetchRichTextConfig({ scene: 'INVITE' }).then(async ruleResult => {
                if (ruleResult.code === BaseEnumsSpace.ENetworkStatus.SUCCESS) {
                  rule = ruleResult.data;
                } else {
                  result = '';
                }
              });
            } else {
              result = '';
            }
          });
        });
      } else {
        result = '';
      }
    });
    BaseInfoManager.changeLoadingModalVisible(false);

    if (result && rule) {
      nav.navigate(RouterConfig.INVITE_USER as any, {
        query: result,
        rule,
        link,
        title,
        pageKey: HitPointEnumsSpace.EPageKey.P_INVITE,
      });
    } else {
      BaseInfoManager.changeLoadingModalVisible(false);
      Log.error('#Land invite page > get query and rule error');
    }

    return result;
  }

  /** 获取vip配置信息跳转启动页 */
  async getVipConfigAndNavigate(
    params: { link?: string; title?: string } = {
      link: '',
      title: '',
    },
  ) {
    const { link = `${BaseConfig.h5Url}/vip`, title = 'Cashback VIP' } = params;
    BaseInfoManager.changeLoadingModalVisible(true);
    let query = '';
    let rule;

    await fetchVipLevel().then(async vipLevelResult => {
      if (vipLevelResult.code !== BaseEnumsSpace.ENetworkStatus.SUCCESS) {
        BaseInfoManager.changeLoadingModalVisible(false);
        return;
      }
      let mobile = BaseInfoManager.context.baseModel.contactConfig.mobile;
      query += `status=${vipLevelResult.data.status}&level=${vipLevelResult.data.level}&effectiveCount=${vipLevelResult.data.effectiveCount}&rate=${vipLevelResult.data.rate}&upgradeCount=${vipLevelResult.data.upgradeCount}&recoverCount=${vipLevelResult.data.recoverCount}&recoverSettleCount=${vipLevelResult.data.recoverSettleCount}&nextRate=${vipLevelResult.data.nextRate}&mobile=${mobile}`;
      await fetchVipRule().then(async vipRuleResult => {
        if (vipRuleResult.code !== BaseEnumsSpace.ENetworkStatus.SUCCESS) {
          BaseInfoManager.changeLoadingModalVisible(false);
          return;
        }
        rule = JSON.stringify(vipRuleResult.data.levelRules);
      });
    });

    /** @todo 携带服务端数据 vip基本信息、等级信息 */
    if (query && rule) {
      nav.navigate(RouterConfig.VIP_RULE as any, {
        query,
        rule,
        link,
        title,
        loadingColor: 'primary-color-600',
        pageKey: HitPointEnumsSpace.EPageKey.P_VIP_CASHBACK,
      });
    } else {
      BaseInfoManager.changeLoadingModalVisible(false);
      Log.error('#Land vip page > get query and rule error');
    }
  }

  /** 跳转vip规则详情页 */
  async navigateVipDetailRule(
    params: { link?: string; title?: string } = {
      link: '',
      title: '',
    },
  ) {
    const { link = `${BaseConfig.h5Url}/vip/detail`, title = 'Conocer Más' } = params;
    BaseInfoManager.changeLoadingModalVisible(true);
    let query = '';

    let mobile = BaseInfoManager.context.baseModel.contactConfig.mobile;
    query += `mobile=${mobile}`;
    nav.navigate(RouterConfig.VIP_RULE_DETAIL as any, {
      query,
      rule: '',
      link,
      title,
      loadingColor: 'primary-color-600',
      pageKey: HitPointEnumsSpace.EPageKey.P_VIP_CASHBACK_DETAIL,
    });
  }

  boardcastContextChange = () => {
    this.messageCenter.next({ ...this.context });
  };
}

const instance = new UserInfoManager();

export default instance;
