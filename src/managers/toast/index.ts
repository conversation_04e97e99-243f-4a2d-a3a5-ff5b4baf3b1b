/**
 * @moudule Manager:Modal
 */
import uuid from 'react-native-uuid';
import { BehaviorSubject } from 'rxjs';

/**
 * @done 弹框封装成传入 title、content、callback
 */
export type ToastContextType = {
  toastList: ToastItemType[];
};

/**
 * @type key 弹框键名
 * @type title 标题
 * @type i18nkey i18n键值
 * @type content 内容
 * @type isBackdropClose 点击背景是否关闭
 * @type confirmBtnName 确认按钮命名
 * @type confirmBtnCallback 确认按钮回调
 * @type cancelBtnName 取消按钮命名
 * @type cancelBtnCallback 取消按钮回调
 * @type extra 扩展属性,临时做扩展使用
 */
export type ToastItemType = {
  i18nKey?: string;
  imageKey?: string;
  content?: string;
  duration?: 'LONG' | 'SHORT';
  id?: string;
};

class ToastManager {
  constructor() {
    this.context = {
      toastList: [],
    };
    this.initContext();
    this.messageCenter = new BehaviorSubject<ToastContextType>(this.context);
  }

  messageCenter: BehaviorSubject<ToastContextType>;
  context: ToastContextType;

  async initContext() {}

  show(toast: ToastItemType) {
    this.context.toastList.push(toast);
    toast.id = uuid.v4() as string;
    const duration = toast.duration === 'LONG' ? 3000 : 2000;
    setTimeout(() => {
      this.context.toastList.shift();
      this.boardcastContextChange();
    }, duration);
    this.boardcastContextChange();
  }

  boardcastContextChange = () => {
    this.messageCenter.next({ ...this.context });
  };
}

const instance = new ToastManager();

export const ToastDataStoreInstance = instance;
