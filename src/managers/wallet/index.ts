/**
 * @moudule Manager:Wallet
 *
 */
import { WalletModel } from '@/dataModel';
import { BehaviorSubject } from 'rxjs';
import { fetchUserWaletInfo, fetchDefaultBankCardInfo, fetchWalletBalanceWithdraw } from '@/server';
import { BaseEnumsSpace } from '@/enums';

export type WalletInfoContextType = {
  walletModel: WalletModel;
};

class WalletManager {
  constructor() {
    this.context = {
      walletModel: new WalletModel(),
    };
    this.messageCenter = new BehaviorSubject<WalletInfoContextType>(this.context);
  }

  messageCenter: BehaviorSubject<WalletInfoContextType>;
  context: WalletInfoContextType;

  boardcastContextChange = () => {
    this.messageCenter.next({ ...this.context });
  };

  /** 更新钱包信息 */
  async updateWalletInfo(): Promise<boolean> {
    let result = await fetchUserWaletInfo();
    if (result.code === BaseEnumsSpace.ENetworkStatus.SUCCESS) {
      this.context.walletModel.walletInfo = result.data;
      if (!this.context.walletModel.walletInfo.withDrawMinMultiple) {
        this.context.walletModel.walletInfo.withDrawMinMultiple = 50;
      }

      this.boardcastContextChange();
    }
    return result.code === BaseEnumsSpace.ENetworkStatus.SUCCESS;
  }

  /** 获取银行卡信息 */
  async updateBankCardInfo(): Promise<boolean> {
    let result = await fetchDefaultBankCardInfo();
    if (result.code === BaseEnumsSpace.ENetworkStatus.SUCCESS) {
      this.context.walletModel.defaultBankCardInfo = result.data;

      this.boardcastContextChange();
    }
    return result.code === BaseEnumsSpace.ENetworkStatus.SUCCESS;
  }

  /** 提现 */
  async doWithdraw(amount: number): Promise<boolean> {
    let result = await fetchWalletBalanceWithdraw({
      amount,
    });
    if (result.code === BaseEnumsSpace.ENetworkStatus.SUCCESS) {
      this.boardcastContextChange();
    }
    return result.code === BaseEnumsSpace.ENetworkStatus.SUCCESS;
  }

  /** 更新展示状态 */
  async updateBalanceTipsDisplay(display: boolean): Promise<boolean> {
    this.context.walletModel.balanceTipsDisplay = display;
    this.boardcastContextChange();
    return true;
  }

  /** 更新提现金额 */
  async updateWithdrawAmount(amount: number): Promise<boolean> {
    this.context.walletModel.withdrawAmount = amount;
    this.context.walletModel.withdrawStatus = 'pending';

    // 且高出余额 => 提示余额不足状态
    if (amount > this.context.walletModel.walletInfo.totalBalance) {
      this.context.walletModel.withdrawStatus = 'not_enough';
    }

    // 提现在正常区间以内
    if (amount <= this.context.walletModel.walletInfo.totalBalance) {
      if (amount % this.context.walletModel.walletInfo.withDrawMinMultiple === 0) {
        // 满足最小提现倍数的条件
        this.context.walletModel.withdrawStatus = 'ok';
      } else {
        // 不满足最小提现倍数的条件
        this.context.walletModel.withdrawStatus = 'not_multiple';
      }
    }

    // 计算利息
    if (amount - this.context.walletModel.walletInfo.withDrawFee > 0) {
      this.context.walletModel.reciveTotal =
        amount - this.context.walletModel.walletInfo.withDrawFee;
    } else {
      this.context.walletModel.reciveTotal = 0;
    }

    this.boardcastContextChange();
    return true;
  }
}

const instance = new WalletManager();

export default instance;
