/**
 * @moudule Manager: defaultConfig
 *
 */
import { defaultConfigModel } from '@/dataModel';
import { BaseEnumsSpace } from '@/enums';
import { fetchAppDefaultConfig } from '@/server';
import { AppDefaultConfig } from '@/types';
import _ from 'lodash';
import { BehaviorSubject } from 'rxjs';

export type AppDefaultConfigContextType = {
  appDefaultConfigModel: defaultConfigModel;
};

class AppDefaultConfigManager {
  constructor() {
    this.context = {
      appDefaultConfigModel: new defaultConfigModel(),
    };
    this.messageCenter = new BehaviorSubject<AppDefaultConfigContextType>(this.context);
  }

  messageCenter: BehaviorSubject<AppDefaultConfigContextType>;
  context: AppDefaultConfigContextType;

  clearAll() {
    this.context.appDefaultConfigModel.defaultConfigState = Object.create(null) as AppDefaultConfig;
    this.boardcastContextChange();
  }

  /** 更新用户状态和基础配置 */
  async updateAppDefaultConfig(): Promise<boolean> {
    // defaultConfigState 有值则不请求接口
    if (!_.isEmpty(this.context.appDefaultConfigModel.defaultConfigState)) {
      return true;
    }
    const result = await fetchAppDefaultConfig();
    if (result?.code !== BaseEnumsSpace.ENetworkStatus.SUCCESS) {
      return false;
    }
    this.context.appDefaultConfigModel.defaultConfigState = result?.data;
    this.boardcastContextChange();
    return true;
  }

  boardcastContextChange = () => {
    this.messageCenter.next({ ...this.context });
  };
}

const instance = new AppDefaultConfigManager();
export default instance;
