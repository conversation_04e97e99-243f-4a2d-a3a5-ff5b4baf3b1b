/**
 * @moudule Manager:CodePush
 */
import { BehaviorSubject } from 'rxjs';

export type CodePushContextType = {
  codePushState: {
    visible: boolean;
    process: number;
    totalSizes: string;
    countdown: number;
  };
};

class CodePushManager {
  constructor() {
    this.context = {
      codePushState: {
        visible: false,
        process: 0,
        totalSizes: '0',
        countdown: 0,
      },
    };
    this.messageCenter = new BehaviorSubject<CodePushContextType>(this.context);
  }

  messageCenter: BehaviorSubject<CodePushContextType>;
  context: CodePushContextType;

  openModal() {
    this.context.codePushState.visible = true;
    this.boardcastContextChange();
  }

  closeModal() {
    this.context.codePushState.visible = false;
    this.boardcastContextChange();
  }

  updateTotalSizes(totalSize: string) {
    this.context.codePushState.totalSizes = totalSize;
    this.boardcastContextChange();
  }

  updateProcess(process: number) {
    this.context.codePushState.process = process;
    this.boardcastContextChange();
  }

  updateCountdown(countdown: number) {
    this.context.codePushState.countdown = countdown;
    this.boardcastContextChange();
  }

  boardcastContextChange = () => {
    this.messageCenter.next({ ...this.context });
  };
}

const instance = new CodePushManager();

// setInterval(() => {
//   let process = instance.context.codePushState.process
//   if (process === 1) {
//     process = 0
//   } else {
//     process += 0.01
//   }
//   instance.updateProcess(Number(process.toFixed(2)))
// }, 100)

export default instance;
