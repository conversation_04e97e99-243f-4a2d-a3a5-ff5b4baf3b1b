/**
 * @moudule Manager:Modal
 */
import { UserInfoManager } from '@/managers';
import { getMessageUnReadCount } from '@/server';
import { UserVOSpace } from '@/types';
import { BehaviorSubject } from 'rxjs';

/**
 * @done 弹框封装成传入 title、content、callback
 */
export type MessageContextType = {
  messageUndoSate: UserVOSpace.UnReadMessageCount;
};

class MessageManager {
  constructor() {
    this.context = {
      messageUndoSate: {
        total: 0,
        activity: 0,
        message: 0,
      },
    };

    this.initContext();

    this.messageCenter = new BehaviorSubject<MessageContextType>(this.context);
  }

  messageCenter: BehaviorSubject<MessageContextType>;

  context: MessageContextType;

  async initContext() {}

  /** 更新未读消息的状态 */
  async updateMessageUnReadState() {
    if (UserInfoManager.context.userModel.isLogined) {
      const result = await getMessageUnReadCount();
      if (result?.code === 0) {
        this.context.messageUndoSate = result.data;
      }
      this.boardcastContextChange();
    }
  }

  boardcastContextChange = () => {
    this.messageCenter.next({ ...this.context });
  };
}

const instance = new MessageManager();

export const MessageDataStoreInstance = instance;
