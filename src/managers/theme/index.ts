/**
 * @moudule Manager:Theme
 */

import { useEffect, useRef, useState } from 'react';
import { Appearance } from 'react-native';
import { BehaviorSubject } from 'rxjs';
import theme from '../../themes';
import Singleton from '../../utils/singleton';
import KVManager from '../kv';

// themes
import {
  DarkTheme as DarkNavigationTheme,
  DefaultTheme as DefaultNavigationTheme,
} from '@react-navigation/native';

/**
 * types
 */
import type { IAdditionalTheme, IThemeAction, IThemeValue, TContextForTheme } from './type';

/**
 * CONSTANTS
 */

const isDarkModeForInitial = false;

// const isDarkModeForInitial =
//   Appearance.getColorScheme() === BaseEnumsSpace.EMode.Dark;
const KEY_FOR_CUSTOM_LIGHT_DARK_MODE = 'config:custom-light-dark-mode';

/**
 * manager class
 */

class ThemeManager {
  value: IThemeValue = {
    currentBasicTheme: theme.defaultTheme,
    currentAdditionalTheme: {
      dark: theme.defaultThemForDark,
      light: theme.defaultThemForLight,
    },
    darkMode: isDarkModeForInitial,
    applicationTheme: {
      ...theme.defaultTheme,
      ...(isDarkModeForInitial ? theme.defaultThemForDark : theme.defaultThemForLight),
    },
    navigationTheme: undefined,
    brandColor: theme.defaultTheme['primary-color-500'],
  };

  context: TContextForTheme;

  broadcastContextChange = () => {
    this.messageCenter.next({ ...this.context });
  };

  messageCenter: BehaviorSubject<TContextForTheme>;

  constructor() {
    this.context = {
      value: this.value,
    };

    this.messageCenter = new BehaviorSubject<TContextForTheme>(this.context);

    // Appearance.addChangeListener(this.syncThemeFromSystem);

    this.updateApplicationTheme();
  }

  syncThemeFromSystem = () => {
    // check if user has been set custom light/dark mode
    const customLightOrDarkMode = KVManager.action.getString(KEY_FOR_CUSTOM_LIGHT_DARK_MODE);

    if (customLightOrDarkMode) {
      return;
    }

    const currentColorSchemeName: 'light' | 'dark' | null | undefined = Appearance.getColorScheme();
    if (currentColorSchemeName) {
      this.context.value.darkMode = currentColorSchemeName === 'dark';
      this.updateApplicationTheme();
    }
  };

  setup = () => {
    const customLightOrDarkMode = KVManager.action.getString(KEY_FOR_CUSTOM_LIGHT_DARK_MODE);
    if (customLightOrDarkMode) {
      this.context.value.darkMode = customLightOrDarkMode === 'dark';
      this.updateApplicationTheme();
    }
  };

  /**
   * action
   */
  action: IThemeAction = {
    updateTheme: (theme: Record<string, string>, additionalTheme?: Partial<IAdditionalTheme>) => {
      this.context.value.currentBasicTheme = theme;
      if (additionalTheme) {
        this.context.value.currentAdditionalTheme = {
          dark: {
            ...this.context.value.currentAdditionalTheme.dark,
            ...additionalTheme.dark,
          },
          light: {
            ...this.context.value.currentAdditionalTheme.light,
            ...additionalTheme.light,
          },
        };
      }
      this.updateApplicationTheme();
    },
    setDarkMode: () => {
      KVManager.action.setString(KEY_FOR_CUSTOM_LIGHT_DARK_MODE, 'dark');
      this.context.value.darkMode = true;
      this.updateApplicationTheme();
    },
    setLightMode: () => {
      KVManager.action.setString(KEY_FOR_CUSTOM_LIGHT_DARK_MODE, 'light');
      this.context.value.darkMode = false;
      this.updateApplicationTheme();
    },
    setDarkOrModeFollowSystem: () => {
      KVManager.action.remove(KEY_FOR_CUSTOM_LIGHT_DARK_MODE);
      this.syncThemeFromSystem();
    },
    getCurrentLightDarkModeKey: () => {
      let result = KVManager.action.getString(KEY_FOR_CUSTOM_LIGHT_DARK_MODE);
      if (result && ['light', 'dark'].indexOf(result) < 0) {
        result = undefined;
        KVManager.action.remove(KEY_FOR_CUSTOM_LIGHT_DARK_MODE);
      }
      if (!result) {
        result = undefined;
      }
      return result as 'light' | 'dark' | undefined;
    },
  };

  updateApplicationTheme = async () => {
    const applicationTheme = this.calculateApplicationTheme();
    const navigationTheme = this.calculateNavigationTheme();
    this.context.value.applicationTheme = applicationTheme;
    this.context.value.navigationTheme = navigationTheme;
    this.context.value.brandColor = applicationTheme['primary-color-500'];
    // const isDark = this.context.value.darkMode;
    // try {
    //   await changeNavigationBarColor(
    //     applicationTheme['background-color-0'],
    //     !isDark,
    //     true,
    //   );
    //   // __DEV__ && console.log(response); // {success: true}
    // } catch (e) {
    //   // __DEV__ && console.log(e); // {success: false}
    // }

    this.broadcastContextChange();
  };

  calculateApplicationTheme = () => {
    const themeKeyForDarkOrLightMode = this.context.value.darkMode ? 'dark' : 'light';
    const applicationTheme = {
      ...this.context.value.currentBasicTheme,
      ...this.context.value.currentAdditionalTheme[themeKeyForDarkOrLightMode],
    };
    return applicationTheme;
  };

  calculateNavigationTheme = (applicationTheme?: any) => {
    if (!applicationTheme) {
      applicationTheme = this.calculateApplicationTheme();
    }

    const backgroundColor = applicationTheme['background-color-0'];
    const ignoreBackgroundColor = /^\$/.test(backgroundColor);
    let theme = this.context.value.darkMode ? DarkNavigationTheme : DefaultNavigationTheme;

    if (!ignoreBackgroundColor) {
      theme = {
        ...theme,
        colors: {
          ...theme.colors,
          // background: backgroundColor,
          background: 'transparent', // set default background color in _page_ screen
          card: backgroundColor,
        },
      };
    }

    return theme;
  };
}

export const ThemeManagerInstance = Singleton.getOrRegister(
  'ThemeManagerInstance',
  new ThemeManager(),
) as ThemeManager;

/**
 * react hook
 */

export const useThemeManager = () => {
  const _instance = useRef(ThemeManagerInstance).current;
  const [contextCache, setContextCache] = useState<TContextForTheme>(_instance.context);

  useEffect(() => {
    const subject = _instance.messageCenter.subscribe(context => {
      setContextCache(context);
    });
    return () => {
      subject.unsubscribe && subject.unsubscribe();
    };
  }, [_instance.messageCenter]);

  return contextCache;
};

export default ThemeManagerInstance;
