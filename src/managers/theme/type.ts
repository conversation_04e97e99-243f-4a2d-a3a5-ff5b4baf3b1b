import { Theme as TNavigationTheme } from '@react-navigation/native';

export interface IAdditionalTheme {
  dark: Record<string, string>;
  light: Record<string, string>;
}
export interface IThemeValue {
  currentBasicTheme: Record<string, string>;
  currentAdditionalTheme: IAdditionalTheme;
  darkMode: boolean;
  applicationTheme: any; // the theme value for ApplicationProvider component 'theme' property
  navigationTheme: TNavigationTheme | undefined;
  brandColor: string;
}

export interface TContextForTheme {
  value: IThemeValue;
}

export interface IThemeAction {
  updateTheme: (
    basicTheme: Record<string, string>,
    additionalTheme?: Partial<IAdditionalTheme>,
  ) => void;
  setDarkMode: () => void;
  setLightMode: () => void;
  setDarkOrModeFollowSystem: () => void;
  getCurrentLightDarkModeKey: () => 'light' | 'dark' | undefined;
}
