/**
 * @moudule Manager:Modal
 */
import { useSubscribeFilter } from '@/hooks';
import { BaseInfoManager } from '@/managers';
import { TextStyle } from 'react-native';
import uuid from 'react-native-uuid';
import { BehaviorSubject } from 'rxjs';
import { TStatus as TTextStatus } from '../../components/text/style';

/**
 * @done 弹框封装成传入 title、content、callback
 */
export type ModalContextType = {
  modalList: ModalItemType[];
  modalListStatus: 'wait' | 'pending';
};

/**
 * @type key 弹框键名
 * @type title 标题
 * @type i18nkey i18n键值
 * @type imageKey 定制图片
 * @type content 内容
 * @type isBackdropClose 点击背景是否关闭
 * @type confirmBtnName 确认按钮命名
 * @type confirmBtnCallback 确认按钮回调
 * @type cancelBtnName 取消按钮命名
 * @type cancelBtnCallback 取消按钮回调
 * @type extra 扩展属性,临时做扩展使用
 */
export type ModalItemType = {
  id?: string;
  key: ModalList;
  hasLinearGradient?: boolean;
  titleKey?: string;
  titleStyle?: TextStyle;
  titleStatus?: TTextStatus;
  topImageKey?: string;
  imageKey?: string;
  i18nKey?: string;
  content?: string;
  isBackdropClose?: boolean;
  /** @description "nomal" 左右排列的按钮布局, "vertical-nomal" 上下排列的正常布局。"vertical-special-1" 特殊的垂直布局 1 型 */
  buttonType?: 'nomal' | 'vertical-nomal' | 'vertical-special-1';
  isBottomIconColse?: Boolean;
  isTopIconColse?: Boolean;
  children?: React.ReactElement;
  confirmBtnName?: string;
  confirmBtnCallback?: Function;
  cancelBtnName?: string;
  cancelBtnCallback?: Function;
  isFocus?: boolean;
  extra?: any;
};

export enum ModalList {
  /** 注册流程,点击发送sms code */
  CALL_YOU_SEND_SMS = 'callYouSendSms',
  /** 注册流程,通过电话接受OTP验证码 */
  RECIVE_OTP_BY_CALL = 'reciveOtpByCall',
  /** 确认clabe账户信息正确,发送贷款申请 */
  COMFIRM_CLABE_SEND_LOAN = 'comfirmClabeSendLoan',
  /** 复贷确认电话号码是否使用 */
  COMFIRM_PHONE_NUMBER_USAGE = 'comfirmPhoneNumberUsage',
  /** 贷款前进行面部识别 */
  INVITE_FICED = 'inviteFiced',
  /** 贷款详情信息 */
  LOAN_DETAIL = 'loanDetail',
  GP_REVIEW = 'gpReview',
  /** 自定义客服提示弹窗 */
  CUSTOM_HOT_LINE = 'customHotLine',
  /** 客服弹框 */
  HOT_LINE = 'hotLine',
  /** 贷款前面部识别弹框 */
  LOAN_PRE_FACED = 'LoanPreFacedModal',
  /** 版本更新弹框 */
  UPDATE = 'updateModal',
  /** 通用消息提示确认弹窗 */
  INFO_PROMPT_CONFIRM = 'infoPromptConfirm',
  /** 优惠券发放 */
  COUPON_SHOW = 'couponShow',
  /** 被邀请用户优惠券发放 */
  COUPON_SHOW_WHEN_BE_INVITED = 'couponShowWhenBeInvited',
  /** 提现二次确认 */
  WITHDRAW_CONFIRM = 'withdrawConfirm',
  /** 提现成功提示 */
  WITHDRAW_SUCCESS = 'withdrawSuccess',
  /** 提现警告 */
  WITHDRAW_WARNING = 'withdrawWarning',
  /** VIP 通知弹窗 */
  VIP_NOTIFY = 'vipNotify',
  /** 权限申请弹窗 */
  PERMISSION_APPLICATION = 'permissionApplication',
  /** 新版本更新内容弹窗 */
  VERSION_NOTE = 'versionNote',
  /** 提额优惠券弹窗 */
  INCREASE_COUPON = 'increaseCoupon',
  /** 优惠券挽留弹窗 */
  COUPON_RETAIN = 'couponRetain',
  /** 申请更改提醒弹窗 */
  APPLY_RECHECK = 'applyRecheck',
  /** 提醒重新贷款弹窗 */
  REMIND_RELOAN = 'remindReload',
  /** 代扣提示弹窗 */
  WITHHOLDER_TIP = 'withholderTip',
  /** 正计时弹窗 */
  COUNT_UP = 'countUp',
}

class ModalManager {
  constructor() {
    this.context = {
      modalList: [],
      modalListStatus: 'wait',
    };
    this.initContext();
    this.messageCenter = new BehaviorSubject<ModalContextType>(this.context);
  }

  messageCenter: BehaviorSubject<ModalContextType>;
  context: ModalContextType;

  async initContext() { }

  openModal(modal: ModalItemType) {
    BaseInfoManager.changeLoadingModalVisible(false);
    modal.id = uuid.v4() as string;
    const notVipModalList = this.context.modalList.filter(
      modal => modal.key !== ModalList.VIP_NOTIFY,
    );
    // vip 通知弹窗 优先级放最高。
    const vipModalList = this.context.modalList.filter(modal => modal.key === ModalList.VIP_NOTIFY);
    this.context.modalList = [...notVipModalList, modal, ...vipModalList];
    this.boardcastContextChange();
    return modal.id;
  }

  closeModal(uuid?: string) {
    const modalList = this.context.modalList.filter(item => item.id !== uuid);
    this.context.modalList = modalList;
    this.boardcastContextChange();
  }

  boardcastContextChange = () => {
    this.messageCenter.next({ ...this.context });
  };
}

const instance = new ModalManager();

export const modalDataStoreInstance = instance;

export const useModalData = () => {
  const subState = useSubscribeFilter({
    subject: modalDataStoreInstance.messageCenter,
    filter: (subject: ModalContextType) => subject,
  });

  return subState;
};
