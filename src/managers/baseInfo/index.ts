/**
 * @moudule Manager:User
 *
 */
import { BaseEnumsSpace } from '@/enums';
import { fetchGetContactConfig } from '@/server';
import { UserVOSpace } from '@/types';
import { BehaviorSubject } from 'rxjs';
import { BaseModel } from '../../dataModel/baseModel';
export type BaseInfoContextType = {
  baseModel: BaseModel;
};

class BaseInfoManager {
  constructor() {
    this.context = {
      baseModel: new BaseModel(),
    };
    this.messageCenter = new BehaviorSubject<BaseInfoContextType>(this.context);
  }
  /** 用于模拟 loading 的关闭时间 */
  loadingCloseTime: number = 0;

  /** de ta c */
  delayCloseTimeLimit: number = 1000;

  messageCenter: BehaviorSubject<BaseInfoContextType>;
  context: BaseInfoContextType;

  // LoadingModal 超时关闭时间30s
  private loadingModalCloseTimeout = 30000;
  private loadingModalCloseTimer = 0;

  boardcastContextChange = () => {
    this.messageCenter.next({ ...this.context });
  };

  changeLoadingModalVisible(visibile: boolean) {
    this.context.baseModel.loadingModal.visible = visibile;
    if (visibile === true) {
      this.loadingCloseTime = Date.now() + this.delayCloseTimeLimit;
      this.boardcastContextChange();
      this.loadingModalCloseTimer = setTimeout(() => {
        this.context.baseModel.loadingModal.visible = false;
        this.boardcastContextChange();
      }, this.loadingModalCloseTimeout);
    } else {
      if (this.loadingModalCloseTimer) {
        clearTimeout(this.loadingModalCloseTimer);
        this.loadingModalCloseTimer = 0;
      }
      const time = Date.now() - this.loadingCloseTime;
      if (time > 0) {
        this.boardcastContextChange();
      } else {
        setTimeout(() => {
          this.boardcastContextChange();
        }, 0 - time);
      }
    }
  }

  /** 更新设置密码状态 */
  updateSetPasswordStatus(isSetPassword: boolean) {
    this.context.baseModel.isSetPassword = isSetPassword;
    this.boardcastContextChange();
  }

  /** 更新跳过设置密码状态 */
  updateSkipSetPasswordStatus(isSkipSetPassword: boolean) {
    this.context.baseModel.isSkipSetPassword = isSkipSetPassword;
    this.boardcastContextChange();
  }

  updateAgreePermissions(agreePermissions: boolean) {
    this.context.baseModel.agreePermissions = agreePermissions;
    this.boardcastContextChange();
  }

  updateFirstLaunch(isNotFirstLaunch: boolean) {
    this.context.baseModel.isNotFirstLaunch = isNotFirstLaunch;
    this.boardcastContextChange();
  }

  async initAppData() {}

  /** 放款成功场景 GooglePlay Reivew状态 */
  saveRepayGPReview(isPermanentlyClosed: boolean) {
    if (isPermanentlyClosed) {
      this.context.baseModel.GPReviewClose = true;
    } else {
      this.context.baseModel.repayGPReview = true;
    }
    this.boardcastContextChange();
  }

  /** 信审等待场景 GooglePlay Reivew状态 */
  saveOrderReviewGPReview(isPermanentlyClosed: boolean) {
    if (isPermanentlyClosed) {
      this.context.baseModel.GPReviewClose = true;
    } else {
      this.context.baseModel.orderReviewGPReview = true;
    }
    this.boardcastContextChange();
  }

  async updateContactConfig() {
    const result = await fetchGetContactConfig();
    if (result?.code === BaseEnumsSpace.ENetworkStatus.SUCCESS) {
      this.context.baseModel.contactConfig = result?.data;
      this.boardcastContextChange();
    }

    return result?.code === BaseEnumsSpace.ENetworkStatus.SUCCESS;
  }

  async updateAppConfig(appConfig: UserVOSpace.AppConfigDataType) {
    this.context.baseModel.appConfig = appConfig;
    this.boardcastContextChange();
    return true;
  }
}

const instance = new BaseInfoManager();

export default instance;
