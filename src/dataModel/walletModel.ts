import { UserEnumsSpace } from '@/enums';
import { OrderVOSpace, UserVOSpace } from '@/types';

export class WalletModel {
  constructor() {}

  /** 提现金额 */
  _withdrawAmount: number = 0;

  /** 获取的金额 */
  _reciveTotal: number = 0;

  /** 是否展示输入框余额tips */
  _balanceTipsDisplay: boolean = true;

  /** 提现状态 */
  _withdrawStatus: 'not_enough' | 'low_min' | 'not_multiple' | 'ok' | 'pending' = 'low_min';

  /** 钱包信息 */
  _walletInfo: UserVOSpace.WalletInfo = {
    balance: 0,
    rewardAmount: 0,
    invitedCount: 0,
    withDrawFee: 0,
    withDrawMinAmount: 0,
    withDrawMinMultiple: 50,
    canWithDrawMinAmount: 0,
    totalBalance: 0,
    cashbackBalance: 0,
    totalCashbackAmount: 0,
    totalCashBackCount: 0,
    redeemCouponsTitle: '',
  };

  /** 默认银行卡信息 */
  _defaultBankCardInfo: OrderVOSpace.DefaultBankCardInfo = {
    cardNo: '',
    bankName: '',
    bankCode: '',
    type: '',
    isDef: UserEnumsSpace.EStatusType.NO,
    isSelf: UserEnumsSpace.EStatusType.NO,
    othersFatherName: '',
    othersMotherName: '',
    othersCurpNumber: '',
    othersName: '',
    othersRelation: '',
    withholdAuthorizeStatus: '',
  };

  /** 是否能够提现 */
  get withdrawCanable(): boolean {
    return Number(this.walletInfo.totalBalance) >= Number(this.walletInfo.canWithDrawMinAmount);
  }

  /** 提现进度 */
  get withdrawProcess(): string {
    if (Number(this.walletInfo.totalBalance) < Number(this.walletInfo.canWithDrawMinAmount)) {
      return Number(
        Number(
          Number(this.walletInfo.totalBalance) / Number(this.walletInfo.canWithDrawMinAmount),
        ) * 100,
      ).toFixed(0);
    }
    return '0';
  }

  get balanceTipsDisplay(): boolean {
    return this._balanceTipsDisplay;
  }

  set balanceTipsDisplay(balanceTipsDisplay: boolean) {
    this._balanceTipsDisplay = balanceTipsDisplay;
  }

  get withdrawAmount(): number {
    return this._withdrawAmount;
  }

  set withdrawAmount(withdrawAmount: number) {
    this._withdrawAmount = withdrawAmount;
  }

  get reciveTotal(): number {
    return this._reciveTotal;
  }

  set reciveTotal(reciveTotal: number) {
    this._reciveTotal = reciveTotal;
  }

  get walletInfo(): UserVOSpace.WalletInfo {
    return this._walletInfo;
  }

  set walletInfo(walletInfo: UserVOSpace.WalletInfo) {
    this._walletInfo = walletInfo;
  }

  get defaultBankCardInfo(): OrderVOSpace.DefaultBankCardInfo {
    return this._defaultBankCardInfo;
  }

  set withdrawStatus(withdrawStatus: 'not_enough' | 'low_min' | 'not_multiple' | 'ok' | 'pending') {
    this._withdrawStatus = withdrawStatus;
  }

  get withdrawStatus(): 'not_enough' | 'low_min' | 'not_multiple' | 'ok' | 'pending' {
    return this._withdrawStatus;
  }

  set defaultBankCardInfo(defaultBankCardInfo: OrderVOSpace.DefaultBankCardInfo) {
    this._defaultBankCardInfo = defaultBankCardInfo;
  }

  // 验证余额是否足够
  get validateIsEnough() {
    return (
      this.withdrawStatus === 'ok' ||
      this.withdrawStatus === 'low_min' ||
      this.withdrawStatus === 'pending' ||
      this.withdrawStatus === 'not_multiple'
    );
  }

  // 验证是否高于最小值
  get validateIsHigherLow() {
    return (
      this.withdrawStatus === 'ok' ||
      this.withdrawStatus === 'not_enough' ||
      this.withdrawStatus === 'pending' ||
      this.withdrawStatus === 'not_multiple'
    );
  }

  // 验证是否高于最小值
  get validateIsMutiple() {
    return (
      this.withdrawStatus === 'ok' ||
      this.withdrawStatus === 'not_enough' ||
      this.withdrawStatus === 'pending' ||
      this.withdrawStatus === 'low_min'
    );
  }

  // 验证高于最小值并且符合倍数
  get validateIsMutipleAndHigherLow() {
    return (
      this.withdrawStatus === 'ok' ||
      this.withdrawStatus === 'not_enough' ||
      this.withdrawStatus === 'pending'
    );
  }
}
