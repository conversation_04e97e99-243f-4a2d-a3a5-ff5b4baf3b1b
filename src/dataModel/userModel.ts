import { UserEnumsSpace } from '@/enums';
import { BaseInfoManager } from '@/managers';
import { UserVOSpace } from '@/types';
import { ELocalKey } from '../localStorage/localKey';
import KVManager from '../managers/kv';

export class UserModel {
  constructor() { }

  _mobile: string = '';
  _token: string = '';
  _userId: string = '';
  _userState: UserVOSpace.BackUserStateType = Object.create(null) as UserVOSpace.BackUserStateType;

  get mobile(): string {
    if (KVManager.action.getString(ELocalKey.PHONE_NUMBER_STRING)) {
      return KVManager.action.getString(ELocalKey.PHONE_NUMBER_STRING) || '';
    }
    return this._mobile;
  }

  set mobile(mobile: string) {
    KVManager.action.setString(ELocalKey.PHONE_NUMBER_STRING, mobile);
    this._mobile = mobile;
  }

  // get newMobile(): string {
  //   return this._mobile;
  // }

  // set newMobile(mobile: string) {
  //   this._mobile = mobile;
  // }

  get token(): string {
    if (KVManager.action.getString(ELocalKey.TOKEN_STRING)) {
      return KVManager.action.getString(ELocalKey.TOKEN_STRING) || '';
    }
    return this._token;
  }

  set token(token: string) {
    KVManager.action.setString(ELocalKey.TOKEN_STRING, token);
    this._token = token;
  }

  get userId(): string {
    if (KVManager.action.getString(ELocalKey.USER_ID_STRING)) {
      const selfUserId = KVManager.action.getString(ELocalKey.USER_ID_STRING) || '';
      return selfUserId;
    }
    return this._userId;
  }

  set userId(userId: string) {
    KVManager.action.setString(ELocalKey.USER_ID_STRING, userId);
    this._userId = userId;
  }

  get userState(): UserVOSpace.BackUserStateType {
    return this._userState;
  }

  set userState(userState: UserVOSpace.BackUserStateType) {
    this._userState = userState;
  }

  /** 是否新客 */
  get isUserTypeNew(): boolean {
    return this.userState?.userType === UserEnumsSpace.EUserType.NEW;
  }

  /** 是否老客 */
  get isUserTypeOld(): boolean {
    return this.userState?.userType === UserEnumsSpace.EUserType.OLD;
  }
  /** 用户是否取消申请单 */
  get isApplyCancel(): boolean {
    return this.userState?.applyStatus === UserEnumsSpace.EApply.CANCEL;
  }

  /** 新户申请单创建异常 */
  get isNewUserCreateApplyError(): boolean {
    return (
      this.isUserTypeNew &&
      !this.isHasActiveOrder &&
      !this.isNeedBasic &&
      !this.isNeedContact &&
      !this.isNeedWork &&
      !this.isNeedCurp &&
      !this.isNeedSelfie &&
      !this.isNeedBindCard
    );
  }

  /** 用户授信（接受贷款）成功 等待活体核验 */
  get isCreditSuccessWaitFace(): boolean {
    return (
      this.userState?.applyStatus === UserEnumsSpace.EApply.SUCCEEDED &&
      this.userState?.acceptCreditStatus === UserEnumsSpace.ECredit.ACCEPT &&
      this.userState?.useCreditStatus === UserEnumsSpace.UseCreditStatus.WAIT_FACE
    );
  }

  /** 是否登录 */
  get isLogined(): boolean {
    return this.token.length > 0;
  }

  // /** 是否是审核账号 */
  // get isAccUser(): boolean {
  //   this.contactConfig?.hotIds?.forEach(item => {
  //     if (this.mobile === item.hotAcc) {
  //       return true;
  //     }
  //   });
  //   return false;
  // }

  /** 订单是否在放款 */
  get isLoaningNow(): boolean {
    let result = false;
    if (this.userState?.orderStatus) {
      if (this.userState?.orderStatus === UserEnumsSpace.EOrder.CREATED) {
        result = true;
      }

      if (this.userState?.orderStatus === UserEnumsSpace.EOrder.LENDING) {
        result = true;
      }
    }
    return result;
  }

  /** 用户授信（接受贷款）等待中 或者等待活体核验 */
  get isCreditWait(): boolean {
    return (
      this.userState?.applyStatus === UserEnumsSpace.EApply.SUCCEEDED &&
      this.userState?.acceptCreditStatus === UserEnumsSpace.ECredit.WAIT
    );
  }

  /** 订单是否处于还款中 */
  get isRepayNow(): boolean {
    let result = false;
    if (this.userState?.orderStatus) {
      if (this.userState?.orderStatus === UserEnumsSpace.EOrder.LOANED) {
        result = true;
      }

      if (this.userState?.orderStatus === UserEnumsSpace.EOrder.OVERDUE) {
        result = true;
      }

      if (this.userState?.orderStatus === UserEnumsSpace.EOrder.BAD_DEBT) {
        result = true;
      }
    }
    return result;
  }

  /** 订单是否被拒绝 - 申请状态 */
  get isLoanReject(): boolean {
    let result = false;
    if (this.userState?.applyStatus) {
      if (
        this.userState?.applyStatus === UserEnumsSpace.EApply.SYS_DENIED ||
        this.userState?.applyStatus === UserEnumsSpace.EApply.MANU_DENIED ||
        this.userState?.applyStatus === UserEnumsSpace.EApply.DS_DENIED
      ) {
        result = true;
      }
    }
    return result;
  }

  /** 订单是否被拒绝 - 订单状态 */
  get isOrderReject(): boolean {
    let result = false;
    if (this.userState?.orderStatus) {
      if (this.userState?.orderStatus === UserEnumsSpace.EOrder.DENIED) {
        result = true;
      }
    }
    return result;
  }

  /** 用户进件状态被锁定 */
  get inEntryPermissionLocking(): boolean {
    return this.userState?.locked === 'YES';
  }

  /** 订单是否被拒绝 */
  get isRejectNow(): boolean {
    return this.isLoanReject || this.isOrderReject;
  }

  /** 订单是否在审核中 */
  get isReviewingNow(): boolean {
    let result = false;
    if (this.userState?.applyStatus) {
      if (
        this.userState?.applyStatus === UserEnumsSpace.EApply.CREATED ||
        this.userState?.applyStatus === UserEnumsSpace.EApply.SYS_CHECK ||
        this.userState?.applyStatus === UserEnumsSpace.EApply.SYS_APROVAL ||
        this.userState?.applyStatus === UserEnumsSpace.EApply.MANU_APROVAL ||
        this.userState?.applyStatus === UserEnumsSpace.EApply.DS_APROVAL
      ) {
        result = true;
      }
    }
    return result;
  }

  /** 新户确认用信 人脸验证超时状态 */
  get isUseCreditFaceExpired(): boolean {
    return this.userState?.faceExpired === 'YES';
  }

  /** 解锁按钮是否可以点击 */
  get isCanUnlocked(): boolean {
    return this.userState?.allowApply === 'YES';
  }

  /** 是否需要进行签约 */
  get isNeedLoanContractSign(): boolean {
    return (
      this.userState?.isNeedSignUp === 'YES' &&
      this.userState?.applyStatus === UserEnumsSpace.EApply.CREATED
    );
  }

  /** 是否需要认证基本信息 */
  get isNeedBasic(): boolean {
    return this.userState?.basic === 'NO';
  }

  /** 是否需要绑定第三方账号 */
  get isNeedAccoutBind(): boolean {
    return this.userState?.gpBind === 'NO';
  }

  /** 是否需要填写风控问券 */
  get isNeedRKQuestion(): boolean {
    return this.userState?.questionGroupQa === 'NO';
  }

  /** 是否需要首贷问卷 */
  get isFirstLoanQuestion(): boolean {
    return this.isUserTypeNew && this.userState.questionGroupQa === 'NO';
  }

  /** 是否需要复贷问卷 */
  get isReLoanQuestion(): boolean {
    return this.isUserTypeOld && this.userState.questionGroupQa === 'NO';
  }

  /** 是否需要选择额度状态 */
  get isNeedSelectAmountStatus(): boolean {
    return this.userState?.selectProductPrice === 'NO';
  }

  /** 是否需要直接放款 */
  get isNeedDirectPayment(): boolean {
    return Boolean(
      this.isNeedSelectAmountStatus &&
      BaseInfoManager.context.baseModel.isSkipConfirmAcceptCreditSwitch,
    );
  }

  /** 是否需要直接放款 */
  get isDirectLendingStatus(): boolean {
    return this.userState?.directLending === 'YES';
  }

  /** 是否需要认证工作信息 */
  get isNeedWork(): boolean {
    return this.userState?.professional === 'NO';
  }

  /** 是否需要认证联系人信息 */
  get isNeedContact(): boolean {
    return this.userState?.contact === 'NO';
  }

  /** 是否需要认证curp信息 */
  get isNeedCurp(): boolean {
    return this.userState?.curp === 'NO';
  }

  /** 是否需要认证face信息 */
  get isNeedFace(): boolean {
    return this.userState?.face === 'NO';
  }

  /** 是否需要认证selfie信息 */
  get isNeedSelfie(): boolean {
    return this.userState?.selfie === 'NO';
  }

  /** 是否需要认证钱包信息 */
  get isNeedBindCard(): boolean {
    return this.userState?.bindCard === 'NO';
  }

  /** 是否需要认证OTP信息 */
  get isNeedOtp(): boolean {
    return this.userState?.otp === 'NO';
  }

  /** 是否需要验证十五天调用过OTP */
  get isNeedOtpVerify(): boolean {
    return this.userState?.otpVerify === 'NO';
  }

  /** 是否可以重传认证信息，新客可以，老客不行 */
  get isCanReloadProofData(): boolean {
    return this.userState?.updInfo === 'YES';
  }

  /** 是否需要填写问卷 NO代表需要填写, YES代表不需要填写; qa等价于问卷是否填写且风控是否需要填写  **/
  get isNeedQa(): boolean {
    if (this.userState?.questionGroupQa === 'NO') {
      return true;
    }
    return false;
  }

  /** 二单是否需要回答问题 */
  get isNeedQaOnLoanTwo(): Boolean {
    if (this.isNeedQa && this.userState?.loanCount == 1) {
      return true;
    }
    return false;
  }

  /** 三单是否需要回答问题 */
  get isNeedQaOnLoanThree(): Boolean {
    if (this.isNeedQa && this.userState?.loanCount == 2) {
      return true;
    }
    return false;
  }

  /** 是否需要补件 */
  get isNeedSupply(): boolean {
    return (
      this.userState?.supplyType === UserEnumsSpace.ESupplyType.CANCEL ||
      this.userState?.supplyType === UserEnumsSpace.ESupplyType.RETRY_TRANSFER
    );
  }

  get isLoanFailNeedSupply(): Boolean {
    return this.userState?.supplyType === UserEnumsSpace.ESupplyType.RETRY_TRANSFER;
  }

  get loanCount(): number {
    return this.userState?.loanCount || 0;
  }

  /** 补件未完成 */
  get isSupplyUnCompleted(): boolean {
    return (
      this.isNeedSupply &&
      (this.isNeedBasic ||
        this.isNeedOtp ||
        this.isNeedContact ||
        this.isNeedCurp ||
        this.isNeedSelfie ||
        this.isNeedWork ||
        this.isNeedBindCard)
    );
  }

  /** 补件完成 */
  get isSupplyCompleted(): boolean {
    return (
      this.isNeedSupply &&
      !(
        this.isNeedBasic ||
        this.isNeedOtp ||
        this.isNeedContact ||
        this.isNeedCurp ||
        this.isNeedSelfie ||
        this.isNeedWork ||
        this.isNeedBindCard
      )
    );
  }

  /** 系统繁忙场景 */
  get MaintenanceInfoItemData() {
    return this.userState?.maintenanceInfo;
  }

  /** OCR出问题 系统繁忙场景 */
  get isOcrSceneError(): boolean {
    let result = false;
    this.userState?.maintenanceInfo?.forEach(item => {
      if (item.maintenance === 'OCR' && item.enabled === 'YES') {
        result = true;
      }
    });
    return result;
  }

  /** 活体出问题 系统繁忙场景 */
  get isFaceSceneError(): boolean {
    let result = false;
    this.userState?.maintenanceInfo?.forEach(item => {
      if (item.maintenance === 'FACE' && item.enabled === 'YES') {
        result = true;
      }
    });
    return result;
  }

  /** 获取用户的 applyOrderId */

  get applyOrderId(): string {
    return this.userState?.applyOrderId || '';
  }

  /** 有 */
  get hasApplyOrderId(): boolean {
    if (this.applyOrderId.length === 0) {
      return false;
    } else {
      return true;
    }
  }

  get preApplyOrderId(): string {
    return this.userState?.preApplyOrderId || '';
  }

  /** 有 */
  get hasPreApplyOrderId(): boolean {
    if (this.preApplyOrderId.length === 0) {
      return false;
    } else {
      return true;
    }
  }

  /** 是否reivew状态 */
  get isReview(): boolean {
    return (
      this.isNeedBasic ||
      this.isNeedContact ||
      this.isNeedCurp ||
      this.isNeedFace ||
      this.isNeedBindCard ||
      this.isNeedOtp ||
      this.isNeedSelfie ||
      this.isNeedWork
    );
  }

  get notHasApplyOrderId(): boolean {
    return !this.hasApplyOrderId;
  }

  /** 用户重新申请提交贷款申请 */
  get isLoanCancel(): Boolean {
    return (
      this.userState.applyStatus === UserEnumsSpace.EApply.SUCCEEDED &&
      this.userState.orderStatus === UserEnumsSpace.EOrder.CANCEL
    );
  }

  /** 提交用户重新申请提交贷款 */
  get isOrderCancel(): Boolean {
    return (this.isLoanCancel && this.isUserTypeNew) || this.isCreditExpired;
  }

  /** 用户授信（接受贷款）超时 被取消 */
  get isCreditExpired(): boolean {
    let result = false;
    switch (this.userState.applyStatus) {
      case UserEnumsSpace.EApply.SUCCEEDED:
        result = this.userState.acceptCreditStatus === UserEnumsSpace.ECredit.EXPIRED;
    }
    return result;
  }

  /**
   * @description 是否提示用户修改弹窗
   * 老用户每有两个新的申请订单号（还完两次款）, 且每三周首次打开App
   */
  get isNotifyModifyMobile(): boolean {
    if (!this.isUserTypeOld) {
      return false;
    }

    let preTimestamp =
      Number(KVManager.action.getString(ELocalKey.RELOAN_RPOMPT_MODIFY_MOBILE_TIMESTAMP)) || 0;
    let preApplyOrderId =
      KVManager.action.getString(ELocalKey.RELOAN_RPOMPT_MODIFY_MOBILE_APPLY_ORDER_ID) || '';

    // 每次申请单更新, 更新贷款次数, 打开时记录和申请订单号
    if (preApplyOrderId !== this.userState.applyOrderId) {
      KVManager.action.setString(
        ELocalKey.RELOAN_RPOMPT_MODIFY_MOBILE_APPLY_ORDER_ID,
        this.userState.applyOrderId as string,
      );

      if (this.loanCount !== 0 && (this.loanCount + 1) % 2 === 0) {
        KVManager.action.setString(
          ELocalKey.RELOAN_RPOMPT_MODIFY_MOBILE_TIMESTAMP,
          String(Date.now()),
        );
        return true;
      }
      // 申请单相同, 判断是否已经间隔三周
    } else {
      if ((this.loanCount + 1) % 2 === 0) {
        let cur_timestamp = Date.now();
        if (Math.floor((preTimestamp - cur_timestamp) / (1000 * 60 * 60 * 24 * 7)) > 3) {
          KVManager.action.setString(
            ELocalKey.RELOAN_RPOMPT_MODIFY_MOBILE_TIMESTAMP,
            String(Date.now()),
          );
          return true;
        }
      }
    }

    return false;
  }

  /** 是否是分期订单 */
  get isHasMultiOrder(): boolean {
    return this._userState.productType === 'INSTALLMENT';
  }

  /**
   * 是否包含在途订单
   */
  get isHasActiveOrder(): boolean {
    return (
      this.isRepayNow ||
      this.isLoaningNow ||
      // this.inEntryPermissionLocking ||
      // this.isUseCreditFaceExpired ||
      this.isCreditWait ||
      this.isCreditSuccessWaitFace ||
      this.isNeedLoanContractSign ||
      this.isReviewingNow
    );
  }
  /**
   * 还没有提交订单的新户
   */
  get isNoApplyIdNewUser(): boolean {
    return this.isUserTypeNew && this.notHasApplyOrderId;
  }
  /** 是否开通过代扣：非首次 */
  get isFirstOpenWithhold(): boolean {
    return this.userState.withholdState === UserEnumsSpace.EStatusType.NO;
  }
}
