import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '@/localStorage';
import { UserInfoManager } from '@/managers';
import { UserVOSpace } from '@/types';
import { log } from '@/utils';
import KVManager from '../managers/kv';

interface ILoadingModal {
  visible: boolean;
  timeout: number;
}

export class BaseModel {
  constructor() {}

  _loadingModal: ILoadingModal = {
    visible: false,
    timeout: 3000,
  };
  _applyOrderId: string = '';
  _versionUpdateStatusFlag: number = 0;
  _agreePermissions: boolean = false;
  _repayGPReview: boolean = false;
  _orderReviewGPReview: boolean = false;
  _GPReviewClose: boolean = false;
  _fcmToken: string = '';
  _isNotFirstLaunch: boolean = false;
  _isSkipSetPassword: boolean = false;
  _isSetPassword: boolean = false;

  _contactConfig: UserVOSpace.ContactConfigDataType = Object.create(
    null,
  ) as UserVOSpace.ContactConfigDataType;
  _appConfig: UserVOSpace.AppConfigDataType = Object.create(null) as UserVOSpace.AppConfigDataType;
  context: any;

  get loadingModal(): ILoadingModal {
    return this._loadingModal;
  }

  set loadingModal(loadingModal: ILoadingModal) {
    this._loadingModal = loadingModal;
  }

  get applyOrderId(): string {
    return this._applyOrderId;
  }

  set applyOrderId(applyOrderId: string) {
    this._applyOrderId = applyOrderId;
  }

  get isSetPassword(): boolean {
    if (
      KVManager.action.getBoolean(
        ELocalKey.IS_SET_PASSWORD + UserInfoManager.context.userModel.mobile,
      )
    ) {
      return Boolean(
        KVManager.action.getBoolean(
          ELocalKey.IS_SET_PASSWORD + UserInfoManager.context.userModel.mobile,
        ),
      );
    }
    return this._isSetPassword;
  }

  set isSetPassword(isSetPassword: boolean) {
    KVManager.action.setBoolean(
      ELocalKey.IS_SET_PASSWORD + UserInfoManager.context.userModel.mobile,
      isSetPassword,
    );
    this._isSetPassword = isSetPassword;
  }

  get isSkipSetPassword(): boolean {
    if (
      KVManager.action.getBoolean(
        ELocalKey.IS_SKIP_SET_PASSWORD + UserInfoManager.context.userModel.mobile,
      )
    ) {
      return Boolean(
        KVManager.action.getBoolean(
          ELocalKey.IS_SKIP_SET_PASSWORD + UserInfoManager.context.userModel.mobile,
        ),
      );
    }
    return this._isSkipSetPassword;
  }

  set isSkipSetPassword(isSkipSetPassword: boolean) {
    KVManager.action.setBoolean(
      ELocalKey.IS_SKIP_SET_PASSWORD + UserInfoManager.context.userModel.mobile,
      isSkipSetPassword,
    );
    this._isSkipSetPassword = isSkipSetPassword;
  }

  get versionUpdateStatusFlag(): number {
    return this._versionUpdateStatusFlag;
  }

  set versionUpdateStatusFlag(versionUpdateStatusFlag: number) {
    this._versionUpdateStatusFlag = versionUpdateStatusFlag;
  }

  get agreePermissions(): boolean {
    if (KVManager.action.getBoolean(ELocalKey.READ_PERMISSION_BOOLEAN)) {
      return Boolean(KVManager.action.getBoolean(ELocalKey.READ_PERMISSION_BOOLEAN));
    }
    return this._agreePermissions;
  }

  set agreePermissions(agreePermissions: boolean) {
    KVManager.action.setBoolean(ELocalKey.READ_PERMISSION_BOOLEAN, agreePermissions);
    this._agreePermissions = agreePermissions;
  }

  /** 放款成功谷歌评价 */
  get repayGPReview(): boolean {
    return (
      KVManager.action.getBoolean(
        ELocalKey.REPAY_GP_REVIEW_BOOLEAN + UserInfoManager.context.userModel.applyOrderId,
      ) || false
    );
  }

  set repayGPReview(repayGPReview: boolean) {
    KVManager.action.setBoolean(
      ELocalKey.REPAY_GP_REVIEW_BOOLEAN + UserInfoManager.context.userModel.applyOrderId,
      repayGPReview,
    );
  }

  /** 提交申请订单 谷歌评价 */
  get orderReviewGPReview(): boolean {
    return (
      KVManager.action.getBoolean(
        ELocalKey.ORDER_CHECK_REVIEW_GP_BOOLEAN + UserInfoManager.context.userModel.applyOrderId,
      ) || false
    );
  }

  set orderReviewGPReview(orderReviewGPReview: boolean) {
    KVManager.action.setBoolean(
      ELocalKey.ORDER_CHECK_REVIEW_GP_BOOLEAN + UserInfoManager.context.userModel.applyOrderId,
      orderReviewGPReview,
    );
  }

  /** 放贷成功 谷歌评价 永久关闭标识 */
  get GPReviewClose(): boolean {
    return (
      KVManager.action.getBoolean(ELocalKey.GP_CLOSED + UserInfoManager.context.userModel.userId) ||
      false
    );
  }

  set GPReviewClose(GPReviewClose: boolean) {
    KVManager.action.setBoolean(
      ELocalKey.GP_CLOSED + UserInfoManager.context.userModel.userId,
      GPReviewClose,
    );
  }

  /** 放贷成功场景 GooglePlay Review状态 */
  get getOrderReviewGPReviewStatus() {
    log.info(
      `# getOrderReviewGPReviewStatus GPReviewClose:${this.GPReviewClose} orderReviewGPReview:${this.orderReviewGPReview}`,
    );
    if (this.GPReviewClose) {
      return false;
    }

    if (this.orderReviewGPReview) {
      return false;
    }

    return true;
  }

  /** 等待信审场景 GooglePlay Review状态 */
  get getRepayGPReviewStatus() {
    log.info(
      `# GooglePlay GPReviewClose:${this.GPReviewClose} repayGPReview:${this.repayGPReview}`,
    );
    if (this.GPReviewClose) {
      return false;
    }

    if (this.repayGPReview) {
      return false;
    }

    return true;
  }

  get fcmToken(): string {
    return this._fcmToken;
  }

  set fcmToken(fcmToken: string) {
    this._fcmToken = fcmToken;
  }

  /**
   * @description 老户、结清时间20天内、otp15天以内需要做otp
   * 如果用户连续两次调用verify失败, 在客户端缓存15天, 这个期间内不会再让用户重新尝试
   */
  get isVerifyOtpErrorSkip(): boolean {
    /** 重试限制天数 */
    const RETRY_TIME_LIMIT = 15;
    /** 重试限制次数 */
    const VERIFY_TIMES = 2;

    let recordTime = this.otpVerifyErrorTime;
    let currentTime = Date.now();
    let duration = Math.floor((recordTime - currentTime) / (1000 * 60 * 60 * 24));
    if (duration >= RETRY_TIME_LIMIT) {
      this.otpVerifyFailedCount = 0;
    }
    return this.otpVerifyFailedCount >= VERIFY_TIMES;
  }

  // otp验证失败次数记录
  set otpVerifyFailedCount(count: number) {
    KVManager.action.setInt(
      ELocalKey.OTP_VERIFY_SKIP + UserInfoManager.context.userModel.userId,
      count,
    );
    this.otpVerifyErrorTime = Date.now();
  }

  get otpVerifyFailedCount(): number {
    let curCount =
      KVManager.action.getInt(
        ELocalKey.OTP_VERIFY_SKIP + UserInfoManager.context.userModel.userId,
      ) || 0;
    return curCount;
  }

  // otp验证失败时间记录
  set otpVerifyErrorTime(time: number) {
    KVManager.action.setString(
      ELocalKey.OTP_VERIFY_ERROR_TIME + UserInfoManager.context.userModel.userId,
      String(time),
    );
  }

  get otpVerifyErrorTime(): number {
    return (
      Number(
        KVManager.action.getString(
          ELocalKey.OTP_VERIFY_ERROR_TIME + UserInfoManager.context.userModel.userId,
        ),
      ) || Date.now()
    );
  }

  get isNotFirstLaunch(): boolean {
    return KVManager.action.getBoolean(ELocalKey.READ_GUIDE_BOOLEAN) || false;
  }

  set isNotFirstLaunch(isFirstLaunch: boolean) {
    KVManager.action.setBoolean(ELocalKey.READ_GUIDE_BOOLEAN, isFirstLaunch);
  }

  get contactConfig(): UserVOSpace.ContactConfigDataType {
    if (KVManager.action.getString('contactConfig')) {
      return JSON.parse(
        KVManager.action.getString('contactConfig') as string,
      ) as UserVOSpace.ContactConfigDataType;
    }
    return this._contactConfig;
  }

  set contactConfig(contactConfig: UserVOSpace.ContactConfigDataType) {
    KVManager.action.setString('contactConfig', JSON.stringify(contactConfig));
    this._contactConfig = contactConfig;
  }

  get appConfig() {
    return this._appConfig;
  }

  set appConfig(_appConfig: UserVOSpace.AppConfigDataType) {
    this._appConfig = _appConfig;
  }

  /** 复贷OTP开关 */
  get isReloanOtpOpen(): Boolean {
    return this._appConfig.reLoanOTPSwitch === 'YES';
  }

  /** 进件页表单自动跳过开关 */
  get isIncomeInputAutoSkipSwitch(): Boolean {
    return this._appConfig.incomeInputAutoSkipSwitch === 'YES';
  }

  /** 复贷二单增信问题开关 */
  get isReloanQuestionOpen(): Boolean {
    return this._appConfig.reLoanQuestionnaireSwitch === 'YES';
  }

  /** 是否跳过选择额度(预申请,直接放款) */
  get isSkipConfirmAcceptCreditSwitch(): Boolean {
    return this._appConfig.skipConfirmAcceptCreditSwitch === 'YES';
  }

  /** 启动页h5页面配置开关 */
  get isSplashH5Switch(): Boolean {
    return this._appConfig.splashH5Switch === 'YES';
  }

  /** 启动页h5页面配置开关 */
  get splashH5Link(): string {
    return this._appConfig.splashH5Link;
  }

  /** 工作类型开关 */
  get isJobTypeOpen(): Boolean {
    return this._appConfig.jobTypeSwitch === 'YES';
  }

  /** 婚姻状态开关 */
  get isMaritalStatusOpen(): Boolean {
    return this._appConfig.maritalStatusSwitch === 'YES';
  }

  /** 小孩数量开关 */
  get isChildNumOpen(): Boolean {
    return this._appConfig.childNumSwitch === 'YES';
  }

  /** 贷款用途开关 */
  get isLoanUseOpen(): Boolean {
    return false;
  }

  /** 薪资周期开关 */
  get isSalaryPeridOpen(): Boolean {
    return true;
  }

  /** 是否贷款状态开关 */
  get isCashLoanedOpen(): Boolean {
    return this._appConfig.cashLoanedSwitch === 'YES';
  }

  /** 复贷用户是否修改手机号开关 */
  get isOldUserMobileExpiredRemindSwitch(): Boolean {
    return this._appConfig.oldUserMobileExpiredRemindSwitch === 'YES';
  }

  /** 教育状态开关 */
  get isEducationLevelOpen(): Boolean {
    return this._appConfig.educationLevelSwitch === 'YES';
  }

  /** 语音验证码开关 */
  get isVoiceSmsSwitchStatus(): Boolean {
    return this._appConfig.voiceSmsSwitchStatus === 'YES';
  }

  /** 增信功能开关 */
  get isEnhanceCredit(): Boolean {
    return this._appConfig.enhanceCreditSwitch === 'YES';
  }

  /** 等待审核页通知权限弹窗开关 */
  get isWaitingPushPop(): Boolean {
    return this._appConfig.waitAuditPush === 'YES';
  }

  /** 新户热更新强制开关 */
  get isNewUserHotUpdateForceSwitch(): boolean {
    return this._appConfig.newUserHotUpdateDisplaySwitch === 'YES';
  }

  /** 老户热更新强制开关 */
  get isOldUserHotUpdateForceSwitch(): boolean {
    return this._appConfig.oldUserHotUpdateDisplaySwitch === 'YES';
  }

  /** vip 功能开关 */
  get isVipFuncSwitch(): boolean {
    return this._appConfig.vipSwitch === 'YES';
  }

  get isMultiPeriodSwitch(): boolean {
    return this._appConfig.installmentSwitch === 'YES';
  }

  /** 代扣功能开关 */
  get isWithholdSwitch(): boolean {
    return this._appConfig.withholdSwitch === 'YES';
  }

  /** clabe 页面开通自动代扣功能开关是否默认选中 */
  get autoWithholdClabePageDefaultValue(): boolean {
    return this._appConfig.clabePage === 'YES';
  }

  /** 首贷选择额度页金额改成滑块功能的开关 */
  get firstSelectAmountSlideSwitch(): boolean {
    return this._appConfig.firstLoanSlideSelectAmountSwitch === 'YES';
  }
  get repayDateSwitch(): boolean {
    return this._appConfig.repayDateSwitch === 'YES';
  }

  /** 首贷合同页开通自动代扣功能开关是否默认选中 */
  get autoWithholdFirstLoanContractPageDefaultValue(): boolean {
    return this._appConfig.firstLoanConfirmationPage === 'YES';
  }

  /** 复贷首页开通自动代扣功能开关是否默认选中 */
  get autoWithholdReLoanHomeDefaultValue(): boolean {
    return this._appConfig.reLoanHomePage === 'YES';
  }

  /** 是否是审核账号 */
  get isAccUser(): Boolean {
    let result = false;
    if (Array.isArray(this.contactConfig?.hotIds)) {
      this.contactConfig?.hotIds?.forEach((gHotIds: UserVOSpace.ContactConfigHotIdDataType) => {
        if (gHotIds?.hotAcc == UserInfoManager.context.userModel.mobile) {
          result = true;
        }
      });
    }
    return result;
  }

  /** 是否是审核账号 */
  get nextSalaryDateSwitch(): Boolean {
    return this._appConfig.nextSalaryDateSwitch === 'YES';
  }
}
