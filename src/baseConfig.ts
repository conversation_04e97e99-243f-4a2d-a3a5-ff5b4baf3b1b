/**
 * 基础配置类
 */

import { Platform } from 'react-native';
import { NativeModules } from '@/native';
import { developerMode } from './utils/developerMode';
import { log } from '@/utils';

const { deviceInfoData, errorLog } = NativeModules;

export enum EAppId {
  ONE = 'one',
  TWO = 'two',
}

export enum EApiMd5Salt {
  DEV = 'biluowa_dev',
  TEST = 'biluowa_test',
  PROD = 'Ph29qej1EeQC',
}
/** @description 公钥配置 */
export enum ERsaPublicKey {
  DEV = 'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCNbSxzF3ZQ4xTnR6BSwVqeRj23DdTMMVDmmK23Gq6ryMYhjqWVGL9A2giEE3lpuJd9BvPL3czuegxjkshGbIkcvrBfpABQX5jkOdGNwpwuAEtEISvWUimmBEtWWDNWF5k77k2EWJcdARmHTRA9aLej0rOg/ZP198aR0SFIllA/TwIDAQAB',
  TEST = 'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCNbSxzF3ZQ4xTnR6BSwVqeRj23DdTMMVDmmK23Gq6ryMYhjqWVGL9A2giEE3lpuJd9BvPL3czuegxjkshGbIkcvrBfpABQX5jkOdGNwpwuAEtEISvWUimmBEtWWDNWF5k77k2EWJcdARmHTRA9aLej0rOg/ZP198aR0SFIllA/TwIDAQAB',
  PROD = 'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC8cgDN6oWSnYuL9ndpy2aBONYKdZvcssnZJR3V5ALR5czJ/H3tyuvHmXSi+uDyoeOW11c8gFoLCf7Xin+jpdE5X5qNA6pRG8B9Ei8VyyIs8t2D67WA9JpKNyxqt4mKH4sKptLJirg2UBY2AtRG9o3Betc4Rga1k6qug7rtHrbN0wIDAQAB',
}

export enum ERsaPrivateKey {
  DEV = '',
  TEST = '',
  PROD = '',
}

/** api URL 配置 */
export enum EApiBaseUrl {
  DEV = 'http://***************:2001',
  TEST = 'http://************:12001',
  PROD = 'https://api.npplus.net',
}

/** 官网配置 */
export enum EWebsiteBaseUrl {
  DEV = 'http://************:1101',
  TEST = 'http://************:1101',
  PROD = 'https://www.npplus.net',
}

/** h5 URL 配置 */
export enum EH5Url {
  DEV = 'http://************:1102',
  TEST = 'http://************:1102',
  PROD = 'https://feature.npplus.net',
}

export enum EENV_TYPE {
  DEV = 'DEV',
  MOCK = 'MOCK',
  TEST = 'TEST',
  PROD = 'PROD',
}

interface IBaseConfigOPtions {
  /** 接口请求环境 */
  env_type: EENV_TYPE;
  /** website url */
  websiteUrl: EWebsiteBaseUrl;
  /** api baseUrl */
  apiBaseUrl: EApiBaseUrl;
  /** h5 url */
  h5Url: EH5Url;
  /** Rsa 加密公钥 */
  rsaPublicKey: ERsaPublicKey;
  /** Rsa 加密私钥 */
  rsaPrivatecKey: string;
  /** api 加密数据验签字段 */
  apiMd5Salt: string;
  /** api 接口是否需要加密 */
  apiEncryptionEnabled: boolean;
  /** 内部 appid  协议参数 */
  appId: string;
  /** 平台 */
  clientType: 'ANDROID' | 'IOS';
  /** 谷歌广告ID 协议参数 */
  gaid: string;
  /** 谷歌广告ID的禁用状态 协议参数 */
  isLimitAdTrackingEnabled: boolean;
  /** androidId  */
  androidId: string;
  /** android sdk code */
  androidSdkCode: number;
  /** app版本号 协议参数 */
  appVersionCode: number;
  /** app版本名 */
  appVersion: string;
  /** app版本名 */
  appVersionName: string;
  /** applicationId */
  applicationId: string;
  /** 构建模式 */
  buildType: string;
  /** adjustKey */
  adjustProdKey: string;
  /** vip 规则地址 */
  vipWebsiteRulesUrl: string;
  /** 隐私协议地址 */
  privacyPolicyUrl: string;
  /** app包名*/
  packageName: string;
  /** 货币符号 */
  financeUnit: string;
  /** 用户谷歌play商店地址 */
  playStoreUrl: string;
  /** 客服电话 */
  hotLine: string;
  /** 构建平台 */
  flavor: string;
  /** isHermesEnabled */
  isHermesEnabled: boolean;
  /** isNewArchitectrueEnabled */
  isNewArchitectrueEnabled: boolean;
  /** 客服邮箱 */
  hotMail: string;
  /** 错误日志文件地址 */
  errorLogFilePath: string;
  /** 是否是 debug 构建  */
  debug: boolean;
  /** 推送通知令牌 协议参数*/
  fcmToken: string;
  /** 是否是nora 生产 release */
  isProductionRelease: boolean;
  /** GPServer 是否可用 */
  isGPServerEnabled: boolean;
  /** 深度链接  */
  deeplinkScheme: boolean;
}
/** 基础配置类 */
class BaseConfigClass {
  env_type: IBaseConfigOPtions['env_type'] = EENV_TYPE.TEST;
  websiteUrl: IBaseConfigOPtions['websiteUrl'] = EWebsiteBaseUrl.TEST;
  apiBaseUrl: IBaseConfigOPtions['apiBaseUrl'] = EApiBaseUrl.TEST;
  h5Url: IBaseConfigOPtions['h5Url'] = EH5Url.TEST;
  rsaPublicKey: IBaseConfigOPtions['rsaPublicKey'] = ERsaPublicKey.TEST;
  rsaPrivatecKey: IBaseConfigOPtions['rsaPrivatecKey'] = ERsaPrivateKey.TEST;
  apiMd5Salt: IBaseConfigOPtions['apiMd5Salt'] = EApiMd5Salt.TEST;
  apiEncryptionEnabled: IBaseConfigOPtions['apiEncryptionEnabled'] = true;
  appId: IBaseConfigOPtions['appId'] = EAppId.ONE;
  clientType: IBaseConfigOPtions['clientType'] = Platform.OS === 'android' ? 'ANDROID' : 'IOS';
  gaid: IBaseConfigOPtions['gaid'] = '';
  isLimitAdTrackingEnabled: IBaseConfigOPtions['isLimitAdTrackingEnabled'] = false;
  androidId: IBaseConfigOPtions['androidId'] = '';
  androidSdkCode: IBaseConfigOPtions['androidSdkCode'] = 1;
  appVersionCode: IBaseConfigOPtions['appVersionCode'] = 0;
  appVersion: IBaseConfigOPtions['appVersion'] = '';
  appVersionName: IBaseConfigOPtions['appVersionName'] = '';
  applicationId: IBaseConfigOPtions['applicationId'] = '';
  buildType: IBaseConfigOPtions['buildType'] = '';
  adjustProdKey: IBaseConfigOPtions['adjustProdKey'] = '';
  vipWebsiteRulesUrl: IBaseConfigOPtions['vipWebsiteRulesUrl'] = '';
  privacyPolicyUrl: IBaseConfigOPtions['privacyPolicyUrl'] = '';
  packageName: IBaseConfigOPtions['packageName'] = '';
  financeUnit: IBaseConfigOPtions['financeUnit'] = '';
  playStoreUrl: IBaseConfigOPtions['playStoreUrl'] = ''; // 商店链接
  hotLine: IBaseConfigOPtions['hotLine'] = '';
  flavor: IBaseConfigOPtions['flavor'] = '';
  isHermesEnabled: IBaseConfigOPtions['isHermesEnabled'] = true;
  isNewArchitectrueEnabled: IBaseConfigOPtions['isNewArchitectrueEnabled'] = false;
  hotMail: IBaseConfigOPtions['hotMail'] = '';
  errorLogFilePath: IBaseConfigOPtions['errorLogFilePath'] = '';
  debug: IBaseConfigOPtions['debug'] = false;
  fcmToken: IBaseConfigOPtions['fcmToken'] = '';
  isProductionRelease: IBaseConfigOPtions['isProductionRelease'] = false;
  isGPServerEnabled: IBaseConfigOPtions['isGPServerEnabled'] = false;
  deleteAccountUrl: IOPtions['deleteAccountUrl'] = '';
  googleWebClientId: string = '';
  deeplinkScheme: string = '';

  constructor(options: IOPtions) {
    this.initBaseInfo(options);
    this.initConfig(options.env_type);
  }

  /** 初始化配置 */
  initConfig = (env_type: EENV_TYPE) => {
    switch (env_type) {
      case EENV_TYPE.DEV:
        this.websiteUrl = EWebsiteBaseUrl.TEST;
        this.apiBaseUrl = EApiBaseUrl.DEV;
        this.h5Url = EH5Url.DEV;
        this.rsaPublicKey = ERsaPublicKey.DEV;
        this.rsaPrivatecKey = ERsaPrivateKey.DEV;
        this.apiMd5Salt = EApiMd5Salt.DEV;
        break;
      case EENV_TYPE.TEST:
        this.websiteUrl = EWebsiteBaseUrl.TEST;
        this.apiBaseUrl = EApiBaseUrl.TEST;
        this.h5Url = EH5Url.TEST;
        this.rsaPublicKey = ERsaPublicKey.TEST;
        this.rsaPrivatecKey = ERsaPrivateKey.TEST;
        this.apiMd5Salt = EApiMd5Salt.TEST;
        break;
      case EENV_TYPE.PROD:
        this.websiteUrl = EWebsiteBaseUrl.PROD;
        this.apiBaseUrl = EApiBaseUrl.PROD;
        this.h5Url = EH5Url.PROD;
        this.rsaPublicKey = ERsaPublicKey.PROD;
        this.rsaPrivatecKey = ERsaPrivateKey.PROD;
        this.apiMd5Salt = EApiMd5Salt.PROD;
        break;
      default:
        this.websiteUrl = EWebsiteBaseUrl.TEST;
        this.apiBaseUrl = EApiBaseUrl.TEST;
        this.h5Url = EH5Url.TEST;
        this.rsaPublicKey = ERsaPublicKey.TEST;
        this.rsaPrivatecKey = ERsaPrivateKey.TEST;
        this.apiMd5Salt = EApiMd5Salt.TEST;
    }
  };

  /** 初始化基础信息 */
  initBaseInfo = (options: IOPtions) => {
    const {
      hotLine,
      financeUnit,
      adjustProdKey,
      hotMail,
      privacyPolicyUrl,
      deleteAccountUrl,
      googleWebClientId,
      vipWebsiteRulesUrl,
      deeplinkScheme,
    } = options;
    this.hotLine = hotLine;
    this.financeUnit = financeUnit;
    this.adjustProdKey = adjustProdKey;
    this.hotMail = hotMail;
    this.privacyPolicyUrl = privacyPolicyUrl;
    this.deleteAccountUrl = deleteAccountUrl;
    this.googleWebClientId = googleWebClientId;
    this.vipWebsiteRulesUrl = vipWebsiteRulesUrl;
    this.deeplinkScheme = deeplinkScheme;
  };

  public setup = async (debug: boolean = false) => {
    this.gaid = await deviceInfoData.getGaid();
    this.isLimitAdTrackingEnabled = await deviceInfoData.getIsLimitAdTrackingEnabled();
    this.androidId = await deviceInfoData.getAndroidID();
    this.appId = await deviceInfoData.getAppId();
    this.applicationId = await deviceInfoData.getApplicationId();
    this.packageName = await deviceInfoData.getPackageName();
    this.androidSdkCode = await deviceInfoData.getSdkVersionCode();
    this.appVersionName = await deviceInfoData.getVersionName();
    this.appVersionCode = await deviceInfoData.getVersionCode();
    this.buildType = await deviceInfoData.getBulidType();
    this.debug = await deviceInfoData.getDebug();
    this.flavor = await deviceInfoData.getFlavor();
    this.isHermesEnabled = await deviceInfoData.getIsHermesEnabled();
    this.isNewArchitectrueEnabled = await deviceInfoData.getIsNewArchitectrueEnabled();
    this.errorLogFilePath = await errorLog.getLogFilePath();
    this.playStoreUrl = await deviceInfoData.getPlayStoreUrl();

    // 生产模式 & release 构建
    if (this.flavor.includes('prod') && !this.debug) {
      this.isProductionRelease = true;
    }

    if (this.isProductionRelease) {
      this.initConfig(EENV_TYPE.PROD);
      this.env_type = EENV_TYPE.PROD;
    } else {
      this.apiEncryptionEnabled = false;
      developerMode.turnOn();
    }

    // if (debug) {
    log.debug('BaseConfig setup', {
      envType: this.env_type,
      websiteUrl: this.websiteUrl,
      apiBaseUrl: this.apiBaseUrl,
      rsaPublicKey: this.rsaPublicKey,
      apiMd5Salt: this.apiMd5Salt,
      apiEncryptionEnabled: this.apiEncryptionEnabled,
      appId: this.appId,
      gaid: this.gaid,
      isLimitAdTrackingEnabled: this.isLimitAdTrackingEnabled,
      androidId: this.androidId,
      applicationId: this.applicationId,
      packageName: this.packageName,
      appVersionName: this.appVersionName,
      appVersionCode: this.appVersionCode,
      buildType: this.buildType,
      debug: this.debug,
      flavor: this.flavor,
      androidSdkCode: this.androidSdkCode,
      isHermesEnabled: this.isHermesEnabled,
      isNewArchitectrueEnabled: this.isNewArchitectrueEnabled,
      errorLogFilePath: this.errorLogFilePath,
      fcmToken: this.fcmToken,
      deeplinkScheme: this.deeplinkScheme,
    });
    // }
  };

  public setFCMToken = (fcmToken: string) => {
    this.fcmToken = fcmToken;
  };

  public setApiEncryptionEnabled = (apiEncryptionEnabled: boolean) => {
    this.apiEncryptionEnabled = apiEncryptionEnabled;
  };

  public geGPServerEnabled = async () => {
    this.isGPServerEnabled = await deviceInfoData.getGPServerEnabled();
  };
}

interface IOPtions
  extends Pick<
    IBaseConfigOPtions,
    | 'env_type'
    | 'hotLine'
    | 'financeUnit'
    | 'adjustProdKey'
    | 'hotMail'
    | 'privacyPolicyUrl'
    | 'vipWebsiteRulesUrl'
  > {
  /** 删除用户url */
  deleteAccountUrl: string;
  googleWebClientId: string;
  deeplinkScheme: string;
}

/** 基础信息配置 */
const BaseConfig = new BaseConfigClass({
  env_type: EENV_TYPE.TEST,
  hotLine: '**********',
  financeUnit: '$',
  adjustProdKey: 'sad79v1o9ds0',
  hotMail: '<EMAIL>',
  privacyPolicyUrl: '/privacy/',
  vipWebsiteRulesUrl: '/gana',
  deleteAccountUrl: '/deleteAccount',
  googleWebClientId: '************-eqdc7prt1am75ipgpmbpqfk71rc4008b.apps.googleusercontent.com',
  deeplinkScheme: 'noroplus://norotech',
});

const baseConfigForSetup = BaseConfig.setup;

const setFCMToken = BaseConfig.setFCMToken;

export { BaseConfig, baseConfigForSetup, setFCMToken };
