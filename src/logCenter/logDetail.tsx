import { useMountedState } from '@/hooks';
import Clipboard from '@react-native-clipboard/clipboard';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import React from 'react';
import { Alert, ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { useLoggerManager } from '../managers/logger/loggerManager';
import logDBOperation from './logDBOperation';
dayjs.extend(relativeTime);

function generateButton(title: String, onPress: () => void | Promise<void>) {
  return (
    <TouchableOpacity onPress={onPress} activeOpacity={0.5} style={styles.button}>
      <Text style={styles.buttonTitle}>{title}</Text>
    </TouchableOpacity>
  );
}

export function unescapeSlashes(str: string) {
  // add another escaped slash if the string ends with an odd
  // number of escaped slashes which will crash JSON.parse
  let parsedStr = str.replace(/(^|[^\\])(\\\\)*\\$/, '$&\\');

  // escape unescaped double quotes to prevent error with
  // added double quotes in json string
  parsedStr = parsedStr.replace(/(^|[^\\])((\\\\)*")/g, '$1\\$2');

  try {
    parsedStr = JSON.parse(`"${parsedStr}"`);
  } catch (e) {
    return str;
  }
  return parsedStr;
}

export default function LogDetail(props: { index: number; close: () => void }) {
  const isMounted = useMountedState();
  const [cellData, setCellData] = React.useState<{ [key: string]: any } | undefined>(undefined);
  const [cellDataError, setCellDataError] = React.useState('');

  const {
    state: { filterText, filterType, logCount },
  } = useLoggerManager();

  React.useEffect(() => {
    async function queryData() {
      const queryResult = await logDBOperation.query({
        limit: 1,
        offset: props.index,
        type: 'data',
        filterText: filterText,
        filterType: filterType,
        desc: true,
      });
      if (!isMounted()) {
        return;
      }

      if (queryResult?.rows?.length === 1 && queryResult.rows?.item(0)) {
        setCellDataError('');
        setCellData(queryResult.rows.item(0));
      } else {
        setCellDataError('Query cell data is empty');
      }
    }
    queryData();
  }, [props.index, filterText, filterType, logCount, isMounted]);

  if (cellDataError) {
    return (
      <View style={styles.container}>
        <Text style={styles.text}>Error: {cellDataError}</Text>
      </View>
    );
  }
  if (cellData === undefined) {
    return (
      <View style={styles.container}>
        <Text style={styles.text}>Loading...</Text>
      </View>
    );
  }

  let additionalDataString = '';
  try {
    additionalDataString = JSON.stringify(
      JSON.parse(unescapeSlashes(cellData.additionalData)),
      null,
      '  ',
    );
  } catch (_) {
    additionalDataString = cellData.additionalData;
  }

  const $closeButton = generateButton('Close', props.close);

  const $copyButton = generateButton('Copy', () => {
    Clipboard.setString(JSON.stringify(cellData));
    Alert.alert('Success', 'Data has been copied to the clipboard');
  });

  const $menuBar = (
    <View style={styles.menuBar}>
      {$closeButton}
      {$copyButton}
    </View>
  );

  let timeDescription = '';
  if (cellData.time) {
    if (cellData.time - Date.now() < 3600 * 1000) {
      timeDescription =
        dayjs(cellData.time).fromNow() + ' | ' + new Date(cellData.time).toLocaleTimeString();
    } else {
      timeDescription = new Date(cellData.time).toLocaleTimeString();
    }
  }

  return (
    <ScrollView
      fadingEdgeLength={10}
      keyboardShouldPersistTaps="always"
      style={styles.container}
      contentContainerStyle={styles.contentContainerStyle}>
      {$menuBar}
      <Text style={styles.textForTime}>Time: {timeDescription}</Text>
      <Text style={styles.textForTime}>
        {cellData.time} | {cellData.timeString}
      </Text>
      <Text style={styles.text}>
        Namespace: {cellData.namespace} | Level: {cellData.level}
      </Text>
      <Text style={styles.text}>Content: {cellData.content}</Text>
      <Text style={styles.text}>Additional Data: </Text>
      <Text style={styles.text}>{additionalDataString}</Text>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(0,0,0,0.85)',
    height: 400,
    borderTopWidth: 1,
    borderColor: 'rgba(40, 44, 52, 1.00)',
  },
  contentContainerStyle: {
    padding: 10,
  },
  text: {
    color: '#ffffff',
    marginBottom: 10,
  },
  textForTime: {
    color: 'rgba(255,255,255,0.5)',
    marginBottom: 10,
  },
  menuBar: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    margin: -10,
    marginBottom: 10,
  },
  button: {
    padding: 10,
  },
  buttonTitle: {
    color: 'rgba(229, 192, 123, 1.00)',
  },
});
