import React from 'react';
import { useLoggerManager } from '../managers/logger/loggerManager';
import logDBOperation from './logDBOperation';
// components
import LogModalView from './logModalView';
import LogMonitor from './logMonitor';

export default function LogCenter(): React.ReactElement {
  // state
  const loggerManager = useLoggerManager();

  React.useEffect(() => {
    if (!loggerManager.state.modalVisiable) {
      logDBOperation.clearCache();
    }
  }, [loggerManager.state.modalVisiable]);

  /**
   * 控制模态床的开启和关闭
   */
  const setVisibility = (visbility: boolean) => {
    if (visbility) {
      loggerManager.action.openModal();
    } else {
      loggerManager.action.closeModal();
    }
  };

  const toggleModalVisiable = () => {
    if (loggerManager.state.modalVisiable) {
      loggerManager.action.closeModal();
    } else {
      loggerManager.action.openModal();
    }
  };
  // todo 关闭
  if (loggerManager.state.showViewStatus) {
    return (
      <>
        <LogMonitor toggleModalVisiable={toggleModalVisiable} />
        <LogModalView
          visibility={loggerManager.state.modalVisiable}
          setVisibility={setVisibility}
        />
      </>
    );
  }

  return <></>;
}
