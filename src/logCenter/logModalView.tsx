import { useMountedState } from '@/hooks';
import React from 'react';
import {
  DevSettings,
  NativeModules,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  VirtualizedList,
  Modal,
} from 'react-native';
import { i18nResources, i18n } from '@/i18n';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import logDBOperation from './logDBOperation';
import LogDetail from './logDetail';
// child components
import { BaseConfig } from '@/baseConfig';
import { loggerManagerInstance, useLoggerManager } from '../managers/logger/loggerManager';
import {
  findAllLogRecordAndParseLogDataListToCsvToUpload,
  findAllLogRecordAndParseLogDataListToTextToUpload,
  parseLogToSqliteToShare,
  parseLogToTextToShare,
  parseI18nToCsvToShare,
} from '../utils/export';
import LogFilter from './logFilter';
import ListCell, { cellHeight } from './logListCell';
import { EExportFileType } from './type';

export default function LogModalView({
  visibility = false,
  setVisibility,
}: {
  visibility: boolean;
  setVisibility: (visibility: boolean) => void;
}) {
  const isMounted = useMountedState();
  const insets = useSafeAreaInsets();
  const [enableAutoRefresh, setEnableAutoRefresh] = React.useState<boolean>(true);
  const [apiEncryptionEnabled, setApiEncryptionEnabled] = React.useState<boolean>(
    BaseConfig.apiEncryptionEnabled,
  );
  const [selectedRowIndex, setSelectedRowIndex] = React.useState<number | undefined>(undefined);

  const [lastUpdateTime, setLastUpdateTime] = React.useState<number>(new Date().getTime());

  const [exportFileType, setExportFileType] = React.useState<EExportFileType>(EExportFileType.csv);
  //@ts-ignore
  const queryIntervalId = React.useRef<NodeJS.Timer | undefined>(undefined);
  const needRecoverAutoRefresh = React.useRef<boolean>(false);
  const hideDetailView = React.useCallback(() => {
    setSelectedRowIndex(undefined);
    if (needRecoverAutoRefresh.current) {
      setEnableAutoRefresh(true);
    }
  }, []);

  const {
    state: { filterText, filterType, logCount },
  } = useLoggerManager();

  React.useEffect(() => {
    // 置空 LogCount
    if (queryIntervalId.current !== undefined) {
      clearInterval(queryIntervalId.current);
      queryIntervalId.current = undefined;
    }
    const getCount = async () => {
      const queryResult = await logDBOperation.query({
        type: 'count',
        filterText: filterText,
        filterType: filterType,
      });
      if (!isMounted()) {
        return;
      }

      if (queryResult?.rows?.length === 1 && queryResult.rows.item(0)) {
        // query success
        const count = queryResult.rows.item(0).count;
        // __DEV__ && console.log('count is', count);
        if (count !== loggerManagerInstance.context.state.logCount) {
          loggerManagerInstance.action.updateLogCount(count);
        }
      } else {
        __DEV__ && console.log('### query count result failed', JSON.stringify(queryResult));
      }
    };

    if (enableAutoRefresh) {
      queryIntervalId.current = setInterval(getCount, 1000);
    }
  }, [enableAutoRefresh, filterText, filterType, isMounted]);

  React.useEffect(() => {
    setLastUpdateTime(new Date().getTime());
  }, [filterText, filterType, logCount]);

  if (!visibility) {
    return null;
  }

  const $list = (
    <ScrollView horizontal fadingEdgeLength={10} keyboardShouldPersistTaps="always">
      <VirtualizedList
        style={styles.list}
        data={[]}
        getItemCount={() => logCount}
        getItem={(data, index) => {
          return { index, data };
        }}
        keyExtractor={(_, index) => index.toString()}
        renderItem={({ index }) => {
          // const descIndex = logCount - index - 1;
          const descIndex = index;
          return (
            <TouchableOpacity
              activeOpacity={0.75}
              onPress={() => {
                if (selectedRowIndex === descIndex) {
                  hideDetailView();
                  return;
                }
                if (selectedRowIndex === undefined) {
                  needRecoverAutoRefresh.current = enableAutoRefresh;
                }
                setSelectedRowIndex(descIndex);
                setEnableAutoRefresh(false);
              }}>
              <ListCell index={index} DESCIndex={index} active={selectedRowIndex === descIndex} />
            </TouchableOpacity>
          );
        }}
        extraData={lastUpdateTime}
        initialNumToRender={30}
        getItemLayout={(_, index) => ({
          length: cellHeight,
          offset: cellHeight * index,
          index,
        })}
        maxToRenderPerBatch={60}
        removeClippedSubviews={true}
        windowSize={5}
      />
    </ScrollView>
  );

  const $closeButton = (
    <TouchableOpacity
      onPress={() => {
        if (needRecoverAutoRefresh.current) {
          setEnableAutoRefresh(true);
        }
        setSelectedRowIndex(undefined);
        setVisibility(!visibility);
      }}
      activeOpacity={0.5}
      style={styles.button}>
      <Text style={styles.closeButtonTitle}>Close</Text>
    </TouchableOpacity>
  );

  const $autoRefreshButton = (
    <TouchableOpacity
      onPress={() => {
        setEnableAutoRefresh(value => !value);
      }}
      activeOpacity={0.5}
      style={styles.button}>
      <Text style={enableAutoRefresh ? styles.buttonOnText : styles.buttonOffText}>
        {enableAutoRefresh ? 'AutoRefresh On' : 'AutoRefresh Off'}
      </Text>
    </TouchableOpacity>
  );

  const $autoSwitchEncrypButton = (
    <TouchableOpacity
      onPress={() => {
        setApiEncryptionEnabled(apiEncryptionEnabled ? false : true);
        BaseConfig.setApiEncryptionEnabled(apiEncryptionEnabled ? false : true);
      }}
      activeOpacity={0.5}
      style={styles.button}>
      <Text style={apiEncryptionEnabled ? styles.buttonOnText : styles.buttonOffText}>
        {apiEncryptionEnabled ? 'EncryptionEnabled On' : 'EncryptionEnabled Off'}
      </Text>
    </TouchableOpacity>
  );

  let $debugMenu: React.ReactElement | null = null;
  let $reloadMenu: React.ReactElement | null = null;
  if (__DEV__) {
    $debugMenu = (
      <TouchableOpacity
        onPress={() => {
          const { DevMenu } = NativeModules;
          DevMenu && DevMenu.show();
        }}
        activeOpacity={0.5}
        style={styles.button}>
        <Text style={styles.buttonText}>DevMenu</Text>
      </TouchableOpacity>
    );
    $reloadMenu = (
      <TouchableOpacity
        onPress={() => {
          // const { DevMenu } = NativeModules;
          // DevMenu.reload();
          DevSettings.reload();
        }}
        activeOpacity={0.5}
        style={styles.button}>
        <Text style={styles.buttonText}>Reload</Text>
      </TouchableOpacity>
    );
  }

  const $menuBar = (
    <ScrollView horizontal fadingEdgeLength={10} style={styles.logOperate}>
      <View style={styles.menuBar}>
        {$closeButton}
        {$autoRefreshButton}
        {$reloadMenu}
        {$debugMenu}
        <Text style={{ color: '#fff' }}>{BaseConfig.appVersion}</Text>
        {$autoSwitchEncrypButton}
      </View>
    </ScrollView>
  );
  /**
   * 清空日志按钮，只清空当前展示的日志，不会影响日志的原始数据
   */
  const $clearButton = (
    <TouchableOpacity
      onPress={() => {
        loggerManagerInstance.action.updateLogCount(0);
        setSelectedRowIndex(undefined);
      }}
      activeOpacity={0.5}
      style={styles.button}>
      <Text style={styles.closeButtonTitle}>Clear</Text>
    </TouchableOpacity>
  );

  /**
   * 导出日志按钮。导出所有经过筛选功能过滤的日志。
   */
  const $exportButton = (
    <TouchableOpacity
      onPress={async () => {
        const result = await logDBOperation.query({
          type: 'data',
          filterText: filterText,
          filterType: filterType,
          limit: logCount,
          desc: true,
          offset: 0,
        });
        if (exportFileType === EExportFileType.text) {
          // @ts-ignore
          parseLogToTextToShare(result.rows._array);
        } else {
          // @ts-ignore
          parseLogToSqliteToShare(result.rows._array);
        }
      }}
      activeOpacity={0.5}
      style={styles.button}>
      <Text style={styles.buttonText}>Export</Text>
    </TouchableOpacity>
  );

  /**
   * 全量导出日志按钮。导出所有日志。
   */
  const $fullExportButton = (
    <TouchableOpacity
      onPress={async () => {
        // query success
        const result = await logDBOperation.query({
          type: 'data',
          desc: true,
        });
        if (exportFileType === EExportFileType.text) {
          //@ts-ignore
          parseLogToTextToShare(result.rows._array);
        } else {
          //@ts-ignore
          parseLogToSqliteToShare(result.rows._array);
        }
      }}
      activeOpacity={0.5}
      style={styles.button}>
      <Text style={styles.buttonText}>Full Export</Text>
    </TouchableOpacity>
  );

  /**
   * 全量导出文本按钮。导出所有文本, 以excel的形式。
   */
  const $fullExportTextButton = (
    <TouchableOpacity
      onPress={async () => {
        // 根据当前使用语言进行导出
        let mxStrObj = i18nResources[i18n.resolvedLanguage as string]['translation'];
        let mxStrArr = [];
        for (const [moduleKey, i18nObj] of Object.entries(mxStrObj)) {
          for (const [i18nKey, content] of Object.entries(i18nObj)) {
            mxStrArr.push({
              country: i18n.resolvedLanguage as string,
              moduleKey: moduleKey,
              i18nKey: i18nKey,
              content: content as string,
            });
          }
        }
        parseI18nToCsvToShare(mxStrArr);
      }}
      activeOpacity={0.5}
      style={styles.button}>
      <Text style={styles.buttonText}>Full Export Text</Text>
    </TouchableOpacity>
  );

  /**
   * 全量删除日志按钮。删除所有的日志。
   */
  const $fullDeleteButton = (
    <TouchableOpacity
      onPress={() => {
        loggerManagerInstance.action.updateLogCount(0);
        logDBOperation.delete();
      }}
      activeOpacity={0.5}
      style={styles.button}>
      <Text style={styles.buttonText}>Full Delete</Text>
    </TouchableOpacity>
  );
  /**
   * 全量上报所有的日志
   */
  const $fullReportButton = (
    <TouchableOpacity
      onPress={async () => {
        if (exportFileType === EExportFileType.text) {
          await findAllLogRecordAndParseLogDataListToTextToUpload();
        } else {
          await findAllLogRecordAndParseLogDataListToCsvToUpload();
        }
      }}
      activeOpacity={0.5}
      style={styles.button}>
      <Text style={styles.buttonText}>Full Report</Text>
    </TouchableOpacity>
  );

  const $exportFilterType = (
    <View style={styles.exportFilterTypeContainer}>
      <Text
        onPress={() => {
          setExportFileType(EExportFileType.csv);
        }}
        style={[
          styles.switchTab,
          EExportFileType.csv === exportFileType ? styles.switchSelected : styles.switchUnSelected,
        ]}>
        {EExportFileType.csv}
      </Text>
      <Text
        onPress={() => {
          setExportFileType(EExportFileType.text);
        }}
        style={[
          styles.switchTab,
          EExportFileType.text === exportFileType ? styles.switchSelected : styles.switchUnSelected,
        ]}>
        {EExportFileType.text}
      </Text>
    </View>
  );

  const $logOperate = (
    <ScrollView fadingEdgeLength={10} horizontal style={styles.logOperate}>
      <View style={styles.menuBar}>
        {$clearButton}
        {$exportFilterType}
        {$exportButton}
        {$fullExportButton}
        {exportFileType === EExportFileType.csv && $fullExportTextButton}
        {$fullDeleteButton}
        {$fullReportButton}
      </View>
    </ScrollView>
  );

  const $filter = <LogFilter />;

  return (
    <View
      style={[styles.container, { paddingTop: insets.top, paddingBottom: insets.bottom }]}
      //   pointerEvents="none"
    >
      <>
        <View>
          {$menuBar}
          {$logOperate}
          {$filter}
        </View>
        {$list}
        {selectedRowIndex !== undefined && (
          <LogDetail index={selectedRowIndex} close={hideDetailView} />
        )}
      </>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    right: 0,
    left: 0,
    bottom: 0,
    height: '100%',
    backgroundColor: 'rgba(0,0,0,0.75)',
    flexDirection: 'column',
    justifyContent: 'flex-start',
    alignItems: 'stretch',
  },
  menuBar: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    width: '100%',
  },
  button: {
    padding: 10,
  },
  buttonText: {
    color: 'rgba(239, 239, 238, 1.00)',
  },
  buttonOnText: {
    color: 'rgba(176, 245, 102, 1.00)',
  },
  buttonOffText: {
    color: 'rgba(94, 107, 159, 1.00)',
  },
  closeButtonTitle: {
    color: 'rgba(241, 73, 34, 1.00)',
    fontSize: 14,
  },
  text: {
    color: '#ffffff',
  },
  list: {
    flexGrow: 1,
  },
  switchTab: { padding: 6 },
  switchSelected: {
    backgroundColor: '#666',
    color: '#fff',
  },
  switchUnSelected: {
    backgroundColor: '#333',
    color: '#888',
  },
  exportFilterTypeContainer: {
    flexDirection: 'row',
    borderRadius: 10,
    overflow: 'hidden',
  },
  logOperate: {
    height: 60,
  },
});
