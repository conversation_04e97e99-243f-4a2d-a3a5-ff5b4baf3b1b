import * as SqlString from 'sqlstring';
import { EFilterType, QueryResult } from './type';
import { executeSQL } from './logDB';

export interface QueryLogParams {
  limit?: number;
  offset?: number;
  type?: 'count' | 'data';
  desc?: boolean;
  // 查询关键字
  filterText?: string;
  filterType?: EFilterType;
}

async function queryLog(params: QueryLogParams): Promise<QueryResult | undefined> {
  const perfix = 'queryLog';

  const { limit, offset, type, desc, filterText, filterType } =
    getQueryLogParamsWithDefaultValue(params);
  try {
    let sql: string | undefined;
    let sqlString: string | undefined;
    switch (type) {
      case 'count':
        sqlString = `SELECT COUNT(*) as count FROM  "logContent" ${
          filterText ? `WHERE ${filterType}` : ''
        } ${filterText ? `LIKE '%${filterText}%'` : ''};`;
        // console.log('sqlString  count ===', sqlString);
        sql = SqlString.format(sqlString, []);
        break;
      case 'data':
        sqlString = `SELECT * FROM  "logContent" ${filterText ? `WHERE ${filterType}` : ''} ${
          filterText ? `LIKE '%${filterText}%'` : ''
        } ORDER BY "id" ${desc ? 'DESC' : 'ASC'} LIMIT ? OFFSET ?;`;
        // console.log('sqlString  data ===', sqlString);
        sql = SqlString.format(sqlString, [limit, offset]);
        break;
      default:
        break;
    }

    if (!sql) {
      console.log('queryLog error: wrong type');
      return undefined;
    }
    const queryRes = await executeSQL(sql);
    // 仅缓存数据查询，忽略数量查询
    if (queryRes && type === 'data') {
      setQueryCache(perfix, params, queryRes);
    }
    return queryRes;
  } catch (e) {
    console.log('queryLog error', e);
  }
  return undefined;
}
/**
 * 删除所有日志
 */
async function d(): Promise<QueryResult | undefined> {
  try {
    const sql = SqlString.format('DELETE FROM "logContent"');
    const queryRes = await executeSQL(sql);
    return queryRes;
  } catch (err) {
    __DEV__ && console.log('deleteLog error', err);
  }
  return undefined;
}

interface DeleteLogParams {
  day?: number;
}
async function d_latest_days(params: DeleteLogParams = {}): Promise<QueryResult | undefined> {
  const { day = 30 } = params;
  try {
    const sql = SqlString.format(` 
      DELETE FROM "logContent"
      WHERE timeString <= strftime('%s', datetime('now', '-${day} days'))
    `);
    const queryRes = await executeSQL(sql);
    return queryRes;
  } catch (err) {
    __DEV__ && console.log('deleteLog where 30 days ago error', err);
  }
  return undefined;
}
/**
 * 自动填充参数默认值，供多个函数使用
 * @param params QueryLogParams
 * @returns
 */
function getQueryLogParamsWithDefaultValue(params: QueryLogParams): QueryLogParams {
  const {
    limit = 300,
    offset = 0,
    type = 'data',
    desc = true,
    filterText = '',
    filterType = EFilterType.content,
  } = params;
  return {
    limit,
    offset,
    type,
    desc,
    filterText,
    filterType,
  };
}

/**
 * 查询缓存逻辑
 * 默认缓存最近的 ？ 条数据，避免反复数据库查询
 */

const __cache__ = {
  queryKeyArray: [] as Array<string>,
  queryKeyArrayMaxLength: 120,
  queryKeyValue: {} as { [key: string]: QueryResult },
};

/**
 * 获取缓存 Key
 * @param perfix 缓存前缀，用于区分执行了什么查询操作
 * @param params
 * @returns
 */
function getKeyForQueryParams(perfix: string, params: QueryLogParams) {
  const { limit, offset, type, desc } = getQueryLogParamsWithDefaultValue(params);
  return `${perfix}|${limit}|${offset}|${type}|${desc}`;
}

/**
 * 尝试获取缓存
 * @param perfix 缓存前缀，用于区分执行了什么查询操作
 * @param params
 */
function getQueryCache(perfix: string, params: QueryLogParams): QueryResult | undefined {
  const key = getKeyForQueryParams(perfix, params);
  return __cache__.queryKeyValue[key];
}

function setQueryCache(perfix: string, params: QueryLogParams, value: QueryResult) {
  if (getQueryCache(perfix, params)) {
    return;
  }

  const key = getKeyForQueryParams(perfix, params);
  __cache__.queryKeyValue[key] = value;
  __cache__.queryKeyArray.push(key);
  if (__cache__.queryKeyArray.length > __cache__.queryKeyArrayMaxLength) {
    const keyForRemove = __cache__.queryKeyArray.shift();
    if (keyForRemove) {
      delete __cache__.queryKeyValue[keyForRemove];
    }
  }
}

function clearCache() {
  __cache__.queryKeyArray = [];
  __cache__.queryKeyValue = {};
}

export default {
  query: queryLog,
  delete: d,
  delete_latest_days: d_latest_days,
  clearCache,
};
