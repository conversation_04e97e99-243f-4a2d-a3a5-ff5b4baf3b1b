export interface QueryResult {
  status?: 0 | 1;
  insertId?: number;
  rowsAffected: number;
  message?: string;
  rows?: {
    /** Raw array with all dataset */
    _array: ISqlLogInfo[];
    /** The lengh of the dataset */
    length: number;
    /** A convenience function to acess the index based the row object
     * @param idx the row index
     * @returns the row structure identified by column names
     */
    item: (idx: number) => any;
  };
}

export interface ISqlLogInfo {
  additionalData: string;
  content: string;
  id: number;
  level: string;
  namespace: string;
  time: number;
  timeString: string;
}

export interface II18nTextInfo {
  country: string;
  moduleKey: string;
  i18nKey: string;
  content: string;
}

export interface IDBConnection {
  executeSql: (
    sql: string,
    args: any[],
    ok: (res: QueryResult) => void,
    fail: (msg: string) => void,
  ) => void;
  close: (ok: (res: any) => void, fail: (msg: string) => void) => void;
}

export enum EExportFileType {
  csv = 'csv',
  text = 'text',
}

export enum EFilterType {
  level = 'level',
  namespace = 'namespace',
  content = 'content',
}
