import React, { useEffect } from 'react';
import { StyleSheet, Text, TextInput, TouchableOpacity, View } from 'react-native';
import { MMKVLoader } from 'react-native-mmkv-storage';
import { loggerManagerInstance } from '../managers/logger/loggerManager';
import { LogFilterTypeSelector } from './logfilterTypeSelector';
import { EFilterType } from './type';
const storage = new MMKVLoader().withInstanceID('loggerManager').initialize();

export const LogFilter = React.memo(() => {
  const [filterRecordTextList, setFilterRecordTextList] = React.useState<string[]>([]);

  const [filterText, setFilterText] = React.useState<string>(
    loggerManagerInstance.context.state.filterText,
  );

  const [filterType, setFilterType] = React.useState<EFilterType>(
    loggerManagerInstance.context.state.filterType,
  );

  const onSetFilterText = (filterRecordText?: string) => {
    // 设置文本
    // 去除首位空格
    let text = filterText.replace(/^\s+|\s+$/gm, '');
    if (filterRecordText !== undefined) {
      setFilterText(filterRecordText);
      text = filterRecordText;
    }

    // 数组去重
    const filterRecordTextListArr = text && [...new Set([text, ...filterRecordTextList])];
    // 只保留十个记录
    const newFilterRecordTextListArr =
      filterRecordTextListArr && filterRecordTextListArr.slice(0, 10);
    // 只有当修改的过滤文本与当前的过滤文本不一致，并且不为全为空格字符串，才会筛选
    if (newFilterRecordTextListArr) {
      storage.setStringAsync('logFilterRecordTextList', JSON.stringify(newFilterRecordTextListArr));
      setFilterRecordTextList(newFilterRecordTextListArr);
    }
    // 只有在当前过滤文本存在，并且不等于当前正在过滤的记录
    if (
      loggerManagerInstance.context.state.filterText !== text ||
      loggerManagerInstance.context.state.filterType !== filterType
    ) {
      loggerManagerInstance.action.updateFilterCondition({
        text: text,
        type: filterType,
      });
    }
  };

  const getLogFilterRecordTextList = async () => {
    const logFilterRecordTextListString = await storage.getStringAsync('logFilterRecordTextList');
    if (logFilterRecordTextListString) {
      try {
        const logFilterRecordTextList = JSON.parse(logFilterRecordTextListString);
        setFilterRecordTextList(logFilterRecordTextList);
      } catch (err) {
        __DEV__ && console.log('getLogFilterRecordTextList error', err);
      }
    }
  };

  // useMount
  useEffect(() => {
    // 从本地缓存中读取记录
    getLogFilterRecordTextList();
  }, []);

  const $input = (
    <View style={styles.inputContianer}>
      <TextInput
        keyboardType="url"
        placeholder="Enter filter text"
        placeholderTextColor={'#FFFFFF55'}
        style={styles.input}
        value={filterText}
        onChange={e => setFilterText(e.nativeEvent.text)}
      />
      <TouchableOpacity
        style={filterText.length > 0 ? styles.clearButtonActive : styles.clearButton}
        onPress={() => onSetFilterText('')}>
        <Text style={{ color: '#ffffff' }}>clear</Text>
      </TouchableOpacity>
    </View>
  );

  const $filter = (
    <View style={styles.container}>
      <LogFilterTypeSelector filterType={filterType} onChange={setFilterType} />

      {$input}

      <TouchableOpacity
        style={styles.filterButton}
        onPress={() => {
          onSetFilterText();
        }}>
        <Text style={styles.buttonTitle}>Filter</Text>
      </TouchableOpacity>
    </View>
  );

  const $filterRecordTextList = filterRecordTextList.length !== 0 && (
    <View style={styles.recordContainer}>
      {filterRecordTextList.map((filterRecordText, index) => {
        return (
          <Text
            key={index.toString()}
            style={styles.recordText}
            onPress={() => onSetFilterText(filterRecordText)}>
            {filterRecordText}
          </Text>
        );
      })}
    </View>
  );
  return (
    <>
      {$filter}
      {$filterRecordTextList}
    </>
  );
});

export default LogFilter;

const styles = StyleSheet.create({
  container: {
    width: '100%',
    height: 60,
    paddingHorizontal: 12,
    marginVertical: 6,
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
  },
  space: {
    width: 6,
    height: 6,
  },
  inputContianer: {
    backgroundColor: '#88888834',
    borderRadius: 6,
    flexGrow: 1,
    marginRight: 8,
    marginLeft: 8,
    height: 44,
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'stretch',
  },
  clearIcon: {
    height: 16,
    width: 16,
    // position: 'absolute',
  },
  input: {
    flex: 1,
    height: 44,
    paddingLeft: 8,
    color: '#FFF',
  },
  recordContainer: {
    flexWrap: 'wrap',
    width: '100%',
    paddingHorizontal: 12,
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
  },
  recordText: {
    backgroundColor: '#349034',
    fontSize: 12,
    paddingHorizontal: 6,
    paddingVertical: 4,
    marginRight: 12,
    marginBottom: 6,
    borderRadius: 6,
    color: '#fff',
  },
  filterButton: {
    borderRadius: 6,
    backgroundColor: 'green',
    height: 44,
    justifyContent: 'center',
    alignItems: 'center',
    paddingLeft: 8,
    paddingRight: 8,
  },
  buttonTitle: {
    color: '#fff',
  },
  clearButton: {
    width: 32,
    justifyContent: 'center',
    alignItems: 'center',
    opacity: 0.3,
  },
  clearButtonActive: {
    width: 32,
    justifyContent: 'center',
    alignItems: 'center',
    opacity: 1,
  },
});
