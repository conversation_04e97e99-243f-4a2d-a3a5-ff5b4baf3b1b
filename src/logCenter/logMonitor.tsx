import React, { useMemo, useRef } from 'react';
import {
  Dimensions,
  NativeModules,
  PanResponder,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import PerformanceStats from 'react-native-performance-stats';
import Animated, { interpolate, useAnimatedStyle, useSharedValue } from 'react-native-reanimated';
import { useLoggerManager } from '../managers/logger/loggerManager';
import { EDevicePerformanceStats } from '../managers/logger/types';
import styles from './logMonitor.style';
// type ContextType = {
//   translateX: number;
//   translateY: number;
// };

const windowWidth = Dimensions.get('window').width;
const maxValueForTranslateX = styles.container.right;
const minValueForTranslateX = -(windowWidth - styles.container.right - styles.container.width);
const minValueForTranslateY = -styles.container.top;
const maxValueForTranslateY = Dimensions.get('window').height - styles.container.top;

export default function LogMonitor({ toggleModalVisiable }: { toggleModalVisiable: () => void }) {
  // state
  // cpu 使用率
  const [cpu, setCPU] = React.useState<number>(0);
  // 内存使用率
  const [memory, setMemory] = React.useState<number>(0);
  // ui帧率
  const [ui, setUI] = React.useState<number>(0);
  // js帧率
  const [js, setJS] = React.useState<number>(0);
  // 总的运行内存
  // const [allMemorySize, setAllMemorySize] = React.useState(1024);

  const [availableRamRate, setAvailableRamRate] = React.useState(0);

  const {
    state: { showList },
  } = useLoggerManager();

  const [cacheOffset, setCacheOffset] = React.useState({ x: 0, y: 0 });

  const [currentTime, setCurrentTime] = React.useState<number>(0);

  // ref
  const devMenuRef = useRef(null);
  const devMenuTextRef = useRef(null);

  // gesture
  const isPressed = useSharedValue(0);
  const offset = useSharedValue({ x: 0, y: 0 });
  const animatedStyles = useAnimatedStyle(() => {
    // console.log('offset ===', offset)
    return {
      transform: [{ translateX: offset.value.x }, { translateY: offset.value.y }],
      opacity: interpolate(isPressed.value, [0, 1], [1, 0.5]),
    };
  });

  // const start = useSharedValue({ x: 0, y: 0 });
  // const isPanGesture = useSharedValue(false);
  // const updateIsPanGesture = React.useCallback(
  //   (value: boolean) => {
  //     isPanGesture.value = value;
  //   },
  //   [isPanGesture]
  // );

  // const _panResponderRef = useRef(null)

  const updateOffsetValue = React.useCallback(
    (x: number, y: number) => {
      if (x > maxValueForTranslateX) {
        x = maxValueForTranslateX;
      }
      if (x < minValueForTranslateX) {
        x = minValueForTranslateX;
      }
      if (y < minValueForTranslateY) {
        y = minValueForTranslateY;
      }
      const currentMaxValueForTranslateY = maxValueForTranslateY - showList.length * 20;
      if (y > currentMaxValueForTranslateY) {
        y = currentMaxValueForTranslateY;
      }
      offset.value = { x, y };
    },
    [offset, showList],
  );

  const _panResponderRef = useMemo(
    () =>
      PanResponder.create({
        // 要求成为响应者：
        onStartShouldSetPanResponder: () => true,
        onStartShouldSetPanResponderCapture: () => true,
        onMoveShouldSetPanResponder: () => true,
        onMoveShouldSetPanResponderCapture: () => true,

        onPanResponderGrant: () => {
          // 开始手势操作。给用户一些视觉反馈，让他们知道发生了什么事情！
          // __DEV__ && console.log('# 触摸开始')
          // todo 手势动画开始记录点击的时间
          setCurrentTime(new Date().getTime());
          // offset.value = {
          //   x: offset.value.x,
          //   y: offset.value.y,
          // }
          // gestureState.{x,y} 现在会被设置为0
        },
        onPanResponderMove: (_, gestureState) => {
          // 最近一次的移动距离为gestureState.move{X,Y}
          // __DEV__ && console.log('# 触摸中', offset)
          // __DEV__ && console.log('cacheOffset ===', cacheOffset)
          updateOffsetValue(cacheOffset.x + gestureState.dx, cacheOffset.y + gestureState.dy);
          // 从成为响应者开始时的累计手势移动距离为gestureState.d{x,y}
        },
        onPanResponderTerminationRequest: () => true,
        onPanResponderRelease: event => {
          event;
          // 用户放开了所有的触摸点，且此时视图已经成为了响应者。
          // __DEV__ && console.log('# 触摸结束')

          const timeEnrty = new Date().getTime() - currentTime < 200;
          const offsetEnrty = (() => {
            if (
              Math.abs(offset.value.x - cacheOffset.x) < 2 &&
              Math.abs(offset.value.x - cacheOffset.x) < 2
            ) {
              return true;
            } else {
              return false;
            }
          })();
          if (timeEnrty && offsetEnrty) {
            // todo 触摸时间小于200毫秒 并且按钮移动的距离小于1表示被点击了
            if (__DEV__) {
              // 开发者模式才会打开开发者菜单
              // todo 特殊处理 release 模式下，此处会产生错误
              //     com.facebook.react.common.JavascriptException: TypeError: null is not an object (evaluating 'Q.current._nativeTag'), stack:
              try {
                const target = event.nativeEvent.target;
                if (
                  // @ts-ignore
                  target === devMenuRef?.current?._nativeTag ||
                  // @ts-ignore
                  target === devMenuTextRef?.current?._nativeTag
                ) {
                  const { DevMenu } = NativeModules;
                  DevMenu && DevMenu.show();
                } else {
                  toggleModalVisiable();
                }
              } catch (error) {
                __DEV__ && console.log('error ', error);
                toggleModalVisiable();
              }
            } else {
              // 其他模式直接打开日志弹窗
              toggleModalVisiable();
            }
            // __DEV__ && console.log('按钮被点击了', event.nativeEvent.target)
          }
          setCacheOffset(offset.value);
          // __DEV__ && console.log('cacheOffset ===', cacheOffset)
          // 一般来说这意味着一个手势操作已经成功完成。
        },
        onPanResponderTerminate: () => {
          // 另一个组件已经成为了新的响应者，所以当前手势将被取消。
        },
        onShouldBlockNativeResponder: () => {
          // 返回一个布尔值，决定当前组件是否应该阻止原生组件成为JS响应者
          // 默认返回true。目前暂时只支持android。
          return true;
        },
      }),
    [
      cacheOffset,
      currentTime,
      offset,
      devMenuRef,
      devMenuTextRef,
      toggleModalVisiable,
      updateOffsetValue,
    ],
  );

  /**
   * 获取系统运行内存大小
   */
  // const getTotalMemory = async () => {
  //   let totalMemory = 0;
  //   if (Platform.OS === 'ios') {
  //     totalMemory = await deviceInfo.getTotalMemory();
  //   } else {
  //     // android
  //     totalMemory = await NativeModules.NativeMethod.getTotalMemory();
  //   }
  //   setAllMemorySize(Number(totalMemory));
  // };

  React.useEffect(() => {
    const listener = PerformanceStats.addListener(stats => {
      const { uiFps, usedCpu, usedRam, jsFps, availableRamRate } = stats;
      setCPU(usedCpu);
      setMemory(usedRam);
      setUI(uiFps);
      setJS(jsFps);
      setAvailableRamRate(availableRamRate);
    });
    openGetDevicePerformanceStats();
    // getTotalMemory();
    return () => {
      closeGetDevicePerformanceStats();
      listener.remove();
    };
  }, []);

  /**
   * 启动系统信息查询
   */
  const openGetDevicePerformanceStats = () => {
    PerformanceStats.start(true);
  };

  /**
   * 关闭系统信息查询
   */
  const closeGetDevicePerformanceStats = () => {
    PerformanceStats.stop();
  };

  const renderValuesItem = (type: EDevicePerformanceStats) => {
    switch (type) {
      case EDevicePerformanceStats.cpu:
        return (
          <View style={styles.valueContainer} key={EDevicePerformanceStats.cpu}>
            <View style={[styles.valueBackground, { width: `${Math.round(cpu)}%` }]} />
            <Text style={styles.valueText}>CPU: {cpu.toFixed(2)}%</Text>
          </View>
        );
      case EDevicePerformanceStats.js:
        return (
          <View style={styles.valueContainer} key={EDevicePerformanceStats.js}>
            <View style={[styles.valueBackground, { width: `${Math.round(js)}%` }]} />
            <Text style={styles.valueText}>JS: {js.toFixed(2)}fps</Text>
          </View>
        );
      case EDevicePerformanceStats.ui:
        return (
          <View style={styles.valueContainer} key={EDevicePerformanceStats.ui}>
            <View style={[styles.valueBackground, { width: `${Math.round(ui)}%` }]} />
            <Text style={styles.valueText}>UI: {ui.toFixed(2)}fps</Text>
          </View>
        );
      case EDevicePerformanceStats.mem:
        return (
          <View style={styles.valueContainer} key={EDevicePerformanceStats.mem}>
            <View
              style={[
                styles.valueBackground,
                {
                  width: `${availableRamRate * 100}%`,
                },
              ]}
            />
            <Text style={styles.valueText}>MEM: {memory.toFixed(2)}MB</Text>
          </View>
        );
      // case EDevicePerformanceStats.totalMem:
      //   return (
      //     <View
      //       style={styles.valueContainer}
      //       key={EDevicePerformanceStats.totalMem}>
      //       <View
      //         style={[
      //           styles.valueBackground,
      //           {
      //             width: `${
      //               allMemorySize === 0
      //                 ? 0
      //                 : Math.round(memory / (allMemorySize / 1000))
      //             }%`,
      //           },
      //         ]}
      //       />
      //       <Text style={styles.valueText}>
      //         ALL MEM: {allMemorySize.toFixed(2)}MB
      //       </Text>
      //     </View>
      //   );
      default:
        return null;
    }
  };

  const $values = (
    <TouchableOpacity
      activeOpacity={0.9}
      onPress={toggleModalVisiable}
      style={styles.leftContainer}>
      {showList.map((type: EDevicePerformanceStats) => {
        return renderValuesItem(type);
      })}
    </TouchableOpacity>
  );

  let $reloadButton: React.ReactElement | null = null;
  if (__DEV__) {
    $reloadButton = (
      <TouchableOpacity
        ref={devMenuRef}
        activeOpacity={0.6}
        onPress={() => {
          const { DevMenu } = NativeModules;
          DevMenu && DevMenu.show();
        }}
        style={styles.reloadContainer}>
        <Text ref={devMenuTextRef} style={styles.reloadText}>
          ·
        </Text>
      </TouchableOpacity>
    );
  }

  const $monitor = (
    // @ts-ignore
    // <PanGestureHandler  onGestureEvent={panGestureEvent}>
    <Animated.View {..._panResponderRef.panHandlers} style={[styles.container, animatedStyles]}>
      {$values}
      {$reloadButton}
    </Animated.View>
    // </PanGestureHandler>
  );

  return $monitor;
}
