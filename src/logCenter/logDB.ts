import RNFS from 'react-native-fs';
import { typeORMDriver } from 'react-native-quick-sqlite';
import type { IDBConnection, QueryResult } from './type';

let db: IDBConnection | undefined;

// const DATA_BASE_NAME: string = 'eventCenter.db'
const DATA_BASE_NAME: string = 'logCenter.db';

export async function getDB(): Promise<IDBConnection | undefined> {
  await setupDatabase();
  return db;
}

export async function executeSQL(
  sql: string,
  params: any[] = [],
): Promise<QueryResult | undefined> {
  if (!(await getDB())) {
    __DEV__ && console.log('# executeSQL error, db is not found');
    return undefined;
  }
  return new Promise(async (resolve, reject) => {
    db?.executeSql(sql, params, resolve, reject);
  });
}

export async function closeDB() {
  if (!db) {
    return;
  }
  return new Promise((resolve, reject) => {
    db?.close(resolve, reject);
  });
}

async function openDB(filePathForDatabase: string) {
  return new Promise((resolve, reject) => {
    typeORMDriver.openDatabase(
      { name: DATA_BASE_NAME, location: filePathForDatabase },
      _db => {
        __DEV__ &&
          console.log(
            '#open log center database success',
            RNFS.DocumentDirectoryPath + '/' + filePathForDatabase,
          );
        db = _db;
        resolve(db);
      },
      error => {
        console.log('#open db error', error);
        reject(error);
      },
    );
  });
}

async function initDB() {
  if (!db) {
    return;
  }
  return new Promise((resolve, reject) => {
    db?.executeSql(
      `
        CREATE TABLE IF NOT EXISTS "logContent" (
            "id" INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL,
            "time" INTEGER(128) NOT NULL,
            "timeString" TEXT(32) NOT NULL,
            "isoTimeString" TEXT(32) NOT NULL,
            "namespace" TEXT(128),
            "level" TEXT(32),
            "content" TEXT(512),
            "additionalData" TEXT(20480)
        );
      `,
      [],
      resolve,
      reject,
    );
  });
}

export async function setupDatabase() {
  if (db) {
    return;
  }
  const filePathForDatabase = 'logCenter.sqlite';

  try {
    __DEV__ && console.log('### log DB 初始化');
    await openDB(filePathForDatabase);
    await initDB();
    __DEV__ && console.log('### log DB 初始化成功');
  } catch (error) {
    // TODO: 处理 database 初始化失败情况
    __DEV__ && console.log('### log DB 初始化失败', error);
  }
}
