import { StyleSheet } from 'react-native';

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    right: 20,
    top: 40,
    width: __DEV__ ? 130 : 100,
    // height: 80,
    backgroundColor: 'rgba(0,0,0,0.8)',
    borderRadius: 6,
    overflow: 'hidden',
    flexDirection: 'row',
    borderWidth: 1,
    borderColor: 'rgba(33, 37, 43, 1.00)',
  },
  leftContainer: {
    flexDirection: 'column',
    flexGrow: 1,
  },
  valueContainer: {
    paddingTop: 2,
    paddingBottom: 2,
    paddingLeft: 6,
    paddingRight: 6,
    flexGrow: 1,
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignContent: 'center',
    overflow: 'hidden',
  },
  valueBackground: {
    backgroundColor: 'rgba(176, 245, 102, 0.5)',
    position: 'absolute',
    top: 0,
    bottom: 0,
    left: 0,
  },
  valueText: {
    color: '#ffffff',
    fontSize: 10,
    lineHeight: 15,
  },
  reloadContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(102, 56, 240, 0.5)',
    flexGrow: 0,
    width: 40,
  },
  reloadText: {
    color: 'rgba(255,255,255,0.6)',
    fontSize: 26,
  },
});

export default styles;
