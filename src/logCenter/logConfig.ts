import dayjs from 'dayjs';
import {
  configLoggerType,
  consoleTransport,
  logger,
  transportFunctionType,
} from 'react-native-logs';
import * as SqlString from 'sqlstring';
import { executeSQL } from './logDB';
import {} from './logDBOperation';

function setupLogger() {
  const transport: transportFunctionType = async props => {
    const { rawMsg, level, extension } = props;
    // console.log('#raw msg', rawMsg);
    // todo 这里会把日志输入到控制台。在非debug模式下需要屏蔽掉
    __DEV__ && consoleTransport(props);

    const namespace = extension || 'default';
    const mainContent = rawMsg[0] || '';
    const additionalData = rawMsg.length > 1 ? JSON.stringify([rawMsg].flat().slice(1)) : undefined;
    const sql = SqlString.format(
      `
        INSERT INTO logContent (time, timeString, isoTimeString, namespace, level, content, additionalData) 
        VALUES (?, ?, ?, ?, ?, ?, ?);
      `,
      [
        Date.now(),
        new Date().getTime(),
        dayjs().toISOString(),
        namespace,
        level.text,
        mainContent,
        additionalData,
      ],
    );
    // console.log('#sql', sql);
    try {
      await executeSQL(sql);
    } catch (e) {
      console.log('executeSQL error', e);
    }
  };

  const config: configLoggerType = {
    transport,
    levels: {
      debug: 0,
      info: 1,
      warn: 2,
      error: 3,
    },
    severity: 'debug',
    transportOptions: {
      colors: {
        info: 'greenBright',
        warn: 'yellowBright',
        error: 'redBright',
        debug: 'blueBright',
      },
    },
    async: true,
    dateFormat: 'time',
    printLevel: true,
    printDate: true,
    enabled: true,
  };

  const log = logger.createLogger(config);
  // log.patchConsole();
  return log;
  //   log.error('#setupLogger');
}

const log = setupLogger();

export function logForNamespace(namespace: string) {
  const currentLog = log.extend(namespace);
  const logInfoParse = (...agrus: unknown[]) => {
    const argus1 = agrus[1];
    let currentArgus1 = null;
    if (argus1 && typeof argus1 === 'string') {
      // console.log('agrus1 ===', 'string');
      // 第二项传递的是字符串
      // arguments[0] 是一定存在的。判断下 arguments[1] 是否存在
      try {
        const val = JSON.parse(argus1);
        if (val && typeof val === 'object') {
          currentArgus1 = val;
        }
      } catch (error) {
        currentArgus1 = argus1;
      }
    } else if (argus1 && Object.prototype.toString.call(argus1) === '[object Array]') {
      // console.log('agrus1 ===', 'array');
      currentArgus1 = [];
      // 第二项为obj
      // @ts-ignore
      argus1.forEach((item: string) => {
        if (item && typeof item === 'string') {
          try {
            const val = JSON.parse(item);
            if (val && typeof val === 'object') {
              currentArgus1.push(val);
            }
          } catch (error) {
            currentArgus1.push(item);
          }
        } else {
          currentArgus1.push(item);
        }
      });
    } else if (argus1 && Object.prototype.toString.call(argus1) === '[object Object]') {
      // console.log('agrus1 ===', 'object');
      currentArgus1 = {};
      for (var key in argus1) {
        // @ts-ignore
        const item = argus1[key];
        if (item && typeof item === 'string') {
          try {
            const val = JSON.parse(item);
            if (val && typeof val === 'object') {
              // @ts-ignore
              currentArgus1[key] = val;
            }
          } catch (error) {
            // @ts-ignore
            currentArgus1[key] = item;
          }
        } else {
          // @ts-ignore
          currentArgus1[key] = item;
        }
      }
    }
    return currentArgus1;
  };

  return {
    ...currentLog,
    info: function (...agrus: unknown[]) {
      const currentArgus1 = logInfoParse(...agrus);
      if (!currentArgus1) {
        currentLog.info(agrus[0]);
      } else {
        currentLog.info(agrus[0], currentArgus1);
      }
    },
    warn: function (...agrus: unknown[]) {
      const currentArgus1 = logInfoParse(...agrus);
      if (!currentArgus1) {
        currentLog.warn(agrus[0]);
      } else {
        currentLog.warn(agrus[0], currentArgus1);
      }
    },
    error: function (...agrus: unknown[]) {
      const currentArgus1 = logInfoParse(...agrus);
      if (!currentArgus1) {
        currentLog.error(agrus[0]);
      } else {
        currentLog.error(agrus[0], currentArgus1);
      }
    },
    debug: function (...agrus: unknown[]) {
      const currentArgus1 = logInfoParse(...agrus);
      if (!currentArgus1) {
        currentLog.debug(agrus[0]);
      } else {
        currentLog.debug(agrus[0], currentArgus1);
      }
    },
  };
}
