import { KVManager } from '@/managers';
import LogRecord, { QueryLogParams } from './logDBOperation';
import { EventPointRequestSpace, fetchLogRecordUpload } from '@/server';
import { EFilterType } from './type';

export { default as LogCenter } from './logCenter';
export { default as logOperation } from './logDBOperation';
export { logForNamespace } from './logConfig';

/** 埋点事件上传缓存 key */
const EVENT_POINT_UPLOAD_CACHE_KEY = 'logRecordUploadCachekey';
/** @description 更新日志缓存 */
const updateLogRecordUploadCache = async (debug: boolean = true) => {
  // 获取日志上传缓存 mmkv 缓存
  const upLoadCacheData = KVManager.action.getMap(EVENT_POINT_UPLOAD_CACHE_KEY);
  // debug &&
  //   log.debug('# updateLogRecordUploadCache upLoadCacheData', upLoadCacheData);
  if (!upLoadCacheData || (upLoadCacheData && Object.keys(upLoadCacheData).length === 0)) {
    // 在把日志数据查出来
    const params: QueryLogParams = {
      limit: 1000,
      offset: 0,
      type: 'data',
      filterType: EFilterType.level,
      filterText: 'info',
      desc: false,
    };
    const logRecordDataList = (await LogRecord.query(params))?.rows?._array || [];
    // 如果有日志数据 生成一个新的上传数据包，写入缓存，然后清空数据库。
    // debug &&
    //   log.debug(
    //     '# updateEventPointUploadCache logRecordDataList',
    //     logRecordDataList,
    //   );
    // 查询出来的事件列表
    const logRecordListLen = logRecordDataList?.length || 0;
    if (logRecordListLen !== 0) {
      const requestData: EventPointRequestSpace.RequestLogRecordType = {
        ut: Date.now(),
        logRecords: logRecordDataList,
      };
      // 缓存请求事件埋点
      KVManager.action.setMap(EVENT_POINT_UPLOAD_CACHE_KEY, requestData);
      // 清空
      await LogRecord.delete();
    }
  }
};

/** @description 上传日志缓存 */
const uploadLogRecordUploadCache = async (debug: boolean = false) => {
  // 获取日志上传缓存 mmkv 缓存
  const upLoadCacheData = KVManager.action.getMap(EVENT_POINT_UPLOAD_CACHE_KEY);
  if (upLoadCacheData && Object.keys(upLoadCacheData).length !== 0) {
    // todo 如果有读取出来然后上传。
    const resp = await fetchLogRecordUpload(
      upLoadCacheData as EventPointRequestSpace.RequestLogRecordType,
    );
    if (resp.code === 0) {
      KVManager.action.setMap(EVENT_POINT_UPLOAD_CACHE_KEY, {});
    }
  }
};

const _uploadCache_ = {
  isUpload: false,
  num: 0,
};

export const runTask = async () => {
  // 检查缓存并上传，上传成功就清理掉缓存
  await uploadLogRecordUploadCache();
  // 查询数据库，增加缓存，清理数据库
  await updateLogRecordUploadCache();
  // 检查缓存并上传，上传成功就清理掉缓存
  await uploadLogRecordUploadCache();
  _uploadCache_.num--;
};

const runTasks = async () => {
  while (_uploadCache_.num > 0) {
    await runTask();
  }
  _uploadCache_.isUpload = false;
};

/** 上传埋点记录 */
export const uploadLogRecord = async () => {
  if (_uploadCache_.isUpload) {
    _uploadCache_.num++;
  } else {
    _uploadCache_.num++;
    _uploadCache_.isUpload = true;
    await runTasks();
  }
};
