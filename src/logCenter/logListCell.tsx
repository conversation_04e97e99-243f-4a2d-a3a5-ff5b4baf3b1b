import React from 'react';
import { View, Text, StyleSheet, Dimensions } from 'react-native';
import logDBOperation from './logDBOperation';
import { useMountedState } from '@/hooks';
import { useLoggerManager } from '../managers/logger/loggerManager';
import { defLvlType } from 'react-native-logs';
import dayjs from 'dayjs';

export const LogListCell = React.memo(
  ({
    index,
    // DESCIndex,
    active,
  }: {
    index: number;
    DESCIndex: number;
    active: boolean;
  }) => {
    const isMounted = useMountedState();
    const [cellData, setCellData] = React.useState<{ [key: string]: any } | undefined>(undefined);
    const [cellDataError, setCellDataError] = React.useState('');

    const {
      state: { filterText, filterType, logCount },
    } = useLoggerManager();

    // component did load
    React.useEffect(() => {
      async function queryCellData() {
        const queryResult = await logDBOperation.query({
          limit: 1,
          offset: index,
          type: 'data',
          filterText: filterText,
          filterType: filterType,
          desc: true,
        });
        if (!isMounted()) {
          return;
        }
        if (queryResult?.rows?.length === 1 && queryResult.rows?.item(0)) {
          setCellDataError('');
          setCellData(queryResult.rows.item(0));
        } else {
          setCellDataError('Query cell data is empty');
        }
      }
      queryCellData();
    }, [index, filterText, filterType, logCount, isMounted]);

    if (cellDataError) {
      return (
        <View style={styles.cellCotnainer}>
          <Text style={styles.text}>Error: {cellDataError}</Text>
        </View>
      );
    }
    if (cellData === undefined) {
      return (
        <View style={styles.cellCotnainer}>
          <Text style={styles.text}>Loading...</Text>
        </View>
      );
    }

    // let additionalData = [];
    const {
      // id,
      time,
      // timeString,
      namespace,
      level,
      content,
      // additionalData: _additionalData,
    } = cellData;
    try {
      // additionalData = JSON.parse(_additionalData);
    } catch (e) {}
    const $timeString = <Text style={styles.textTime}>{dayjs(time).format('HH:mm:ss')}</Text>;
    let levelColor: string = '#5cc9f5';
    let contentColor: string = '#dddddd';
    switch (level as defLvlType) {
      case 'info':
        levelColor = '#5cc9f5';
        contentColor = '#dddddd';
        break;
      case 'warn':
        levelColor = '#ffaa00';
        contentColor = '#ffaa00';
        break;
      case 'error':
        levelColor = '#ff3535';
        contentColor = '#ff3535';

        break;
      case 'debug':
        levelColor = '#BFA6F6';
        contentColor = '#BFA6F6';
        break;
    }

    const $level = (
      <Text style={[styles.textLevel, { backgroundColor: levelColor }]}>
        {level.toLocaleUpperCase()}
      </Text>
    );
    const $namespace = <Text style={styles.textNamespace}>{namespace}</Text>;
    const $content = <Text style={[styles.text, { color: contentColor }]}>{content}</Text>;

    return (
      <View
        style={[
          styles.cellCotnainer,
          active ? styles.cellContainerForActive : styles.cellContainerForInactive,
        ]}>
        {$timeString}
        {$level}
        {$namespace}
        {$content}
      </View>
    );
  },
);

export default LogListCell;

export const cellHeight = 30;

const styles = StyleSheet.create({
  cellCotnainer: {
    paddingLeft: 8,
    paddingRight: 8,
    paddingTop: 4,
    paddingBottom: 4,
    flexDirection: 'row',
    minWidth: Dimensions.get('window').width,
    justifyContent: 'flex-start',
    alignItems: 'center',
    height: cellHeight,
    overflow: 'hidden',
  },
  cellContainerForActive: {
    backgroundColor: 'rgba(176, 245, 102, 0.3)',
  },
  cellContainerForInactive: {},
  text: {
    color: '#ffffff',
  },
  textTime: {
    color: '#4af2a1',
    fontSize: 10,
    marginRight: 4,
  },
  textLevel: {
    borderRadius: 6,
    padding: 3,
    overflow: 'hidden',
    color: '#FFFFFF',
    fontSize: 10,
    marginRight: 4,
    fontWeight: 'bold',
  },
  textNamespace: {
    color: '#b0f566',
    fontSize: 10,
    marginRight: 4,
  },
});
