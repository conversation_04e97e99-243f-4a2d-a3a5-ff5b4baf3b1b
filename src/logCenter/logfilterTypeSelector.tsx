import React from 'react';
import { StyleSheet, Text } from 'react-native';
import SelectDropdown from 'react-native-select-dropdown';
import { EFilterType } from './type';

export type TLogFilterType = {
  filterType: EFilterType;
  onChange: (filterType: EFilterType) => void;
};

const FilterTypeList: EFilterType[] = [
  EFilterType.level,
  EFilterType.namespace,
  EFilterType.content,
];

export const LogFilterTypeSelector = React.memo(({ filterType, onChange }: TLogFilterType) => {
  return (
    <SelectDropdown
      renderDropdownIcon={() => <Text style={{ color: '#000000' }}>V</Text>}
      buttonStyle={styles.selectButton}
      buttonTextStyle={styles.selectButtonText}
      rowStyle={styles.selectButton}
      rowTextStyle={styles.selectButtonText}
      data={FilterTypeList}
      defaultValue={filterType}
      onSelect={onChange}
    />
  );
});

const styles = StyleSheet.create({
  selectButton: {
    borderRadius: 6,
    width: 100,
    padding: 0,
    margin: 0,
    height: 40,
  },
  selectButtonText: {
    fontSize: 12,
    margin: 0,
    padding: 0,
  },
  buttonIcon: {
    width: 16,
    height: 16,
    marginRight: 4,
  },
});
