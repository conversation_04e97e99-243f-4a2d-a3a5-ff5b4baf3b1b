import React, { ReactNode, useContext, useEffect, useState } from 'react';
import { GenericModal } from '@/modals';
import uuid from 'react-native-uuid';

export interface ModalContext<T> {
  openModal: (key: EModalKey) => void;
  closeModal: (id: string) => void;
}

export type ModalItemType = {
  id: string;
  key: EModalKey;
};

export enum EModalKey {
  TEST = 'test',
  COMMON = 'common',
}

// 创建一个上下文，用于管理全局弹窗
const ModalManagerContext = React.createContext<ModalContext<any>>({
  openModal: (key: EModalKey) => {},
  closeModal: (id: string) => {},
});

// 自定义钩子，方便使用 ModalManagerContext
export const useModalManager = () => useContext(ModalManagerContext);

// ModalManagerProvider 组件，管理全局的弹窗状态
export default function ModalManagerProvider({ children }: { children: ReactNode }) {
  const [modals, setModals] = useState<Array<ModalItemType>>([]);

  const openModal = (key: EModalKey) => {
    setModals([
      ...modals,
      {
        id: String(uuid.v4()),
        key,
      },
    ]);
  };

  const closeModal = (id: string) => {
    setModals(modals.filter(item => item.id !== id));
  };

  return (
    <ModalManagerContext.Provider value={{ openModal, closeModal }}>
      {children}
      {modals?.map(modalItem => {
        switch (modalItem.key) {
          case EModalKey.TEST:
            return <GenericModal key={modalItem.id} closeModal={closeModal} id={modalItem.id} />;
        }

        return <></>;
      })}
    </ModalManagerContext.Provider>
  );
}
