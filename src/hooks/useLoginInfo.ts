import { BaseInfoManager } from '@/managers';
import { isInviteCodeInput } from '@/utils';
import { useState } from 'react';

export default () => {
  const [phoneNumber, setPhoneNumber] = useState<string>('');
  const [inviteCode, setInviteCode] = useState<string>('');
  const [password, setPassword] = useState<string>('');
  const [smsCode, setSmsCode] = useState<string>('');
  const [secureTextEntry, setSecureTextEntry] = useState<boolean>(true);
  const [rePassword, setRePassword] = useState<string>('');
  const [reSecureTextEntry, setReSecureTextEntry] = useState<boolean>(true);
  const [readPrivacyNoticeBool, setReadPrivacyNoticeBool] = useState<boolean>(
    BaseInfoManager.context.baseModel.agreePermissions,
  );

  const onSetPhoneNumber = (value: string) => {
    setPhoneNumber(value);
  };

  const onSetInviteCode = (value: string) => {
    if (isInviteCodeInput(value)) {
      setInviteCode(value);
    }
  };

  const onSetSmsCode = (value: string) => {
    setSmsCode(value);
  };

  const onChangeReadPrivacyNoticeBool = (nextChecked: boolean) => {
    setReadPrivacyNoticeBool(nextChecked);
  };

  const onToggleSecureEntry = (): void => {
    setSecureTextEntry(!secureTextEntry);
  };

  const onToggleReSecureEntry = (): void => {
    setReSecureTextEntry(!reSecureTextEntry);
  };

  const onPasswordChange = (_password: string) => {
    setPassword(_password);
  };

  const onRePasswordChange = (_password: string) => {
    setRePassword(_password);
  };

  return {
    phoneNumber,
    setPhoneNumber,
    smsCode,
    setSmsCode,
    inviteCode,
    onSetInviteCode,
    password,
    readPrivacyNoticeBool,
    setReadPrivacyNoticeBool,
    setPassword,
    rePassword,
    setRePassword,
    secureTextEntry,
    setSecureTextEntry,
    reSecureTextEntry,
    setReSecureTextEntry,
    onChangeReadPrivacyNoticeBool,
    onSetPhoneNumber,
    onSetSmsCode,
    onToggleSecureEntry,
    onToggleReSecureEntry,
    onPasswordChange,
    onRePasswordChange,
  };
};
