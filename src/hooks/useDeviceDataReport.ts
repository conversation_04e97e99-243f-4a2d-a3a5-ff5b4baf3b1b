/** 上报采集设备数据 */
import { UserInfoManager } from '@/managers';
import { NativeModules } from '../../src/native/_export_';
const { deviceDataReport } = NativeModules;

export const useDeviceDataReport = () => {
  const {
    launchReportDeviceData,
    inApplyReportDeviceData,
    applyReportDeviceData,
    preApplyReportDeviceData,
    repaymentReportDeviceData,
    calendarReportDeviceData,
  } = deviceDataReport;

  const onLaunchReportDeviceData = async () => {
    await launchReportDeviceData(UserInfoManager.context.userModel.token);
  };

  const onInApplyReportDeviceData = async () => {
    await inApplyReportDeviceData(UserInfoManager.context.userModel.token);
  };

  const onApplyReportDeviceData = async (applyOrderId: string) => {
    await applyReportDeviceData(UserInfoManager.context.userModel.token, applyOrderId);
  };

  const onPreApplyReportDeviceData = async (applyOrderId: string) => {
    await preApplyReportDeviceData(UserInfoManager.context.userModel.token, applyOrderId);
  };

  const onRepaymentReportDeviceData = async () => {
    await repaymentReportDeviceData(
      UserInfoManager.context.userModel.token,
      UserInfoManager.context.userModel.applyOrderId,
    );
  };

  const onCalendarReportDeviceData = async () => {
    await calendarReportDeviceData(
      UserInfoManager.context.userModel.token,
      UserInfoManager.context.userModel.applyOrderId,
    );
  };

  return {
    /** 启动时上报设备数据 */
    onLaunchReportDeviceData,
    /** 开始进件时上报设备数据 */
    onInApplyReportDeviceData,
    /** 创建申请单时上报设备数据 */
    onApplyReportDeviceData,
    /** 创建预申请单上报设备数据 */
    onPreApplyReportDeviceData,
    /** 还款时设备数据上报 */
    onRepaymentReportDeviceData,
    /** 还款页日历数据单独上报 */
    onCalendarReportDeviceData,
  };
};
