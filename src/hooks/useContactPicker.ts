/** 使用 advance.ai 活体识别 */
import { useEffect } from 'react';
import { NativeEventEmitter } from 'react-native';
import { NativeModules as nativeModules } from '../native/_export_';
import { IContactPickerData } from '../native/module/contactPicker';
import { ENativeEventEmitterEventName } from '../native/type';
const { contactPicker } = nativeModules;
const { pickContact } = contactPicker;
/** 使用 face 活体检测  */
export const useContactPicker = (
  listenerContacPickerStateChange: (event: IContactPickerData) => void,
) => {
  useEffect(() => {
    const eventEmitter = new NativeEventEmitter();
    let eventListener = eventEmitter.addListener(
      ENativeEventEmitterEventName.CONTACT_PICKER_EVENT_NAME_SPACE,
      (event: IContactPickerData) => {
        listenerContacPickerStateChange(event);
      },
    );
    return () => {
      eventListener && eventListener.remove();
    };
  }, []);

  return [pickContact];
};
