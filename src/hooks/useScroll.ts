import React from 'react';

export function useScroll({ navigation, initDataCallback }: any) {
  const scrollViewRef = React.useRef(null);

  React.useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      initDataCallback && initDataCallback();
      //@ts-ignore
      if (scrollViewRef.current && scrollViewRef.current?.scrollTo) {
        //@ts-ignore
        scrollViewRef.current?.scrollTo({ y: 0, animated: true });
      }
    });

    return unsubscribe;
  }, [navigation]);

  return { scrollViewRef };
}
