import { BaseEnumsSpace, HitPointEnumsSpace, UserEnumsSpace } from '@/enums';
import {
  useAppicatioStateActiveAndPageFocusHandle,
  useNavigationFocusAndBlurHandle,
  useSubscribeFilter,
} from '@/hooks';
import {
  BaseInfoManager,
  ModalList,
  UserInfoContextType,
  UserInfoManager,
  modalDataStoreInstance,
  AppDefaultConfigManager,
} from '@/managers';
import { RouterConfig } from '@/routes';
import { trackCommonEvent } from '@/trackEvent';
import { Log, TrackEvent, deepLinkAction, hasDeepLinkAction, log, nav } from '@/utils';
import { fetchCouponsForShow, fetchRichTextConfig } from '@/server';
import _ from 'lodash';
import { useCallback, useRef, useState } from 'react';
import { UserVOSpace } from '@/types';
import { NativeModules } from '@/native';
const { intentData } = NativeModules;

interface IProps {
  /** 埋点页面key */
  pageKey?: HitPointEnumsSpace.EPageKey;
  /** 埋点事件key */
  eventKey?: HitPointEnumsSpace.EEventKey;
  /** 页面初始化方法回调 */
  callback?: (refersh?: boolean) => void;
  /** 下拉刷新方法回调 */
  refreshCallback?: () => void;
  /** 定制的埋点方法 */
  buryCallback?: () => void;
  /** 定制的埋点失焦方法 */
  buryBlurCallback?: () => void;
  /** 是否在路由返回当前页面时，自动刷新。从其它的页面返回此页面 */
  isActivityAutoRefresh?: boolean;
  /** 是否路，自动刷新。从其它的页面返回此页面 */
  isBackAutoRefresh?: boolean;
}
export default (props: IProps) => {
  const {
    callback,
    buryCallback,
    buryBlurCallback,
    refreshCallback,
    pageKey = '',
    isActivityAutoRefresh = true,
    isBackAutoRefresh = false,
  } = props;
  const [refreshing, setRefreshing] = useState<boolean>(false);
  const loading = useRef<boolean>(false);

  // 页面初始化状态
  const isInitedRef = useRef(false);

  // 屏幕是否聚焦
  const isPageFocusRef = useRef(false);

  /** 初始化方法 */
  const onInit = async () => {
    if (!loading.current) {
      loading.current = true;

      typeof callback === 'function' && callback();

      loading.current = false;
    }
  };

  /** 下拉刷新方法 */
  const onRefresh = useCallback(
    _.debounce(async () => {
      if (!loading.current) {
        loading.current = true;
        setRefreshing(true);
        if (typeof refreshCallback === 'function') {
          await refreshCallback();
        } else {
          typeof callback === 'function' && (await callback(true));
        }
        loading.current = false;
        setRefreshing(false);
        await doGlobalLogic();
      }
    }, 1000),
    [refreshing, pageKey],
  );

  /** 进入页面埋点 */
  const onBuryEnterPage = () => {
    if (typeof buryCallback === 'function') {
      buryCallback();
    } else {
      if (pageKey) {
        trackCommonEvent(
          {
            p: pageKey,
            e: HitPointEnumsSpace.EEventKey.IN_PAGE,
          },
          '1',
        );
      }
    }
  };

  /** 离开页面埋点 */
  const onBuryLeavePage = () => {
    if (typeof buryBlurCallback === 'function') {
      buryBlurCallback();
    } else {
      if (pageKey) {
        trackCommonEvent(
          {
            p: pageKey,
            e: HitPointEnumsSpace.EEventKey.OUT_PAGE,
          },
          '1',
        );
      }
    }
  };

  /** 屏幕失去焦点时刷新 */
  useNavigationFocusAndBlurHandle(
    /** 页面进入时处理逻辑 */
    async () => {
      await onBuryEnterPage();
      isPageFocusRef.current = true;
      if (isInitedRef.current) {
        // 初始化之后只能执行刷新操作
        isBackAutoRefresh && (await onRefresh());
      } else {
        // 未初始化才能执行初始化操作
        await onInit();
        isInitedRef.current = true;
      }
      await doGlobalLogic();
      if (UserInfoManager.context.userModel.isLogined) {
        AppDefaultConfigManager.updateAppDefaultConfig();
      }
    },
    /** 页面离开时逻辑处理 */
    () => {
      onBuryLeavePage();
      log.info('上传埋点数据');
      TrackEvent.uploadEventLog();
      isPageFocusRef.current = false;
    },
  );

  /** app 活跃非活跃状态逻辑处理 */
  useAppicatioStateActiveAndPageFocusHandle(
    /** app运行到前台时 */
    async () => {
      if (isInitedRef.current && isPageFocusRef.current) {
        // 从后台回到前台刷新一下
        isActivityAutoRefresh && onRefresh();
        onBuryEnterPage();
        const { launchMethod, data } = await intentData.getLaunchMethod();
        Log.info(`#launchMethod resume ${launchMethod} ${data}`);
        hasDeepLinkAction(data) && deepLinkAction(data);
        hasDeepLinkAction(data) && (await intentData.clearIntentData());
      }
    },
    /** app运行到后台时上报数据 */
    async () => {
      if (isInitedRef.current && isPageFocusRef.current) {
        await onBuryLeavePage();
        await TrackEvent.uploadEventLog();
      }
    },
  );

  const phoneNumber = useSubscribeFilter({
    subject: UserInfoManager.messageCenter,
    filter: (subject: UserInfoContextType) => {
      return subject.userModel.mobile;
    },
  });

  /**
   * 执行全局操作
   * 弹窗越后置, 优先级越高
   */
  const doGlobalLogic = async () => {
    if (!UserInfoManager.context.userModel.isLogined) {
      return;
    }

    if (pageKey === HitPointEnumsSpace.EPageKey.P_LAUNCH) {
      return;
    }

    await getCouponShowAsync({
      /** 被邀请用户只在以下几种场景查询优惠券 */
      includeInvite:
        (pageKey === HitPointEnumsSpace.EPageKey.P_HOMEPAGE &&
          UserInfoManager.context.userModel.isUserTypeNew) ||
        pageKey === HitPointEnumsSpace.EPageKey.P_AMOUNT ||
        pageKey === HitPointEnumsSpace.EPageKey.P_REPAY ||
        pageKey === HitPointEnumsSpace.EPageKey.P_CREDIT_TBC,
    }).then(async data => {
      /** 展示优惠券暂存本地存储, 来源于被邀请的优惠券不会即时展示 */

      let couponsFromBeInvited: UserVOSpace.CouponsItem[] = []; // 来自被邀请产生的优惠券
      let couponsFromNotBeInvited: UserVOSpace.CouponsItem[] = []; // 来自非被邀请产生的优惠券

      data.forEach(couponItem => {
        if (
          couponItem.distributeScene === UserEnumsSpace.ECouponsDistributeSceneStatus.BE_INVITED
        ) {
          couponsFromBeInvited.push(couponItem);
        } else {
          couponsFromNotBeInvited.push(couponItem);
        }
      });

      /** 展示不是来自被邀请产生的优惠券, 并从本地缓存清除 */
      if (couponsFromNotBeInvited.length > 0) {
        modalDataStoreInstance.openModal({
          key: ModalList.COUPON_SHOW,
          extra: {
            data: couponsFromNotBeInvited,
          },
          confirmBtnCallback: () => {
            if (pageKey) {
              trackCommonEvent(
                {
                  p: pageKey,
                  e: HitPointEnumsSpace.EEventKey.BTN_CUPON_OBTAIN,
                },
                '1',
              );
            }
          },
        });
      }

      /** 在特定场景, 展示来自被邀请产生的优惠券, 并从本地缓存清除 */
      if (couponsFromBeInvited.length > 0) {
        const { code, data } = await fetchRichTextConfig({
          scene: 'COUPON_MODAL_BE_INVITED',
        });
        if (code === 0) {
          modalDataStoreInstance.openModal({
            key: ModalList.COUPON_SHOW_WHEN_BE_INVITED,
            extra: {
              data: couponsFromBeInvited,
              tips: data,
            },
            confirmBtnCallback: () => {
              if (pageKey) {
                trackCommonEvent(
                  {
                    p: pageKey,
                    e: HitPointEnumsSpace.EEventKey.BTN_CUPON_OBTAIN,
                  },
                  '1',
                );
              }
            },
          });
        }
      }
    });

    /** 仅在首页出现的弹窗 */
    if (pageKey === HitPointEnumsSpace.EPageKey.P_HOMEPAGE) {
      if (
        UserInfoManager.context.userModel.isNotifyModifyMobile &&
        BaseInfoManager.context.baseModel.isOldUserMobileExpiredRemindSwitch
      ) {
        /** 确认电话使用弹窗 */
        modalDataStoreInstance.openModal({
          key: ModalList.COMFIRM_PHONE_NUMBER_USAGE,
          i18nKey: 'modalString.comfirmPhoneNumberUsage',
          isBackdropClose: true,
          confirmBtnName: 'btnString.conrfirm',
          cancelBtnName: 'btnString.modify',
          extra: {
            phoneNumber,
          },
          confirmBtnCallback: async () => {
            trackCommonEvent(
              {
                p: HitPointEnumsSpace.EPageKey.P_HOMEPAGE,
                e: HitPointEnumsSpace.EEventKey.E_BTN_PHONE_EXPIRE_REMIND_CHOOSE,
              },
              '0',
            );
          },
          cancelBtnCallback: () => {
            trackCommonEvent(
              {
                p: HitPointEnumsSpace.EPageKey.P_HOMEPAGE,
                e: HitPointEnumsSpace.EEventKey.E_BTN_PHONE_EXPIRE_REMIND_CHOOSE,
              },
              '1',
            );
            nav.navigate(RouterConfig.VERIFY_PWD as any);
          },
        });
      }
    }
  };

  /** 获取优惠券展示 */
  const getCouponShowAsync = async ({
    isMock = false,
    includeInvite = false,
  }: {
    /** 是否返回mock数据 */
    isMock?: boolean;
    /** 是否查询被邀请用户奖励的优惠券 */
    includeInvite?: boolean;
  }): Promise<UserVOSpace.CouponsItem[]> => {
    let result = await fetchCouponsForShow({
      includeInvite: includeInvite ? 'YES' : 'NO',
    });
    if (result.code === BaseEnumsSpace.ENetworkStatus.SUCCESS && result?.data?.length > 0) {
      return result?.data;
    }

    return [];
  };

  return {
    loading,
    onInit,
    refreshing,
    setRefreshing,
    onRefresh,
  };
};
