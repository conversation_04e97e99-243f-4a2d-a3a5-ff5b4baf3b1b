import { useEffect } from 'react';
/** tab 点击回调
 * 可以在此处做刷新处理，或者跳转到其他的页面
 */

export default function useTabPress() {
  useEffect(() => {
    // const unsubscribe =
    //   // @ts-ignore
    //   navigationRef.current
    //     .getParent(RouterConfig.HOME_TABS)
    //     .addListener('tabPress', (e: any) => {
    //       // Do something
    //       console.log('tabPress', e);
    //     });
  });
}
