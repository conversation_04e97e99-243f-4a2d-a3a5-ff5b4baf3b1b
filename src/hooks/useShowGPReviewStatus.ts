import { BaseInfoManager, modalDataStoreInstance, ModalList } from '@/managers';
import { useEffect } from 'react';
import { submitComment } from '@/server';
import { jumpCurrentAppGooglePlayStore, log } from '@/utils';

interface PropsType {
  screen: 'LOAN' | 'APPLY';
}
/**
 * 打开Google Play评价
 */
export function useShowGPReviewStatus(props: PropsType) {
  const { screen } = props;

  const showGooglePlayReviewOnRepay = () => {
    if (!BaseInfoManager.context.baseModel.getRepayGPReviewStatus) {
      return;
    }
    /** 只弹出一次弹框, 关闭或者退出app都不会在该场景弹出评论弹窗 */
    BaseInfoManager.saveRepayGPReview(false);

    modalDataStoreInstance.openModal({
      key: ModalList.GP_REVIEW,
      confirmBtnCallback: async (score: number, comment: string) => {
        BaseInfoManager.changeLoadingModalVisible(true);
        if (!(await onSubmitComment(String(score), comment))) {
          BaseInfoManager.changeLoadingModalVisible(false);
          return;
        }

        BaseInfoManager.changeLoadingModalVisible(false);
        if (score >= 4) {
          BaseInfoManager.saveRepayGPReview(true);
          jumpCurrentAppGooglePlayStore();
        } else {
          BaseInfoManager.saveRepayGPReview(false);
          jumpCurrentAppGooglePlayStore();
          setTimeout(() => {
            modalDataStoreInstance.openModal({
              key: ModalList.INFO_PROMPT_CONFIRM,
              confirmBtnName: 'btnString.OK',
              i18nKey: 'loanConfirmString.gp_review_feedback_tip',
              confirmBtnCallback: () => {},
              cancelBtnCallback: () => {},
            });
          }, 600);
        }
      },
    });
  };

  const showGooglePlayReviewOnOrder = () => {
    if (!BaseInfoManager.context.baseModel.getOrderReviewGPReviewStatus) {
      return;
    }
    /** 只弹出一次弹框, 关闭或者退出app都不会在该场景弹出评论弹窗 */
    BaseInfoManager.saveOrderReviewGPReview(false);

    modalDataStoreInstance.openModal({
      key: ModalList.GP_REVIEW,
      confirmBtnCallback: async (score: number, comment: string) => {
        BaseInfoManager.changeLoadingModalVisible(true);
        if (!(await onSubmitComment(String(score), comment))) {
          BaseInfoManager.changeLoadingModalVisible(false);
          return;
        }

        BaseInfoManager.changeLoadingModalVisible(false);
        if (score >= 4) {
          BaseInfoManager.saveOrderReviewGPReview(true);
          jumpCurrentAppGooglePlayStore();
        } else {
          BaseInfoManager.saveOrderReviewGPReview(false);
          jumpCurrentAppGooglePlayStore();
          setTimeout(() => {
            modalDataStoreInstance.openModal({
              key: ModalList.INFO_PROMPT_CONFIRM,
              confirmBtnName: 'btnString.OK',
              i18nKey: 'loanConfirmString.gp_review_feedback_tip',
              confirmBtnCallback: () => {},
              cancelBtnCallback: () => {},
            });
          }, 600);
        }
      },
    });
  };

  /** 提交评分 */
  const onSubmitComment = async (score: string, comment: string) => {
    let result = await submitComment({
      score,
      comment,
      commentStage: screen,
    });

    if (result.code !== 0) {
      return false;
    }

    return result.code === 0;
  };

  return {
    /** 用信等待 */
    showGooglePlayReviewOnRepay,
    /** 放款成功 */
    showGooglePlayReviewOnOrder,
  };
}
