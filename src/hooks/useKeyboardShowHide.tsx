import { useEffect } from 'react';
import { Keyboard } from 'react-native';

export const useKeyboardShowHideTabBar = (showCallback?: () => void, hideCallback?: () => void) => {
  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', () => {
      showCallback && showCallback();
    });
    const keyboardDidHideListener = Keyboard.addListener('keyboardDidHide', () => {
      hideCallback && hideCallback();
    });

    return () => {
      keyboardDidShowListener.remove();
      keyboardDidHideListener.remove();
    };
  }, []);
};
