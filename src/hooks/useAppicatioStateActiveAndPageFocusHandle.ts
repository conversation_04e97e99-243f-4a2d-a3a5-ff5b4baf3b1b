import { MessageDataStoreInstance } from '@/managers';
import { ApplicationState, applicationState } from '@/utils';
import { useIsFocused } from '@react-navigation/core';
import { useEffect, useRef } from 'react';
/** app 活跃状态和非活跃状态的处理, 在屏幕聚焦的情况下才会执行 的hook */
export function useAppicatioStateActiveAndPageFocusHandle(
  /** 活跃状态回调，可以用于页面的刷新 */
  activeCallback = () => {},
  /** 非活跃状态, 可以用于数据的上报 */
  inActiveCallback = () => {},
) {
  const isFocusedRef = useRef<boolean>(true);
  const isFocused = useIsFocused();
  useEffect(() => {
    isFocusedRef.current = isFocused;
  }, [isFocused]);

  useEffect(() => {
    const subscribe = applicationState.instance.subject.subscribe(state => {
      if (state === ApplicationState.active && isFocusedRef.current) {
        // // 更新未读消息的数量
        MessageDataStoreInstance.updateMessageUnReadState();
        activeCallback && activeCallback();
      } else {
        // 从活跃状态变成非活跃状态
        inActiveCallback && inActiveCallback();
      }
    });
    return () => {
      subscribe.unsubscribe();
    };
  }, []);
}
