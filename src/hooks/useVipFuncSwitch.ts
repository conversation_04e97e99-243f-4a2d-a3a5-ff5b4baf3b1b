import { BaseInfoContextType, BaseInfoManager } from '@/managers';
import { useSubscribeFilter } from './useSubscribeFilter';

type TBaseInfoSubject = {
  isVipFuncSwitch: boolean;
};

export const useVipFuncSwitch = () => {
  const { isVipFuncSwitch } = useSubscribeFilter({
    subject: BaseInfoManager.messageCenter,
    filter: (subject: BaseInfoContextType) => {
      return {
        isVipFuncSwitch: subject.baseModel.isVipFuncSwitch,
      };
    },
    defaultData: {
      isVipFuncSwitch: false,
    },
  }) as TBaseInfoSubject;
  return isVipFuncSwitch;
};
