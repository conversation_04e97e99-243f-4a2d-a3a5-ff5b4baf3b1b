import { useState, useCallback } from 'react';

/**
 * @description curp证件相关数据
 */
export default function useCurp() {
  const [curpFrontImg, setCurpFrontImg] = useState<string>(Object.create(null));
  const [curpBackImg, setCurpBackImg] = useState<string>(Object.create(null));
  const [curpType, setCurpType] = useState<string>(Object.create(null));
  const [curpSubType, setCurpSubType] = useState<string>(Object.create(null));
  const [curpNumber, setCurpNumber] = useState<string>(Object.create(null));
  const [curpName, setCurpName] = useState<string>(Object.create(null));
  const [curpFatherSurname, setCurpFatherSurname] = useState<string>(Object.create(null));
  const [curpMotherSurname, setCurpMotherSurname] = useState<string>(Object.create(null));
  const [curpGender, setCurpGender] = useState<string>(Object.create(null));
  const [curpBirthDate, setCurpBirthDate] = useState<string>(Object.create(null));
  const [curpStreet, setCurpStreet] = useState<string>(Object.create(null));
  const [curpDistrict, setCurpDistrict] = useState<string>(Object.create(null));
  const [curpCity, setCurpCity] = useState<string>(Object.create(null));
  const [curpFolio, setCurpFolio] = useState<string>(Object.create(null));
  const [curpClaveelector, setCurpClaveelector] = useState<string>(Object.create(null));
  const [curpRegistro, setCurpRegistro] = useState<string>(Object.create(null));
  const [curpEdad, setCurpEdad] = useState<string>(Object.create(null));
  const [curpEstado, setCurpEstado] = useState<string>(Object.create(null));
  const [curpMunicipio, setCurpMunicipio] = useState<string>(Object.create(null));
  const [curpLocalidad, setCurpLocalidad] = useState<string>(Object.create(null));
  const [curpSeccion, setCurpSeccion] = useState<string>(Object.create(null));
  const [curpEmision, setCurpEmision] = useState<string>(Object.create(null));
  const [curpVigencia, setCurpVigencia] = useState<string>(Object.create(null));
  const [applyOrderId, setApplyOrderId] = useState<string>(Object.create(null));

  const onChangeCurpFrontImg = useCallback((curpFrontImg: string) => {
    setCurpFrontImg(curpFrontImg);
  }, []);

  const onChangeCurpBackImg = useCallback((curpBackImg: string) => {
    setCurpBackImg(curpBackImg);
  }, []);

  const onChangeCurpType = useCallback((curpType: string) => {
    setCurpType(curpType);
  }, []);

  const onChangeCurpSubType = useCallback((curpSubType: string) => {
    setCurpSubType(curpSubType);
  }, []);

  const onChangeCurpNumber = useCallback((curpNumber: string) => {
    setCurpNumber(curpNumber);
  }, []);

  const onChangeCurpName = useCallback((curpName: string) => {
    setCurpName(curpName);
  }, []);

  const onChangeCurpFatherSurname = useCallback((curpFatherSurname: string) => {
    setCurpFatherSurname(curpFatherSurname);
  }, []);

  const onChangeCurpMotherSurname = useCallback((curpMotherSurname: string) => {
    setCurpMotherSurname(curpMotherSurname);
  }, []);

  const onChangeCurpGender = useCallback((curpGender: string) => {
    setCurpGender(curpGender);
  }, []);

  const onChangeCurpBirthDate = useCallback((curpBirthDate: string) => {
    setCurpBirthDate(curpBirthDate);
  }, []);

  const onChangeCurpStreet = useCallback((curpStreet: string) => {
    setCurpStreet(curpStreet);
  }, []);

  const onChangeCurpDistrict = useCallback((curpDistrict: string) => {
    setCurpDistrict(curpDistrict);
  }, []);

  const onChangeCurpCity = useCallback((curpCity: string) => {
    setCurpCity(curpCity);
  }, []);

  const onChangeCurpFolio = useCallback((curpFolio: string) => {
    setCurpFolio(curpFolio);
  }, []);

  const onChangeCurpClaveelector = useCallback((curpClaveelector: string) => {
    setCurpClaveelector(curpClaveelector);
  }, []);

  const onChangeCurpRegistro = useCallback((curpRegistro: string) => {
    setCurpRegistro(curpRegistro);
  }, []);

  const onChangeCurpEdad = useCallback((curpEdad: string) => {
    setCurpEdad(curpEdad);
  }, []);

  const onChangeCurpEstado = useCallback((curpEstado: string) => {
    setCurpEstado(curpEstado);
  }, []);

  const onChangeCurpMunicipio = useCallback((curpMunicipio: string) => {
    setCurpMunicipio(curpMunicipio);
  }, []);

  const onChangeCurpLocalidad = useCallback((curpLocalidad: string) => {
    setCurpLocalidad(curpLocalidad);
  }, []);

  const onChangeCurpSeccion = useCallback((curpSeccion: string) => {
    setCurpSeccion(curpSeccion);
  }, []);

  const onChangeCurpEmision = useCallback((curpEmision: string) => {
    setCurpEmision(curpEmision);
  }, []);

  const onChangeCurpVigencia = useCallback((curpVigencia: string) => {
    setCurpVigencia(curpVigencia);
  }, []);

  const onChangeApplyOrderId = useCallback((applyOrderId: string) => {
    setApplyOrderId(applyOrderId);
  }, []);

  return {
    curpFrontImg,
    onChangeCurpFrontImg,
    curpBackImg,
    onChangeCurpBackImg,
    curpType,
    onChangeCurpType,
    curpSubType,
    onChangeCurpSubType,
    curpNumber,
    onChangeCurpNumber,
    curpName,
    onChangeCurpName,
    curpFatherSurname,
    onChangeCurpFatherSurname,
    curpMotherSurname,
    onChangeCurpMotherSurname,
    curpGender,
    onChangeCurpGender,
    curpBirthDate,
    onChangeCurpBirthDate,
    curpStreet,
    onChangeCurpStreet,
    curpDistrict,
    onChangeCurpDistrict,
    curpCity,
    onChangeCurpCity,
    curpFolio,
    onChangeCurpFolio,
    curpClaveelector,
    onChangeCurpClaveelector,
    curpRegistro,
    onChangeCurpRegistro,
    curpEdad,
    onChangeCurpEdad,
    curpEstado,
    onChangeCurpEstado,
    curpMunicipio,
    onChangeCurpMunicipio,
    curpLocalidad,
    onChangeCurpLocalidad,
    curpSeccion,
    onChangeCurpSeccion,
    curpEmision,
    onChangeCurpEmision,
    curpVigencia,
    onChangeCurpVigencia,
    applyOrderId,
    onChangeApplyOrderId,
  };
}
