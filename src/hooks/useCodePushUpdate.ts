// import { HitPointEnumsSpace } from '@/enums';
// import { CodePushManager } from '@/managers';
// import { log, TrackEvent } from '@/utils';
// import CodePush from 'react-native-code-push';
// import SplashScreen from 'react-native-splash-screen';

// /** 热更新 */
// export function useCodePushUpdate() {
//   const CodePushUpdate = (callback: () => void) => {
//     CodePush.sync(
//       {
//         // 下次恢复
//         installMode: CodePush.InstallMode.ON_NEXT_RESTART,
//         // 下次重启
//         mandatoryInstallMode: CodePush.InstallMode.ON_NEXT_RESTART,
//         updateDialog: undefined,
//       },
//       status => {
//         switch (status) {
//           case CodePush.SyncStatus.CHECKING_FOR_UPDATE:
//             log.info('#CodePush > Checking for update.');
//             break;
//           case CodePush.SyncStatus.AWAITING_USER_ACTION:
//             log.info('#CodePush > Awaiting user action.');
//             break;
//           case CodePush.SyncStatus.DOWNLOADING_PACKAGE:
//             // 开始热更新
//             log.info('#CodePush > Downloading package.');
//             break;
//           case CodePush.SyncStatus.INSTALLING_UPDATE:
//             log.info('#CodePush > Installing update.');
//             break;
//           case CodePush.SyncStatus.UP_TO_DATE:
//             // 已经更新
//             log.info('#CodePush > App is up to date.');
//             callback && callback();
//             break;
//           case CodePush.SyncStatus.UPDATE_IGNORED:
//             // 用户忽略更新
//             log.info('#CodePush > User cancelled the update.');
//             callback && callback();
//             break;
//           case CodePush.SyncStatus.UPDATE_INSTALLED:
//             // 更新完成, 重启后更新
//             log.info('#CodePush > Update installed.');
//             callback && callback();
//             break;
//           case CodePush.SyncStatus.UNKNOWN_ERROR:
//             // 异常状态兜底
//             log.info('#CodePush > An unknown error occurred.');
//             callback && callback();
//             break;
//         }
//       },
//       ({ receivedBytes, totalBytes }) => {
//         // const progress = receivedBytes / totalBytes;
//         // // Update download progress UI, e.g., update a progress bar
//         // let total = (totalBytes / 1024 / 1024).toFixed(2);
//         // CodePushManager.updateTotalSizes(total);
//         // CodePushManager.updateProcess(Math.round(progress * 100));
//       },
//     );
//   };

//   const CodePushUpdateCheck = async (isForce: boolean, callback?: () => void) => {
//     let updateState: "checking" | "ckecked" | "downloading" | "downloaded" | "install" | "installed" = "checking"

//     try {
//       log.info('# hot update checking local', { isForce })
//       const localData = await CodePush.getUpdateMetadata()
//       log.info('# hot update checked local', { isForce, localData })

//       if (localData) {
//         TrackEvent.trackCommonEvent(
//           {
//             p: HitPointEnumsSpace.EPageKey.P_APP,
//             e: HitPointEnumsSpace.EEventKey.HOT_PATCH,
//           },
//           localData?.appVersion + "_" + localData?.label + "_" + localData?.description + "_" + localData?.packageHash + "_" + localData?.packageSize,
//           localData?.failedInstall ? "0" : "1"
//         );
//       }
//     } catch (e) {
//       log.error(e)
//     }

//     try {

//       // todo 检查更新
//       log.info('# hot update checking remote', { isForce })
//       const updateData = await CodePush.checkForUpdate()
//       log.info('# hot update checked remote', { isForce, updateData })
//       if (updateData !== null) {
//         updateState = "ckecked"
//         TrackEvent.trackCommonEvent(
//           {
//             p: HitPointEnumsSpace.EPageKey.P_APP,
//             e: HitPointEnumsSpace.EEventKey.HOT_PATCH,
//           },
//           updateData?.appVersion + "_" + updateData?.label + "_" + updateData?.description + "_" + updateData?.packageHash + "_" + updateData?.packageSize,
//           "0"
//         );
//         if (updateData.failedInstall || updateData.isFirstRun || updateData.isPending) {
//           // 坏的更新 第一次运行。更新已经安装待使用
//           // 直接下一步
//           callback && callback()
//           return
//         }
//         if (!updateData.isMandatory || !isForce) {
//           // 非强制更新。直接下载安装应用 不阻挡主流程
//           callback && callback()
//           const downloadData = await updateData.download()
//           await downloadData.install(CodePush.InstallMode.ON_NEXT_RESTART)
//           return
//         } else {
//           updateState = "downloading"
//           const downloadData = await updateData.download(({ receivedBytes, totalBytes }) => {
//             const progress = receivedBytes / totalBytes;
//             // Update download progress UI, e.g., update a progress bar
//             CodePushManager.openModal()
//             let total = (totalBytes / 1024 / 1024).toFixed(2);
//             CodePushManager.updateTotalSizes(total);
//             CodePushManager.updateProcess(progress);
//           })
//           updateState = "downloaded"
//           if (downloadData) {
//             updateState = "install"
//             await downloadData.install(CodePush.InstallMode.ON_NEXT_RESTART)
//             updateState = "installed"
//           }
//         }
//       } else {
//         callback && callback()
//       }
//     } catch (e) {
//       log.error(e)
//     }
//     if (updateState === "checking" || updateState === 'ckecked') {
//       callback && callback()
//     } else {
//       // todo 倒计时3秒自动重启
//       let countdown = 3
//       CodePushManager.updateCountdown(countdown)
//       let timer = setInterval(() => {
//         if (CodePushManager.context.codePushState.countdown === 1) {
//           CodePushManager.closeModal()
//           clearInterval(timer)
//           SplashScreen.show();
//           CodePush.restartApp()
//         }
//         countdown--
//         CodePushManager.updateCountdown(countdown)
//       }, 1000)
//     }
//   }

//   return {
//     CodePushUpdate,
//     CodePushUpdateCheck
//   };
// }
