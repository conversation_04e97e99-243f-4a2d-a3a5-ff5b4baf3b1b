import { MessageDataStoreInstance } from '@/managers';
import { useNavigation } from '@react-navigation/native';
import React from 'react';

/** 屏幕聚焦和事件的回调处理 hook */
export function useNavigationFocusAndBlurHandle(
  /** 屏幕聚焦回调 */
  focusCallback: () => void = () => {},
  /** 屏幕失焦回调 */
  blurCallback: () => void = () => {},
) {
  const navigation = useNavigation();
  React.useEffect(() => {
    const focusUnsubscribe = navigation.addListener('focus', () => {
      // 更新未读消息的数量
      MessageDataStoreInstance.updateMessageUnReadState();
      focusCallback && focusCallback();
    });

    const blurUnsubscribe = navigation.addListener('blur', () => {
      blurCallback && blurCallback();
    });

    return () => {
      navigation.removeListener('focus', focusUnsubscribe);
      navigation.removeListener('blur', blurUnsubscribe);
    };
  }, []);
}
