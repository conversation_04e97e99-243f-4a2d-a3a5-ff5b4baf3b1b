/** 权限 hooks */
import { HitPointEnumsSpace } from '@/enums';
import { ModalList, modalDataStoreInstance } from '@/managers';
import {
  EPermissionStatus,
  TrackEvent,
  isHasAllPermission,
  isHasCalendarPermission,
  isHasCameraPermission,
  isHasLaunchPermission,
  isHasPushNotification,
  jumpAppSettingActivity,
  nav,
  requestAllPermission,
  requestCalendarPermission,
  requestCameraPermission,
  requestLaunchPermission,
  requestPermission,
  requestPushNotifitionPermission,
} from '@/utils';
import { useCallback } from 'react';
import { PermissionsAndroid } from 'react-native';
import { CloudMessage } from '../business/_export_';
import { RouterConfig } from '@/routes';

/** 确认并且申请相机权限 */
export const useCheckCameraPermissionAndRequestPermission = () => {
  const checkPermission = useCallback(async () => {
    // 先确认是否包含所有权限
    const checkResult = await isHasCameraPermission();

    // 如果已经包含所有权限就返回 true
    if (checkResult) {
      return true;
    }

    // 如果没有获取所有权限，则去申请所有权限。
    const { grantedPermissionList, deniedPermissionList, neverAskPermissionList } =
      await requestCameraPermission();

    if (grantedPermissionList.length !== 0) {
      trackEvent(grantedPermissionList, HitPointEnumsSpace.EPermissionStatus.AGREE);
      await TrackEvent.uploadEventLog();
    }

    if (neverAskPermissionList.length !== 0) {
      trackEvent(neverAskPermissionList, HitPointEnumsSpace.EPermissionStatus.NOT_ASK);
      await TrackEvent.uploadEventLog();
    }

    if (deniedPermissionList.length !== 0) {
      trackEvent(deniedPermissionList, HitPointEnumsSpace.EPermissionStatus.REJECT);
      await TrackEvent.uploadEventLog();
    }

    // 如果有不在提示的权限状态，弹窗提示，点击确认跳转到设置页面
    if (neverAskPermissionList.length !== 0) {
      // 弹窗提示跳转到设置页面
      modalDataStoreInstance.openModal({
        key: ModalList.INFO_PROMPT_CONFIRM,
        titleKey: 'permissionAgreeString.peso_per_apply_again_title',
        i18nKey: 'permissionAgreeString.mach_per_req_all',
        isTopIconColse: true,
        titleStatus: 'primary',
        confirmBtnCallback: () => {
          jumpAppSettingActivity();
        },
        confirmBtnName: 'btnString.toSetting',
      });
      // 如果存在拒绝状态的权限，弹窗提示，并且重新申请权限
    } else if (deniedPermissionList.length !== 0) {
      // 弹窗提示重新申请权限
      modalDataStoreInstance.openModal({
        key: ModalList.INFO_PROMPT_CONFIRM,
        titleKey: 'permissionAgreeString.peso_per_apply_again_title',
        i18nKey: 'permissionAgreeString.mach_per_req_all',
        isTopIconColse: true,
        titleStatus: 'primary',
        confirmBtnCallback: () => {
          checkPermission();
        },
        confirmBtnName: 'btnString.toSetting',
      });
    }
    return await isHasCameraPermission();
  }, []);
  return [checkPermission];
};

/** 确认并且申请通知权限 */
export const useCheckPushNotificationPermissionAndRequestPermission = () => {
  const checkPermission = useCallback(async () => {
    // 先确认是否包含所有权限
    const checkResult = await isHasPushNotification();

    if (checkResult) {
      CloudMessage.getToken();
      return true;
    }
    // 如果没有获取所有权限，则去申请所有权限。
    const { status, grantedPermissionList, deniedPermissionList, neverAskPermissionList } =
      await requestPushNotifitionPermission();

    if (grantedPermissionList.length !== 0) {
      trackEvent(grantedPermissionList, HitPointEnumsSpace.EPermissionStatus.AGREE);
      await TrackEvent.uploadEventLog();
    }

    if (neverAskPermissionList.length !== 0) {
      trackEvent(neverAskPermissionList, HitPointEnumsSpace.EPermissionStatus.NOT_ASK);
      await TrackEvent.uploadEventLog();
    }

    if (deniedPermissionList.length !== 0) {
      trackEvent(deniedPermissionList, HitPointEnumsSpace.EPermissionStatus.REJECT);
      await TrackEvent.uploadEventLog();
    }

    // 如果有不在提示的权限状态，弹窗提示，点击确认跳转到设置页面
    if (neverAskPermissionList.length !== 0) {
      modalDataStoreInstance.openModal({
        key: ModalList.INFO_PROMPT_CONFIRM,
        titleKey: 'permissionAgreeString.peso_per_apply_again_title',
        i18nKey: 'permissionAgreeString.peso_per_req_notify',
        isTopIconColse: true,
        titleStatus: 'primary',
        confirmBtnCallback: () => {
          jumpAppSettingActivity();
        },
        confirmBtnName: 'btnString.toSetting',
      });
      // 如果存在拒绝状态的权限，弹窗提示，并且重新申请权限
    } else if (deniedPermissionList.length !== 0) {
      modalDataStoreInstance.openModal({
        key: ModalList.INFO_PROMPT_CONFIRM,
        i18nKey: 'permissionAgreeString.peso_per_req_notify',
        titleKey: 'permissionAgreeString.peso_per_apply_again_title',
        isTopIconColse: true,
        titleStatus: 'primary',
        confirmBtnCallback: () => {
          checkPermission();
        },
        confirmBtnName: 'btnString.toSetting',
      });
    }
    return await isHasPushNotification();
  }, []);
  return [checkPermission];
};

/** 确认并且申请启动权限 */
export const useCheckLaunchPermissionAndRequestPermission = () => {
  const checkPermission = useCallback(async () => {
    // 先确认是否包含所有权限
    const checkResult = await isHasLaunchPermission();

    // 如果已经包含所有权限就返回 true
    if (checkResult) {
      return true;
    }

    // 如果没有获取所有权限，则去申请所有权限。
    const { status, grantedPermissionList, deniedPermissionList, neverAskPermissionList } =
      await requestLaunchPermission();

    if (grantedPermissionList.length !== 0) {
      trackEvent(grantedPermissionList, HitPointEnumsSpace.EPermissionStatus.AGREE);
      await TrackEvent.uploadEventLog();
    }

    if (neverAskPermissionList.length !== 0) {
      trackEvent(neverAskPermissionList, HitPointEnumsSpace.EPermissionStatus.NOT_ASK);
      await TrackEvent.uploadEventLog();
    }

    if (deniedPermissionList.length !== 0) {
      trackEvent(deniedPermissionList, HitPointEnumsSpace.EPermissionStatus.REJECT);
      await TrackEvent.uploadEventLog();
    }

    // 如果有不在提示的权限状态，弹窗提示，点击确认跳转到设置页面
    if (neverAskPermissionList.length !== 0) {
      modalDataStoreInstance.openModal({
        key: ModalList.INFO_PROMPT_CONFIRM,
        titleKey: 'permissionAgreeString.peso_per_apply_again_title',
        i18nKey: 'permissionAgreeString.peso_per_req_launch',
        isTopIconColse: true,
        titleStatus: 'primary',
        confirmBtnCallback: () => {
          jumpAppSettingActivity();
        },
        confirmBtnName: 'btnString.toSetting',
      });
      // 如果存在拒绝状态的权限，弹窗提示，并且重新申请权限
    } else if (deniedPermissionList.length !== 0) {
      modalDataStoreInstance.openModal({
        key: ModalList.INFO_PROMPT_CONFIRM,
        titleKey: 'permissionAgreeString.peso_per_apply_again_title',
        i18nKey: 'permissionAgreeString.peso_per_req_launch',
        isTopIconColse: true,
        titleStatus: 'primary',
        confirmBtnCallback: () => {
          checkPermission();
        },
        confirmBtnName: 'btnString.toSetting',
      });
    }
    return await isHasLaunchPermission();
  }, []);
  return [checkPermission];
};

/** 确认并且申请所有权限 */
export const useCheckAllPermissionAndRequestPermission = () => {
  const checkPermission = useCallback(async () => {
    // 先确认是否包含所有权限
    const checkResult = await isHasAllPermission();

    if (checkResult) {
      return 'agree';
    }

    // 如果没有获取所有权限，则去申请所有权限。
    const { status, grantedPermissionList, deniedPermissionList, neverAskPermissionList } =
      await requestAllPermission();

    if (grantedPermissionList.length !== 0) {
      trackEvent(grantedPermissionList, HitPointEnumsSpace.EPermissionStatus.AGREE);
      await TrackEvent.uploadEventLog();
    }

    if (neverAskPermissionList.length !== 0) {
      trackEvent(neverAskPermissionList, HitPointEnumsSpace.EPermissionStatus.NOT_ASK);
      await TrackEvent.uploadEventLog();
    }

    if (deniedPermissionList.length !== 0) {
      trackEvent(deniedPermissionList, HitPointEnumsSpace.EPermissionStatus.REJECT);
      await TrackEvent.uploadEventLog();
    }

    // 如果有不在提示的权限状态，弹窗提示，点击确认跳转到设置页面
    if (deniedPermissionList.length !== 0) {
      // modalDataStoreInstance.openModal({
      //   key: ModalList.INFO_PROMPT_CONFIRM,
      //   i18nKey: 'permissionAgreeString.mach_per_req_all',
      //   titleKey: 'permissionAgreeString.peso_per_apply_again_title',
      //   isTopIconColse: true,
      //   titleStatus: 'primary',
      //   confirmBtnCallback: () => {
      //     checkPermission();
      //   },
      //   confirmBtnName: 'btnString.toSetting',
      // });
      return 'denied';
    } else if (neverAskPermissionList.length !== 0) {
      // todo 跳转到权限开启提醒页
      // modalDataStoreInstance.openModal({
      //   key: ModalList.INFO_PROMPT_CONFIRM,
      //   i18nKey: 'permissionAgreeString.mach_per_req_all',
      //   titleKey: 'permissionAgreeString.peso_per_apply_again_title',
      //   isTopIconColse: true,
      //   titleStatus: 'primary',
      //   confirmBtnCallback: () => {
      //     jumpAppSettingActivity();
      //   },
      //   confirmBtnName: 'btnString.toSetting',
      // });
      // 如果存在拒绝状态的权限，弹窗提示，并且重新申请权限
      return 'neverAsk';
    } else {
      return 'agree';
    }
  }, []);
  return [checkPermission];
};

/** 确认并且申请日历权限 */
export const useCheckCalendarPermissionAndRequestPermission = () => {
  const checkPermission = useCallback(async () => {
    // 先确认是否包含所有权限
    const checkResult = await isHasCalendarPermission();

    if (checkResult) {
      return true;
    }

    // 如果没有获取所有权限，则去申请所有权限。
    const { status, grantedPermissionList, deniedPermissionList, neverAskPermissionList } =
      await requestCalendarPermission();

    if (grantedPermissionList.length !== 0) {
      trackEvent(grantedPermissionList, HitPointEnumsSpace.EPermissionStatus.AGREE);
      await TrackEvent.uploadEventLog();
    }

    if (neverAskPermissionList.length !== 0) {
      trackEvent(neverAskPermissionList, HitPointEnumsSpace.EPermissionStatus.NOT_ASK);
      await TrackEvent.uploadEventLog();
    }

    if (deniedPermissionList.length !== 0) {
      trackEvent(deniedPermissionList, HitPointEnumsSpace.EPermissionStatus.REJECT);
      await TrackEvent.uploadEventLog();
    }

    // 如果有不在提示的权限状态，弹窗提示，点击确认跳转到设置页面
    if (neverAskPermissionList.length !== 0) {
      modalDataStoreInstance.openModal({
        key: ModalList.INFO_PROMPT_CONFIRM,
        i18nKey: 'permissionAgreeString.peso_per_req_calendar',
        titleKey: 'permissionAgreeString.peso_per_apply_again_title',
        isTopIconColse: true,
        titleStatus: 'primary',
        confirmBtnCallback: () => {
          jumpAppSettingActivity();
        },
        confirmBtnName: 'btnString.toSetting',
      });
      // 如果存在拒绝状态的权限，弹窗提示，并且重新申请权限
    } else if (deniedPermissionList.length !== 0) {
      modalDataStoreInstance.openModal({
        key: ModalList.INFO_PROMPT_CONFIRM,
        titleKey: 'permissionAgreeString.peso_per_apply_again_title',
        i18nKey: 'permissionAgreeString.peso_per_req_calendar',
        isTopIconColse: true,
        titleStatus: 'primary',
        confirmBtnCallback: () => {
          checkPermission();
        },
        confirmBtnName: 'btnString.toSetting',
      });
    }
    return await isHasCalendarPermission();
  }, []);
  return [checkPermission];
};

/** 上报通知权限埋点 */
export const trackPushNotifitionPermssion = async (
  p: HitPointEnumsSpace.EPageKey = HitPointEnumsSpace.EPageKey.P_LAUNCH,
) => {
  switch (await requestPermission.POST_NOTIFICATIONS()) {
    case EPermissionStatus.GRANTED:
      TrackEvent.trackCommonEvent(
        {
          p,
          e: HitPointEnumsSpace.EEventKey.BTN_POST_NOTIFICTIONS,
        },
        HitPointEnumsSpace.EPermissionStatus.AGREE,
      );
      await TrackEvent.uploadEventLog();
      break;

    case EPermissionStatus.DENIED:
      TrackEvent.trackCommonEvent(
        {
          p,
          e: HitPointEnumsSpace.EEventKey.BTN_POST_NOTIFICTIONS,
        },
        HitPointEnumsSpace.EPermissionStatus.REJECT,
      );
      await TrackEvent.uploadEventLog();
      break;
    case EPermissionStatus.NEVER_ASK_AGAIN:
      TrackEvent.trackCommonEvent(
        {
          p,
          e: HitPointEnumsSpace.EEventKey.BTN_POST_NOTIFICTIONS,
        },
        HitPointEnumsSpace.EPermissionStatus.NOT_ASK,
      );
      await TrackEvent.uploadEventLog();
      break;
  }
};

/** 上报权限埋点 */
export const trackEvent = async (list: String[], type: HitPointEnumsSpace.EPermissionStatus) => {
  list.forEach(permission => {
    switch (permission) {
      // case PermissionsAndroid.PERMISSIONS.READ_PHONE_STATE:
      //   TrackEvent.trackCommonEvent({
      //     p: HitPointEnumsSpace.EPageKey.P_PERMISSION_OBTAIN,
      //     e: HitPointEnumsSpace.EEventKey.BTN_READ_PHONE_STATE
      //   }, type);
      //   break;
      case PermissionsAndroid.PERMISSIONS.CAMERA:
        TrackEvent.trackCommonEvent(
          {
            p: HitPointEnumsSpace.EPageKey.P_PERMISSION_OBTAIN,
            e: HitPointEnumsSpace.EEventKey.BTN_CAMERA,
          },
          type,
        );
        break;
      case PermissionsAndroid.PERMISSIONS.ACCESS_COARSE_LOCATION:
        TrackEvent.trackCommonEvent(
          {
            p: HitPointEnumsSpace.EPageKey.P_PERMISSION_OBTAIN,
            e: HitPointEnumsSpace.EEventKey.BTN_ACCESS_COARSE_LOCATION,
          },
          type,
        );
        break;
      case PermissionsAndroid.PERMISSIONS.READ_SMS:
        TrackEvent.trackCommonEvent(
          {
            p: HitPointEnumsSpace.EPageKey.P_PERMISSION_OBTAIN,
            e: HitPointEnumsSpace.EEventKey.BTN_READ_SMS,
          },
          type,
        );
        break;
      case PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS:
        TrackEvent.trackCommonEvent(
          {
            p: HitPointEnumsSpace.EPageKey.P_PERMISSION_OBTAIN,
            e: HitPointEnumsSpace.EEventKey.BTN_POST_NOTIFICTIONS,
          },
          type,
        );
        break;
      case PermissionsAndroid.PERMISSIONS.READ_CALENDAR:
        TrackEvent.trackCommonEvent(
          {
            p: HitPointEnumsSpace.EPageKey.P_PERMISSION_OBTAIN,
            e: HitPointEnumsSpace.EEventKey.BTN_READ_CALENDAR,
          },
          type,
        );
        break;
      case PermissionsAndroid.PERMISSIONS.WRITE_CALENDAR:
        TrackEvent.trackCommonEvent(
          {
            p: HitPointEnumsSpace.EPageKey.P_PERMISSION_OBTAIN,
            e: HitPointEnumsSpace.EEventKey.BTN_WRITE_CALENDAR,
          },
          type,
        );
        break;
    }
  });
};
