/** 使用 face++ 活体识别 */
import { useEffect } from 'react';
import { NativeEventEmitter } from 'react-native';
import { NativeModules as nativeModules } from '../native/_export_';
import { EFaceLiveEventType, IFaceEventData } from '../native/module/faceLive';
import { ENativeEventEmitterEventName } from '../native/type';
import { IEventPoint, trackInputEventEnd, trackInputEventStart } from '../trackEvent';
const { faceLive } = nativeModules;
const { startFaceLiveVerification } = faceLive;
/** 使用 face 活体检测  */
export const useFaceLive = (
  eventPoint: IEventPoint,
  listenerFaceLiveStateChange: (event: IFaceEventData) => void,
) => {
  useEffect(() => {
    const eventEmitter = new NativeEventEmitter();
    let eventListener = eventEmitter.addListener(
      ENativeEventEmitterEventName.FACE_LIVE_EVENT_NAME_SPACE,
      (event: IFaceEventData) => {
        switch (event.eventType) {
          case EFaceLiveEventType.FACE_LIVE_PRE_START:
            break;
          case EFaceLiveEventType.FACE_LIVE_PRE_DETECT:
            break;
          case EFaceLiveEventType.FACE_LIVE_PRE_DETECT_STATE_FAIL:
            break;
          case EFaceLiveEventType.FACE_LIVE_PRE_FINISH:
            break;
          case EFaceLiveEventType.FACE_LIVE_PRE_FINISH_STATE_FAIL:
            break;
          case EFaceLiveEventType.FACE_LIVE_START_DETECT:
            // 活体准备工作结束，开始前端活体检测 在这里记录活体开始的埋点事件记录
            trackInputEventStart(eventPoint);
            break;
          case EFaceLiveEventType.FACE_LIVE_START_DETECT_STATE_SUCCESS:
            // 前端活体检测成功，保存成功的埋点数据
            trackInputEventEnd(eventPoint, '1');
            break;
          case EFaceLiveEventType.FACE_LIVE_START_DETECT_STATE_FAIL:
            // 前端活体检测失败，保存失败的埋点数据
            trackInputEventEnd(eventPoint, '0');
            break;
        }
        listenerFaceLiveStateChange(event);
      },
    );
    return () => {
      eventListener && eventListener.remove();
    };
  }, []);

  return [startFaceLiveVerification];
};
