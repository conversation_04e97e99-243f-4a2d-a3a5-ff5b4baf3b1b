import { ApplicationState, applicationState } from '@/utils';
import { useEffect } from 'react';
/** app 活跃状态和非活跃状态的处理hook */
export function useApplicationStateChangeHandle(
  /** 活跃状态回调，可以用于页面的刷新 */
  activeCallback = () => {},
  /** 非活跃状态, 可以用于数据的上报 */
  inActiveCallback = () => {},
) {
  useEffect(() => {
    const subscribe = applicationState.instance.subject.subscribe(state => {
      if (state === ApplicationState.active) {
        // 从非活跃状态变换成活跃状态
        activeCallback && activeCallback();
      } else {
        // 从活跃状态变成非活跃状态
        inActiveCallback && inActiveCallback();
      }
    });
    return () => {
      subscribe.unsubscribe();
    };
  }, []);

  return [];
}
