/** 使用 advance.ai 活体识别 */
import { useEffect } from 'react';
import { NativeEventEmitter } from 'react-native';
import { NativeModules as nativeModules } from '../native/_export_';
import { IGmailPickerData } from '../native/module/gmailPicker';
import { ENativeEventEmitterEventName } from '../native/type';
const { gmailPicker } = nativeModules;
const { pickGmail } = gmailPicker;

export const useGmailPicker = (
  listenerGmailPickerStateChange: (event: IGmailPickerData) => void,
) => {
  useEffect(() => {
    const eventEmitter = new NativeEventEmitter();
    let eventListener = eventEmitter.addListener(
      ENativeEventEmitterEventName.GMAIL_PICKER_EVENT_NAME_SPACE,
      (event: IGmailPickerData) => {
        listenerGmailPickerStateChange(event);
      },
    );
    return () => {
      eventListener && eventListener.remove();
    };
  }, []);

  return [pickGmail];
};
