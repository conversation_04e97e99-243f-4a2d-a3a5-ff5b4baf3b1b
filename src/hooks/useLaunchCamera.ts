import { useCheckCameraPermissionAndRequestPermission } from '@/hooks';
import { log } from '@/utils';
import { useState } from 'react';
import { ErrorCode, ImagePickerResponse, launchCamera } from 'react-native-image-picker';

interface ConfigProps {
  /** 媒体类型 */
  mediaType?: 'photo' | 'video' | 'mixed';
  /**最大宽度, 和压缩比例成负相关关系 */
  maxWidth?: number;
  /**最大高度, 和压缩比例成负相关关系 */
  maxHeight?: number;
  /** 质量 */
  quality?: 0 | 0.1 | 0.2 | 0.3 | 0.4 | 0.5 | 0.6 | 0.7 | 0.8 | 0.9 | 1;
  /** 相机前置或者后置 */
  cameraType: 'back' | 'front';
}

enum EErrorCode {
  /** 相机不可用 */
  CAMERA_UNAVAILABLE = 'camera_unavailable',
  /** 权限被取消 */
  PERMISSION = 'permission',
  /** 其他情况 */
  OTHER = 'others',
}

/** 使用相机 */
export const useLaunchCamera = (configProps: ConfigProps) => {
  const {
    mediaType = 'photo',
    maxWidth = 1000,
    maxHeight = 1000,
    quality = 0.8,
    cameraType,
  } = configProps;

  const [media, setMedia] = useState<{
    uri: string;
    type: string;
    name: string;
  }>({
    uri: '',
    type: '',
    name: '',
  });

  const [checkCameraPermission] = useCheckCameraPermissionAndRequestPermission();

  /** 开始拍照 */
  const onLaunchCamera = async (): Promise<
    | {
        uri: string;
        type: string;
        name: string;
      }
    | Boolean
  > => {
    return new Promise(async resolve => {
      if (!(await checkCameraPermission())) {
        resolve(false);
      }
      launchCamera(
        {
          mediaType,
          maxWidth, // 设置选择照片的大小，设置小的话会相应的进行压缩
          maxHeight,
          quality,
          cameraType,
        },
        async (res: ImagePickerResponse) => {
          /** 错误优先处理 */
          switch (res?.errorCode as ErrorCode) {
            case EErrorCode.CAMERA_UNAVAILABLE:
              log.error('#onLaunchCamera > CAMERA_UNAVAILABLE');
              resolve(false);
              break;
            case EErrorCode.PERMISSION:
              log.error('#onLaunchCamera > PERMISSION');
              await checkCameraPermission();
              resolve(false);
              break;
            case EErrorCode.OTHER:
              log.error('#onLaunchCamera > OTHER');
              resolve(false);
              break;
          }

          /** 交互状态处理, 拍照完后取消进行重拍 */
          if (res?.didCancel) {
            return false;
          }

          /** 不存在assets */
          if (res?.assets === undefined || res?.assets[0] === undefined) {
            resolve(false);
          } else {
            /** 成功场景, 返回媒体信息 */
            resolve({
              uri: res?.assets[0]?.uri || '',
              type: res?.assets[0]?.type?.split('/')[1] || '',
              name: res?.assets[0]?.fileName || '',
            });
          }
        },
      );
    });
  };

  return {
    media,
    onLaunchCamera,
  };
};
