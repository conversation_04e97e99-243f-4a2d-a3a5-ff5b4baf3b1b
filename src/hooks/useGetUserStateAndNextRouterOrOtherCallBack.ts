/** 获取用户的状态，默认进行路由跳转，也可以自定义回掉处理 */

import { UserInfoManager } from '@/managers';
import { Log, nav } from '@/utils';
import { useCallback } from 'react';

/**
 *
 * @param def 是否采用默认的行为尝试路由跳转
 * @param callback （可选）获取userState之后的回调函数
 */
export const useGetUserStateAndNextRouterOrOtherCallBack = (curPageName: string = '') => {
  const getUserStateAndNextRouterOrOtherCallBack = useCallback(
    async (def: boolean = true, callback?: (result: boolean) => void) => {
      // 此方法会返回更新的状态，如果是状态为 flase 表示更新失败，不执行默认操作
      const result = await UserInfoManager.updateUserState();
      def && result && nav.nextToTopRouter(curPageName);
      callback && (await callback(result));
    },
    [],
  );

  return [getUserStateAndNextRouterOrOtherCallBack];
};
