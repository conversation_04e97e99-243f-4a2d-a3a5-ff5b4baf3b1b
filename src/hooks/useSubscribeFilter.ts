import { useEffect, useRef, useState } from 'react';
import { Subject } from 'rxjs';
import subscribe from '../utils/subscribe';

export function useSubscribeFilter<T, X>(_config: {
  subject: Subject<T>;
  filter: (subject: T) => X;
  /**
   * 订阅源默认值，针对 config.subject 类型不是 BehaviorSubject 的场景
   */
  defaultData?: X;
  /**
   * 订阅最新数据变更，用于特殊逻辑处理
   * @param data {prev: X; next: X}
   * @returns
   */
  subscribe?: (data: { prev: X | undefined; next: X | undefined }) => void;
}): X | undefined {
  const config = useRef(_config).current;

  const defaultData: T = (config.subject as any).value
    ? (config.subject as any).value
    : config.defaultData;

  const initialSubStateValue = useRef(defaultData ? config.filter(defaultData) : undefined);
  const [subState, setSubState] = useState<X | undefined>(initialSubStateValue.current);

  useEffect(() => {
    const sub = subscribe
      .generateSubForSpecificChange(config)
      .subscribe(([prev, next]: [X | undefined, X]) => {
        if (config.subscribe) {
          config.subscribe({ prev, next });
        }
        setSubState(next);
      });

    return () => {
      sub && sub.unsubscribe();
    };
  }, [config]);

  return subState;
}
