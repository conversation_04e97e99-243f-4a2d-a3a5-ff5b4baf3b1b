import { ENativeEventEmitterEventName } from '../native/type';
import _ from 'lodash';
import { useEffect } from 'react';
import { EmitterSubscription, DeviceEventEmitter, NativeModules } from 'react-native';

const SmsRetriever = NativeModules.SmsRetriever;
export function useSmsCodeHandler(callback?: (smsCode: string) => void) {
  useEffect(() => {
    let smsListener: undefined | EmitterSubscription;
    SmsRetriever.getAppSignature().then((data: any) => {
      const { signature } = data;
      console.log('SMS_TEST', 'signature', JSON.stringify(signature));
    });
    async function innerAsync() {
      // set Up SMS Listener;
      smsListener = DeviceEventEmitter.addListener(
        ENativeEventEmitterEventName.SMS_CODE_RECEIVE_NAME_SPACE,
        (data: { message?: string }) => {
          console.log('SMS_EVENT SMS value', data);
          handleSmsRetriever(data);
        },
      );

      try {
        const isRegistered = await SmsRetriever.startSmsRetriever();
        console.log('SMS_TEST', 'isRegistered', isRegistered);
      } catch (e: any) {}
    }
    innerAsync();

    return () => {
      if (smsListener) {
        smsListener.remove();
      }
    };
  }, []);

  // 处理验证码短信，解析后回填验证码
  const handleSmsRetriever = (smsData: { message?: string }) => {
    if (smsData?.message) {
      const words = smsData.message.split(' ');
      const smsCode = words.find((word: string) => !_.isNaN(word) && word.length === 4);
      smsCode && callback?.(smsCode);
    }
  };
}
