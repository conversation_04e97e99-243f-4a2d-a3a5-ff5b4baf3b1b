import defaultTheme from './custom-theme';
import defaultThemForDark from './custom-theme-dark';
import defaultThemForLight from './custom-theme-light';

type ICustomTheme = {
  defaultThemForDark: Record<string, string>;
  defaultThemForLight: Record<string, string>;
  defaultTheme: Record<string, string>;
};

const customTheme: ICustomTheme = {
  defaultThemForDark,
  defaultThemForLight,
  defaultTheme,
};
// 类型工具
type Replace<
  S extends string,
  From extends string,
  To extends string,
> = S extends `${infer L}${From}${infer R}` ? `${L}${To}${Replace<R, From, To>}` : S;

type ThemeColorValues = keyof typeof defaultThemForLight;
type ThemeColorKeys = Uppercase<Replace<ThemeColorValues, '-', '_'>>;
type ThemeColors = Record<ThemeColorKeys, ThemeColorValues>;
const createColorKeys = () => {
  const colorKeys = {} as ThemeColors;
  (Object.keys(defaultThemForLight) as ThemeColorValues[]).forEach(key => {
    colorKeys[key.replace(/-/g, '_').toUpperCase() as ThemeColorKeys] = key;
  });
  return colorKeys;
};
export const Colors: ThemeColors = createColorKeys();
export default customTheme;
