export interface QueryResult {
  status?: 0 | 1;
  insertId?: number;
  rowsAffected: number;
  message?: string;
  rows?: {
    /** Raw array with all dataset */
    _array: ISqlEventInfo[];
    /** The lengh of the dataset */
    length: number;
    /** A convenience function to acess the index based the row object
     * @param idx the row index
     * @returns the row structure identified by column names
     */
    item: (idx: number) => any;
  };
}

export interface ISqlEventInfo {
  /** page */
  p: string;
  /** event */
  e: string;
  /** content */
  c: string;
  /** type */
  t: string;
  /** start time */
  st: number;
  /** end time */
  et: number;
  /** version code */
  vc: number;
  /** apply order id */
  aoi: string;
}

export interface IDBConnection {
  executeSql: (
    sql: string,
    args: any[],
    ok: (res: QueryResult) => void,
    fail: (msg: string) => void,
  ) => void;
  close: (ok: (res: any) => void, fail: (msg: string) => void) => void;
}
