import * as SqlString from 'sqlstring';
import log from '../utils/log';
import { executeSQL } from './eventDB';
import { ISqlEventInfo, QueryResult } from './type';

export interface QueryEventParams {
  limit?: number;
  offset?: number;
  type?: 'count' | 'data';
  desc?: boolean;
}

async function queryEventLog(params: QueryEventParams): Promise<QueryResult | undefined> {
  const perfix = 'queryEvent';

  const { limit, offset, type, desc } = getQueryEventLogParamsWithDefaultValue(params);
  try {
    let sql: string | undefined;
    let sqlString: string | undefined;
    switch (type) {
      case 'count':
        sqlString = `SELECT COUNT(*) as count FROM "eventContent";`;
        // console.log('sqlString  count ===', sqlString);
        sql = SqlString.format(sqlString, []);
        break;
      case 'data':
        sqlString = `SELECT "p", "e", "c", "t", "st", "et", "vc", "aoi" FROM eventContent ORDER BY id ${
          desc ? 'DESC' : 'ASC'
        } LIMIT ? OFFSET ?;`;
        sql = SqlString.format(sqlString, [limit, offset]);
        break;
      default:
        break;
    }
    if (!sql) {
      log.error('queryEvent error: wrong type');
      return undefined;
    }
    const queryRes = await executeSQL(sql);
    // 仅缓存数据查询，忽略数量查询
    if (queryRes && type === 'data') {
      setQueryCache(perfix, params, queryRes);
    }
    return queryRes;
  } catch (e) {
    log.error('queryEvent error', e);
  }
  return undefined;
}
/**
 * 删除所有 event 记录
 */
async function deleteEventLog(len = 10000) {
  try {
    __DEV__ && console.log(`# deleteEventLog length ${len}`);
    const sql = SqlString.format(`DELETE FROM "eventContent"
      WHERE id IN (
      SELECT id
      FROM "eventContent"
      ORDER BY id ASC
      LIMIT ${len}
    )`);
    const queryRes = await executeSQL(sql);
    return queryRes;
  } catch (err) {
    log.error('deleteEventLog error', err);
  }
  return undefined;
}
/**
 * 自动填充参数默认值，供多个函数使用
 * @param params QueryEventLogParams
 * @returns
 */
function getQueryEventLogParamsWithDefaultValue(params: QueryEventParams): QueryEventParams {
  const { limit = 300, offset = 0, type = 'data', desc = false } = params;
  return {
    limit,
    offset,
    type,
    desc,
  };
}

/**
 * 查询缓存逻辑
 * 默认缓存最近的 ？ 条数据，避免反复数据库查询
 */

const __cache__ = {
  queryKeyArray: [] as Array<string>,
  queryKeyArrayMaxLength: 120,
  queryKeyValue: {} as { [key: string]: QueryResult },
};

/**
 * 获取缓存 Key
 * @param perfix 缓存前缀，用于区分执行了什么查询操作
 * @param params
 * @returns
 */
function getKeyForQueryParams(perfix: string, params: QueryEventParams) {
  const { limit, offset, type, desc } = getQueryEventLogParamsWithDefaultValue(params);
  return `${perfix}|${limit}|${offset}|${type}|${desc}`;
}

/**
 * 尝试获取缓存
 * @param perfix 缓存前缀，用于区分执行了什么查询操作
 * @param params
 */
function getQueryCache(perfix: string, params: QueryEventParams): QueryResult | undefined {
  const key = getKeyForQueryParams(perfix, params);
  return __cache__.queryKeyValue[key];
}

function setQueryCache(perfix: string, params: QueryEventParams, value: QueryResult) {
  if (getQueryCache(perfix, params)) {
    return;
  }

  const key = getKeyForQueryParams(perfix, params);
  __cache__.queryKeyValue[key] = value;
  __cache__.queryKeyArray.push(key);
  if (__cache__.queryKeyArray.length > __cache__.queryKeyArrayMaxLength) {
    const keyForRemove = __cache__.queryKeyArray.shift();
    if (keyForRemove) {
      delete __cache__.queryKeyValue[keyForRemove];
    }
  }
}

function clearCache() {
  __cache__.queryKeyArray = [];
  __cache__.queryKeyValue = {};
}

async function recordEventLog(eventInfo: ISqlEventInfo) {
  const { p, e, c, t = '', st, et, vc, aoi } = eventInfo;
  const sql = SqlString.format(
    'INSERT INTO eventContent (p, e, c, t, st, et, vc, aoi) VALUES (?, ?, ?, ?, ?, ?, ?, ?);',
    [p, e, c, t, st, et, vc, aoi],
  );
  try {
    await executeSQL(sql);
  } catch (e) {
    log.error('executeSQL error', e);
  }
}

export default {
  query: queryEventLog,
  record: recordEventLog,
  delete: deleteEventLog,
  clearCache,
};
