/** 事件埋点 */

import { HitPointEnumsSpace } from '@/enums';
import { KVManager, UserInfoManager } from '@/managers';
import { EventPointRequestSpace, fetchUserEventPointUpload } from '@/server';
import { BaseConfig } from '@/baseConfig';
import log from '../utils/log';
import EventLog, { QueryEventParams } from './eventDBOperation';
import { ISqlEventInfo } from './type';

/** 启用事件埋点的参数传递 */
export interface IEventPoint {
  p: HitPointEnumsSpace.EPageKey;
  e: HitPointEnumsSpace.EEventKey;
}

type TEventCache = Record<string, ISqlEventInfo | undefined>;

let _eventCache_: TEventCache = {};

const getEventCacheKey = (e: IEventPoint) => `${e.p}_${e.e}`;

/** 获取事件埋点数据 */
const getEventCacheData = (e: IEventPoint) => {
  return _eventCache_[getEventCacheKey(e)];
};
/** 设置事件埋点数据 */
const setEventCacheData = (e: IEventPoint, eventData: ISqlEventInfo) => {
  const eD = _eventCache_[getEventCacheKey(e)];
  if (eD) {
    _eventCache_[getEventCacheKey(e)] = {
      ...eD,
      ...eventData,
      vc: BaseConfig.appVersionCode,
      aoi: UserInfoManager.context.userModel.applyOrderId,
    };
  } else {
    _eventCache_[getEventCacheKey(e)] = eventData;
  }
};

/** 清理事件埋点数据 */
const clearEventCacheData = (e: IEventPoint) => {
  const eD = _eventCache_[getEventCacheKey(e)];
  if (eD) {
    _eventCache_[getEventCacheKey(e)] = undefined;
  }
};

/** 记录输入开始事件 */
export const trackInputEventStart = async (e: IEventPoint) => {
  try {
    const eventData = {
      ...e,
      c: '',
      t: '',
      st: Date.now(),
      et: 0,
      vc: BaseConfig.appVersionCode,
      aoi: UserInfoManager.context.userModel.applyOrderId,
    };
    log.info('# trackInputEventStart', eventData);
    // 缓存事件数据
    setEventCacheData(e, eventData);
  } catch (err) {
    log.error('# trackInputEventStart', {
      ...e,
      err,
    });
  }
};
/** 记录输入结束事件 */
export const trackInputEventEnd = async (e: IEventPoint, c: string, t: string = '') => {
  try {
    const cacheEventData = getEventCacheData(e);
    if (cacheEventData) {
      const eventData = {
        ...cacheEventData,
        t,
        c,
        et: Date.now(),
      };
      log.info('# trackInputEventEnd', eventData);
      await EventLog.record(eventData);
    }
    // 缓存事件数据
    clearEventCacheData(e);
  } catch (err) {
    log.error('# trackInputEventEnd', {
      ...e,
      c,
      t,
      err,
    });
  }
};

/** 记录单一事件日志 */
export const trackCommonEvent = async (e: IEventPoint, c: string, t: string = '') => {
  try {
    const nowTime = Date.now();
    const eventData = {
      ...e,
      c,
      t,
      st: nowTime,
      et: nowTime,
      vc: BaseConfig.appVersionCode,
      aoi: UserInfoManager.context.userModel.applyOrderId,
    };
    log.info('# trackCommonEvent', { eventData });

    await EventLog.record(eventData);
  } catch (err) {
    log.error('# trackCommonEvent', {
      ...e,
      c,
      t,
      err,
    });
  }
};

/** 埋点事件上传缓存 key */
const EVENT_POINT_UPLOAD_CACHE_KEY = 'eventPointUploadCachekey';

/** @description 更新日志缓存 */
const updateEventPointUploadCache = async (debug: boolean = true) => {
  // 获取日志上传缓存 mmkv 缓存
  const upLoadCacheData = KVManager.action.getMap(EVENT_POINT_UPLOAD_CACHE_KEY);
  debug && log.debug('# updateEventPointUploadCache upLoadCacheData', upLoadCacheData);
  if (!upLoadCacheData || (upLoadCacheData && Object.keys(upLoadCacheData).length === 0)) {
    // 在把日志数据查出来
    const params: QueryEventParams = {
      limit: 1000,
      offset: 0,
      type: 'data',
      desc: false,
    };
    const eventPointDataList = (await EventLog.query(params))?.rows?._array || [];
    // 如果有日志数据 生成一个新的上传数据包，写入缓存，然后清空数据库。
    debug && log.debug('# updateEventPointUploadCache eventPointDataList', eventPointDataList);
    // 查询出来的事件列表
    const eventListLen = eventPointDataList?.length || 0;
    if (eventListLen !== 0) {
      const requestData: EventPointRequestSpace.RequestEventPointType = {
        ut: Date.now(),
        events: eventPointDataList,
      };
      // 缓存请求事件埋点
      KVManager.action.setMap(EVENT_POINT_UPLOAD_CACHE_KEY, requestData);
      // 清空
      await EventLog.delete(eventListLen);
    }
  }
};

/** @description 上传日志缓存 */
const uploadEventPointUploadCache = async (debug: boolean = false) => {
  // 获取日志上传缓存 mmkv 缓存
  const upLoadCacheData = KVManager.action.getMap(EVENT_POINT_UPLOAD_CACHE_KEY);
  if (upLoadCacheData && Object.keys(upLoadCacheData).length !== 0) {
    // todo 如果有读取出来然后上传。
    const resp = await fetchUserEventPointUpload(
      upLoadCacheData as EventPointRequestSpace.RequestEventPointType,
    );
    if (resp.code === 0) {
      KVManager.action.setMap(EVENT_POINT_UPLOAD_CACHE_KEY, {});
    }
  }
};

const _uploadCache_ = {
  isUpload: false,
  num: 0,
};

export const runTask = async () => {
  // 检查缓存并上传，上传成功就清理掉缓存
  await uploadEventPointUploadCache();
  // 查询数据库，增加缓存，清理数据库
  await updateEventPointUploadCache();
  // 检查缓存并上传，上传成功就清理掉缓存
  await uploadEventPointUploadCache();
  _uploadCache_.num--;
};

const runTasks = async () => {
  while (_uploadCache_.num > 0) {
    await runTask();
  }
  _uploadCache_.isUpload = false;
};

/** 上传埋点记录 */
export const uploadEventLog = async () => {
  if (_uploadCache_.isUpload) {
    _uploadCache_.num++;
  } else {
    _uploadCache_.num++;
    _uploadCache_.isUpload = true;
    await runTasks();
  }
};
