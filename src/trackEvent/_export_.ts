import type { IEventPoint } from './index';
import type { QueryResult, ISqlEventInfo, IDBConnection } from './type';
import {
  trackInputEventStart,
  trackInputEventEnd,
  trackCommonEvent,
  uploadEventLog,
} from './index';

export {
  QueryResult,
  ISqlEventInfo,
  IDBConnection,
  /** 启用事件埋点的参数传递 */
  IEventPoint,
  /** 记录输入开始事件 */
  trackInputEventStart,
  /** 记录输入结束事件 */
  trackInputEventEnd,
  /** 记录单一事件日志 */
  trackCommonEvent,
  /** 上传埋点记录 */
  uploadEventLog,
};
