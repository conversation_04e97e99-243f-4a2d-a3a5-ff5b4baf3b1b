import RNFS from 'react-native-fs';
import { typeORMDriver } from 'react-native-quick-sqlite';
import log from '../utils/log';
import type { IDBConnection, QueryResult } from './type';

let db: IDBConnection | undefined;

const DATA_BASE_NAME: string = 'eventCenter.db';

export async function getDB(): Promise<IDBConnection | undefined> {
  await setupDatabase();
  return db;
}

export async function executeSQL(sql: string): Promise<QueryResult | undefined> {
  if (!(await getDB())) {
    log.debug('# executeSQL error, db is not found');
    return undefined;
  }
  return new Promise(async (resolve, reject) => {
    db?.executeSql(sql, [], resolve, reject);
  });
}

export async function closeDB() {
  if (!db) {
    return;
  }
  return new Promise((resolve, reject) => {
    db?.close(resolve, reject);
  });
}

async function openDB(filePathForDatabase: string) {
  return new Promise((resolve, reject) => {
    typeORMDriver.openDatabase(
      { name: DATA_BASE_NAME, location: filePathForDatabase },
      _db => {
        __DEV__ &&
          console.log(
            '#open event center database success',
            RNFS.DocumentDirectoryPath + '/' + filePathForDatabase,
          );
        db = _db;
        resolve(db);
      },
      error => {
        console.error('#open db error', error);
        reject(error);
      },
    );
  });
}

async function initDB() {
  if (!db) {
    return;
  }

  return new Promise((resolve, reject) => {
    db?.executeSql(
      `
        CREATE TABLE IF NOT EXISTS "eventContent" (
            "id" INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL,
            "p" TEXT(32) NOT NULL,
            "e" TEXT(32) NOT NULL,
            "c" TEXT(512) NOT NULL,
            "t" TEXT(32) NOT NULL,
            "st" INTEGER(128) NOT NULL,
            "et" INTEGER(128) NOT NULL,
            "vc" INTEGER(128) NOT NULL,
            "aoi" TEXT(512) NOT NULL
        );
      `,
      [],
      resolve,
      reject,
    );
  });
}

export async function setupDatabase() {
  if (db) {
    return;
  }
  const filePathForDatabase = 'eventCenterOne.sqlite';
  try {
    await openDB(filePathForDatabase);
    await initDB();
  } catch (error) {
    // TODO: 处理 database 初始化失败情况
    log.error('### event DB 初始化失败', error);
  }
}
