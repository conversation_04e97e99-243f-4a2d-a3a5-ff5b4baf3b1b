import { Divider, Layout, Text, TopNavigation, View } from '@/components';
import { HitPointEnumsSpace } from '@/enums';
import { useOnInit } from '@/hooks';
import { EvidenceVOSpace, ScreenProps } from '@/types';
import { nav } from '@/utils';
import { useCallback, useEffect, useState, useTransition } from 'react';
import AtoZList from 'react-native-atoz-list';

export default ({
  route,
}: ScreenProps<{
  bankList: EvidenceVOSpace.BankItemDataType[];
}>) => {
  const [data, setData] = useState<Record<string, string[]>>(
    Object.fromEntries('ABCDEFGHIJKLMNOPQRSTUVWXYZ'.split('').map(c => [c, []])),
  );
  const [isPending, startTransition] = useTransition();

  /** 初始化方法 */
  useOnInit({
    callback: async () => {
      startTransition(() => {
        const options = route.params.options || [];

        let result: { [key: string]: string[] } = Object.fromEntries(
          'ABCDEFGHIJKLMNOPQRSTUVWXYZ'.split('').map(c => [c, []]),
        );

        options.forEach((option: string) => {
          (result as any)[`${option.charAt(0)}`].push(option);
        });
        // 移除result中空数组的 key
        Object.keys(result).forEach(key => {
          if (result[key].length === 0) {
            delete result[key];
          }
        });
        setData(result);
      });
    },
    pageKey: HitPointEnumsSpace.EPageKey.P_CITY_SELECT_LIST,
  });

  const onPress = useCallback(
    (item: string) => {
      route.params?.onPress(item);
      nav.navigationGoBack();
    },
    [route],
  );

  const onCancel = useCallback(() => {
    route.params?.onCancel();
    nav.navigationGoBack();
  }, [route]);

  const renderCellComponent = useCallback(
    (item: string) => {
      return (
        <View
          style={{
            backgroundColor: 'background-color-100',
          }}>
          <View
            style={{
              backgroundColor: 'background-color-0',
            }}
            margin="0 16 0 16">
            <Text padding="8 8 8 8" onPress={() => onPress(item)} textContent={item} />
            <Divider margin="0 8 0 8" />
          </View>
        </View>
      );
    },
    [onPress],
  );

  const renderSectionComponent = useCallback((section: { sectionId: string }) => {
    return (
      <Text
        padding="8 16 8 16"
        style={{ backgroundColor: 'background-color-100' }}
        textContent={section.sectionId}
        bold={'bold'}
      />
    );
  }, []);

  useEffect(() => {
    console.log('citydata', JSON.stringify(data));
  }, [data]);

  if (isPending) {
    return null;
  }

  return (
    <Layout pLevel="0" level="1">
      <TopNavigation goBack={onCancel} />
      <View style={{ flex: 1, backgroundColor: 'background-color-100' }}>
        <AtoZList
          data={data}
          ItemSeparatorComponent={() => <Divider />}
          renderCell={renderCellComponent}
          renderSection={renderSectionComponent}
          sectionHeaderHeight={20}
          cellHeight={60}
        />
      </View>
    </Layout>
  );
};
