import { BaseInputRefType } from '@/components';
import { isStringExist, validateCurp } from '@/utils';
import { IState } from './type';
import { useCallback, useState, useRef } from 'react';
import { HitPointEnumsSpace } from '@/enums';
import { BaseInfoManager } from '@/managers';
import { t } from 'i18next';

export default function useAction() {
  const fatherNameRef = useRef<BaseInputRefType>(null);
  const motherNameRef = useRef<BaseInputRefType>(null);
  const nameRef = useRef<BaseInputRefType>(null);
  const birthdateRef = useRef<BaseInputRefType>(null);
  const curpRef = useRef<BaseInputRefType>(null);
  const validityeRef = useRef<BaseInputRefType>(null);
  const [state, setState] = useState<IState>({
    status: '',
    annul: undefined,
    curp: '',
    validity: '',
    name: '',
    fatherName: '',
    motherName: '',
    birthDate: '',
    frontFile: '',
    backFile: '',
    frontOssFile: '',
    backOssFile: '',
    cardInfo: {
      /** 成功 失败 */
      status: '',
      /** YES 手动 NO 自动 */
      annul: '',
      /** 父姓 */
      fatherName: '',
      /** 身份证正面 */
      frontFile: '',
      /** 身份证反面 */
      backFile: '',
      /** 用户姓名 */
      name: '',
      /** 母姓 */
      motherName: '',
      /** 有效期 */
      validity: '',
      /** 生日 */
      birthDate: '',
      /** 银行卡号 */
      curp: '',
    },
  });

  /** 检查表单填写状态, 进行一定的UX反馈 */
  const checkCanableSubmit = () => {
    let flag = true;

    const { curp, validity, name, fatherName, motherName, birthDate } = state;

    if (!(isStringExist(fatherName) || isStringExist(motherName))) {
      if (!isStringExist(fatherName)) {
        fatherNameRef.current?.emitErrorStatus(t('verificationString.required'));
      }
      if (!isStringExist(motherName)) {
        motherNameRef.current?.emitErrorStatus(t('verificationString.required'));
      }
      flag = false;
    }

    if (!isStringExist(name)) {
      nameRef.current?.emitErrorStatus(t('verificationString.required'));
      flag = false;
    }

    if (!isStringExist(curp)) {
      curpRef.current?.emitErrorStatus(t('verificationString.required'));
      flag = false;
    } else if (!validateCurp(curp)) {
      curpRef.current?.emitErrorStatus(
        t('verificationString.failed', { name: t('ocrString.curp') }),
      );
      flag = false;
    }

    return flag;
  };

  const onChangeCurp = useCallback((curp: IState['curp']) => {
    setState((preState: IState): IState => {
      return {
        ...preState,
        curp: String(curp).trim(),
      };
    });
  }, []);

  const onChangeValidity = useCallback((validity: IState['validity']) => {
    setState((preState: IState): IState => {
      return {
        ...preState,
        validity,
      };
    });
  }, []);

  const onChangeName = useCallback((name: IState['name']) => {
    setState((preState: IState): IState => {
      return {
        ...preState,
        name,
      };
    });
  }, []);

  const onChangeFatherName = useCallback((fatherName: IState['fatherName']) => {
    setState((preState: IState): IState => {
      return {
        ...preState,
        fatherName: String(fatherName).trim(),
      };
    });
  }, []);

  const onChangeMotherName = useCallback((motherName: IState['motherName']) => {
    setState((preState: IState): IState => {
      return {
        ...preState,
        motherName: String(motherName).trim(),
      };
    });
  }, []);

  const onChangeBirthDate = useCallback((birthDate: IState['birthDate']) => {
    setState((preState: IState): IState => {
      return {
        ...preState,
        birthDate,
      };
    });
  }, []);

  return {
    state,
    setState,
    onChangeCurp,
    onChangeValidity,
    onChangeName,
    onChangeFatherName,
    onChangeMotherName,
    onChangeBirthDate,
    checkCanableSubmit,
    fatherNameRef,
    motherNameRef,
    nameRef,
    birthdateRef,
    curpRef,
    validityeRef,
  };
}
