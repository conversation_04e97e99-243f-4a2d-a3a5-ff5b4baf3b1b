import { EvidenceRequestSpace } from '@/server';
import { EvidenceVOSpace, OrderVOSpace } from '@/types';

export type IState = {
  status: string;
  annul?: string;
  curp: string;
  validity: string;
  name: string;
  fatherName: string;
  motherName: string;
  birthDate: string;
  frontFile: string;
  backFile: string;
  frontOssFile: string;
  backOssFile: string;
  cardInfo: OrderVOSpace.ImageOcrDataV7Type;
};

export type ICardInfo = EvidenceRequestSpace.RequestEvidenceType;
