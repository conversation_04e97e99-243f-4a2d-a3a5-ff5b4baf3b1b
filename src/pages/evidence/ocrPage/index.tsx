/* eslint-disable react-native/no-inline-styles */
/* eslint-disable react/react-in-jsx-scope */

import {
  Button,
  DatePickerInput,
  Divider,
  Image,
  Layout,
  PrefixInput,
  ProcessNav,
  SeaInput,
  Text,
  TopNavigation,
  View,
} from '@/components';
import { HitPointEnumsSpace } from '@/enums';
import { UserInfoManager } from '@/managers';
import { ScreenProps } from '@/types';
import { isStringExist, validateCurp } from '@/utils';
import React, { ReactElement, memo, useMemo } from 'react';
import { Pressable, ScrollView } from 'react-native';
import { default as OcrCard } from './components/uploadOcr';
import useData from './useData';
import { Strings } from '@/i18n';
import { t } from 'i18next';
import ProgressView from '../../components/ProgressView';
import { convert2Progress } from '../utils/dataUtils';
import CurpDesModal from './components/curpDesModal';
import { Colors } from '@/themes';
import { EProcessStatus } from '../constants/enums';

export default ({ navigation }: ScreenProps<{}>): ReactElement => {
  const {
    fatherNameRef,
    motherNameRef,
    nameRef,
    birthdateRef,
    curpRef,
    validityeRef,
    curp,
    status,
    annul,
    validity,
    name,
    fatherName,
    motherName,
    birthDate,
    frontFile,
    backFile,
    onChangeCurp,
    onChangeValidity,
    onChangeName,
    onChangeFatherName,
    onChangeMotherName,
    onChangeBirthDate,
    isNeedSupply,
    handleNext,
    handleGoBack,
    handleClickFrondCurp,
    handleClickBackCurp,
    openCurpDesModal,
    closeCurpDesModal,
    frontOssFile,
    backOssFile,
    ocrIsCompleted,
    pageState,
    curpDesModalVisible,
  } = useData();
  const showProcessView = useMemo(
    () =>
      !UserInfoManager.context.userModel.hasApplyOrderId &&
      !UserInfoManager.context.userModel.isNeedSupply,
    [UserInfoManager.context.userModel],
  );

  const TipsInfoView = (
    <View
      layoutStrategy="flexRowCenterCenter"
      margin="0 0 16 0"
      padding="12 12 12 12"
      style={{ borderRadius: 4, backgroundColor: Colors.PRIMARY_COLOR_100, zIndex: 2 }}>
      <Text
        style={{ flex: 1, color: Colors.TEXT_COLOR_800 }}
        category="p1"
        i18nKey={Strings.ocrString.flowTip}
      />
    </View>
  );

  const $ocrView = useMemo(() => {
    return (
      <View margin="16 12 0 12" style={{ backgroundColor: 'background-color-0', borderRadius: 8 }}>
        {TipsInfoView}
        <View padding="0 12 16 12">
          <OcrCard
            frontFile={frontFile}
            backFile={backFile}
            handleClickFrondCurp={handleClickFrondCurp}
            handleClickBackCurp={handleClickBackCurp}
            status={status}
            annul={annul}
            ocrFrontState={pageState.ocrFrontState}
            ocrBackState={pageState.ocrBackState}
          />
        </View>
      </View>
    );
  }, [frontFile, backFile, handleClickFrondCurp, handleClickBackCurp, status, annul, pageState]);

  const $curpInfoView = useMemo(() => {
    if (!annul || !status) {
      return null;
    }
    return (
      <View
        margin="12 12 12 12"
        padding="16 12 16 12"
        style={{
          flex: 1,
          borderRadius: 8,
          backgroundColor: 'background-color-0',
        }}>
        {status && (!ocrIsCompleted || annul === 'YES') ? (
          <View
            margin="12 0 12 0"
            padding="8 12 8 12"
            style={{ backgroundColor: 'secondary-color-100', borderRadius: 8 }}
            layoutStrategy="flexRowStart">
            <Image name="_grayInfo" margin="4 6 0 0" />
            <Text
              i18nKey="ocrString.infoTip"
              style={{ flex: 1, color: 'text-color-600' }}
              category="c2"
              bold="600"
            />
          </View>
        ) : null}
        <SeaInput
          type="line"
          ref={fatherNameRef}
          prefixKey={'ocrString.lastName'}
          prefixMargin={'24 0 0 0'}
          placeholderKey={'ocrString.placeholder'}
          value={fatherName}
          pageKey={HitPointEnumsSpace.EPageKey.P_OCR}
          eventKey={HitPointEnumsSpace.EEventKey.E_FATHER_NAME}
          setValue={onChangeFatherName}
          // validateConfig={[
          //   {
          //     condition: isStringExist,
          //     info: '',
          //     status: 'danger',
          //   },
          // ]}
        />
        <SeaInput
          type="line"
          ref={motherNameRef}
          prefixKey={'ocrString.motherLastName'}
          prefixMargin={'16 0 0 0'}
          placeholderKey={'ocrString.placeholder'}
          value={motherName}
          pageKey={HitPointEnumsSpace.EPageKey.P_OCR}
          eventKey={HitPointEnumsSpace.EEventKey.E_MOTHER_NAME}
          setValue={onChangeMotherName}
          // validateConfig={[
          //   {
          //     condition: isStringExist,
          //     info: t('verificationString.required'),
          //     status: 'danger',
          //   },
          // ]}
        />
        <SeaInput
          type="line"
          ref={nameRef}
          prefixKey={'ocrString.name'}
          prefixMargin={'16 0 0 0'}
          placeholderKey={'ocrString.placeholder'}
          value={name}
          pageKey={HitPointEnumsSpace.EPageKey.P_OCR}
          eventKey={HitPointEnumsSpace.EEventKey.E_NAME_MODIFY}
          setValue={onChangeName}
          validateConfig={[
            {
              condition: isStringExist,
              info: t('verificationString.required'),
              status: 'danger',
            },
          ]}
        />
        {/* <DatePickerCalendarInput
          uiType="line"
          ref={birthdateRef}
          prefixKey={'ocrString.birthdate'}
          prefixMargin={'16 0 0 0'}
          placeholderKey={'ocrString.placeholder'}
          type="day"
          format="DD/MM/YYYY"
          value={birthDate}
          pageKey={HitPointEnumsSpace.EPageKey.P_OCR}
          eventKey={HitPointEnumsSpace.EEventKey.E_BIRTHDAY}
          setValue={onChangeBirthDate}
        /> */}
        <SeaInput
          type="line"
          ref={curpRef}
          maxLength={18}
          prefixKey={'ocrString.curp'}
          prefixMargin={'16 0 0 0'}
          placeholderKey={'ocrString.placeholder'}
          value={curp}
          pageKey={HitPointEnumsSpace.EPageKey.P_OCR}
          eventKey={HitPointEnumsSpace.EEventKey.E_CURP}
          setValue={onChangeCurp}
          validateConfig={[
            {
              condition: isStringExist.bind(this, curp),
              info: t('verificationString.required'),
              status: 'danger',
            },
            {
              condition: validateCurp.bind(this, curp),
              info: t('verificationString.failed', {
                name: t('ocrString.curp'),
              }),
              status: 'danger',
            },
          ]}
        />
        <Pressable onPress={openCurpDesModal}>
          <View margin="0 0 0 0" layoutStrategy="flexRowStart">
            <Text
              category="p2"
              i18nKey="ocrString.curpDesTip"
              style={{ color: 'text-color-600' }}
            />
          </View>
        </Pressable>

        {/* <DatePickerInput
          uiType="line"
          ref={validityeRef}
          prefixKey={'ocrString.validity'}
          prefixMargin={'16 0 0 0'}
          placeholderKey={'ocrString.validity'}
          type="year"
          format="YYYY"
          value={validity}
          setValue={onChangeValidity}
        /> */}
        {ocrIsCompleted && annul === 'NO' && (
          <View margin="24 0 0 0" layoutStrategy="flexRowStart">
            <Image margin="4 6 0 0" name="_evidenceCheckedIcon" />
            <Text
              category="c1"
              i18nKey="ocrString.ocrSuccessTips"
              style={{ color: 'text-color-700', flex: 1 }}
            />
          </View>
        )}
      </View>
    );
  }, [
    fatherName,
    motherName,
    name,
    birthDate,
    curp,
    validity,
    frontFile,
    backFile,
    frontOssFile,
    backOssFile,
    ocrIsCompleted,
    annul,
    status,
  ]);

  const $tipCardView = useMemo(() => {
    if (ocrIsCompleted) {
      return null;
    }
    return (
      <View
        margin="12 12 12 12"
        padding="16 12 16 12"
        style={{ backgroundColor: '#fff', borderRadius: 8 }}>
        <View layoutStrategy="flexRowStart">
          <View style={{ flex: 1 }} layoutStrategy="flexColumnCenterCenter">
            <Image name="_evidenceOcrErrorTip1" />
            <Image style={{ top: -7 }} name="_evidenceSelfieErrorIcon" />
            <Text padding="8 0 8 0" category="c1" i18nKey={Strings.ocrString.ocrErrorTip1} />
          </View>
          <View style={{ flex: 1 }} layoutStrategy="flexColumnCenterCenter">
            <Image name="_evidenceOcrErrorTip2" />
            <Image style={{ top: -7 }} name="_evidenceSelfieErrorIcon" />
            <Text padding="8 0 8 0" category="c1" i18nKey={Strings.ocrString.ocrErrorTip2} />
          </View>
        </View>
        <View margin="8 0 0 0" layoutStrategy="flexRowStart">
          <View style={{ flex: 1 }} layoutStrategy="flexColumnCenterCenter">
            <Image name="_evidenceOcrErrorTip3" />
            <Image style={{ top: -7 }} name="_evidenceSelfieErrorIcon" />
            <Text
              isCenter
              padding="8 0 8 0"
              category="c1"
              i18nKey={Strings.ocrString.ocrErrorTip3}
            />
          </View>
          <View style={{ flex: 1 }} layoutStrategy="flexColumnCenterCenter">
            <Image name="_evidenceOcrErrorTip4" />
            <Image style={{ top: -7 }} name="_evidenceSelfieErrorIcon" />
            <Text
              isCenter
              padding="8 0 8 0"
              category="c1"
              i18nKey={Strings.ocrString.ocrErrorTip4}
            />
          </View>
        </View>
        <View margin="12 0 12 0" layoutStrategy="flexRowStartCenter">
          <Image name="_grayInfo" margin="0 6 0 0" />
          <Text
            i18nKey="ocrString.warnTip"
            style={{ flex: 1, color: 'text-color-600' }}
            category="c2"
            bold="600"
          />
        </View>
      </View>
    );
  }, [ocrIsCompleted]);

  return (
    <>
      <Layout pLevel="0" level="1">
        <TopNavigation
          titleKey={'ocrString.indentifyInfo'}
          isBack={!isNeedSupply}
          goBack={handleGoBack}
          bottomLine
        />

        {showProcessView ? <ProgressView current={convert2Progress(EProcessStatus.OCR)} /> : null}
        <SupplyDesciptionView isNeedSupply={isNeedSupply} />
        <ScrollView fadingEdgeLength={10} keyboardShouldPersistTaps={'always'}>
          {$ocrView}
          {$curpInfoView}
          {$tipCardView}
        </ScrollView>
        <ButtonView
          handleNext={handleNext}
          disabled={(!backFile && !backOssFile) || (!frontOssFile && !frontOssFile)}
        />
      </Layout>
      <CurpDesModal visible={curpDesModalVisible} onCancel={closeCurpDesModal} />
    </>
  );
};

interface ButtonViewProps {
  handleNext: Function;
  disabled: boolean;
}
/** 底部按钮组件 */
const ButtonView = memo(function ButtonView({ handleNext, disabled }: ButtonViewProps) {
  return (
    <View
      margin="0 0 0 0"
      padding="16 16 16 16"
      style={{
        backgroundColor: 'background-color-0',
        // borderColor: 'line-color-200',
        // borderTopWidth: 1,
      }}>
      {!UserInfoManager.context.userModel.hasApplyOrderId && (
        <View
          margin="0 8 0 8"
          layoutStrategy="flexRowBetweenCenter"
          style={{ alignSelf: 'center' }}>
          <Image margin="0 10 0 0" name="_safeTipIcon" />
          <Text style={{ fontSize: 14 }} category="p1" i18nKey="ocrString.safeTip" />
        </View>
      )}
      <Button
        disabled={disabled}
        margin={'16 16 0 16'}
        status="primary"
        onPress={() => handleNext()}
        textI18nKey="btnString.next"
      />
    </View>
  );
});

interface SupplyDesciptionViewProp {
  isNeedSupply: boolean;
}
/** OCR补件描述 */
const SupplyDesciptionView = memo(function SupplyDesciptionView({
  isNeedSupply,
}: SupplyDesciptionViewProp) {
  /** 不需要补件的情况不展示 */
  if (!isNeedSupply) {
    return <></>;
  }

  return (
    <View
      margin="12 16 0 16"
      padding="8 8 8 8"
      style={{
        flexDirection: 'row',
        borderRadius: 8,
        // borderWidth: 1,
        // borderColor: 'line-color-200',
        backgroundColor: 'warn-color-100',
      }}>
      <Image margin="0 4 0 0" name="_infoIcon" />
      <View margin="0 16 0 0">
        <Text
          category="p2"
          i18nKey={'supplyString.ocr'}
          style={{
            color: 'text-color-800',
          }}
        />
      </View>
    </View>
  );
});
