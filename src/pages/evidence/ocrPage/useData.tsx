import { BaseEnumsSpace, EvidenceEnumsSpace, HitPointEnumsSpace } from '@/enums';
import {
  useCheckAllPermissionAndRequestPermission,
  useDeviceDataReport,
  useLaunchCamera,
  useOnInit,
} from '@/hooks';
import { BaseInfoManager, ModalList, UserInfoManager, modalDataStoreInstance } from '@/managers';
import { Toast } from '@/nativeComponents';
import {
  fetchOcrInfo,
  fetchSaveCurp,
  fetchSelfieInfo,
  fetchUploadImage,
  fetchUploadImageAndOCR,
  firstCancelConfirmApply,
} from '@/server';
import { trackCommonEvent } from '@/trackEvent';
import { TrackEvent, nav } from '@/utils';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { BackHandler } from 'react-native';
import { Strings, useNameSpace } from '../../../i18n';
import { IState } from './type';
import useAction from './useAction';
import { RouterConfig } from '@/routes';
import { CardType } from '../ocrTakePhotoPage';

type PageStateType = {
  ocrFrontState: 'fail' | 'success' | '';
  ocrBackState: 'fail' | 'success' | '';
};

export default function useData() {
  /** ref */

  const { state, setState, ...restActions } = useAction();
  const {
    status,
    annul,
    frontFile,
    backFile,
    frontOssFile,
    backOssFile,
    cardInfo,
    birthDate,
    curp,
    fatherName,
    motherName,
    name,
    validity,
  } = state;
  const { onApplyReportDeviceData } = useDeviceDataReport();
  const [checkPermission] = useCheckAllPermissionAndRequestPermission();
  const t = useNameSpace().t;
  const { onLaunchCamera } = useLaunchCamera({ cameraType: 'back' });
  const [pageState, setupPageState] = useState<PageStateType>({
    ocrFrontState: '',
    ocrBackState: '',
  });
  const [curpDesModalVisible, setCurpDesModalVisible] = useState<boolean>(false);

  const selfieFileRef = useRef('');

  /** 初始化方法 */
  useOnInit({
    callback: async () => {
      // 如果需要补件，则不请求ocr信息进行回显
      if (UserInfoManager.context.userModel.isNeedSupply) {
        return;
      }
      const { code, data } = await fetchOcrInfo();
      const { code: selfieCode, data: selfieData } = await fetchSelfieInfo();
      console.log('\x1b[93m%s\x1b[0m', 'selfieData:', selfieData);
      if (selfieCode === 0 && selfieData) {
        selfieFileRef.current = selfieData;
      }
      if (code === 0 && data) {
        const {
          status,
          annul,
          fatherName,
          frontFile,
          backFile,
          name,
          motherName,
          validity,
          birthDate,
          curp,
        } = data;
        setState({
          status,
          annul,
          curp,
          validity,
          name,
          fatherName,
          motherName,
          birthDate,
          frontFile,
          backFile,
          frontOssFile: backFile,
          backOssFile: backFile,
          cardInfo: {
            /** 成功 失败 */
            status: '',
            /** YES 手动 NO 自动 */
            annul: '',
            /** 父姓 */
            fatherName: '',
            /** 身份证正面 */
            frontFile: '',
            /** 身份证反面 */
            backFile: '',
            /** 用户姓名 */
            name: '',
            /** 母姓 */
            motherName: '',
            /** 有效期 */
            validity: '',
            /** 生日 */
            birthDate: '',
            /** 银行卡号 */
            curp: '',
          },
        });
      }
    },
    isActivityAutoRefresh: false,
    pageKey: HitPointEnumsSpace.EPageKey.P_OCR,
  });

  const openCurpDesModal = useCallback(() => {
    trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_OCR,
        e: HitPointEnumsSpace.EEventKey.E_CURP_QUERY_GUIDE_MODAL,
      },
      '1',
    );
    setCurpDesModalVisible(true);
  }, []);

  const closeCurpDesModal = useCallback(() => {
    setCurpDesModalVisible(false);
  }, []);

  /** 是否需要补件 */
  const isNeedSupply = useMemo(() => {
    return UserInfoManager.context.userModel.isNeedSupply;
  }, []);

  /** OCR完成, 进入下一步自拍 */
  const handleNext = async () => {
    if (!restActions.checkCanableSubmit()) {
      Toast(t(Strings.clabeString.peso_data_verify_pls_fill_all_options));
      return;
    }

    if (!((await checkPermission()) === 'agree')) {
      return;
    }

    BaseInfoManager.changeLoadingModalVisible(true);
    const param = {
      applyOrderId: '',
      curpBackImg: backOssFile,
      curpBirthDate: cardInfo?.birthDate || '',
      // curpCity: cardInfo?.ciudad || '',
      // curpClaveelector: cardInfo?.claveElector || '',
      // curpDistrict: cardInfo?.colonia || '',
      // curpEdad: cardInfo?.fechaNacimiento || '',
      curpEmision: birthDate || '',
      // curpEstado: cardInfo?.estado || '',
      curpFatherSurname: fatherName || '',
      // curpFolio: cardInfo?.folio || '',
      curpFrontImg: frontOssFile,
      // curpGender: cardInfo?.sexo || '',
      // curpLocalidad: cardInfo?.localidad || '',
      curpMotherSurname: motherName || '',
      // curpMunicipio: cardInfo?.municipio || '',
      curpName: name?.trim?.() || '',
      curpNumber: curp || '',
      // curpRegistro: cardInfo?.registro || '',
      // curpSeccion: cardInfo?.seccion || '',
      // curpStreet: cardInfo?.calle || '',
      // curpSubType: cardInfo?.subTipo || '',
      // curpType: cardInfo?.tipo || '',
      curpVigencia: validity || '',
    };
    let result = await fetchSaveCurp(param);

    if (result?.code === BaseEnumsSpace.ENetworkStatus.SUCCESS) {
      if (!(await UserInfoManager.updateUserState())) {
        BaseInfoManager.changeLoadingModalVisible(false);
        return;
      }

      const { isSupplyCompleted, isLoanFailNeedSupply, isHasActiveOrder, isUserTypeNew } =
        UserInfoManager.context.userModel;
      /** OCR补件场景, 创建订单 */
      if (
        isSupplyCompleted &&
        !isLoanFailNeedSupply &&
        !isLoanFailNeedSupply &&
        !isHasActiveOrder &&
        isUserTypeNew
      ) {
        let applyId = await onFirstCancelConfirmApply();
        if (!applyId) {
          BaseInfoManager.changeLoadingModalVisible(false);
          return;
        }

        Toast(t('messageString.submit_success'));
        await onApplyReportDeviceData(applyId);

        if (!(await UserInfoManager.updateUserState())) {
          BaseInfoManager.changeLoadingModalVisible(false);
          return;
        }
      }

      try {
        await TrackEvent.uploadEventLog();
      } catch (error) {
        BaseInfoManager.changeLoadingModalVisible(false);
      }

      BaseInfoManager.changeLoadingModalVisible(false);
      if (selfieFileRef.current) {
        //@ts-ignore
        nav.navigate(RouterConfig.TAKE_PHOTO_RESULT, {
          uri: selfieFileRef.current,
        });
        return;
      }
      nav.nextToTopRouter();
    } else {
      BaseInfoManager.changeLoadingModalVisible(false);
    }
  };

  /** 点击返回 */
  const handleGoBack = () => {
    if (UserInfoManager.context.userModel.isNeedSupply) {
      return true;
    } else {
      modalDataStoreInstance.openModal({
        key: ModalList.INFO_PROMPT_CONFIRM,
        i18nKey: 'basicInfoString.get_loan',
        imageKey: '_modalCry',
        cancelBtnName: 'btnString.exit',
        confirmBtnName: 'btnString.continueOn',
        isBackdropClose: false,
        confirmBtnCallback: async () => {
          TrackEvent.trackCommonEvent(
            {
              p: HitPointEnumsSpace.EPageKey.P_INTERCEPT_POPUP,
              e: HitPointEnumsSpace.EEventKey.BTN_POPUP,
            },
            '1',
          );
          await TrackEvent.uploadEventLog();
        },
        cancelBtnCallback: async () => {
          TrackEvent.trackCommonEvent(
            {
              p: HitPointEnumsSpace.EPageKey.P_INTERCEPT_POPUP,
              e: HitPointEnumsSpace.EEventKey.BTN_POPUP,
            },
            '0',
          );
          nav.navigationGoBack();
        },
      });
      return true;
    }
  };

  /** 首贷取消补件后主动提交申请 */
  const onFirstCancelConfirmApply = async (): Promise<string> => {
    let result = await firstCancelConfirmApply();
    if (result.code !== 0) {
      return '';
    }
    UserInfoManager.updateApplyId(result.data);

    return result.data;
  };

  /** 证件两面都上传成功, 开始OCR识别 */
  const onOcrGetCurb = async (fontFile: string, backFile: string) => {
    if (fontFile && backFile) {
      const modalId = modalDataStoreInstance.openModal({
        key: ModalList.COUNT_UP,
        i18nKey: Strings.ocrString.ocrIdentifyLoadingTip,
        titleKey: Strings.ocrString.ocrIdentifyTimeoutTip,
        confirmBtnCallback: async () => {
          onOcrGetCurb(fontFile, backFile);
        }
      });
      let result = await fetchUploadImageAndOCR({
        imageFrontUrl: fontFile,
        imageBackUrl: backFile,
        cardType: EvidenceEnumsSpace.ETakePicture.FRONT,
        timeout: 1000 * 60,
      });
      if (result?.code === BaseEnumsSpace.ENetworkStatus.SUCCESS) {
        if (result.data.status === 'SUCCEEDED') {
          // ocr 识别成功
          TrackEvent.trackCommonEvent(
            {
              p: HitPointEnumsSpace.EPageKey.P_OCR,
              e: HitPointEnumsSpace.EEventKey.E_OCR_SUCCESS,
            },
            '1',
          );
          result?.data?.fatherName && restActions.fatherNameRef.current?.clearErrorStatus();
          result?.data?.motherName && restActions.motherNameRef.current?.clearErrorStatus();
          result?.data?.name && restActions.nameRef.current?.clearErrorStatus();
          result?.data?.birthDate && restActions.birthdateRef.current?.clearErrorStatus();
          result?.data?.curp && restActions.curpRef.current?.clearErrorStatus();
          result?.data?.validity && restActions.validityeRef.current?.clearErrorStatus();
          setState((preState: IState): IState => {
            return {
              ...preState,
              ...result?.data,
            };
          });
        } else if (result.data.status === 'FAILED') {
          setupPageState({
            ocrBackState: 'fail',
            ocrFrontState: 'fail',
          });
          TrackEvent.trackCommonEvent(
            {
              p: HitPointEnumsSpace.EPageKey.P_OCR,
              e: HitPointEnumsSpace.EEventKey.E_OCR_SUCCESS,
            },
            '0',
          );
          setState((preState: IState): IState => {
            return {
              ...preState,
              ...result?.data,
              frontFile: '',
              frontOssFile: '',
              backFile: '',
              backOssFile: '',
            };
          });
        }
      }
      modalDataStoreInstance.closeModal(modalId);
    }
  };

  /** 上传CURP证件正面 */
  const handleClickFrondCurp = useCallback(async () => {
    trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_OCR,
        e: HitPointEnumsSpace.EEventKey.E_CLICK_FRONT_IDCARD,
      },
      '1',
    );
    setupPageState((preState: any) => ({
      ...preState,
      ocrFrontState: 'success',
    }));
    nav.navigate(RouterConfig.OCR_TAKE_PHOTO as any, {
      cardType: CardType.frontCard,
      onTakePhoto: async ({ fileUri }: { fileUri: string; error: string }) => {
        if (!fileUri) {
          return;
        }

        BaseInfoManager.changeLoadingModalVisible(true);
        let res = await fetchUploadImage(fileUri, {
          cardType: EvidenceEnumsSpace.ETakePicture.FRONT,
          fileType: 'image/jpg',
        });
        if (res?.code === BaseEnumsSpace.ENetworkStatus.SUCCESS) {
          trackCommonEvent(
            {
              p: HitPointEnumsSpace.EPageKey.P_OCR,
              e: HitPointEnumsSpace.EEventKey.E_FRONT_IDCARD_SUCCESS,
            },
            '1',
          );
          setState((preState: IState): IState => {
            return {
              ...preState,
              frontFile: fileUri,
              frontOssFile: res?.data,
            };
          });
        }
        if (res?.data && backOssFile) {
          onOcrGetCurb(res?.data, backOssFile);
        } else {
          BaseInfoManager.changeLoadingModalVisible(false);
        }
      },
    });
    // await onLaunchCamera().then(async (result: any) => {
    //   if (!result) {
    //     return;
    //   }

    //   BaseInfoManager.changeLoadingModalVisible(true);
    //   let res = await fetchUploadImage(result?.uri, {
    //     cardType: EvidenceEnumsSpace.ETakePicture.FRONT,
    //     fileType: result?.type,
    //   });
    //   if (res?.code === BaseEnumsSpace.ENetworkStatus.SUCCESS) {
    //     setupPageState((preState: any) => ({
    //       ...preState,
    //       ocrFrontState: 'success',
    //     }));
    //     trackCommonEvent(
    //       {
    //         p: HitPointEnumsSpace.EPageKey.P_OCR,
    //         e: HitPointEnumsSpace.EEventKey.E_FRONT_IDCARD_SUCCESS,
    //       },
    //       '1',
    //     );
    //     setState((preState: IState): IState => {
    //       return {
    //         ...preState,
    //         frontFile: result?.uri,
    //         frontOssFile: res?.data,
    //       };
    //     });
    //   }
    //   if (res?.data && backOssFile) {
    //     onOcrGetCurb(res?.data, backOssFile);
    //   } else {
    //     BaseInfoManager.changeLoadingModalVisible(false);
    //   }
    // });
  }, [frontOssFile, backOssFile]);

  /** 上传CURP证件背面 */
  const handleClickBackCurp = useCallback(async () => {
    // 拍摄idcard背面
    trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_OCR,
        e: HitPointEnumsSpace.EEventKey.E_CLICK_BACK_IDCARD,
      },
      '1',
    );
    setupPageState((preState: any) => ({
      ...preState,
      ocrBackState: 'success',
    }));
    nav.navigate(RouterConfig.OCR_TAKE_PHOTO as any, {
      cardType: CardType.backCard,
      onTakePhoto: async ({ fileUri }: { fileUri: string }) => {
        if (!fileUri) {
          return;
        }

        BaseInfoManager.changeLoadingModalVisible(true);
        let res = await fetchUploadImage(fileUri, {
          cardType: EvidenceEnumsSpace.ETakePicture.BACK,
          fileType: 'image/jpg',
        });
        if (res?.code === BaseEnumsSpace.ENetworkStatus.SUCCESS) {
          // 上传背面照片成功
          trackCommonEvent(
            {
              p: HitPointEnumsSpace.EPageKey.P_OCR,
              e: HitPointEnumsSpace.EEventKey.E_BACK_IDCARD_SUCCESS,
            },
            '1',
          );
          setState((preState: IState): IState => {
            return {
              ...preState,
              backFile: fileUri,
              backOssFile: res?.data,
            };
          });
        }
        if (frontOssFile && res?.data) {
          onOcrGetCurb(frontOssFile, res?.data);
        } else {
          BaseInfoManager.changeLoadingModalVisible(false);
        }
      },
    });
    // await onLaunchCamera().then(async (result: any) => {
    //   if (!result) {
    //     return;
    //   }

    //   BaseInfoManager.changeLoadingModalVisible(true);
    //   let res = await fetchUploadImage(result?.uri, {
    //     cardType: EvidenceEnumsSpace.ETakePicture.BACK,
    //     fileType: result?.type,
    //   });
    //   if (res?.code === BaseEnumsSpace.ENetworkStatus.SUCCESS) {
    //     setupPageState((preState: any) => ({
    //       ...preState,
    //       ocrFrontState: 'success',
    //     }));
    //     // 上传背面照片成功
    //     trackCommonEvent(
    //       {
    //         p: HitPointEnumsSpace.EPageKey.P_OCR,
    //         e: HitPointEnumsSpace.EEventKey.E_BACK_IDCARD_SUCCESS,
    //       },
    //       '1',
    //     );
    //     setState((preState: IState): IState => {
    //       return {
    //         ...preState,
    //         backFile: result?.uri,
    //         backOssFile: res?.data,
    //       };
    //     });
    //   }
    //   if (frontOssFile && res?.data) {
    //     onOcrGetCurb(frontOssFile, res?.data);
    //   } else {
    //     BaseInfoManager.changeLoadingModalVisible(false);
    //   }
    // });
  }, [frontOssFile, backOssFile]);

  /** 前后证件是否都成功拍摄 */
  const ocrIsCompleted = useMemo(() => {
    return status === 'SUCCEEDED';
  }, [status]);

  return {
    status,
    annul,
    curp,
    validity,
    name,
    fatherName,
    motherName,
    birthDate,
    frontFile,
    backFile,
    ...restActions,
    isNeedSupply,
    handleNext,
    handleGoBack,
    handleClickFrondCurp,
    handleClickBackCurp,
    openCurpDesModal,
    closeCurpDesModal,
    frontOssFile,
    backOssFile,
    ocrIsCompleted,
    pageState,
    curpDesModalVisible,
  };
}
