import {
  But<PERSON>,
  Check,
  ConfigInputRefType,
  Layout,
  PrefixInput,
  Text,
  TopNavigation,
  View,
} from '@/components';
import { HitPointEnumsSpace } from '@/enums';
import { useOnInit } from '@/hooks';
import { useNameSpace } from '@/i18n';
import { BaseInfoManager, ModalList, UserInfoManager, modalDataStoreInstance } from '@/managers';
import { fetchEnhanceCreditDetail } from '@/server';
import { nav } from '@/utils';
import { ReactElement, memo, useCallback, useMemo, useRef, useState } from 'react';
import { ScrollView } from 'react-native';

interface IState {
  nss: string;
  instagram: string;
  facebook: string;
  rfc: string;
}
enum EConfig {
  NO = 'NO',
  YES = 'YES',
}
const useData = () => {
  const CONFIG_LIST = [
    { label: EConfig.YES, value: 'Sí' },
    { label: EConfig.NO, value: 'No' },
  ];
  const t = useNameSpace('').t;
  /** 初始化 */
  useOnInit({
    pageKey: HitPointEnumsSpace.EPageKey.P_CREDIT_ENHANCE,
  });

  const nssInputRef = useRef<ConfigInputRefType>(null);
  const [state, setState] = useState<IState>({
    nss: '',
    instagram: '',
    facebook: '',
    rfc: '',
  });

  const [agreeRfcUsed, setAgreeNfcUsed] = useState<boolean>(false);

  // 表单填写状态
  const [formFillingStatus, setFormFillingStatus] = useState<boolean>(true);

  const onChangeAgreeRfcUsed = useMemo(
    () => () => {
      setAgreeNfcUsed(!agreeRfcUsed);
    },
    [agreeRfcUsed],
  );

  const onChangeNss = useCallback((nss: IState['nss']) => {
    setState((preState: IState) => ({ ...preState, nss }));
  }, []);

  const onChangeInstagram = useCallback((instagram: IState['instagram']) => {
    setState((preState: IState) => ({ ...preState, instagram }));
  }, []);

  const onChangeFacebook = useCallback((facebook: IState['facebook']) => {
    setState((preState: IState) => ({ ...preState, facebook }));
  }, []);

  const onChangeRfc = useCallback((rfc: IState['rfc']) => {
    setState((preState: IState) => ({ ...preState, rfc }));
  }, []);

  const handleSkip = useCallback(() => {
    nav.nextToTopRouter();
  }, []);

  const handleNext = useCallback(async () => {
    const { rfc } = state;
    if (!rfc || !agreeRfcUsed) {
      setFormFillingStatus(false);
    } else {
      BaseInfoManager.changeLoadingModalVisible(true);
      await onSendINcreaseCredit();
      BaseInfoManager.changeLoadingModalVisible(false);
    }
  }, [state.rfc, agreeRfcUsed]);

  const onSendINcreaseCredit = async () => {
    const { nss, instagram, facebook, rfc } = state;
    let result = await fetchEnhanceCreditDetail({
      nss,
      instagram,
      facebook,
      rfc,
    });
    if (result.code === 0) {
      modalDataStoreInstance.openModal({
        key: ModalList.INFO_PROMPT_CONFIRM,
        isBackdropClose: false,
        confirmBtnName: 'increaseCreditString.close',
        i18nKey: 'increaseCreditString.peso_submit_increase_letter_success',
        confirmBtnCallback: async () => {
          if (!(await UserInfoManager.updateUserState())) {
            BaseInfoManager.changeLoadingModalVisible(false);
            return;
          }
          nav.nextToTopRouter();
        },
      });
    }
    return result.code === 0;
  };

  return {
    ...state,
    nssInputRef,
    CONFIG_LIST,
    onChangeNss,
    onChangeInstagram,
    onChangeFacebook,
    onChangeRfc,
    handleSkip,
    handleNext,
    agreeRfcUsed,
    onChangeAgreeRfcUsed,
    formFillingStatus,
  };
};

const IncreaseCreditPage = (): ReactElement => {
  const {
    nss,
    instagram,
    facebook,
    rfc,
    onChangeNss,
    onChangeInstagram,
    onChangeFacebook,
    onChangeRfc,
    handleSkip,
    handleNext,
    agreeRfcUsed,
    onChangeAgreeRfcUsed,
    formFillingStatus,
  } = useData();

  const $checkBoxItem = useMemo(() => {
    return (
      <View layoutStrategy="flexRowStartCenter" margin="6 0 0 0">
        <Check
          margin={'0 8 8 0'}
          checked={agreeRfcUsed}
          onChange={onChangeAgreeRfcUsed}
          pageKey={HitPointEnumsSpace.EPageKey.P_CREDIT_ENHANCE}
          eventKey={HitPointEnumsSpace.EEventKey.CHECK_AGREE_USE_RFC}
        />
        <Text
          category="p2"
          onPress={onChangeAgreeRfcUsed}
          i18nKey={'increaseCreditString.agreeRfcUsedText'}
          style={{
            color: formFillingStatus || agreeRfcUsed ? 'text-color-600' : 'danger-color-500',
          }}
        />
      </View>
    );
  }, [formFillingStatus, agreeRfcUsed]);

  return (
    <>
      <Layout pLevel="0" level="0">
        <TopNavigation
          alignment={'center'}
          isBack={false}
          titleKey="increaseCreditString.increase_credit"
        />
        <ScrollView
          fadingEdgeLength={10}
          // keyboardShouldPersistTaps="always"
          style={{ marginHorizontal: 16 }}>
          <View
            margin="32 0 0 0"
            padding="12 12 12 12"
            style={{ flexDirection: 'row', backgroundColor: '#EEF2FF' }}>
            <Text>
              <Text
                category="p2"
                i18nKey="increaseCreditString.increase_credit_description_1"
                style={{ color: 'text-color-800' }}
              />
              <Text
                category="p2"
                i18nKey="increaseCreditString.increase_credit_description_2"
                bold="bold"
              />
              <Text
                category="p2"
                i18nKey="increaseCreditString.increase_credit_description_3"
                style={{ color: 'text-color-800' }}
              />
            </Text>
          </View>

          <PrefixInput
            type="line"
            prefixKey={'increaseCreditString.rfc'}
            prefixMargin={'16 0 0 0'}
            placeholderKey={'basicInfoString.placeholder'}
            value={rfc}
            setValue={onChangeRfc}
            pageKey={HitPointEnumsSpace.EPageKey.P_CREDIT_ENHANCE}
            eventKey={HitPointEnumsSpace.EEventKey.E_RFC}
          />
          {$checkBoxItem}
          <PrefixInput
            type="line"
            prefixKey={'increaseCreditString.NSS'}
            prefixMargin={'16 0 0 0'}
            placeholderKey={'basicInfoString.placeholder'}
            value={nss}
            setValue={onChangeNss}
            pageKey={HitPointEnumsSpace.EPageKey.P_CREDIT_ENHANCE}
            eventKey={HitPointEnumsSpace.EEventKey.E_NSS}
          />
          <PrefixInput
            type="line"
            prefixKey={'increaseCreditString.instagram'}
            prefixMargin={'16 0 0 0'}
            placeholderKey={'basicInfoString.placeholder'}
            value={instagram}
            setValue={onChangeInstagram}
            pageKey={HitPointEnumsSpace.EPageKey.P_CREDIT_ENHANCE}
            eventKey={HitPointEnumsSpace.EEventKey.E_INS}
          />
          <PrefixInput
            type="line"
            prefixKey={'increaseCreditString.facebook'}
            prefixMargin={'16 0 0 0'}
            placeholderKey={'basicInfoString.placeholder'}
            value={facebook}
            setValue={onChangeFacebook}
            pageKey={HitPointEnumsSpace.EPageKey.P_CREDIT_ENHANCE}
            eventKey={HitPointEnumsSpace.EEventKey.E_FACEBOOK}
          />
        </ScrollView>
        <View
          layoutStrategy="flexRowBetweenCenter"
          padding="16 16 16 16"
          style={{
            backgroundColor: 'background-color-0',
          }}>
          <Button
            margin="8 8 0 0"
            status="primary"
            appearance="outline"
            style={{ flex: 1 }}
            onPress={handleSkip}
            textI18nKey="btnString.ignore"
          />
          <Button
            margin="8 0 0 0"
            status="primary"
            style={{ flex: 1 }}
            onPress={handleNext}
            textI18nKey="btnString.send"
          />
        </View>
      </Layout>
    </>
  );
};

export default memo(IncreaseCreditPage);
