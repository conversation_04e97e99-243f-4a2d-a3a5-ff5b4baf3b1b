/* eslint-disable react/react-in-jsx-scope */
/* eslint-disable react-hooks/exhaustive-deps */

import { Button, Image, Layout, Text, TopNavigation, View } from '@/components';
import { BaseInfoManager } from '@/managers';
import { RouterConfig } from '@/routes';
import type { NavigationHelpers, Route } from '@react-navigation/core';
import { useCallback, useEffect, useMemo } from 'react';
import WebView from 'react-native-webview';
import { useOnInit } from '@/hooks';
import { HitPointEnumsSpace } from '@/enums';
import { nav } from '@/utils';
import { fetchWithholdAuthorizationOpen } from '@/server';

type RouteParams = {
  uri?: string;
  html?: string;
};

type ScreenProps = {
  route: Route<string, RouteParams>;
  navigation: NavigationHelpers<any>;
} & any;

/** 自动代扣合同页 */
export default ({ route, navigation }: ScreenProps) => {
  /** 初始化方法 */
  useOnInit({
    pageKey: HitPointEnumsSpace.EPageKey.P_AUTOMATIC_REPAYMENT_CONTRACT,
  });

  const onCancelAccept = useCallback(() => {
    nav.navigationGoBack();
  }, []);

  const onAccept = useCallback(async () => {
    BaseInfoManager.changeLoadingModalVisible(true);
    if (route.params?.currentRoute) {
      if (route.params?.acceptHandle) {
        route.params?.acceptHandle();
        nav.navigationGoBack();
      } else {
        nav.resetRouteNavigateCanGoback(route.params?.currentRoute as any, {
          agreeAutoWithHold: true,
        });
      }
    } else {
      nav.navigationGoBack();
    }
    BaseInfoManager.changeLoadingModalVisible(false);
  }, [route.params]);

  useEffect(() => {
    onLoadStart();
  }, []);

  const onLoadStart = useCallback(() => {
    BaseInfoManager.changeLoadingModalVisible(true);
  }, []);

  const onLoadEnd = useCallback(() => {
    BaseInfoManager.changeLoadingModalVisible(false);
  }, []);

  const $renderWebview = useMemo(() => {
    if (route.params?.uri) {
      return (
        <WebView
          onLoadEnd={onLoadEnd}
          originWhitelist={['*']}
          javaScriptEnabled={true}
          useWebkit={true}
          source={{ uri: route.params.uri }}
        />
      );
    }

    if (route.params?.html) {
      return (
        <WebView
          onLoadEnd={onLoadEnd}
          originWhitelist={['*']}
          javaScriptEnabled={true}
          useWebkit={true}
          source={{
            html: route.params.html,
            baseUrl: '',
          }}
        />
      );
    }
    return null;
  }, [route.params]);

  return (
    <Layout pLevel="0">
      <TopNavigation titleKey="autoWithholdString.autoWithholdContractTitle" />
      {$renderWebview}
      <View
        padding="16 0 16 0"
        layoutStrategy="flexRowBetweenCenter"
        style={{
          backgroundColor: 'background-color-0',
          borderTopColor: 'line-color-200',
          borderTopWidth: 1,
        }}>
        <View layoutStrategy="flexRowStart">
          <Image name="_keyIcon" />
          <Text i18nKey="autoWithholdString.autoWithholdProtocolTips" />
        </View>
        <Button
          margin="0 16 0 16"
          style={{ flex: 1, height: 44 }}
          appearance="outline"
          onPress={() => {
            onCancelAccept();
          }}
          textI18nKey="btnString.noAccept"
        />
        <Button
          margin="0 16 0 16"
          style={{ flex: 1, height: 44 }}
          onPress={() => {
            onAccept();
          }}
          textI18nKey="btnString.accept"
        />
      </View>
    </Layout>
  );
};
