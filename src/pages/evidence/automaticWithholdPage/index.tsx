import { Button, Check, Image, Layout, Text, TopNavigation, View } from '@/components';
import { BaseEnumsSpace, HitPointEnumsSpace } from '@/enums';
import { useOnInit } from '@/hooks';
import { BaseInfoManager, UserInfoManager } from '@/managers';
import {
  fetchDefaultBankCardInfo,
  fetchWithholdAuthorizationOpen,
  fetchWithholdContract,
} from '@/server';
import { nav, TrackEvent } from '@/utils';
import { memo, ReactElement, useCallback, useMemo, useState } from 'react';
import { ScrollView } from 'react-native';
import { RouterConfig } from '@/routes';
import CryptoJS from 'crypto-js';
import React from 'react';
import { ScreenProps } from '@/types';

const useData = () => {
  /** 银行卡号 */
  const [clabeNo, setClabeNo] = useState<string>('');
  /** 是否同意自动代扣协议 */
  const [isChecked, setIsChecked] = useState<boolean>(true);

  /** 初始化 */
  useOnInit({
    callback: async () => {
      // 获取第三方账户的绑定状态
      await onGetAccountBindingState();
    },
    isActivityAutoRefresh: true,
    pageKey: HitPointEnumsSpace.EPageKey.P_AUTOMATIC_REPAYMENT,
  });

  /** 获取第三方账户的绑定状态 */
  const onGetAccountBindingState = async () => {
    BaseInfoManager.changeLoadingModalVisible(true);
    let result = await fetchDefaultBankCardInfo();

    if (result.code === 0) {
      const cardNo = result.data.cardNo;
      setClabeNo(`CLABE **** **** ${cardNo.substring(cardNo.length - 4)}`);
    }
    BaseInfoManager.changeLoadingModalVisible(false);
  };

  /** 跳转到自动代扣协议 */
  const openAutoWithhold = async () => {
    TrackEvent.trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_AUTOMATIC_REPAYMENT,
        e: HitPointEnumsSpace.EEventKey.BTN_NEGOTIATE_CHECK,
      },
      '1',
    );
    let result = await fetchWithholdContract();
    if (result.code === BaseEnumsSpace.ENetworkStatus.SUCCESS) {
      nav.navigate(RouterConfig.AUTOMATIC_WITHHOLD_PROTOCOL as any, {
        html: CryptoJS.enc.Base64.parse(String(result.data)).toString(CryptoJS.enc.Utf8),
        currentRoute: RouterConfig.AUTOMATIC_WITHHOLD,
      });
    }

    return result?.code === BaseEnumsSpace.ENetworkStatus.SUCCESS;
  };
  /** 下一步 */
  const handleNext = useCallback(async () => {
    // TrackEvent.trackCommonEvent(
    //   {
    //     p: HitPointEnumsSpace.EPageKey.P_FIRST_BINDING,
    //     e: HitPointEnumsSpace.EEventKey.BTN_CONTINUE,
    //   },
    //   '1',
    // );
    BaseInfoManager.changeLoadingModalVisible(true);
    if (isChecked) {
      const { code } = await fetchWithholdAuthorizationOpen();
      if (code === 0) {
        if (await UserInfoManager.updateUserState()) {
          BaseInfoManager.changeLoadingModalVisible(false);
          nav.nextToTopRouter(RouterConfig.SPLASH);
        }
      }
    } else {
      if (await UserInfoManager.updateUserState()) {
        BaseInfoManager.changeLoadingModalVisible(false);
        nav.nextToTopRouter(RouterConfig.SPLASH);
      }
    }
    BaseInfoManager.changeLoadingModalVisible(false);
  }, [isChecked]);

  const handleBack = useCallback(async () => {
    // TrackEvent.trackCommonEvent(
    //   {
    //     p: HitPointEnumsSpace.EPageKey.P_FIRST_BINDING,
    //     e: HitPointEnumsSpace.EEventKey.BTN_CONTINUE,
    //   },
    //   '1',
    // );
    if (await UserInfoManager.updateUserState()) {
      BaseInfoManager.changeLoadingModalVisible(false);
      nav.nextToTopRouter(RouterConfig.SPLASH);
    }
  }, []);

  const onChangeIsCheckout = useCallback(() => {
    setIsChecked(preIsChecked => !preIsChecked);
  }, []);

  return {
    clabeNo,
    handleNext,
    handleBack,
    openAutoWithhold,
    setIsChecked,
    isChecked,
    onChangeIsCheckout,
  };
};

/** 自动代扣页面 */
const AutomaticWithholdPage = ({
  route,
  navigation,
}: ScreenProps<{ agreeLoanContract: boolean }>): ReactElement => {
  const {
    clabeNo,
    handleNext,
    handleBack,
    isChecked,
    onChangeIsCheckout,
    openAutoWithhold,
    setIsChecked,
  } = useData();

  /** 自动代扣服务绑定的参数回调 */
  React.useEffect(() => {
    try {
      const { agreeAutoWithHold } = route.params || { agreeAutoWithHold: false };
      if (agreeAutoWithHold) {
        setIsChecked(true);
      }
    } catch (e) {}
  }, [route.params]);

  // 银行卡号卡片
  const $clabeNoCard = useMemo(() => {
    return (
      <View
        padding="12 16 12 16"
        margin="16 0 0 0"
        cardType="baseType"
        layoutStrategy="flexColumnStartCenter">
        <Text
          bold="bold"
          isCenter={false}
          style={{
            width: '100%',
            textAlign: 'left',
          }}
          i18nKey="autoWithholdString.currentClabe"
        />
        <View
          margin="12 0 12 0"
          padding="6 8 6 8"
          style={{
            borderColor: 'line-color-200',
            width: '100%',
            borderRadius: 8,
            borderWidth: 1,
          }}>
          <Text
            style={{
              width: '100%',
              textAlign: 'left',
              color: 'text-color-700',
            }}
            textContent={clabeNo}
          />
        </View>
        <View margin="8 0 0 0" layoutStrategy="flexRowStart">
          <Image
            style={{
              tintColor: 'text-color-600',
            }}
            margin="2 8 0 0"
            name="_infoIcon"
            height={14}
            width={14}
          />
          <Text
            category="c1"
            i18nKey={'autoWithholdString.bankCardTip'}
            style={{
              color: 'text-color-700',
            }}
          />
        </View>
      </View>
    );
  }, [clabeNo]);

  const $contract = useMemo(() => {
    return (
      <View layoutStrategy="flexRowStartCenter" margin="12 0 12 0">
        <Check
          margin={'0 8 0 0'}
          checked={isChecked}
          pageKey={HitPointEnumsSpace.EPageKey.P_AUTOMATIC_REPAYMENT}
          eventKey={HitPointEnumsSpace.EEventKey.BTN_AUTOMATIC_REPAY_CLICK}
          onChange={onChangeIsCheckout}
        />
        <View layoutStrategy="flexRowStartCenter">
          <Text
            category="p2"
            i18nKey={'autoWithholdString.agree'}
            style={{
              color: 'text-color-800',
            }}
          />
          <Text
            category="p2"
            onPress={openAutoWithhold}
            i18nKey={'autoWithholdString.automaticWithhold'}
            style={{
              color: 'primary-color-500',
              textDecorationLine: 'underline',
            }}
          />
        </View>
      </View>
    );
  }, [isChecked]);

  const $Button = useMemo(() => {
    return (
      <View
        padding="16 16 16 16"
        style={{
          backgroundColor: 'background-color-0',
          // borderColor: 'line-color-200',
          // borderTopWidth: 1,
        }}>
        <Button status="primary" onPress={handleNext} textI18nKey="btnString.next" />
      </View>
    );
  }, [isChecked]);

  return (
    <>
      <Layout pLevel="0" level="0" topCompensateColor="secondary-color-500">
        <TopNavigation
          selfBgKey="_withholdTopBg"
          goBack={handleBack}
          titleKey="autoWithholdString.autoWithholdTitle"
          bottomLine={false}
        />
        <ScrollView fadingEdgeLength={10} style={{ marginHorizontal: 16, marginVertical: 16 }}>
          {$clabeNoCard}
          {$contract}
          <Image resizeMode="contain" name="_withholdRewardBanner" />
        </ScrollView>
        {$Button}
      </Layout>
    </>
  );
};

export default memo(AutomaticWithholdPage);
