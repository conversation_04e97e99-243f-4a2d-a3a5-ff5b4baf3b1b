import { Button, Image, Layout, Text, TopNavigation, View } from '@/components';

import type { NavigationHelpers, Route } from '@react-navigation/core';
import { useMemo } from 'react';
import { RefreshControl, ScrollView } from 'react-native';
import useData from './useData';

type RouteParams = {
  uri: string;
};

type ScreenProps = {
  route: Route<string, RouteParams>;
  navigation: NavigationHelpers<any>;
} & any;

/** 用户确认用信人脸超时页面 */
export default ({ route, navigation }: ScreenProps) => {
  const { refreshing, onRefresh, faceExpiredMessage, handleNext } = useData();

  const $rendErrorTip = useMemo(() => {
    if (Array.isArray(faceExpiredMessage) && faceExpiredMessage.length > 0) {
      return faceExpiredMessage?.map((message: string, index: number) => (
        <Text key={index} margin="12 0 0 0" category="p2" isCenter textContent={message} />
      ));
    }

    return <></>;
  }, [faceExpiredMessage]);

  return (
    <>
      <Layout level="0" pLevel="1">
        <TopNavigation showLogoAction={true} isShowVip type="basic" showMessage bottomLine />
        <ScrollView
          fadingEdgeLength={10}
          keyboardShouldPersistTaps="always"
          refreshControl={
            <RefreshControl colors={['#4C6EFF']} refreshing={refreshing} onRefresh={onRefresh} />
          }>
          <View
            style={{
              flexDirection: 'column',
              alignItems: 'center',
            }}>
            <Image margin="40 0 0 0" name="_evidenceFaceCancel" />
            <View
              layoutStrategy="flexColumnBetweenCenter"
              margin="40 0 0 0"
              padding="8 8 8 8"
              style={{
                width: '80%',
                backgroundColor: 'background-color-0',
                borderRadius: 8,
                // borderWidth: 1,
                // borderColor: 'line-color-200',
              }}>
              {$rendErrorTip}
            </View>
          </View>
        </ScrollView>
        <Button
          margin="16 0 32 0"
          onPress={handleNext}
          status="primary"
          textI18nKey="btnString.againApply"
        />
      </Layout>
    </>
  );
};
