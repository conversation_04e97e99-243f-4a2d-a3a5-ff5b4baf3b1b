import {
  useGetUserStateAndNextRouterOrOtherCallBack,
  useSubscribeFilter,
  useOnInit,
} from '@/hooks';
import { BaseInfoManager, UserInfoContextType, UserInfoManager } from '@/managers';
import { confirmUnlock } from '@/server';
import { useCallback, useRef, useState } from 'react';
import { HitPointEnumsSpace } from '@/enums';
import { RouterConfig } from '@/routes';

interface IFiletrData {
  faceExpiredMessage: string[];
}

export default function useData() {
  const [getUserStateAndNextRouterOrOtherCallBack] = useGetUserStateAndNextRouterOrOtherCallBack(
    RouterConfig.FACE_EXPIRED,
  );

  /** 初始化方法 */
  const { loading, refreshing, setRefreshing, onRefresh } = useOnInit({
    callback: async () => {
      BaseInfoManager.changeLoadingModalVisible(true);
      loading.current = true;
      await getUserStateAndNextRouterOrOtherCallBack();
      loading.current = false;
      BaseInfoManager.changeLoadingModalVisible(false);
    },
    refreshCallback: async () => {
      setRefreshing(true);
      loading.current = true;
      await getUserStateAndNextRouterOrOtherCallBack();
      loading.current = false;
      setRefreshing(false);
    },
    pageKey: HitPointEnumsSpace.EPageKey.P_CREDIT_FACE_TIMEOUT,
  });

  const handleNext = useCallback(async () => {
    if (!loading.current) {
      BaseInfoManager.changeLoadingModalVisible(true);
      loading.current = true;
      // 确认重新贷款
      const { code } = await confirmUnlock();
      if (code === 0) {
        await getUserStateAndNextRouterOrOtherCallBack();
        // 更新状态列表数据
      }
      loading.current = false;
      BaseInfoManager.changeLoadingModalVisible(false);
    }
  }, []);

  /** 人脸超时的提示信息 */
  const { faceExpiredMessage } = useSubscribeFilter({
    subject: UserInfoManager.messageCenter,
    filter: (subject: UserInfoContextType) => {
      return {
        faceExpiredMessage: subject.userModel?.userState?.faceExpiredMessage,
      };
    },
  }) as IFiletrData;

  return {
    onRefresh,
    refreshing,
    handleNext,
    faceExpiredMessage,
  };
}
