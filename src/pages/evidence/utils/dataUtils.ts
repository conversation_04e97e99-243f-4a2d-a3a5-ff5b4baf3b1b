/**
 * evidence 相关数据处理工具
 */

import { EProcessStatus } from '../constants/enums';

// 进件过程枚举进度转化
export const convert2Progress = (progressStatus: EProcessStatus): number => {
  switch (progressStatus) {
    case EProcessStatus.BASIC:
      return 25;
    case EProcessStatus.CURP:
    case EProcessStatus.OCR:
      return 50;
    case EProcessStatus.TAKE_PHOTO:
      return 75;
    case EProcessStatus.TAKE_PHOTO_RESULT:
      return 90;
    case EProcessStatus.CLABE:
      return 95;
  }
  return 99;
};
