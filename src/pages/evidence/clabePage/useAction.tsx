import { BaseInputRefType } from '@/components';
import { isStringExist, validateCurp, verifyClabe } from '@/utils';
import { useCallback, useRef, useState } from 'react';
import { ERelationship, IState } from './type';
import { BaseInfoManager } from '@/managers';
import { UserEnumsSpace } from '@/enums';
import { t } from 'i18next';

export default function useAction() {
  const clabeInputRef = useRef<BaseInputRefType>(null);
  // const bankNameRef = useRef<BaseInputRefType>(null);
  const lastNameRef = useRef<BaseInputRefType>(null);
  const motherNameRef = useRef<BaseInputRefType>(null);
  const nameRef = useRef<BaseInputRefType>(null);
  const curpRef = useRef<BaseInputRefType>(null);
  const relationshipRef = useRef<BaseInputRefType>(null);
  const selfBankCardRef = useRef<BaseInputRefType>(null);

  const [state, setState] = useState<IState>({
    isPersonal: false,
    isSelfBankCard: 'YES',
    bankCode: '',
    oldBankCode: '',
    bankList: [],
    clabe: '',
    bankName: '',
    fatherName: '',
    motherName: '',
    name: '',
    curp: '',
    relationship: '',
    message: '',
  });

  /** 检查表单填写状态, 进行一定的UX反馈 */
  const checkCanableSubmit = () => {
    let flag = true;

    const {
      isPersonal,
      clabe,
      bankName,
      fatherName,
      motherName,
      name,
      curp,
      relationship,
      isSelfBankCard,
    } = state;

    if (!isStringExist(clabe) || String(clabe).length !== 18) {
      clabeInputRef.current?.emitErrorStatus(t('clabeString.clabe_length_error_match_first'));
      flag = true;
    }

    if (!verifyClabe(clabe)) {
      flag = true;
    }
    // if (!isStringExist(bankName)) {
    //   // bankNameRef.current?.emitErrorStatus(t('verificationString.required'));
    //   flag = false;
    // }

    if (!isStringExist(isSelfBankCard)) {
      selfBankCardRef.current?.emitErrorStatus(t('verificationString.required'));
      flag = false;
    }
    if (isPersonal && !(isStringExist(fatherName) || isStringExist(motherName))) {
      if (!isStringExist(fatherName)) {
        lastNameRef.current?.emitErrorStatus(t('verificationString.required'));
      }

      if (!isStringExist(motherName)) {
        motherNameRef.current?.emitErrorStatus(t('verificationString.required'));
      }
      flag = false;
    }

    if (isPersonal && !isStringExist(name)) {
      nameRef.current?.emitErrorStatus(t('verificationString.required'));
      flag = false;
    }

    if (isPersonal && (!isStringExist(curp) || !validateCurp(curp))) {
      if (!isStringExist(curp)) {
        curpRef.current?.emitErrorStatus(t('verificationString.required'));
      } else if (!validateCurp(curp)) {
        curpRef.current?.emitErrorStatus(
          t('verificationString.failed', {
            name: t('clabeString.curp'),
          }),
        );
      }
      flag = false;
    }

    if (isPersonal && !isStringExist(relationship)) {
      relationshipRef.current?.emitErrorStatus(t('verificationString.required'));
      flag = false;
    }

    return flag;
  };

  const onChangeSetIsPersonal = useCallback((isPersonal: IState['isPersonal']) => {
    setState((preState: IState): IState => {
      return {
        ...preState,
        isPersonal,
      };
    });
  }, []);

  const onChageIsSelfBankCard = useCallback((isSelfBankCard: IState['isSelfBankCard']) => {
    setState((preState: IState): IState => {
      return {
        ...preState,
        isSelfBankCard: isSelfBankCard,
        isPersonal: isSelfBankCard === 'NO',
      };
    });
  }, []);

  const onChangeBankName = useCallback((bankName: IState['bankName']) => {
    setState((preState: IState): IState => {
      return {
        ...preState,
        bankName: String(bankName).trim(),
      };
    });
  }, []);

  const onChangeCurp = useCallback((curp: IState['curp']) => {
    setState((preState: IState): IState => {
      return {
        ...preState,
        curp: String(curp).trim(),
      };
    });
  }, []);

  const onChangeName = useCallback((name: IState['name']) => {
    setState((preState: IState): IState => {
      return {
        ...preState,
        name: String(name).trim(),
      };
    });
  }, []);

  const onChangeFatherName = useCallback((fatherName: IState['fatherName']) => {
    setState((preState: IState): IState => {
      return {
        ...preState,
        fatherName: String(fatherName).trim(),
      };
    });
  }, []);

  const onChangeMotherName = useCallback((motherName: IState['motherName']) => {
    setState((preState: IState): IState => {
      return {
        ...preState,
        motherName: String(motherName).trim(),
      };
    });
  }, []);

  const onChangeRelationship = useCallback((relationship: IState['relationship']) => {
    setState((preState: IState): IState => {
      return {
        ...preState,
        relationship,
      };
    });
  }, []);

  /** 关系 */
  const relationshipSelectList: ERelationship[] = [
    ERelationship.PARENTS,
    ERelationship.COUPLE,
    ERelationship.CHILDREN,
    ERelationship.OTHER,
  ];

  const slefBankCardSelectList: string[] = ['Sí', 'No'];

  return {
    state,
    setState,
    onChangeSetIsPersonal,
    onChangeBankName,
    onChageIsSelfBankCard,
    onChangeCurp,
    onChangeName,
    onChangeFatherName,
    onChangeMotherName,
    onChangeRelationship,
    relationshipSelectList,
    slefBankCardSelectList,
    clabeInputRef,
    selfBankCardRef,
    // bankNameRef,
    lastNameRef,
    motherNameRef,
    nameRef,
    curpRef,
    relationshipRef,
    checkCanableSubmit,
  };
}
