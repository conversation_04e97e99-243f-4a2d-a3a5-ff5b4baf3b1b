/* eslint-disable react-native/no-inline-styles */

import {
  Button,
  Image,
  Layout,
  PrefixInput,
  PrefixSelectInput,
  SeaInput,
  Text,
  TopNavigation,
  View,
} from '@/components';
import { HitPointEnumsSpace } from '@/enums';
import { BaseInfoManager, UserInfoManager } from '@/managers';
import { ConfirmClabeSendLoanModal } from '@/modals';
import { ScreenProps } from '@/types';
import { isStringExist, validateCurp, verifyClabe } from '@/utils';
import React, { ReactElement, memo, useMemo } from 'react';
import { Pressable, ScrollView, TouchableWithoutFeedback } from 'react-native';
import useData from './useData';
import { useTheme } from '@/hooks';
import ProgressView from '../../components/ProgressView';
import { Colors } from '@/themes';
import { convert2Progress } from '../utils/dataUtils';
import { EProcessStatus } from '../constants/enums';
import { Strings, useNameSpace } from '@/i18n';
import CURPHelpModal from './components/CURPHelpModal';
import CurpDesModal from '../ocrPage/components/curpDesModal';
import { RouterConfig } from '@/routes';


export default ({ route }: ScreenProps<{
  pageKey?: RouterConfig.COMFIRM_LOAN | ""
}>): ReactElement => {
  const pageKey = route?.params?.pageKey || ""
  const {
    clabeInputRef,
    // bankNameRef,
    lastNameRef,
    motherNameRef,
    nameRef,
    curpRef,
    selfBankCardRef,
    relationshipRef,
    isPending,
    handleNext,
    handleGoBack,
    onGoBankListPage,
    isPersonal,
    onChangeSetIsPersonal,
    clabe,
    bankName,
    isSelfBankCard,
    curp,
    name,
    relationship,
    relationshipSelectList,
    fatherName,
    motherName,
    message,
    autoWithhold,
    onChangeClabe,
    onChangeBankName,
    // onChageIsSelfBankCard,
    onChangeCurp,
    onChangeName,
    onChangeFatherName,
    onChangeMotherName,
    onChangeRelationship,
    onOpenAutoWithhold,
    onCloseAutoWithhold,
    openAutoWithhold,
    onSetIsSelfBankCard,
    onSetIsNotSelfBankCard,
    verifyClabeCodeSupport,
    curpHelpModalVisible,
    onShowCurpHelpModal,
    onCloseCurpHelpModal,
  } = useData({ pageKey });
  const t = useNameSpace('clabeString').t;
  const theme = useTheme();
  const showProcessView = useMemo(
    () =>
      !pageKey && !UserInfoManager.context.userModel.hasApplyOrderId &&
      !UserInfoManager.context.userModel.isNeedSupply,
    [UserInfoManager.context.userModel],
  );
  const TipsInfoView = (
    <View
      layoutStrategy="flexRowCenterCenter"
      margin="0 0 16 0"
      padding="12 12 12 12"
      style={{ borderRadius: 4, backgroundColor: Colors.PRIMARY_COLOR_100, zIndex: 2 }}>
      <Text
        style={{ flex: 1, color: Colors.TEXT_COLOR_800 }}
        category="p1"
        i18nKey={Strings.clabeString.clabeHeaderTip}
      />
    </View>
  );

  const $autoWithholdView = useMemo(() => {
    const selecedIsSelfBankCard = isSelfBankCard === 'YES';
    const selecedIsNotSelfBankCard = isSelfBankCard === 'NO';

    return (
      <>
        <View width={'100%'} layoutStrategy="flexColumnStart">
          <Text margin="6 0 6 12" i18nKey="clabeString.selfBankCard" />
          <View
            layoutStrategy="flexRowCenterCenter"
            width={'100%'}
            style={{
              borderRadius: 8,
              borderColor: 'text-color-600',
              backgroundColor: 'background-color-100',
            }}>
            <TouchableWithoutFeedback style={{ flex: 1 }} onPress={onSetIsSelfBankCard}>
              <View
                padding="8 12 8 12"
                style={{
                  flex: 1,
                  borderRadius: 8,
                  backgroundColor: selecedIsSelfBankCard
                    ? 'primary-color-500'
                    : 'background-color-100',
                }}
                layoutStrategy="flexRowCenterCenter">
                <Text status={selecedIsSelfBankCard ? 'control' : 'basic'} i18nKey="btnString.Si" />
              </View>
            </TouchableWithoutFeedback>
            <TouchableWithoutFeedback style={{ flex: 1 }} onPress={onSetIsNotSelfBankCard}>
              <View
                padding="8 12 8 12"
                style={{
                  flex: 1,
                  borderRadius: 8,
                  backgroundColor: selecedIsNotSelfBankCard
                    ? 'primary-color-500'
                    : 'background-color-100',
                }}
                layoutStrategy="flexRowCenterCenter">
                <Text
                  status={selecedIsNotSelfBankCard ? 'control' : 'basic'}
                  i18nKey="btnString.No"
                />
              </View>
            </TouchableWithoutFeedback>
          </View>
        </View>
        {BaseInfoManager.context.baseModel.isWithholdSwitch && (
          <View
            width={'100%'}
            style={{
              borderRadius: 4,
            }}
            margin="32 0 0 0">
            <View
              width={'100%'}
              style={{
                borderTopWidth: 1,
                borderStyle: 'dashed',
                borderTopColor: 'primary-color-500',
              }}>
              {(selecedIsNotSelfBankCard || selecedIsSelfBankCard) && (
                <Image
                  style={[
                    { position: 'absolute', top: -11 },
                    selecedIsSelfBankCard ? { left: 70 } : { right: 90 },
                  ]}
                  name="_evidenceTopTriangleIcon"
                />
              )}
              <View
                style={{
                  borderRadius: 4,
                  backgroundColor: 'background-color-100',
                }}>
                {/* 自动代扣卡片 */}
                {selecedIsSelfBankCard && (
                  <>
                    <View
                      margin="12 6 12 6"
                      layoutStrategy="flexColumnStart"
                      style={{ alignItems: 'flex-start' }}>
                      <Text
                        category="p2"
                        i18nKey="autoWithholdString.autoWithholdSelectTitle"></Text>
                      <View margin="10 0 0 0" layoutStrategy="flexRowStart">
                        <Image margin="0 6 0 0" name="_evidenceEmphasisIcon" />
                        <View style={{ flex: 1 }}>
                          <Text
                            category="c1"
                            style={{
                              color: 'text-color-700',
                            }}
                            i18nKey="autoWithholdString.autoWithholdSelectDes"
                          />
                          <View margin="10 0 0 0" layoutStrategy="flexRowBetweenCenter">
                            <Button
                              width={126}
                              height={44}
                              status="primary"
                              appearance={autoWithhold ? 'filled' : 'outline'}
                              onPress={onOpenAutoWithhold}
                              textI18nKey="btnString.Si"></Button>
                            <Button
                              width={126}
                              height={44}
                              onPress={onCloseAutoWithhold}
                              status="primary"
                              appearance={autoWithhold ? 'outline' : 'filled'}
                              textI18nKey="btnString.No"></Button>
                          </View>
                        </View>
                      </View>
                    </View>
                    <View
                      margin="4 4 4 4"
                      padding="4 8 4 8"
                      style={{
                        borderBottomLeftRadius: 4,
                        borderBottomRightRadius: 4,
                        backgroundColor: 'background-color-0',
                      }}
                      layoutStrategy="flexRowStartCenter">
                      <Image
                        margin="2 8 0 0"
                        style={{
                          tintColor: 'fill-color-500',
                        }}
                        name="_vector"></Image>
                      <View layoutStrategy="flexRowStartCenter">
                        <Text
                          category="c2"
                          i18nKey={'autoWithholdString.agree'}
                          style={{
                            color: 'text-color-600',
                          }}
                        />
                        <Text
                          category="c2"
                          onPress={openAutoWithhold}
                          i18nKey={'autoWithholdString.automaticWithhold'}
                          style={{
                            color: 'text-color-700',
                            textDecorationLine: 'underline',
                          }}
                        />
                      </View>
                    </View>
                  </>
                )}
                {/* 非本人卡片说明 */}
                {isPersonal && (
                  <View margin="12 6 12 6" layoutStrategy="flexRowStart">
                    <Image margin="2 8 0 0" name="_evidenceMoreInfoIcon"></Image>
                    <Text style={{ flex: 1 }} i18nKey="clabeString.cardInfo" />
                  </View>
                )}
              </View>
            </View>
          </View>
        )}
      </>
    );
  }, [
    isPersonal,
    isSelfBankCard,
    autoWithhold,
    onOpenAutoWithhold,
    onCloseAutoWithhold,
    onSetIsSelfBankCard,
    onSetIsNotSelfBankCard,
  ]);

  const ClabeInfoView = (
    <>
      <View margin={'16 16 16 16'} cardType="baseType">
        {TipsInfoView}
        <View margin="16 16 16 16">
          <SeaInput
            type="line"
            ref={clabeInputRef}
            prefixKey={'clabeString.clabeNo'}
            placeholderKey={'clabeString.placeholder'}
            value={clabe}
            keyboardType="number-pad"
            pageKey={HitPointEnumsSpace.EPageKey.P_BANK}
            eventKey={HitPointEnumsSpace.EEventKey.E_CLABE}
            setValue={onChangeClabe}
            validateConfig={[
              {
                condition: isStringExist.bind(this, clabe),
                info: t('verificationString.required'),
                status: 'danger',
              },
              {
                condition: verifyClabe.bind(this, clabe),
                info: t('clabe_input_error_tips'),
                status: 'danger',
              },
              {
                condition: verifyClabeCodeSupport.bind(this, clabe),
                info: t('clabe_input_error_banklist_tips'),
                linkInfo: t('clabe_input_error_banklist_tips_link'),
                linkAction: onGoBankListPage,
                status: 'danger',
              },
              {
                condition: () => clabe.length === 18,
                info: t('clabe_length_error_match_first'),
                status: 'danger',
              },
            ]}
          />
          {bankName ? (
            <View
              layoutStrategy="flexRowStartCenter"
              padding="12 8 12 8"
              margin="16 0 0 0"
              style={{
                borderWidth: 1,
                borderRadius: 8,
                borderColor: 'line-color-100',
              }}>
              <Text
                category="p1"
                bold="500"
                i18nKey="clabeString.bankName"
                style={{ color: 'text-color-500' }}
              />
              <Text
                category="p1"
                bold="500"
                textContent={` ${bankName}`}
                style={{ flex: 1, color: 'text-color-600' }}
              />
            </View>
          ) : null}
          {/* <SeaInput
            type="line"
            ref={bankNameRef}
            disabled={true}
            prefixKey={'clabeString.bankName'}
            prefixMargin={'16 0 0 0'}
            placeholderKey={'clabeString.placeholder'}
            value={bankName}
            setValue={onChangeBankName}
          /> */}
          {/* <View margin="8 0 0 0" layoutStrategy="flexRowStartCenter">
            <Image
              tintColor={theme['primary-color-500']}
              margin="2 8 0 0"
              name="_vector"
              height={12}
              width={12}
            />
            <TouchableWithoutFeedback onPress={onGoBankListPage}>
              <Text
                category="c1"
                i18nKey={'clabeString.tinyInfo'}
                style={{
                  color: 'primary-color-500',
                  textDecorationLine: 'underline',
                }}
              />
            </TouchableWithoutFeedback>
          </View> */}
          {/* <View layoutStrategy="flexRowStartCenterWarp">
            <PrefixSelectInput
              type="line"
              // @ts-ignore
              ref={selfBankCardRef}
              prefixKey={'clabeString.selfBankCard'}
              prefixMargin={'16 0 0 0'}
              placeholderKey={'basicInfoString.placeholder'}
              options={slefBankCardSelectList}
              value={isSelfBankCard}
              setValue={onChageIsSelfBankCard}
            />
            <View margin="8 0 0 0" layoutStrategy="flexRowStart">
              <Image
                tintColor={theme['text-color-600']}
                margin="2 8 0 0"
                name="_infoIcon"
                height={14}
                width={14}
              />
              <Text
                category="c1"
                i18nKey={'clabeString.selfBankCardTip'}
                style={{
                  color: 'text-color-700',
                }}
              />
            </View>
          </View> */}
          {verifyClabe(clabe) && !!bankName && !pageKey && (
            <View margin="0 0 0 0" layoutStrategy="flexRowStartCenterWarp">
              {$autoWithholdView}
            </View>
          )}

          {isPersonal && verifyClabe(clabe) && (
            <>
              <SeaInput
                type="line"
                ref={lastNameRef}
                prefixKey={'clabeString.lastName'}
                prefixMargin={'16 0 0 0'}
                placeholderKey={'clabeString.placeholder'}
                value={fatherName}
                setValue={onChangeFatherName}
              />
              <SeaInput
                type="line"
                ref={motherNameRef}
                prefixKey={'clabeString.motherName'}
                prefixMargin={'16 0 0 0'}
                placeholderKey={'clabeString.placeholder'}
                value={motherName}
                setValue={onChangeMotherName}
              />
              <SeaInput
                type="line"
                ref={nameRef}
                prefixKey={'clabeString.name'}
                prefixMargin={'16 0 0 0'}
                placeholderKey={Strings.clabeString.placeholder}
                value={name}
                setValue={onChangeName}
                validateConfig={[
                  {
                    condition: isStringExist.bind(this, name),
                    info: t(Strings.verificationString.required),
                    status: 'danger',
                  },
                ]}
              />
              <SeaInput
                type="line"
                ref={curpRef}
                prefixKey={'clabeString.curp'}
                prefixMargin={'16 0 0 0'}
                placeholderKey={'clabeString.placeholder'}
                value={curp}
                setValue={onChangeCurp}
                validateConfig={[
                  {
                    condition: isStringExist.bind(this, curp),
                    info: t(Strings.verificationString.required),
                    status: 'danger',
                  },
                  {
                    condition: validateCurp.bind(this, curp),
                    info: t(Strings.verificationString.required),
                    status: 'danger',
                  },
                ]}
              />
              <Pressable
                onPress={onShowCurpHelpModal}
                style={{ flexDirection: 'row', alignItems: 'center' }}>
                <Text category="p2" i18nKey="clabeString.curpHelpTips" />
                <Image name="_grayArrowRightIcon" />
              </Pressable>
              <PrefixSelectInput
                type="line"
                ref={relationshipRef}
                selectModel="actionSheet"
                prefixKey={'basicInfoString.relationship'}
                prefixMargin={'16 0 0 0'}
                placeholderKey={'basicInfoString.placeholder'}
                options={relationshipSelectList}
                value={relationship}
                setValue={onChangeRelationship}
              />
            </>
          )}
        </View>
      </View>
    </>
  );

  const ButtonView = (
    <View
      padding="16 16 16 16"
      style={{
        backgroundColor: 'background-color-0',
        borderColor: 'line-color-200',
        borderTopWidth: 1,
      }}>
      {!UserInfoManager.context.userModel.hasApplyOrderId && (
        <View
          margin="0 8 0 8"
          layoutStrategy="flexRowBetweenCenter"
          style={{ alignSelf: 'center' }}>
          <Image margin="0 10 0 0" name="_safeTipIcon" />
          <Text
            category="p1"
            style={{ fontSize: 14 }}
            i18nKey={Strings.basicInfoString.protect_your_privacy}
          />
        </View>
      )}
      <Button
        margin="16 16 0 16"
        status="primary"
        onPress={handleNext}
        textI18nKey="btnString.next"
        disabled={!verifyClabe(clabe)}
      />
    </View>
  );

  if (isPending) {
    return <></>;
  }

  return (
    <>
      <Layout pLevel="0" level="1">
        <TopNavigation
          titleKey={'clabeString.bankInfo'}
          isBack={!UserInfoManager.context.userModel.isNeedSupply}
          goBack={handleGoBack}
          bottomLine
        />
        {showProcessView ? <ProgressView current={convert2Progress(EProcessStatus.CLABE)} /> : null}
        <SupplyDesciptionView
          message={message}
          isNeedSupply={UserInfoManager.context.userModel.isSupplyUnCompleted}
        />
        <ScrollView fadingEdgeLength={10} keyboardShouldPersistTaps="always">
          {ClabeInfoView}
        </ScrollView>
        {ButtonView}
      </Layout>
      <ConfirmClabeSendLoanModal />
      <CurpDesModal visible={curpHelpModalVisible} onCancel={onCloseCurpHelpModal} />
    </>
  );
};

interface SupplyDesciptionViewProp {
  isNeedSupply: boolean;
  message?: string;
}
/** 补件描述 */
const SupplyDesciptionView = memo(function SupplyDesciptionView({
  isNeedSupply,
  message = '',
}: SupplyDesciptionViewProp) {
  /** 不需要补件的情况不展示 */
  if (!isNeedSupply) {
    return <></>;
  }

  return (
    <View
      margin="12 16 0 16"
      padding="8 8 8 8"
      style={{
        flexDirection: 'row',
        borderRadius: 8,
        // borderWidth: 1,
        // borderColor: 'line-color-200',
        backgroundColor: 'warn-color-100',
      }}>
      <Image margin="0 4 0 0" name="_infoIcon" />
      <View margin="0 16 0 0">
        {message && (
          <Text
            category="p2"
            textContent={message}
            style={{
              color: 'text-color-800',
            }}
          />
        )}
        {!message && (
          <Text
            category="p2"
            i18nKey={'supplyString.ocr'}
            style={{
              color: 'text-color-800',
            }}
          />
        )}
      </View>
    </View>
  );
});
