import { EvidenceVOSpace } from '@/types';

export type IState = {
  isPersonal: boolean;
  oldBankCode: string;
  bankCode: string;
  bankList: Partial<EvidenceVOSpace.BankItemDataType>[];
  clabe: string;
  bankName: string;
  fatherName: string;
  motherName: string;
  name: string;
  curp: string;
  relationship: string;
  message: string;
  isSelfBankCard: string;
};

/** 联系人关系 */
export enum ERelationship {
  /** 父母 */
  PARENTS = 'Padres',
  /** 夫妻 */
  COUPLE = 'Pareja',
  /** 孩子 */
  CHILDREN = 'Hijos',
  /** 其他 */
  OTHER = 'Otro',
}
