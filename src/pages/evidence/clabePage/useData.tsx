/* eslint-disable react-hooks/exhaustive-deps */
import { BaseEnumsSpace, HitPointEnumsSpace } from '@/enums';
import { useCheckAllPermissionAndRequestPermission, useDeviceDataReport, useOnInit } from '@/hooks';
import { ELocalKey } from '@/localStorage';
import {
  BaseInfoManager,
  KVManager,
  ModalList,
  UserInfoManager,
  modalDataStoreInstance,
} from '@/managers';
import { Toast } from '@/nativeComponents';
import { RouterConfig } from '@/routes';
import {
  fetchBankCardDefaultText,
  fetchBankCardSupplySubmit,
  fetchDefaultBankCardInfo,
  fetchGetBankCardInfo,
  fetchGetBankList,
  fetchWithholdContract,
  firstCancelConfirmApply,
} from '@/server';
import { TrackEvent, isStringExist, nav, verifyClabe } from '@/utils';
import { useCallback, useEffect, useMemo, useRef, useState, useTransition } from 'react';
import { BackHand<PERSON> } from 'react-native';
import { useNameSpace } from '../../../i18n';
import { IState } from './type';
import useAction from './useAction';
import CryptoJS from 'crypto-js';
import { EvidenceVOSpace } from '@/types';

type Props = {
  pageKey: RouterConfig.COMFIRM_LOAN | ""
}
export default function useData(props: { pageKey: Props }) {
  const { pageKey } = props
  const t = useNameSpace('clabeString').t;
  const [isPending, startTransition] = useTransition();
  const { state, setState, ...restAction } = useAction();
  /** ref */
  const {
    isPersonal,
    isSelfBankCard,
    bankList,
    bankCode,
    oldBankCode,
    clabe,
    bankName,
    fatherName,
    motherName,
    name,
    curp,
    relationship,
  } = state;
  const [curpHelpModalVisible, setCurpHelpModalVisible] = useState(false);

  const [checkPermission] = useCheckAllPermissionAndRequestPermission();
  const { onApplyReportDeviceData } = useDeviceDataReport();

  const [autoWithhold, setAutoWithhold] = useState<boolean>(
    BaseInfoManager.context.baseModel.autoWithholdClabePageDefaultValue,
  );
  const bankListRef = useRef<EvidenceVOSpace.BankItemDataType[]>([]);

  let submitCount = useRef<number>(0).current;

  let requireWithholdAuthorize = useRef<boolean>(false).current;

  const onOpenAutoWithhold = useCallback(() => {
    TrackEvent.trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_CLABE,
        e: HitPointEnumsSpace.EEventKey.BTN_SELECT_AUTODEBIT,
      },
      '1',
    );
    setAutoWithhold(true);
  }, []);

  const onCloseAutoWithhold = useCallback(() => {
    TrackEvent.trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_CLABE,
        e: HitPointEnumsSpace.EEventKey.BTN_SELECT_AUTODEBIT,
      },
      '0',
    );
    setAutoWithhold(false);
  }, []);

  const onSetIsSelfBankCard = useCallback(() => {
    restAction.onChageIsSelfBankCard('YES');
  }, []);

  const onSetIsNotSelfBankCard = useCallback(() => {
    restAction.onChageIsSelfBankCard('NO');
  }, []);

  /** 初始化方法 */
  useOnInit({
    callback: async (refersh: boolean = false) => {
      if (pageKey) {
        return
      }
      BaseInfoManager.changeLoadingModalVisible(true);
      if (!(await onGetBankList())) {
      }
      const { hasApplyOrderId, isSupplyUnCompleted, isLoanFailNeedSupply } =
        UserInfoManager.context.userModel;
      if (hasApplyOrderId) {
        if (!(await onGetSupplyInfo())) {
        }
      }
      if (!refersh) {
        if (isSupplyUnCompleted && isLoanFailNeedSupply) {
          modalDataStoreInstance.openModal({
            key: ModalList.INFO_PROMPT_CONFIRM,
            imageKey: '_infoIcon',
            i18nKey: 'clabeString.clabe_supply_tips',
            confirmBtnName: 'btnString.OK',
            isBackdropClose: false,
            confirmBtnCallback: () => { },
          });
        }
      }
      BaseInfoManager.changeLoadingModalVisible(false);
    },
    pageKey: HitPointEnumsSpace.EPageKey.P_CLABE,
  });

  /** 按钮disabled状态计算 */
  const computedButtonDisable = useMemo(() => {
    if (isSelfBankCard === 'NO') {
      return !(
        isStringExist(bankName) &&
        (isStringExist(fatherName) || isStringExist(motherName)) &&
        isStringExist(name) &&
        isStringExist(curp) &&
        isStringExist(relationship)
      );
    } else {
      return !isStringExist(bankName);
    }
  }, [bankName, clabe, curp, fatherName, motherName, name, relationship, isSelfBankCard]);

  /** 自动填充bankName */
  const onChangeClabe = (clabe: string) => {
    setState((preState: IState): IState => {
      return {
        ...preState,
        clabe: String(clabe).replace(/ /g, ''),
      };
    });
    renderErrorTips(clabe);
  };

  /** 校验clabe是否在支持的银行列表中 */
  const verifyClabeCodeSupport = (clabe: string) => {
    if (clabe.length < 3) {
      return true;
    }
    let _clabe = clabe.replace(/\s/g, '');
    let firstThreeChars = _clabe.substring(0, 3);
    return bankListRef.current?.some(bank => {
      return bank?.bankCode?.substring(bank?.bankCode?.length - 3) === firstThreeChars;
    });
  };

  /** 错误规则计算 */
  const renderErrorTips = (clabe: string) => {
    let _clabe = clabe.replace(/\s/g, '');
    let firstThreeChars = _clabe.substring(0, 3);
    // clabe 是否是支持的银行
    let isBankSupport = true;
    let isClabeAvaliable = true;
    if (_clabe.length >= 3 && _clabe.length < 18) {
      isBankSupport = verifyClabeCodeSupport(_clabe);
      // isBankSupport && restAction.bankNameRef.current?.clearErrorStatus();
    }
    if (_clabe.length === 18) {
      isClabeAvaliable = verifyClabe(_clabe);
    }

    // 18 位且格式合法
    if (_clabe.length === 18 && isBankSupport && isClabeAvaliable) {
      // restAction.bankNameRef.current?.clearErrorStatus();
      const bankData = bankListRef.current?.find(bank => {
        return bank?.bankCode?.substring(bank?.bankCode?.length - 3) === firstThreeChars;
      });
      setState((preState: IState): IState => {
        return {
          ...preState,
          bankCode: bankData?.bankCode || '',
          bankName: bankData?.bankName || '',
        };
      });
      return;
    }

    if (_clabe.length === 18 && !isClabeAvaliable) {
      restAction.clabeInputRef?.current?.emitErrorStatus(t('clabe_input_error_tips'));
    } else if (_clabe.length >= 3 && !isBankSupport) {
      restAction.clabeInputRef?.current?.emitErrorStatus(
        t('clabe_input_error_banklist_tips'),
        t('clabe_input_error_banklist_tips_link'),
        onGoBankListPage,
      );
    } else if (!_clabe?.length) {
      restAction.clabeInputRef?.current?.emitErrorStatus(t('verificationString.required'));
    } else {
      restAction.clabeInputRef?.current?.clearErrorStatus();
    }
  };

  /** 获取银行列表数据 */
  const onGetBankList = () => {
    return new Promise(async resolve => {
      let result = await fetchGetBankList();

      if (result?.code === BaseEnumsSpace.ENetworkStatus.SUCCESS) {
        bankListRef.current = result?.data?.bankList;
        startTransition(() => {
          setState((preState: IState): IState => {
            return {
              ...preState,
              bankList: result?.data?.bankList,
            };
          });
        });
        resolve(true);
      }
      resolve(false);
    });
  };

  /** 获取银行列表数据 */
  const onGetSupplyInfo = () => {
    return new Promise(async resolve => {
      let result = await fetchBankCardDefaultText();

      if (result?.code === BaseEnumsSpace.ENetworkStatus.SUCCESS) {
        if (result?.data) {
          setAutoWithhold(result?.data?.withholdAuthorizeStatus === 'YES');
          startTransition(() => {
            setState((preState: IState): IState => {
              return {
                ...preState,
                oldBankCode: result?.data?.bankCode,
                bankCode: result?.data?.bankCode,
                clabe: result?.data?.cardNo,
                bankName: result?.data?.bankName,
                isSelfBankCard: result?.data?.isSelf,
                isPersonal: result?.data?.isSelf === 'NO',
                fatherName: result?.data?.othersFatherName,
                motherName: result?.data?.othersMotherName,
                curp: result?.data?.othersCurpNumber,
                name: result?.data?.othersName,
                relationship: result?.data?.othersRelation,
                message: result?.data?.message,
              };
            });
          });
        }
        resolve(true);
      }
      resolve(false);
    });
  };

  /** 上传银行卡号信息 */
  const onSendBankCardInfo = async (): Promise<boolean> => {
    let withholdAuthorizeStatus = BaseInfoManager.context.baseModel.isWithholdSwitch
      ? autoWithhold && !isPersonal
        ? 'YES'
        : 'NO'
      : 'NO';
    let result = await fetchGetBankCardInfo({
      bankCode: bankCode,
      bankName: bankName,
      cardNo: clabe.replace(/\s/g, ''),
      type: 'clabe',
      isSelf: isSelfBankCard,
      othersFatherName: fatherName,
      othersMotherName: motherName,
      othersName: name,
      othersCurpNumber: curp,
      othersRelation: relationship,
      withholdAuthorizeStatus,
    });
    if (result?.code == BaseEnumsSpace.ENetworkStatus.SUCCESS) {
      requireWithholdAuthorize = result?.data.requireWithholdAuthorize === 'YES';
    }

    // BaseInfoManager.changeLoadingModalVisible(false);

    return result?.code == BaseEnumsSpace.ENetworkStatus.SUCCESS;
  };

  /** 上传银行卡号补件信息 */
  const onSendBankCardSupplyInfo = async (): Promise<boolean> => {
    let withholdAuthorizeStatus = BaseInfoManager.context.baseModel.isWithholdSwitch
      ? autoWithhold && !isPersonal
        ? 'YES'
        : 'NO'
      : 'NO';

    let result = await fetchBankCardSupplySubmit({
      bankCode: bankCode,
      bankName: bankName,
      cardNo: clabe.replace(/\s/g, ''),
      type: 'clabe',
      isSelf: isSelfBankCard,
      othersFatherName: fatherName,
      othersMotherName: motherName,
      othersName: name,
      othersCurpNumber: curp,
      othersRelation: relationship,
      withholdAuthorizeStatus,
    });

    // BaseInfoManager.changeLoadingModalVisible(false);
    if (result?.code === BaseEnumsSpace.ENetworkStatus.SUCCESS) {
      requireWithholdAuthorize = result?.data.requireWithholdAuthorize === 'YES';
      if (bankCode !== oldBankCode) {
        // 银行卡变更缓存
        KVManager.action.setBoolean(
          ELocalKey.CLABE_SUPPLY_UPDATED + '_' + UserInfoManager.context.userModel.applyOrderId,
          true,
        );
      }
      return true;
    }
    return false;
  };

  // /** 创建申请单 */
  // const onCreateApply = (): Promise<string> => {
  //   return new Promise(async resolve => {
  //     let result = await fetchCreateApply();

  //     if (result?.code === BaseEnumsSpace.ENetworkStatus.SUCCESS) {
  //       resolve(result.data);
  //     }
  //     resolve('');
  //   });
  // };

  /** 获取默认银行卡信息 */
  const getBankData = async () => {
    const result = await fetchDefaultBankCardInfo();
    if (result.code != 0) {
      nav.navigationGoBack();
      return false;
    }

    startTransition(() => {
      setState((preState: IState): IState => {
        return {
          ...preState,
          bankCode: result?.data?.bankCode,
          oldBankCode: result?.data?.bankCode,
          clabe: result.data.cardNo,
          bankName: result.data.bankName,
          isSelfBankCard: result?.data?.isSelf === 'NO' ? 'No' : 'Sí',
          isPersonal: result?.data?.isSelf === 'NO',
          fatherName: result.data.othersFatherName,
          motherName: result.data.othersMotherName,
          curp: result.data.othersCurpNumber,
          name: result.data.othersName,
          relationship: result.data.othersRelation,
        };
      });
      restAction.clabeInputRef?.current?.clearErrorStatus();
    });

    return new Promise(resolve => {
      BaseInfoManager.changeLoadingModalVisible(false);
      resolve(result.code === 0);
    });
  };

  /** 跳转查看银行列表页面 */
  const onGoBankListPage = () => {
    nav.navigate(RouterConfig.BANK_LIST as any, { bankList });
  };

  /** 进行下一步按钮 */
  const handleNext = async () => {
    const clabeLen = clabe.length;
    if (!restAction.checkCanableSubmit()) {
      Toast(t('peso_data_verify_pls_fill_all_options'));
      return;
    }

    /** 提交前校验 */
    if (submitCount === 0) {
      if (clabeLen != 18) {
        restAction.clabeInputRef?.current?.emitErrorStatus(t('clabe_length_error_match_first'));
        submitCount++;
        return;
      }
    } else {
      if (!verifyClabe(clabe)) {
        modalDataStoreInstance.openModal({
          key: ModalList.INFO_PROMPT_CONFIRM,
          i18nKey: 'clabeString.clabe_length_error_match_second',
          imageKey: '_modalDangerIcon',
          confirmBtnName: 'btnString.OK',
          isBackdropClose: false,
        });
        return;
      }
    }
    if (!verifyClabe(clabe)) {
      submitCount++;
      return;
    }
    if (!((await checkPermission()) === 'agree')) {
      return;
    }

    onOpenConfirmClabeModal();
  };

  /** 提交前确认clabe弹窗 */
  const onOpenConfirmClabeModal = () => {
    let modalI18nKey = 'clabeString.confrimSendApplyOrder';
    if (UserInfoManager.context.userModel.hasApplyOrderId) {
      modalI18nKey =
        bankCode === oldBankCode
          ? 'clabeString.confrimSendApplyOrderIsNotModifyClabe'
          : 'clabeString.confrimSendApplyOrderIsModifyClabe';
    }

    modalDataStoreInstance.openModal({
      key: ModalList.COMFIRM_CLABE_SEND_LOAN,
      i18nKey: modalI18nKey,
      isBackdropClose: true,
      confirmBtnName: 'btnString.confirm',
      cancelBtnName: 'btnString.cancel',
      extra: {
        cardNo: clabe.replace(/\s/g, ''),
        bankName: bankName,
      },
      confirmBtnCallback: async () => {
        BaseInfoManager.changeLoadingModalVisible(true);
        submitCardInfoCallback();
      },
      cancelBtnCallback: () => { },
    });
  };

  /** 提交银行卡回调 */
  const submitCardInfoCallback = async () => {
    const {
      isSupplyCompleted,
      isSupplyUnCompleted,
      isLoanFailNeedSupply,
      isHasActiveOrder,
      isUserTypeNew,
      isNeedSupply,
      hasApplyOrderId,
    } = UserInfoManager.context.userModel;
    if (hasApplyOrderId) {
      // 确认用信和银行卡列表页场景只需要上传银行卡号
      if (await onSendBankCardSupplyInfo() && !pageKey) {
        /** OCR补件场景, 创建订单 */
        if (!isLoanFailNeedSupply && isUserTypeNew && !isHasActiveOrder) {
          let applyId = await onFirstCancelConfirmApply();
          if (applyId) {
            onApplyReportDeviceData(applyId);
            restAction.clabeInputRef.current?.blur();
            TrackEvent.uploadEventLog();
          }
        }
        if (await UserInfoManager.updateUserState()) {
          /** 自动代扣 */
          // if (
          //   requireWithholdAuthorize &&
          //   BaseInfoManager.context.baseModel.isWithholdSwitch
          // ) {
          //   nav.resetRouteNavigate(RouterConfig.AUTOMATIC_WITHHOLD as any);
          // } else {
          nav.nextToTopRouter(RouterConfig.CLABE_BASIC_INFO);
          // }
        }
      }
    } else {
      if (await onSendBankCardInfo() && !pageKey) {
        let applyId = await onFirstCancelConfirmApply();
        if (applyId) {
          onApplyReportDeviceData(applyId);
          restAction.clabeInputRef.current?.blur();
          TrackEvent.uploadEventLog();
        }
        if (await UserInfoManager.updateUserState()) {
          // 增信
          if (BaseInfoManager.context.baseModel.isEnhanceCredit) {
            nav.resetRouteNavigate(RouterConfig.INCREASE_CREDIT as any);
          } else {
            /** 自动代扣 */
            // if (
            //   requireWithholdAuthorize &&
            //   BaseInfoManager.context.baseModel.isWithholdSwitch
            // ) {
            //   nav.resetRouteNavigate(RouterConfig.AUTOMATIC_WITHHOLD as any);
            // } else {
            nav.nextToTopRouter(RouterConfig.CLABE_BASIC_INFO);
            // }
          }
        }
      }
    }
    BaseInfoManager.changeLoadingModalVisible(false);
    pageKey && nav.navigationGoBack();
  };

  /** 点击返回 */
  const handleGoBack = () => {
    if (UserInfoManager.context.userModel.isNeedSupply) {
      return true;
    } else {
      modalDataStoreInstance.openModal({
        key: ModalList.INFO_PROMPT_CONFIRM,
        i18nKey: 'basicInfoString.get_loan',
        imageKey: '_modalCry',
        confirmBtnName: 'btnString.continueOn',
        cancelBtnName: 'btnString.exit',
        isBackdropClose: false,
        confirmBtnCallback: async () => {
          TrackEvent.trackCommonEvent(
            {
              p: HitPointEnumsSpace.EPageKey.P_INTERCEPT_POPUP,
              e: HitPointEnumsSpace.EEventKey.BTN_POPUP,
            },
            '1',
          );
          await TrackEvent.uploadEventLog();
        },
        cancelBtnCallback: async () => {
          TrackEvent.trackCommonEvent(
            {
              p: HitPointEnumsSpace.EPageKey.P_INTERCEPT_POPUP,
              e: HitPointEnumsSpace.EEventKey.BTN_POPUP,
            },
            '0',
          );
          nav.navigationGoBack();
        },
      });
      return true;
    }
  };

  /** 首贷取消补件后主动提交申请 */
  const onFirstCancelConfirmApply = async (): Promise<string> => {
    let result = await firstCancelConfirmApply();
    if (result.code !== 0) {
      return '';
    }
    UserInfoManager.updateApplyId(result.data);
    return result.data;
  };

  /** 跳转到自动代扣协议 */
  const openAutoWithhold = async () => {
    TrackEvent.trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_CLABE,
        e: HitPointEnumsSpace.EEventKey.BTN_NEGOTIATE_CHECK,
      },
      '1',
    );
    let result = await fetchWithholdContract({
      cardNo: clabe,
      bankName: bankName,
    });
    if (result.code === BaseEnumsSpace.ENetworkStatus.SUCCESS) {
      nav.navigate(RouterConfig.AUTOMATIC_WITHHOLD_PROTOCOL as any, {
        html: CryptoJS.enc.Base64.parse(String(result.data)).toString(CryptoJS.enc.Utf8),
        currentRoute: RouterConfig.CLABE_BASIC_INFO,
        acceptHandle: onOpenAutoWithhold,
        rejectHandle: onCloseAutoWithhold,
      });
    }

    return result?.code === BaseEnumsSpace.ENetworkStatus.SUCCESS;
  };

  const onShowCurpHelpModal = () => {
    setCurpHelpModalVisible(true);
  };

  const onCloseCurpHelpModal = () => {
    setCurpHelpModalVisible(false);
  };

  return {
    ...restAction,
    ...state,
    isPending,
    curpHelpModalVisible,
    computedButtonDisable,
    autoWithhold,
    onOpenAutoWithhold,
    onCloseAutoWithhold,
    handleNext,
    handleGoBack,
    onGoBankListPage,
    openAutoWithhold,
    onSetIsSelfBankCard,
    onSetIsNotSelfBankCard,
    verifyClabeCodeSupport,
    onShowCurpHelpModal,
    onCloseCurpHelpModal,
    onChangeClabe,
  };
}
