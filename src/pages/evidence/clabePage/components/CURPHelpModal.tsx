/**
 * CURP帮助弹窗
 */
import { ActionSheet, Image } from '@/components';
import React, { useEffect, useState } from 'react';
import { Dimensions, ScrollView } from 'react-native';

interface CURPHelpModalProps {
  visible: boolean;
  onClose: () => void;
}
export default function CURPHelpModal({ visible, onClose }: CURPHelpModalProps) {
  const [curpHelpModalVisible, setCurpHelpModalVisible] = useState(visible);
  useEffect(() => {
    setCurpHelpModalVisible(visible);
  }, [visible]);
  const closeModal = () => {
    setCurpHelpModalVisible(false);
    onClose();
  };

  return (
    <ActionSheet visible={curpHelpModalVisible} onClose={closeModal}>
      <ScrollView
        style={{
          backgroundColor: 'background-color-0',
          maxHeight: Dimensions.get('window').height * 0.8,
        }}
        contentContainerStyle={{
          padding: 16,
          alignItems: 'center',
        }}
        showsVerticalScrollIndicator={false}>
        <Image name="_curpHelpIcon" />
      </ScrollView>
    </ActionSheet>
  );
}
