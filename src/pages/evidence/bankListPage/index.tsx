import { ReactElement, useCallback, useState, useTransition } from 'react';
import { ScreenProps } from '@/types';
import AtoZList from 'react-native-atoz-list';
import { Layout, Text, TopNavigation, View } from '@/components';
import { EvidenceVOSpace } from '@/types';
import { HitPointEnumsSpace } from '@/enums';
import { useOnInit } from '@/hooks';

export default ({
  route,
}: ScreenProps<{
  bankList: EvidenceVOSpace.BankItemDataType[];
}>): ReactElement => {
  const [data, setData] = useState<any>({
    A: [],
    B: [],
    C: [],
    D: [],
    E: [],
    F: [],
    G: [],
    H: [],
    I: [],
    J: [],
    K: [],
    L: [],
    M: [],
    N: [],
    O: [],
    P: [],
    Q: [],
    R: [],
    S: [],
    T: [],
    U: [],
    V: [],
    W: [],
    X: [],
    Y: [],
    Z: [],
  });
  const [isPending, startTransition] = useTransition();

  /** 初始化方法 */
  useOnInit({
    callback: async () => {
      startTransition(() => {
        const bankList = route.params.bankList || [];

        let result = {
          A: [],
          B: [],
          C: [],
          D: [],
          E: [],
          F: [],
          G: [],
          H: [],
          I: [],
          J: [],
          K: [],
          L: [],
          M: [],
          N: [],
          O: [],
          P: [],
          Q: [],
          R: [],
          S: [],
          T: [],
          U: [],
          V: [],
          W: [],
          X: [],
          Y: [],
          Z: [],
        };

        bankList.forEach((bank: EvidenceVOSpace.BankItemDataType) => {
          (result as any)[`${bank.bankName.charAt(0)}`].push(bank);
        });
        setData(result);
      });
    },
    pageKey: HitPointEnumsSpace.EPageKey.P_BANK_LIST,
  });

  const renderCellComponent = useCallback((item: EvidenceVOSpace.BankItemDataType) => {
    return <Text margin="8 0 8 8" textContent={item.bankName} />;
  }, []);

  const renderSectionComponent = useCallback((section: { sectionId: string }) => {
    return <Text margin="8 0 8 8" textContent={section.sectionId} bold={'bold'} />;
  }, []);

  if (isPending) {
    return <></>;
  }

  return (
    <Layout pLevel="0" level="0">
      <TopNavigation titleKey={'clabeString.bankList'} />
      <View style={{ flex: 1 }}>
        <AtoZList
          data={data}
          renderCell={renderCellComponent}
          renderSection={renderSectionComponent}
          sectionHeaderHeight={20}
          cellHeight={60}
        />
      </View>
    </Layout>
  );
};
