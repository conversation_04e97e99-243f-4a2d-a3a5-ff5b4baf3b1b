import { BaseInputRefType, SelectRefType } from '@/components';
import {
  IState,
  EProfesional,
  EMaritalStatus,
  EChildNumber,
  EEducationLeve,
  ERelationship,
  ESalary,
  ECashLoaned,
  ELoanUse,
  ESalaryPeriod,
} from './type';
import { useCallback, useRef, useState, useTransition } from 'react';
import {
  Log,
  isNameValid,
  isStringExist,
  verifyAge,
  verifyEmail,
  verifyPhoneNumber,
} from '@/utils';
import { BaseInfoManager, UserInfoManager } from '@/managers';
import { Toast } from '@/nativeComponents';
import { Strings, useNameSpace } from '@/i18n';

export default function useAction() {
  const t = useNameSpace('').t;
  let scrollRef = useRef<any>(null);
  const emailRef = useRef<BaseInputRefType>(null);
  const whatsAppRef = useRef<BaseInputRefType>(null);
  const postCodeRef = useRef<BaseInputRefType>(null);
  const stateRef = useRef<SelectRefType & BaseInputRefType>(null);
  const cityRef = useRef<SelectRefType & BaseInputRefType>(null);
  const educationRef = useRef<BaseInputRefType>(null);
  const profesionalRef = useRef<BaseInputRefType>(null);
  const monthlySalaryRef = useRef<BaseInputRefType>(null);
  const salaryPeriodRef = useRef<BaseInputRefType>(null);
  const salaryDateRef = useRef<BaseInputRefType>(null);
  const maritalRef = useRef<BaseInputRefType>(null);
  const childNumberRef = useRef<BaseInputRefType>(null);
  const loanUseRef = useRef<BaseInputRefType>(null);
  const hasLoanRecordRef = useRef<BaseInputRefType>(null);
  const phoneNumberRef = useRef<BaseInputRefType>(null);
  const relationshipRef = useRef<BaseInputRefType>(null);
  const ageRef = useRef<BaseInputRefType>(null);
  const fullNameRef = useRef<BaseInputRefType>(null);
  const [_state, setState] = useState<IState>({
    googleServerIsUsage: false,
    stateList: [],
    cityList: [],
    pinCodeList: [],
    basicInfo: {
      city: '',
      email: '',
      gpEmail: '',
      pincode: '',
      state: '',
      whatsApp: UserInfoManager.context.userModel.mobile,
      age: '',
      educationLevel: '',
      childNum: '',
      cashLoaned: '',
      maritalStatus: '',
      jobType: '',
      salaryPeriod: '',
      loanUse: '',
      couponMsg: '',
      salaryDate: '',
    },
    contact: {
      userReferences: [
        {
          fullName: '',
          mobile: '',
          relation: '',
        },
      ],
    },
    professional: {
      salary: '',
    },
  });

  /** 检查表单填写状态, 进行一定的UX反馈 */
  const checkCanableSubmit = () => {
    const {
      basicInfo: {
        email,
        gpEmail,
        whatsApp,
        pincode,
        state,
        city,
        educationLevel,
        jobType,
        maritalStatus,
        childNum,
        cashLoaned,
        age,
        salaryPeriod,
        salaryDate,
        loanUse,
      },
      professional: { salary },
      contact: { userReferences },
    } = _state;
    let flag = true;

    if (!isStringExist(whatsApp)) {
      whatsAppRef.current?.emitErrorStatus(t('verificationString.required'));
      flag = false;
    } else if (!verifyPhoneNumber(whatsApp)) {
      whatsAppRef.current?.emitErrorStatus(
        t(Strings.verificationString.failed, {
          name: t(Strings.basicInfoString.whatsapp),
        }),
      );
      flag = false;
    }

    if (!isStringExist(email) && !isStringExist(gpEmail)) {
      emailRef.current?.emitErrorStatus(t('verificationString.required'));
      flag = false;
    } else if (!(verifyEmail(email) || verifyEmail(gpEmail))) {
      emailRef.current?.emitErrorStatus(
        t('verificationString.failed', { name: t('basicInfoString.email') }),
      );
      flag = false;
    }

    // if (!isStringExist(pincode)) {
    //   postCodeRef.current?.emitErrorStatus(t('verificationString.required'));
    //   flag = false;
    // }

    if (!isStringExist(state)) {
      stateRef.current?.emitErrorStatus(t('verificationString.required'));
      flag = false;
    }

    if (!isStringExist(city)) {
      cityRef.current?.emitErrorStatus(t('verificationString.required'));
      flag = false;
    }

    // if (!verifyAge(String(age))) {
    //   ageRef.current?.emitErrorStatus(t('verificationString.required'));
    //   flag = false;
    // }

    if (
      !(BaseInfoManager.context.baseModel.isEducationLevelOpen
        ? isStringExist(educationLevel)
        : true)
    ) {
      educationRef.current?.emitErrorStatus(t('verificationString.required'));
      flag = false;
    }

    if (!(BaseInfoManager.context.baseModel.isJobTypeOpen ? isStringExist(jobType) : true)) {
      profesionalRef.current?.emitErrorStatus(t('verificationString.required'));
      flag = false;
    }

    if (!isStringExist(salary)) {
      monthlySalaryRef.current?.emitErrorStatus(t('verificationString.required'));
      flag = false;
    }

    if (
      !(BaseInfoManager.context.baseModel.isSalaryPeridOpen ? isStringExist(salaryPeriod) : true)
    ) {
      salaryPeriodRef.current?.emitErrorStatus(t('verificationString.required'));
      flag = false;
    }

    if (
      !(BaseInfoManager.context.baseModel.nextSalaryDateSwitch ? isStringExist(salaryDate) : true)
    ) {
      salaryDateRef.current?.emitErrorStatus(t('verificationString.required'));
      flag = false;
    }

    if (
      !(BaseInfoManager.context.baseModel.isMaritalStatusOpen ? isStringExist(maritalStatus) : true)
    ) {
      maritalRef.current?.emitErrorStatus(t('verificationString.required'));
      flag = false;
    }

    if (!(BaseInfoManager.context.baseModel.isChildNumOpen ? isStringExist(childNum) : true)) {
      childNumberRef.current?.emitErrorStatus(t('verificationString.required'));
      flag = false;
    }

    if (!(BaseInfoManager.context.baseModel.isCashLoanedOpen ? isStringExist(cashLoaned) : true)) {
      hasLoanRecordRef.current?.emitErrorStatus(t('verificationString.required'));
      flag = false;
    }

    if (!(BaseInfoManager.context.baseModel.isLoanUseOpen ? isStringExist(loanUse) : true)) {
      loanUseRef.current?.emitErrorStatus(t('verificationString.required'));
      flag = false;
    }

    if (!verifyPhoneNumber(userReferences[0].mobile)) {
      phoneNumberRef.current?.emitErrorStatus(t('verificationString.required'));
      flag = false;
    } else if (!verifyPhoneNumber(userReferences[0].mobile)) {
      phoneNumberRef.current?.emitErrorStatus(
        t('verificationString.failed', {
          name: t('basicInfoString.phoneNumber'),
        }),
      );
      flag = false;
    }

    if (!isStringExist(userReferences[0].fullName)) {
      fullNameRef.current?.emitErrorStatus(t('verificationString.required'));
      flag = false;
    } else if (!isNameValid(userReferences[0].fullName)) {
      fullNameRef.current?.emitErrorStatus(
        t('verificationString.failed', {
          name: t('basicInfoString.name'),
        }),
      );
      flag = false;
    }

    if (!isStringExist(userReferences[0].relation)) {
      relationshipRef.current?.emitErrorStatus(t('verificationString.required'));
      flag = false;
    }
    return flag;
  };

  const checkContactIsRegisterSubmit = () => {
    const {
      contact: { userReferences },
    } = _state;
    let flag = true;
    if (userReferences[0].mobile === UserInfoManager.context.userModel.mobile) {
      phoneNumberRef.current?.emitErrorStatus(
        t('basicInfoString.select_contact_mobile_is_not_register'),
      );
      flag = false;
    }

    return flag;
  };

  const onSetPhoneNumber = (mobile: string) => {
    setState((preState: IState): IState => {
      return {
        ...preState,
        contact: {
          userReferences: [
            {
              ...preState.contact.userReferences[0],
              mobile,
            },
          ],
        },
      };
    });
  };

  const onChangeJobType = useCallback(
    (jobType: IState['basicInfo']['jobType']) => {
      setState((preState: IState): IState => {
        return {
          ...preState,
          basicInfo: {
            ...preState.basicInfo,
            jobType,
          },
        };
      });
    },
    [_state.basicInfo.jobType],
  );

  const onChangeSalaryPeriod = useCallback(
    (salaryPeriod: IState['basicInfo']['salaryPeriod']) => {
      setState((preState: IState): IState => {
        return {
          ...preState,
          basicInfo: {
            ...preState.basicInfo,
            salaryPeriod,
          },
        };
      });
    },
    [_state.basicInfo.salaryPeriod],
  );

  const onChangeLoanUse = useCallback(
    (loanUse: IState['basicInfo']['loanUse']) => {
      setState((preState: IState): IState => {
        return {
          ...preState,
          basicInfo: {
            ...preState.basicInfo,
            loanUse,
          },
        };
      });
    },
    [_state.basicInfo.loanUse],
  );

  const onChangeMaritalStatus = useCallback(
    (maritalStatus: IState['basicInfo']['maritalStatus']) => {
      setState((preState: IState): IState => {
        return {
          ...preState,
          basicInfo: {
            ...preState.basicInfo,
            maritalStatus,
          },
        };
      });
    },
    [_state.basicInfo.maritalStatus],
  );

  const onChangeAge = useCallback(
    (age: IState['basicInfo']['age']) => {
      setState((preState: IState): IState => {
        return {
          ...preState,
          basicInfo: {
            ...preState.basicInfo,
            age: String(age).trim(),
          },
        };
      });
    },
    [_state.basicInfo.age],
  );

  const onChangeName = (name: string) => {
    setState((preState: IState): IState => {
      return {
        ...preState,
        contact: {
          userReferences: [
            {
              ...preState.contact.userReferences[0],
              fullName: String(name).trim(),
            },
          ],
        },
      };
    });
  };

  const onChangeEmail = useCallback(
    (email: IState['basicInfo']['email']) => {
      setState((preState: IState): IState => {
        return {
          ...preState,
          basicInfo: {
            ...preState.basicInfo,
            gpEmail: '',
            email: String(email).trim(),
          },
        };
      });
    },
    [_state.basicInfo.email],
  );

  const onChangeGpEmail = useCallback(
    (gpEmail: IState['basicInfo']['gpEmail']) => {
      setState((preState: IState): IState => {
        return {
          ...preState,
          basicInfo: {
            ...preState.basicInfo,
            email: String(gpEmail).trim(),
            gpEmail,
          },
        };
      });
    },
    [_state.basicInfo.email],
  );

  const onChangeWhatsApp = useCallback(
    (whatsApp: IState['basicInfo']['whatsApp']) => {
      setState((preState: IState): IState => {
        return {
          ...preState,
          basicInfo: {
            ...preState.basicInfo,
            whatsApp: String(whatsApp).trim(),
          },
        };
      });
    },
    [_state.basicInfo.whatsApp],
  );

  const onChangePincode = useCallback(
    (pincode: IState['basicInfo']['pincode']) => {
      setState((preState: IState): IState => {
        return {
          ...preState,
          basicInfo: {
            ...preState.basicInfo,
            pincode,
          },
        };
      });
    },
    [_state.basicInfo.pincode],
  );

  const onChangeState = useCallback(
    (_state: IState['basicInfo']['state']) => {
      setState((preState: IState): IState => {
        return {
          ...preState,
          basicInfo: {
            ...preState.basicInfo,
            state: _state,
          },
        };
      });
    },
    [_state.basicInfo.state],
  );

  const onChangeCity = useCallback(
    (city: IState['basicInfo']['city']) => {
      setState((preState: IState): IState => {
        return {
          ...preState,
          basicInfo: {
            ...preState.basicInfo,
            city,
          },
        };
      });
    },
    [_state.basicInfo.city],
  );

  const onChangeSalary = useCallback(
    (salary: IState['professional']['salary']) => {
      setState((preState: IState): IState => {
        return {
          ...preState,
          professional: {
            ...preState.professional,
            salary,
          },
        };
      });
    },
    [_state.professional.salary],
  );

  const onChangeSalaryDate = useCallback(
    (salaryDate: IState['basicInfo']['salaryDate']) => {
      salaryDateRef.current?.clearErrorStatus();
      setState((preState: IState): IState => {
        return {
          ...preState,
          basicInfo: {
            ...preState.basicInfo,
            salaryDate,
          },
        };
      });
    },
    [_state.basicInfo.salaryDate],
  );

  const onChangeRelationship = (relation: string) => {
    setState((preState: IState): IState => {
      return {
        ...preState,
        contact: {
          userReferences: [
            {
              ...preState.contact.userReferences[0],
              relation,
            },
          ],
        },
      };
    });
  };

  const onChangeEducation = useCallback(
    (educationLevel: IState['basicInfo']['educationLevel']) => {
      setState((preState: IState): IState => {
        return {
          ...preState,
          basicInfo: {
            ...preState.basicInfo,
            educationLevel,
          },
        };
      });
    },
    [_state.basicInfo.educationLevel],
  );

  const onChangeChildNumber = useCallback(
    (childNum: IState['basicInfo']['childNum']) => {
      setState((preState: IState): IState => {
        return {
          ...preState,
          basicInfo: {
            ...preState.basicInfo,
            childNum,
          },
        };
      });
    },
    [_state.basicInfo.childNum],
  );

  const onChangeHasLoanRecord = useCallback(
    (cashLoaned: IState['basicInfo']['cashLoaned']) => {
      setState((preState: IState): IState => {
        return {
          ...preState,
          basicInfo: {
            ...preState.basicInfo,
            cashLoaned,
          },
        };
      });
    },
    [_state.basicInfo.cashLoaned],
  );

  /** 职业类型 */
  const profesionalSelectList: EProfesional[] = [
    EProfesional.PRIVATE_SECTOR_EMPLOYEE,
    EProfesional.PUBLIC_SECTOR_EMPLOYEE,
    EProfesional.COMPANY_OWNER,
    EProfesional.INFOMAL_ORTEMPORARY_EMPLOYMENT,
    EProfesional.SELF_EMPLOYED_OR_INDEPENDENT,
    EProfesional.HOUSEWIFE,
    EProfesional.STUDENT,
    EProfesional.RETIRED,
    EProfesional.MILITAR,
    EProfesional.UNEMPLOYED,
  ];

  /** 婚姻状态类型 */
  const maritalSelectList: EMaritalStatus[] = [
    EMaritalStatus.SINGLE,
    EMaritalStatus.MARRIED,
    EMaritalStatus.DIVORCED,
    EMaritalStatus.OTHER,
  ];

  /** 孩子数量 */
  const childNumberList: EChildNumber[] = [
    EChildNumber.ZERO,
    EChildNumber.ONE,
    EChildNumber.TWO,
    EChildNumber.THREE_MORE,
  ];

  /** 教育状况 */
  const educationSelectList: EEducationLeve[] = [
    EEducationLeve.MASTER_OR_PHD,
    EEducationLeve.UNIVERSITY_OR_TECHNOLOGICAL_INSTITUTE,
    EEducationLeve.BACHILLERATO_O_PROFECIONAL_TECNICO,
    EEducationLeve.SECONDARY,
    EEducationLeve.PRIMARY,
    EEducationLeve.SIN_ESTUDIOS,
  ];

  /** 关系 */
  const relationshipSelectList: ERelationship[] = [
    ERelationship.PARENTS,
    ERelationship.COUPLE,
    ERelationship.CHILDREN,
    ERelationship.OTHER,
  ];

  /** 薪资范围 */
  const salarySelectList: ESalary[] = [
    ESalary.MXN0_3000,
    ESalary.MXN6000_9000,
    ESalary.MXN9000_12000,
    ESalary.MXN12000_15000,
    ESalary.MXN15000_more,
  ];

  /** 贷款状态 */
  const cashLoanedList: ECashLoaned[] = [ECashLoaned.YES, ECashLoaned.NO];

  /** 薪资发放周期 */
  const salaryPeriodList: ESalaryPeriod[] = [
    ESalaryPeriod.NONE,
    ESalaryPeriod.DAY,
    ESalaryPeriod.ONCE_WEEK,
    ESalaryPeriod.TWICE_WEEK,
    ESalaryPeriod.MONTH,
    ESalaryPeriod.OTHER,
  ];

  /** 贷款用途 */
  const loanUseList: ELoanUse[] = [ELoanUse.BUSSINESS, ELoanUse.PERSONAL];

  return {
    _state,
    setState,
    onSetPhoneNumber,
    onChangeJobType,
    onChangeMaritalStatus,
    onChangeSalaryPeriod,
    onChangeLoanUse,
    onChangeAge,
    onChangeName,
    onChangeEmail,
    onChangeGpEmail,
    onChangeWhatsApp,
    onChangePincode,
    onChangeState,
    onChangeCity,
    onChangeSalary,
    onChangeSalaryDate,
    onChangeEducation,
    onChangeChildNumber,
    onChangeHasLoanRecord,
    onChangeRelationship,
    profesionalSelectList,
    maritalSelectList,
    childNumberList,
    educationSelectList,
    relationshipSelectList,
    salarySelectList,
    cashLoanedList,
    loanUseList,
    salaryPeriodList,
    scrollRef,
    emailRef,
    whatsAppRef,
    ageRef,
    fullNameRef,
    postCodeRef,
    stateRef,
    cityRef,
    educationRef,
    profesionalRef,
    monthlySalaryRef,
    maritalRef,
    loanUseRef,
    salaryPeriodRef,
    salaryDateRef,
    childNumberRef,
    hasLoanRecordRef,
    phoneNumberRef,
    relationshipRef,
    checkCanableSubmit,
    checkContactIsRegisterSubmit,
  };
}
