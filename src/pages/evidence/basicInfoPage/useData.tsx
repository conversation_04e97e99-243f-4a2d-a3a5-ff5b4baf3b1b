import { BaseEnumsSpace, HitPointEnumsSpace, UserEnumsSpace } from '@/enums';
import {
  useCheckAllPermissionAndRequestPermission,
  useContactPicker,
  useGmailPicker,
  useOnInit,
} from '@/hooks';
import { useNameSpace } from '@/i18n';
import { BaseInfoManager, ModalList, UserInfoManager, modalDataStoreInstance } from '@/managers';
import { Toast } from '@/nativeComponents';
import {
  fetchBasicInfo,
  fetchContact,
  fetchGetAddress,
  fetchRKQuestionnaireEnterLabel,
  fetchSaveBasicInfo,
  fetchSaveContactInfo,
  fetchSaveProfessionalInfo,
  firstCancelConfirmApply,
} from '@/server';
import {
  IEventPoint,
  trackCommonEvent,
  trackInputEventEnd,
  trackInputEventStart,
} from '@/trackEvent';
import { TrackEvent, isStringExist, log, nav, verifyEmail, verifyPhoneNumber } from '@/utils';
import { useCallback, useEffect, useRef, useTransition } from 'react';
import { BackHandler } from 'react-native';
import { NativeModules } from '../../../native/_export_';
import { EContactPickerEventType, IContactPickerData } from '../../../native/module/contactPicker';
import { EGmailPickerEventType, IGmailPickerData } from '../../../native/module/gmailPicker';
import { AddressType, ECashLoaned, IBasicInfo, IContact, IProfessional, IState } from './type';
import useAction from './useAction';
const { deviceInfoData } = NativeModules;

export default function useData() {
  const t = useNameSpace('').t;
  const [checkPermission] = useCheckAllPermissionAndRequestPermission();
  const [isPending, startTransition] = useTransition();
  const { _state, setState, ...restAction } = useAction();
  const dataRef = useRef<IState>(_state);

  /** 初始化方法 */
  useOnInit({
    pageKey: HitPointEnumsSpace.EPageKey.P_BASE_INFO,
    callback: async () => {
      BaseInfoManager.changeLoadingModalVisible(true);

      onGetGpServiceStatus();

      // if (!(await BaseInfoManager.updateAppConfig())) {
      //   BaseInfoManager.changeLoadingModalVisible(false);
      //   return;
      // }

      if (!(await onGetAddress({}))) {
        BaseInfoManager.changeLoadingModalVisible(false);
        return;
      }

      // if (UserInfoManager.context.userModel.hasApplyOrderId) {
      if (!(await onGetBasicInfo())) {
        BaseInfoManager.changeLoadingModalVisible(false);
        return;
      }

      if (!(await onGetContact())) {
        BaseInfoManager.changeLoadingModalVisible(false);
        return;
      }
      // }

      BaseInfoManager.changeLoadingModalVisible(false);
    },
    isBackAutoRefresh: false,
    isActivityAutoRefresh: false,
  });

  useEffect(() => {
    dataRef.current = _state;
  }, [_state]);

  /** 获取谷歌服务状态 */
  const onGetGpServiceStatus = async () => {
    let googleServerIsUsage = await deviceInfoData.getGPServerEnabled();
    setState(
      (preState: IState): IState => ({
        ...preState,
        googleServerIsUsage,
      }),
    );
  };

  /** 上传基础信息 */
  const onSaveBasicInfo = async (params: IBasicInfo) => {
    const { cashLoaned } = params;
    let _cashLoaned =
      cashLoaned === ECashLoaned.YES
        ? UserEnumsSpace.EStatusType.YES
        : cashLoaned === ECashLoaned.NO
        ? UserEnumsSpace.EStatusType.NO
        : '';
    let result = await fetchSaveBasicInfo({
      ...params,
      ...{ cashLoaned: _cashLoaned },
    });

    return result?.code === BaseEnumsSpace.ENetworkStatus.SUCCESS;
  };

  /** 上传职业信息 */
  const onSaveProfessionalInfo = async (params: IProfessional) => {
    let result = await fetchSaveProfessionalInfo(params);

    return result?.code === BaseEnumsSpace.ENetworkStatus.SUCCESS;
  };

  /** 上传联系人信息 */
  const onSaveContactInfo = async (params: IContact) => {
    let result = await fetchSaveContactInfo(params);

    return result?.code === BaseEnumsSpace.ENetworkStatus.SUCCESS;
  };

  /** 下一步操作 */
  const handleNext = async () => {
    const { professional, basicInfo, contact } = _state;

    restAction.checkContactIsRegisterSubmit();

    if (!restAction.checkCanableSubmit()) {
      Toast(t('basicInfoString.peso_data_verify_pls_fill_all_options'));
      return;
    }

    if (!((await checkPermission()) === 'agree')) {
      return;
    }

    BaseInfoManager.changeLoadingModalVisible(true);
    if (!(await onSaveBasicInfo(basicInfo))) {
      BaseInfoManager.changeLoadingModalVisible(false);
      return;
    }

    if (!(await onSaveProfessionalInfo(professional))) {
      BaseInfoManager.changeLoadingModalVisible(false);
      return;
    }

    if (!(await onSaveContactInfo(contact))) {
      BaseInfoManager.changeLoadingModalVisible(false);
      return;
    }

    if (
      UserInfoManager.context.userModel.isUserTypeNew &&
      !UserInfoManager.context.userModel.isNeedAccoutBind
    ) {
      // const result = await fetchRKQuestionnaireEnterLabel();
      // if (result.code !== 0) {
      //   // 失败上报获取风控问卷结果失败埋点
      //   TrackEvent.trackCommonEvent(
      //     {
      //       p: HitPointEnumsSpace.EPageKey.P_BASE_INFO,
      //       e: HitPointEnumsSpace.EEventKey.FETCH_QUESTION_FAILED,
      //     },
      //     '1',
      //   );
      // }
    }

    if (!(await UserInfoManager.updateUserState())) {
      BaseInfoManager.changeLoadingModalVisible(false);
      return;
    }

    const { isSupplyCompleted, isLoanFailNeedSupply, isHasActiveOrder, isUserTypeNew } =
      UserInfoManager.context.userModel;

    if (
      isSupplyCompleted &&
      !isLoanFailNeedSupply &&
      !isHasActiveOrder &&
      isUserTypeNew &&
      !(await onFirstCancelConfirmApply())
    ) {
      BaseInfoManager.changeLoadingModalVisible(false);
      return;
    }

    try {
      await TrackEvent.uploadEventLog();
    } catch (error) {}

    BaseInfoManager.changeLoadingModalVisible(false);
    // 第三方账户绑定
    nav.nextToTopRouter();
  };

  /** 点击返回 */
  const handleGoBack = () => {
    if (UserInfoManager.context.userModel.isNeedSupply) {
      return true;
    } else {
      modalDataStoreInstance.openModal({
        key: ModalList.INFO_PROMPT_CONFIRM,
        i18nKey: 'basicInfoString.get_loan',
        imageKey: '_modalCry',
        confirmBtnName: 'btnString.continueOn',
        cancelBtnName: 'btnString.exit',
        isBackdropClose: false,
        confirmBtnCallback: async () => {
          TrackEvent.trackCommonEvent(
            {
              p: HitPointEnumsSpace.EPageKey.P_INTERCEPT_POPUP,
              e: HitPointEnumsSpace.EEventKey.BTN_POPUP,
            },
            '1',
          );
          await TrackEvent.uploadEventLog();
        },
        cancelBtnCallback: async () => {
          TrackEvent.trackCommonEvent(
            {
              p: HitPointEnumsSpace.EPageKey.P_INTERCEPT_POPUP,
              e: HitPointEnumsSpace.EEventKey.BTN_POPUP,
            },
            '0',
          );
          nav.navigationGoBack();
        },
      });
      return true;
    }
  };

  /** 首贷取消补件后主动提交申请 */
  const onFirstCancelConfirmApply = async () => {
    let result = await firstCancelConfirmApply();
    if (result.code === BaseEnumsSpace.ENetworkStatus.SUCCESS) {
      UserInfoManager.updateApplyId(result.data);
    }

    return result.code === BaseEnumsSpace.ENetworkStatus.SUCCESS;
  };

  /** 获取基础信息 */
  const onGetBasicInfo = async () => {
    let result = await fetchBasicInfo();

    if (result.code === BaseEnumsSpace.ENetworkStatus.SUCCESS) {
      startTransition(() => {
        const { salary, whatsApp, ...basicInfo } = result.data;
        setState((preState: IState): IState => {
          return {
            ...preState,
            professional: { salary },
            basicInfo: {
              ...basicInfo,
              age: basicInfo.age || '',
              salaryPeriod: basicInfo.salaryPeriod || '',
              loanUse: basicInfo.loanUse || '',
              whatsApp: whatsApp || preState.basicInfo.whatsApp,
            },
          };
        });
      });
    }

    return result.code === BaseEnumsSpace.ENetworkStatus.SUCCESS;
  };

  /** 获取联系人 */
  const onGetContact = async () => {
    let result = await fetchContact();
    if (result.code === BaseEnumsSpace.ENetworkStatus.SUCCESS) {
      if (Array.isArray(result.data) && result.data.length <= 0) {
        return false;
      }

      startTransition(() => {
        setState((preState: IState): IState => {
          return {
            ...preState,
            contact: {
              userReferences: result.data,
            },
          };
        });
      });
    }

    return result.code === BaseEnumsSpace.ENetworkStatus.SUCCESS;
  };

  /** 获取墨西哥地址信息 */
  const onGetAddress = async (params: AddressType) => {
    const { first = 'YES' } = params;
    let result = await fetchGetAddress({
      ...{
        first: 'YES',
        zipCode: '',
        city: '',
        state: '',
        municipality: '',
      },
      ...params,
    });
    if (result?.code !== BaseEnumsSpace.ENetworkStatus.SUCCESS) {
      return false;
    }

    /** 首次拉取默认邮编、州、城市进行下拉框填充 */
    if (first === 'YES') {
      startTransition(() => {
        setState((preState: IState): IState => {
          return {
            ...preState,
            pinCodeList: Array.from(result.data, ({ zipcode }: { zipcode: string }) => {
              return zipcode;
            }),
            cityList: Array.from(result.data, ({ city }: { city: string }) => {
              return city;
            }),
            stateList: Array.from(result.data, ({ state }: { state: string }) => {
              return state;
            }),
          };
        });
      });
    } else if (first === 'NO') {
      /**
       * 如果邮编包含的地址的州、市有且仅有一个, 那么自动选择
       * 如果邮编包含的地址的州、市有多个那么, 就填充下拉框, 让用户进行选择
       */
      startTransition(() => {
        setState((preState: IState): IState => {
          return {
            ...preState,
            basicInfo: {
              ...preState.basicInfo,
              state: '',
              city: '',
            },
          };
        });
      });
      let _stateList = Array.from(result.data, ({ state }: { state: string }) => {
        return state;
      });
      let _cityList = Array.from(result.data, ({ city }: { city: string }) => {
        return city;
      });

      startTransition(() => {
        restAction.stateRef.current?.clearErrorStatus();
        restAction.cityRef.current?.clearErrorStatus();
        if ([...new Set(_stateList)].length === 1) {
          setState((preState: IState): IState => {
            return {
              ...preState,
              basicInfo: {
                ...preState.basicInfo,
                state: result.data[0].state,
              },
            };
          });
        }

        if ([...new Set(_cityList)].length === 1) {
          setState((preState: IState): IState => {
            return {
              ...preState,
              basicInfo: {
                ...preState.basicInfo,
                city: result.data[0].city,
              },
            };
          });
        }
      });
    }
    return result?.code === BaseEnumsSpace.ENetworkStatus.SUCCESS;
  };

  /** 获取州和城市 */
  const onGetStateAndCity = async (zipCode: string) => {
    BaseInfoManager.changeLoadingModalVisible(true);
    await onGetAddress({
      first: 'NO',
      zipCode,
      city: '',
      state: '',
      municipality: '',
    });
    BaseInfoManager.changeLoadingModalVisible(false);
  };

  /** 获取州的信息 */
  const onGetInfoByState = async (state: string) => {
    BaseInfoManager.changeLoadingModalVisible(true);
    await onGetAddress({
      first: 'NO',
      state,
      zipCode: _state.basicInfo.pincode,
      city: '',
    });
    BaseInfoManager.changeLoadingModalVisible(false);
  };

  /** 获取城市的信息 */
  const onGetInfoByCity = async (city: string) => {};

  /** 谷歌邮箱选择回调 */
  const gmailPickResultCallback = useCallback(
    async (event: IGmailPickerData) => {
      log.debug('# gmailPickResultCallback', event);
      const { eventType, email } = event;
      const eMailEvent: IEventPoint = {
        p: HitPointEnumsSpace.EPageKey.P_BASE_INFO,
        e: HitPointEnumsSpace.EEventKey.E_EMAIL,
      };
      switch (eventType) {
        case EGmailPickerEventType.GMAIL_GET_START:
          break;
        // 关闭loading
        case EGmailPickerEventType.GMAIL_GET_SUCCESS:
          const cMail = email || '';
          if (!verifyEmail(email)) {
            restAction.emailRef.current?.emitErrorStatus();
          } else {
            restAction.emailRef.current?.clearErrorStatus();
          }
          trackCommonEvent(eMailEvent, cMail, '1');
          trackCommonEvent(eMailEvent, cMail, '1');
          setState((preState: IState): IState => {
            return {
              ...preState,
              basicInfo: {
                ...preState.basicInfo,
                gpEmail: cMail || '',
                email: cMail,
              },
            };
          });
          restAction.emailRef.current?.blur();
          if (
            BaseInfoManager.context.baseModel.isIncomeInputAutoSkipSwitch &&
            verifyEmail(cMail) &&
            !isStringExist(_state.basicInfo.whatsApp)
          ) {
            restAction.whatsAppRef.current?.focus();
          }
          break;
        case EGmailPickerEventType.GMAIL_GET_FAIL:
          // 关闭loading
          // 提示邮箱获取失败
          trackCommonEvent(eMailEvent, '', '1');
          trackCommonEvent(eMailEvent, '', '1');
          break;
      }
    },
    [_state.basicInfo.whatsApp],
  );

  const pickGmail = useGmailPicker(gmailPickResultCallback)[0];

  /** 联系人选择回调 */
  const contactPickResultCallback = useCallback(async (event: IContactPickerData) => {
    log.debug('# contactPickResultCallback', event);
    const { eventType, name, number } = event;
    // 联系人名的埋点事件
    const eNameEvent: IEventPoint = {
      p: HitPointEnumsSpace.EPageKey.P_BASE_INFO,
      e: HitPointEnumsSpace.EEventKey.E_CONTACT_NAME,
    };
    // 联系人手机号的埋点事件
    const eNumberEvent: IEventPoint = {
      p: HitPointEnumsSpace.EPageKey.P_BASE_INFO,
      e: HitPointEnumsSpace.EEventKey.E_CONTACT_PHONE,
    };
    switch (eventType) {
      case EContactPickerEventType.CONTRACT_GET_START:
        break;
      // 关闭loading
      case EContactPickerEventType.CONTRACT_GET_SUCCESS:
        let cName = name || '';
        let cNumber = number || '';
        if (cNumber === UserInfoManager.context.userModel.mobile) {
          cNumber = '';
          restAction.phoneNumberRef.current?.emitErrorStatus(
            t('basicInfoString.select_contact_mobile_is_not_register'),
          );
        } else {
          if (!isStringExist(cName)) {
            restAction.fullNameRef.current?.emitErrorStatus();
          } else {
            restAction.fullNameRef.current?.clearErrorStatus();
          }

          if (!verifyPhoneNumber(cNumber)) {
            restAction.phoneNumberRef.current?.emitErrorStatus();
          } else {
            restAction.phoneNumberRef.current?.clearErrorStatus();
          }
        }
        trackCommonEvent(eNameEvent, cName, '1');
        trackCommonEvent(eNumberEvent, cNumber, '1');
        // 获取联系人信息成功
        setState((preState: IState): IState => {
          return {
            ...preState,
            contact: {
              userReferences: [
                {
                  ...preState.contact.userReferences[0],
                  fullName: cName,
                  mobile: cNumber,
                },
              ],
            },
          };
        });
        restAction.phoneNumberRef.current?.blur();
        break;
      case EContactPickerEventType.CONTRACT_GET_FAIL:
        trackCommonEvent(eNameEvent, '', '1');
        trackCommonEvent(eNumberEvent, '', '1');
        // 关闭loading
        // 提示选择通讯录失败
        break;
    }
  }, []);

  const pickContact = useContactPicker(contactPickResultCallback)[0];

  return {
    googleServerIsUsage: _state.googleServerIsUsage,
    dataRef,
    stateList: _state.stateList,
    cityList: _state.cityList,
    pinCodeList: _state.pinCodeList,
    phoneNumber: _state.contact.userReferences[0].mobile,
    fullName: _state.contact.userReferences[0].fullName,
    relation: _state.contact.userReferences[0].relation,
    ..._state.basicInfo,
    ..._state.professional,
    ...restAction,
    isPending,
    onGetStateAndCity,
    onGetInfoByState,
    onGetInfoByCity,
    pickGmail,
    pickContact,
    isNeedSupply: UserInfoManager.context.userModel.isNeedSupply,
    handleNext,
    handleGoBack,
  };
}
