/* eslint-disable react-native/no-inline-styles */
/* eslint-disable react/react-in-jsx-scope */

import {
  Button,
  EProcessStatus,
  Image,
  Layout,
  PhoneNumberInput,
  PincodeSelectInput,
  PrefixInput,
  PrefixSelectInput,
  ProcessNav,
  SeaInput,
  Text,
  TopNavigation,
  View,
  LinearGradient,
  DatePickerCalendarInput,
} from '@/components';
import { HitPointEnumsSpace } from '@/enums';
import { BaseInfoManager, UserInfoManager } from '@/managers';
import { ScreenProps } from '@/types';
import {
  isNameValid,
  isStringExist,
  nav,
  verifyAge,
  verifyEmail,
  verifyPhoneNumber,
} from '@/utils';
import React, { ReactElement, useMemo, useState } from 'react';
import { Pressable, ScrollView } from 'react-native';
import useData from './useData';
import { trackCommonEvent } from '@/trackEvent';
import { t } from 'i18next';
import { Strings } from '@/i18n';
import ProgressView from '../../components/ProgressView';
import { convert2Progress } from '../utils/dataUtils';
import { Colors } from '@/themes';
import { RouterConfig } from '@/routes';

export default ({ navigation }: ScreenProps<{}>): ReactElement => {
  const emailConfing = [
    '@gmail.com',
    '@hotmail.com',
    '@icloud.com',
    '@yahoo.com',
    '@live.com.mx',
    '@outlook.com',
    '@gmx.es',
  ];

  const {
    scrollRef,
    emailRef,
    whatsAppRef,
    ageRef,
    fullNameRef,
    postCodeRef,
    stateRef,
    cityRef,
    educationRef,
    profesionalRef,
    monthlySalaryRef,
    maritalRef,
    childNumberRef,
    hasLoanRecordRef,
    phoneNumberRef,
    relationshipRef,
    loanUseRef,
    salaryPeriodRef,
    salaryDateRef,
    handleNext,
    handleGoBack,
    isPending,
    stateList,
    dataRef,
    cityList,
    pinCodeList,
    phoneNumber,
    state,
    city,
    relation,
    age,
    fullName,
    email,
    gpEmail,
    whatsApp,
    salary,
    pincode,
    cashLoanedList,
    childNumberList,
    educationSelectList,
    educationLevel,
    childNum,
    cashLoaned,
    maritalStatus,
    jobType,
    loanUse,
    salaryPeriod,
    onChangeLoanUse,
    onChangeSalaryPeriod,
    onChangeWhatsApp,
    onChangeEducation,
    onChangeChildNumber,
    onChangeHasLoanRecord,
    profesionalSelectList,
    maritalSelectList,
    relationshipSelectList,
    salarySelectList,
    loanUseList,
    salaryPeriodList,
    salaryDate,
    pickGmail,
    pickContact,
    onSetPhoneNumber,
    onChangeJobType,
    onChangeMaritalStatus,
    onChangeAge,
    onChangeName,
    onChangeEmail,
    onChangePincode,
    onChangeState,
    onChangeCity,
    onChangeSalary,
    onChangeSalaryDate,
    onGetStateAndCity,
    onGetInfoByState,
    onGetInfoByCity,
    onChangeRelationship,
    googleServerIsUsage,
    couponMsg,
  } = useData();

  /** email 输入框的是否聚焦 */
  const [emailFocus, setEmailFocus] = useState<boolean>(false);
  const [salaryViewLayoutTimestamp, setSalaryViewLayoutTimestamp] = useState(0);

  const showProcessView = useMemo(
    () =>
      !UserInfoManager.context.userModel.hasApplyOrderId &&
      !UserInfoManager.context.userModel.isNeedSupply,
    [UserInfoManager.context.userModel],
  );

  const onEmailFocus = () => {
    setEmailFocus(true);
  };
  const onEmailBlur = () => {
    setEmailFocus(false);
  };

  const TipsInfoView = (
    <View
      layoutStrategy="flexRowCenterCenter"
      padding="12 12 12 12"
      style={{ top: 22, borderRadius: 4, backgroundColor: Colors.PRIMARY_COLOR_100, zIndex: 2 }}>
      <Text
        style={{ flex: 1, color: Colors.TEXT_COLOR_800 }}
        category="p1"
        i18nKey={Strings.basicInfoString.flowTip}
      />
    </View>
  );

  const $emailInput = useMemo(() => {
    return (
      <View layoutStrategy="flexColumnStart">
        <SeaInput
          ref={emailRef}
          type="line"
          prefixKey={'basicInfoString.email'}
          prefixMargin={'16 0 0 0'}
          placeholderKey={'basicInfoString.placeholder'}
          value={gpEmail || email}
          setValue={onChangeEmail}
          onFocus={onEmailFocus}
          onBlur={onEmailBlur}
          pageKey={HitPointEnumsSpace.EPageKey.P_BASE_INFO}
          eventKey={HitPointEnumsSpace.EEventKey.E_EMAIL}
          validateConfig={[
            {
              condition: isStringExist,
              info: t('verificationString.required'),
              status: 'danger',
            },
            {
              condition: verifyEmail,
              info: t('verificationString.failed', {
                name: t(Strings.basicInfoString.email),
              }),
              status: 'danger',
            },
          ]}
        />
        {/* 常用邮箱后缀选择 */}
        {email && emailFocus && (
          <View margin="6 0 0 0" layoutStrategy="flexRowBetweenCenterWrap">
            {emailConfing.map((item, index) => {
              return (
                <Text
                  key={index}
                  margin="6 0 6 0"
                  padding="4 8 4 8"
                  textContent={item}
                  onPress={() => {
                    onChangeEmail(email + item);
                    onEmailBlur();
                    trackCommonEvent(
                      {
                        p: HitPointEnumsSpace.EPageKey.P_BASE_INFO,
                        e: HitPointEnumsSpace.EEventKey.E_EMAIL_SUFFIX,
                      },
                      item,
                    );
                  }}
                  style={{
                    color: 'text-color-700',
                    backgroundColor: 'background-color-100',
                    borderRadius: 99,
                  }}
                />
              );
            })}
          </View>
        )}
      </View>
    );
  }, [email, gpEmail, whatsApp, onChangeEmail, pickGmail, googleServerIsUsage]);

  const BasicInfoView = (
    <View margin={'12 16 0 16'}>
      {TipsInfoView}
      <View
        margin="16 0 0 0"
        padding={'16 12 24 12'}
        style={{
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'space-between',
          backgroundColor: 'background-color-0',
          borderBottomLeftRadius: 8,
          borderBottomRightRadius: 8,
        }}>
        <View layoutStrategy="flexRowStartCenter" style={{ alignSelf: 'flex-start' }}>
          <Image name="_evidenceBasicInfo" />
          <View margin="0 0 0 8">
            <View
              width={178}
              height={7}
              style={{
                position: 'absolute',
                bottom: 3,
                left: -2,
                borderRadius: 9,
                backgroundColor: Colors.PRIMARY_COLOR_100,
              }}
            />
            <Text
              bold="bold"
              category="h3"
              i18nKey={'basicInfoString.basicInfo'}
              style={{
                color: 'text-color-800',
              }}
            />
          </View>
        </View>
        <SeaInput
          ref={whatsAppRef}
          type="line"
          keyboardType="numeric"
          prefixKey={'basicInfoString.whatsapp'}
          prefixMargin={'16 0 0 0'}
          placeholderKey={'basicInfoString.placeholder'}
          value={whatsApp}
          pageKey={HitPointEnumsSpace.EPageKey.P_BASE_INFO}
          eventKey={HitPointEnumsSpace.EEventKey.E_WHATSAPP}
          setValue={onChangeWhatsApp}
          validateConfig={[
            {
              condition: isStringExist,
              info: t('verificationString.required'),
              status: 'danger',
            },
            {
              condition: verifyPhoneNumber,
              info: t('verificationString.failed', {
                name: t(Strings.basicInfoString.whatsapp),
              }),
              status: 'danger',
            },
          ]}
        />
        {$emailInput}
      </View>
      <View
        margin="12 0 0 0"
        padding={'16 12 24 12'}
        style={{
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'space-between',
          backgroundColor: 'background-color-0',
          borderRadius: 8,
        }}>
        <PrefixSelectInput
          ref={stateRef}
          type="line"
          selectModel="actionSheet"
          prefixKey={'basicInfoString.state'}
          prefixMargin={'16 0 0 0'}
          placeholderKey={'basicInfoString.placeholder'}
          options={stateList}
          changeSelectCallback={onGetInfoByState}
          value={state}
          setValue={onChangeState}
          onPress={() => {
            nav.navigate(RouterConfig.REGIONAL_SELECTION_PAGE as any, {
              options: dataRef.current?.stateList ?? [],
              onPress: stateRef.current?.onClickItem,
              onCancel: stateRef.current?.onCloseModal,
            });
          }}
          validateConfig={[
            {
              condition: isStringExist,
              info: t('verificationString.required'),
              status: 'danger',
            },
          ]}
        />
        <PrefixSelectInput
          ref={cityRef}
          type="line"
          selectModel="actionSheet"
          prefixKey={'basicInfoString.city'}
          prefixMargin={'16 0 0 0'}
          placeholderKey={'basicInfoString.placeholder'}
          options={cityList}
          changeSelectCallback={onGetInfoByCity}
          value={city}
          disabled={!state}
          setValue={onChangeCity}
          onPress={() => {
            nav.navigate(RouterConfig.REGIONAL_SELECTION_PAGE as any, {
              options: dataRef.current?.cityList ?? [],
              onPress: cityRef.current?.onClickItem,
              onCancel: cityRef.current?.onCloseModal,
            });
          }}
          validateConfig={[
            {
              condition: isStringExist,
              info: t('verificationString.required'),
              status: 'danger',
            },
          ]}
        />
        {/* <SeaInput
          ref={ageRef}
          type="line"
          keyboardType="number-pad"
          prefixKey={'basicInfoString.age'}
          prefixMargin={'16 0 0 0'}
          placeholderKey={'basicInfoString.placeholder'}
          value={String(age)}
          pageKey={HitPointEnumsSpace.EPageKey.P_BASE_INFO}
          eventKey={HitPointEnumsSpace.EEventKey.E_AGE}
          setValue={onChangeAge}
          validateConfig={[
            {
              condition: verifyAge(String(age)),
              info: '',
              status: 'danger',
            },
          ]}
        /> */}
      </View>
      <View
        margin="12 0 0 0"
        padding={'16 12 24 12'}
        style={{
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'space-between',
          backgroundColor: 'background-color-0',
          borderRadius: 8,
        }}
        onLayout={event => {
          setTimeout(() => {
            setSalaryViewLayoutTimestamp(event.timeStamp);
          }, 500);
        }}>
        {BaseInfoManager.context.baseModel.isJobTypeOpen && (
          <PrefixSelectInput
            ref={profesionalRef}
            type="line"
            selectModel="actionSheet"
            prefixKey={'basicInfoString.profesional'}
            prefixMargin={'16 0 0 0'}
            placeholderKey={'basicInfoString.placeholder'}
            pageKey={HitPointEnumsSpace.EPageKey.P_BASE_INFO}
            eventKey={HitPointEnumsSpace.EEventKey.E_PROFESION}
            options={profesionalSelectList}
            value={jobType}
            setValue={onChangeJobType}
            validateConfig={[
              {
                condition: isStringExist,
                info: t('verificationString.required'),
                status: 'danger',
              },
            ]}
          />
        )}
        <PrefixSelectInput
          ref={monthlySalaryRef}
          type="line"
          selectModel="actionSheet"
          prefixKey={'basicInfoString.monthlySalary'}
          prefixMargin={'16 0 0 0'}
          placeholderKey={'basicInfoString.placeholder'}
          options={salarySelectList}
          value={salary}
          pageKey={HitPointEnumsSpace.EPageKey.P_BASE_INFO}
          eventKey={HitPointEnumsSpace.EEventKey.E_SALARY}
          setValue={onChangeSalary}
          validateConfig={[
            {
              condition: isStringExist,
              info: t('verificationString.required'),
              status: 'danger',
            },
          ]}
        />
        {BaseInfoManager.context.baseModel.isSalaryPeridOpen && (
          <PrefixSelectInput
            ref={salaryPeriodRef}
            type="line"
            selectModel="actionSheet"
            prefixKey={'basicInfoString.salaryPeriod'}
            prefixMargin={'16 0 0 0'}
            placeholderKey={'basicInfoString.placeholder'}
            pageKey={HitPointEnumsSpace.EPageKey.P_BASE_INFO}
            eventKey={HitPointEnumsSpace.EEventKey.E_SALARY_PERIOD}
            options={salaryPeriodList}
            value={salaryPeriod}
            setValue={onChangeSalaryPeriod}
            validateConfig={[
              {
                condition: isStringExist,
                info: t('verificationString.required'),
                status: 'danger',
              },
            ]}
          />
        )}

        {BaseInfoManager.context.baseModel.nextSalaryDateSwitch && (
          <DatePickerCalendarInput
            uiType="line"
            ref={salaryDateRef}
            prefixKey={'basicInfoString.salaryDate'}
            prefixMargin={'16 0 0 0'}
            placeholderKey={'basicInfoString.salaryDate'}
            type="day"
            format="DD/MM/YYYY"
            value={salaryDate}
            pageKey={HitPointEnumsSpace.EPageKey.P_OCR}
            eventKey={HitPointEnumsSpace.EEventKey.E_BIRTHDAY}
            setValue={onChangeSalaryDate}
            inputStyle={{
              borderWidth: 0,
              borderBottomWidth: 1,
              paddingLeft: 6,
              borderColor: 'fill-color-500',
              borderRadius: 0,
            }}
            calendarIconStyle={{ display: 'none' }}
            inputArrowIconStyle={{ width: 24, height: 24 }}
            extraData={salaryViewLayoutTimestamp}
          />
        )}

        {BaseInfoManager.context.baseModel.isEducationLevelOpen && (
          <PrefixSelectInput
            ref={educationRef}
            type="line"
            selectModel="actionSheet"
            prefixKey={'basicInfoString.education'}
            prefixMargin={'16 0 0 0'}
            placeholderKey={'basicInfoString.placeholder'}
            pageKey={HitPointEnumsSpace.EPageKey.P_BASE_INFO}
            eventKey={HitPointEnumsSpace.EEventKey.E_EDUCATION}
            infoText={'basicInfoString.accept_high_education'}
            options={educationSelectList}
            value={educationLevel}
            setValue={onChangeEducation}
            validateConfig={[
              {
                condition: isStringExist,
                info: t('verificationString.required'),
                status: 'danger',
              },
            ]}
          />
        )}
        {BaseInfoManager.context.baseModel.isMaritalStatusOpen && (
          <PrefixSelectInput
            ref={maritalRef}
            type="line"
            selectModel="actionSheet"
            prefixKey={'basicInfoString.marital'}
            prefixMargin={'16 0 0 0'}
            placeholderKey={'basicInfoString.placeholder'}
            pageKey={HitPointEnumsSpace.EPageKey.P_BASE_INFO}
            eventKey={HitPointEnumsSpace.EEventKey.E_MARITAL}
            options={maritalSelectList}
            value={maritalStatus}
            setValue={onChangeMaritalStatus}
            validateConfig={[
              {
                condition: isStringExist,
                info: t('verificationString.required'),
                status: 'danger',
              },
            ]}
          />
        )}
        {BaseInfoManager.context.baseModel.isChildNumOpen && (
          <PrefixSelectInput
            ref={childNumberRef}
            type="line"
            selectModel="actionSheet"
            prefixKey={'basicInfoString.child_number'}
            prefixMargin={'16 0 0 0'}
            placeholderKey={'basicInfoString.placeholder'}
            pageKey={HitPointEnumsSpace.EPageKey.P_BASE_INFO}
            eventKey={HitPointEnumsSpace.EEventKey.E_CHILDREN}
            options={childNumberList}
            value={childNum}
            setValue={onChangeChildNumber}
            validateConfig={[
              {
                condition: isStringExist,
                info: t('verificationString.required'),
                status: 'danger',
              },
            ]}
          />
        )}
        {BaseInfoManager.context.baseModel.isCashLoanedOpen && (
          <PrefixSelectInput
            ref={hasLoanRecordRef}
            type="line"
            selectModel="actionSheet"
            prefixKey={'basicInfoString.hasLoanRecord'}
            prefixMargin={'16 0 0 0'}
            placeholderKey={'basicInfoString.placeholder'}
            pageKey={HitPointEnumsSpace.EPageKey.P_BASE_INFO}
            eventKey={HitPointEnumsSpace.EEventKey.E_PENDING_LOAN}
            options={cashLoanedList}
            value={cashLoaned}
            setValue={onChangeHasLoanRecord}
            validateConfig={[
              {
                condition: isStringExist,
                info: t('verificationString.required'),
                status: 'danger',
              },
            ]}
          />
        )}
        {BaseInfoManager.context.baseModel.isLoanUseOpen && (
          <PrefixSelectInput
            ref={loanUseRef}
            type="line"
            selectModel="actionSheet"
            prefixKey={'basicInfoString.loanUse'}
            prefixMargin={'16 0 0 0'}
            placeholderKey={'basicInfoString.placeholder'}
            pageKey={HitPointEnumsSpace.EPageKey.P_BASE_INFO}
            eventKey={HitPointEnumsSpace.EEventKey.E_LOAN_USE}
            options={loanUseList}
            value={loanUse}
            setValue={onChangeLoanUse}
            validateConfig={[
              {
                condition: isStringExist,
                info: t('verificationString.required'),
                status: 'danger',
              },
            ]}
          />
        )}
      </View>
    </View>
  );

  const RelationshopView = (
    <View margin={'12 16 12 16'}>
      <View
        margin="0 0 0 0"
        padding={'16 12 24 12'}
        style={{
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'space-between',
          backgroundColor: 'background-color-0',
          borderRadius: 8,
        }}>
        <View layoutStrategy="flexRowStartCenter" style={{ alignSelf: 'flex-start' }}>
          <Image name="_evidenceBasicReleation" />
          <View margin="0 0 0 8">
            <View
              width={243}
              height={7}
              style={{
                position: 'absolute',
                bottom: 3,
                left: -4,
                borderRadius: 9,
                backgroundColor: Colors.PRIMARY_COLOR_100,
              }}
            />
            <Text
              bold="bold"
              category="h3"
              i18nKey={'basicInfoString.contactInfo'}
              style={{
                color: 'text-color-800',
              }}
            />
          </View>
        </View>

        <View
          style={{
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'space-between',
            flex: 1,
          }}>
          <Pressable pointerEvents="box-only" onPress={pickContact}>
            <SeaInput
              ref={phoneNumberRef}
              type="line"
              prefixKey={'basicInfoString.phoneNumber'}
              prefixMargin="16 0 0 0"
              placeholderKey={'basicInfoString.placeholder'}
              value={phoneNumber}
              setValue={phoneNumber ? onSetPhoneNumber : pickContact}
              pageKey={HitPointEnumsSpace.EPageKey.P_BASE_INFO}
              eventKey={HitPointEnumsSpace.EEventKey.E_CONTACT_PHONE}
              // onPress={pickContact}
              validateConfig={[
                {
                  condition: isStringExist.bind(this, phoneNumber),
                  info: t('verificationString.required'),
                  status: 'danger',
                },
                {
                  condition: verifyPhoneNumber,
                  info: t('verificationString.failed', {
                    name: t('basicInfoString.phoneNumber'),
                  }),
                  status: 'danger',
                },
              ]}
            />
          </Pressable>
          {/* <PhoneNumberInput
            ref={phoneNumberRef}
            type="line"
            prefixKey={'basicInfoString.phoneNumber'}
            prefixMargin="16 0 0 0"
            placeholderKey={'basicInfoString.placeholder'}
            phoneNumber={phoneNumber}
            setPhoneNumber={onSetPhoneNumber}
            pageKey={HitPointEnumsSpace.EPageKey.P_BASE_INFO}
            eventKey={HitPointEnumsSpace.EEventKey.E_CONTACT_PHONE}
            onPressIn={pickContact}
          />*/}
          <SeaInput
            ref={fullNameRef}
            type="line"
            prefixKey={'basicInfoString.name'}
            prefixMargin={'16 0 0 0'}
            placeholderKey={'basicInfoString.placeholder'}
            value={fullName}
            setValue={onChangeName}
            pageKey={HitPointEnumsSpace.EPageKey.P_BASE_INFO}
            eventKey={HitPointEnumsSpace.EEventKey.E_CONTACT_NAME}
            validateConfig={[
              {
                condition: isStringExist.bind(this, fullName),
                info: t('verificationString.required'),
                status: 'danger',
              },
              {
                condition: isNameValid.bind(this, fullName),
                info: t('verificationString.failed', {
                  name: t('basicInfoString.name'),
                }),
                status: 'danger',
              },
            ]}
          />
          <PrefixSelectInput
            ref={relationshipRef}
            type="line"
            selectModel="actionSheet"
            prefixKey={'basicInfoString.relationship'}
            prefixMargin={'16 0 0 0'}
            placeholderKey={'basicInfoString.placeholder'}
            options={relationshipSelectList}
            value={relation}
            pageKey={HitPointEnumsSpace.EPageKey.P_BASE_INFO}
            eventKey={HitPointEnumsSpace.EEventKey.E_CONTACT_TYPE}
            setValue={onChangeRelationship}
          />
        </View>
      </View>
    </View>
  );

  const ButtonView = (
    <View
      padding="16 16 16 16"
      style={{
        backgroundColor: 'background-color-0',
      }}>
      {!UserInfoManager.context.userModel.hasApplyOrderId && (
        <View margin="0 8 0 8" layoutStrategy="flexRowCenterCenter" style={{ alignSelf: 'center' }}>
          <Image margin="0 10 0 0" name="_safeTipIcon" />
          <Text
            category="p1"
            style={{ fontSize: 14 }}
            i18nKey={Strings.basicInfoString.protect_your_privacy}
          />
        </View>
      )}
      <Button
        margin="16 16 0 16"
        status="primary"
        onPress={handleNext}
        textI18nKey="btnString.next"
      />
    </View>
  );
  const CouponView = useMemo(() => {
    if (!couponMsg) {
      return null;
    }
    return (
      <LinearGradient
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
        colors={['#717AFF', '#717AFF', '#996BEF']}>
        <View layoutStrategy="flexRowStartCenter" padding="8 32 8 32">
          <Image name="_basicInfoCoupon" />
          <Text
            padding="0 0 0 8"
            textContent={couponMsg}
            category="c1"
            style={{ color: Colors.TEXT_COLOR_0 }}
          />
        </View>
      </LinearGradient>
    );
  }, [couponMsg]);
  return (
    <>
      <Layout pLevel="0" level="1">
        <TopNavigation
          titleKey={'basicInfoString.generalInfo'}
          isBack={!UserInfoManager.context.userModel.isNeedSupply}
          goBack={handleGoBack}
          bottomLine
        />
        {showProcessView ? <ProgressView current={convert2Progress(EProcessStatus.BASIC)} /> : null}
        {CouponView}
        <ScrollView fadingEdgeLength={10} ref={scrollRef} keyboardShouldPersistTaps="always">
          {BasicInfoView}
          {RelationshopView}
        </ScrollView>
        {ButtonView}
      </Layout>
    </>
  );
};
