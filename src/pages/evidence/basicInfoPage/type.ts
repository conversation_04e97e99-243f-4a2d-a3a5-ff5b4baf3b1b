import { EvidenceVOSpace } from '@/types';

export type IState = {
  googleServerIsUsage: boolean;
  professional: IProfessional;
  basicInfo: IBasicInfo;
  contact: IContact;
} & {
  /** 州列表 */
  stateList: string[];
  /** 城市列表 */
  cityList: string[];
  /** 邮编列表 */
  pinCodeList: string[];
};

export type IBasicInfo = Pick<
  EvidenceVOSpace.RequestBasicType,
  | 'city'
  | 'email'
  | 'gpEmail'
  | 'pincode'
  | 'state'
  | 'whatsApp'
  | 'age'
  | 'educationLevel'
  | 'childNum'
  | 'cashLoaned'
  | 'maritalStatus'
  | 'jobType'
  | 'salaryPeriod'
  | 'salaryDate'
  | 'loanUse'
  | 'couponMsg'
>;

export type AddressType = {
  city?: string;
  state?: string;
  first?: 'YES' | 'NO';
  municipality?: string;
  zipCode?: string;
};

/** 职业状态 */
export enum EProfesional {
  /** 私营企业雇员 */
  PRIVATE_SECTOR_EMPLOYEE = 'Empleado del sector privado',
  /** 公共部门雇员 */
  PUBLIC_SECTOR_EMPLOYEE = 'Empleado del sector público',
  /** 企业所有者 */
  COMPANY_OWNER = 'Dueño de la empresa',
  /** 非正式或临时就业 */
  INFOMAL_ORTEMPORARY_EMPLOYMENT = 'Empleo informal o temporal',
  /** 自营职业或独立 */
  SELF_EMPLOYED_OR_INDEPENDENT = 'Autoempleado o independiente',
  /** 学生 */
  STUDENT = 'Estudiante',
  /** 家庭主妇 */
  HOUSEWIFE = 'Me dedico al hogar',
  /** 退休 */
  RETIRED = 'Jubilado',
  /** 军队 */
  MILITAR = 'Militar',
  /** 失业 */
  UNEMPLOYED = 'Desempleado',
}

/** 婚姻状态 */
export enum EMaritalStatus {
  /** 单身的 */
  SINGLE = 'Soltero',
  /** 已婚 */
  MARRIED = 'Casado',
  /** 离婚 */
  DIVORCED = 'Divorciado',
  /** 其他 */
  OTHER = 'Otro',
}

/** 子女状态 */
export enum EChildNumber {
  ZERO = '0',
  ONE = '1',
  TWO = '2',
  THREE_MORE = '3+',
}

/** 教育状态 */
export enum EEducationLeve {
  /** 硕士或博士 */
  MASTER_OR_PHD = 'Maestría o doctorado',
  /** 大学或技术学院 */
  UNIVERSITY_OR_TECHNOLOGICAL_INSTITUTE = 'Universidad o Instituto Tecnológico',
  /** 学士学位或专业技术人员 */
  BACHILLERATO_O_PROFECIONAL_TECNICO = 'Bachillerato o Profecional Técnico',
  SECONDARY = 'Secundaria',
  PRIMARY = 'Primaria',
  SIN_ESTUDIOS = 'Sin estudios',
}

/** 联系人关系 */
export enum ERelationship {
  /** 父母 */
  PARENTS = 'Padres',
  /** 夫妻 */
  COUPLE = 'Pareja',
  /** 孩子 */
  CHILDREN = 'Hijos',
  /** 其他 */
  OTHER = 'Otro',
}

/** 薪资范围 */
export enum ESalary {
  MXN0_3000 = '0-3,000 MXN',
  MXN6000_9000 = '6,000-9,000 MXN',
  MXN9000_12000 = '9,000-12,000 MXN',
  MXN12000_15000 = '12,000-15,000 MXN',
  MXN15000_more = '15,000+MXN',
}

/** 贷款记录状态 */
export enum ECashLoaned {
  YES = 'Sí',
  NO = 'No',
}

/** 贷款用途 */
export enum ELoanUse {
  BUSSINESS = 'Gastos comerciales',
  PERSONAL = 'Gastos personales',
}

/** 薪资发放周期 */
export enum ESalaryPeriod {
  NONE = 'Ninguno',
  DAY = 'Diario',
  ONCE_WEEK = 'Semanal',
  TWICE_WEEK = 'Quincenal',
  MONTH = 'Mensual',
  OTHER = 'No fijo',
}

export type IProfessional = Pick<EvidenceVOSpace.RequestWorkType, 'salary'>;

export type IContact = EvidenceVOSpace.RequestContactType;
