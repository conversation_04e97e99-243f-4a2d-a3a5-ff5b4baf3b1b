/* eslint-disable react-native/no-inline-styles */
/* eslint-disable react/react-in-jsx-scope */

import { Button, Layout, TopNavigation } from '@/components';
import React, { ReactElement } from 'react';
import useData from './useData';

export default (): ReactElement => {
  const { onStartLiveExamine } = useData();
  return (
    <Layout>
      <TopNavigation titleKey={'basicInfoString.generalInfo'} />
      <Button
        margin="20 0 0 0"
        status="primary"
        onPress={onStartLiveExamine}
        textI18nKey="btnString.quickGetLoan"
      />
    </Layout>
  );
};
