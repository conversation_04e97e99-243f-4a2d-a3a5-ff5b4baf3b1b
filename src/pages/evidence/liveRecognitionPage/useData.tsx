import { HitPointEnumsSpace } from '@/enums';
import { useFaceLive, useGetUserStateAndNextRouterOrOtherCallBack, useOnInit } from '@/hooks';
import { faceVerify, fetchGetBizToken, fetchUploadImage } from '@/server';
import { FileUtils, TrackEvent, log } from '@/utils';
import { useCallback, useRef } from 'react';
import { EFaceLiveEventType, IFaceEventData } from '../../../native/module/faceLive';

export default function useData() {
  useOnInit({
    pageKey: HitPointEnumsSpace.EPageKey.P_LIVE,
  });

  const pointParam = useRef<TrackEvent.IEventPoint>({
    p: HitPointEnumsSpace.EPageKey.P_LIVE,
    e: HitPointEnumsSpace.EEventKey.E_LIVE_ACTION,
  });

  const bizToken = useRef('');

  const faceLiveExamineResultCallback = useCallback((event: IFaceEventData) => {
    log.debug('# faceLiveExamineResultCallback', event);
    const { eventType, faceLivenessFilePath, megliveData } = event;
    console.log('#eventType', eventType);
    switch (eventType) {
      case EFaceLiveEventType.FACE_LIVE_PRE_START:
        break;
      case EFaceLiveEventType.FACE_LIVE_PRE_DETECT:
        break;
      case EFaceLiveEventType.FACE_LIVE_PRE_FINISH:
        break;
      case EFaceLiveEventType.FACE_LIVE_START_DETECT:
        break;
      case EFaceLiveEventType.FACE_LIVE_START_DETECT_STATE_SUCCESS:
        // 活体成功
        onStartLiveCompare(faceLivenessFilePath, megliveData);
        break;
      case EFaceLiveEventType.FACE_LIVE_PRE_DETECT_STATE_FAIL:
      case EFaceLiveEventType.FACE_LIVE_PRE_FINISH_STATE_FAIL:
      case EFaceLiveEventType.FACE_LIVE_START_DETECT_STATE_FAIL:
        // 关闭loading
        break;
    }
  }, []);

  const [startFaceLiveVerification] = useFaceLive(
    pointParam.current,
    faceLiveExamineResultCallback,
  );

  /** 开始活体检测 */
  const onStartLiveExamine = async () => {
    // 启用loading
    // 1、先获取biztoken

    const resp = await fetchGetBizToken({ get_liveness_video: 1 });
    if (resp.code === 0) {
      const { biz_token } = resp.data;
      if (biz_token) {
        bizToken.current = biz_token;
        // 2、调用faceLive的方法
        startFaceLiveVerification(biz_token);
      }
    }
  };

  const [getUserStateAndNextRouterOrOtherCallBack] = useGetUserStateAndNextRouterOrOtherCallBack();

  const onStartLiveCompare = async (
    faceLivenessFilePath: string = '',
    megliveData: string = '',
  ) => {
    if (megliveData !== '') {
      const faceVerifyParam = {
        bizToken: bizToken.current,
        megliveData: megliveData,
      };
      const faceVerifyResp = await faceVerify(faceVerifyParam);
      if (faceVerifyResp.code === 0) {
        const { passFlag } = faceVerifyResp.data;
        if (passFlag.includes('YES')) {
          // 活体验证通过直接下一步
          getUserStateAndNextRouterOrOtherCallBack();
        }
        const absoluteFilePath = FileUtils.getPlatformPathDir(faceLivenessFilePath);
        if (faceLivenessFilePath !== '') {
          // 上传原始的活体加密文件
          log.info(`上传活体加密文件 ${absoluteFilePath}`);
          const uploadResp = await fetchUploadImage(absoluteFilePath, {
            cardType: 'original_face',
            fileType: 'megvii',
          });
          if (uploadResp.code === 0) {
            log.debug('上传活体加密文件成功');
          }
        }
      }
    }
  };

  return { onStartLiveExamine };
}
