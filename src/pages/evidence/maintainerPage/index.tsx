/* eslint-disable react-native/no-inline-styles */
/* eslint-disable react/react-in-jsx-scope */

import { Image, Layout, Text, View } from '@/components';
import { HitPointEnumsSpace } from '@/enums';
import { useOnInit } from '@/hooks';
import React, { ReactElement } from 'react';
import { Dimensions } from 'react-native';

export default ({ navigation, route }: any): ReactElement => {
  /** 初始化 */
  useOnInit({
    pageKey: HitPointEnumsSpace.EPageKey.P_MAINTENANCE,
  });

  return (
    <Layout>
      <View
        margin={'48 0 0 0'}
        style={{
          flexDirection: 'column',
          alignItems: 'center',
          height: Dimensions.get('window').height * 0.8,
        }}>
        <View
          layoutStrategy="flexColumnBetweenCenter"
          style={{
            width: '80%',
          }}>
          <Image name="_maintainer" />
          <Text
            category="p1"
            i18nKey={'settingString.mantainerInfo'}
            isCenter={true}
            margin={'32 0 0 0'}
            style={{
              color: 'text-color-600',
            }}
          />
        </View>
      </View>
    </Layout>
  );
};
