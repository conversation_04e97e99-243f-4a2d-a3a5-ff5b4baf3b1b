import { Button, Image, Layout, Text, TopNavigation, View } from '@/components';
import { HitPointEnumsSpace, UserEnumsSpace } from '@/enums';
import { useOnInit } from '@/hooks';
import { BaseInfoManager, UserInfoManager } from '@/managers';
import {
  fetchRKQuestionnaireEnterLabel,
  fetchSkipGPBind,
  getAccountBindingState3rd,
  submitAccountBinding,
} from '@/server';
import { log, nav, TrackEvent } from '@/utils';
import { GoogleSignin } from '@react-native-google-signin/google-signin';
import { memo, ReactElement, useCallback, useMemo, useState, useTransition } from 'react';
import { ScrollView } from 'react-native';
import { BaseConfig } from '@/baseConfig';

GoogleSignin.configure({
  webClientId: BaseConfig.googleWebClientId,
});

interface IState {
  facebook: boolean;
  google: boolean;
}

const defaultState: IState = {
  facebook: false,
  google: false,
};
const useData = () => {
  const [isPending, startTransition] = useTransition();
  const [state, setState] = useState<IState>(defaultState);

  /** 初始化 */
  useOnInit({
    callback: async () => {
      // 获取第三方账户的绑定状态
      await onGetAccountBindingState();
    },
    isActivityAutoRefresh: true,
    pageKey: HitPointEnumsSpace.EPageKey.P_FIRST_BINDING,
  });

  /** 获取第三方账户的绑定状态 */
  const onGetAccountBindingState = async () => {
    BaseInfoManager.changeLoadingModalVisible(true);
    let result = await getAccountBindingState3rd();

    if (result.code === 0) {
      const { FACEBOOK, GOOGLE } = result.data;
      startTransition(() => {
        setState({
          facebook: FACEBOOK === UserEnumsSpace.EStatusType.YES,
          google: GOOGLE === UserEnumsSpace.EStatusType.YES,
        });
      });
    }
    BaseInfoManager.changeLoadingModalVisible(false);
  };

  const handGoogleBindng = async () => {
    const { google } = state;
    TrackEvent.trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_BING,
        e: HitPointEnumsSpace.EEventKey.BTN_BINDING_GOOGLE,
      },
      '1',
    );
    if (!google) {
      try {
        BaseInfoManager.changeLoadingModalVisible(true);
        // Check if your device supports Google Play
        const isHasPlayServices = await GoogleSignin.hasPlayServices({
          showPlayServicesUpdateDialog: true,
        });
        if (isHasPlayServices) {
          if (await GoogleSignin.isSignedIn()) {
            await GoogleSignin.signOut();
          }
          const idToken = (await GoogleSignin.signIn()).idToken || '';
          if (idToken) {
            const result = await submitAccountBinding({
              oauthType: 'GOOGLE',
              accessToken: idToken,
            });
            if (result.code === 0) {
              startTransition(() => {
                setState(preState => ({
                  ...preState,
                  google: true,
                }));
              });
            }
          }
        }
      } catch (error) {
        log.error('google sign error', {
          error,
        });
      }
      BaseInfoManager.changeLoadingModalVisible(false);
    }
  };

  return {
    isPending,
    ...state,
    handGoogleBindng,
    // handleFacebookBinding,
  };
};

/** 谷歌账号绑定 */
const Account3rdBindingPage = (): ReactElement => {
  const {
    facebook,
    google,
    handGoogleBindng,
    // handleFacebookBinding
  } = useData();

  /** 跳过 */
  const handleSkip = useCallback(async () => {
    TrackEvent.trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_FIRST_BINDING,
        e: HitPointEnumsSpace.EEventKey.BTN_SKIP,
      },
      '1',
    );
    BaseInfoManager.changeLoadingModalVisible(true);
    await fetchSkipGPBind();
    if (UserInfoManager.context.userModel.isUserTypeNew) {
      // const result = await fetchRKQuestionnaireEnterLabel();
      // if (result.code !== 0) {
      //   // 失败上报获取风控问卷结果失败埋点
      //   TrackEvent.trackCommonEvent(
      //     {
      //       p: HitPointEnumsSpace.EPageKey.P_FIRST_BINDING,
      //       e: HitPointEnumsSpace.EEventKey.FETCH_QUESTION_FAILED,
      //     },
      //     '1',
      //   );
      // }
    }
    if (await UserInfoManager.updateUserState()) {
      nav.nextToTopRouter();
    }
    BaseInfoManager.changeLoadingModalVisible(false);
  }, []);

  /** 下一步 */
  const handleNext = useCallback(async () => {
    TrackEvent.trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_FIRST_BINDING,
        e: HitPointEnumsSpace.EEventKey.BTN_CONTINUE,
      },
      '1',
    );
    BaseInfoManager.changeLoadingModalVisible(true);
    if (UserInfoManager.context.userModel.isUserTypeNew) {
      // const result = await fetchRKQuestionnaireEnterLabel();
      // if (result.code !== 0) {
      //   // 失败上报获取风控问卷结果失败埋点
      //   TrackEvent.trackCommonEvent(
      //     {
      //       p: HitPointEnumsSpace.EPageKey.P_FIRST_BINDING,
      //       e: HitPointEnumsSpace.EEventKey.FETCH_QUESTION_FAILED,
      //     },
      //     '1',
      //   );
      // }
    }
    if (await UserInfoManager.updateUserState()) {
      BaseInfoManager.changeLoadingModalVisible(false);
      nav.nextToTopRouter();
    }
  }, []);

  // gmail 绑定卡片
  const $gmailBindingCard = useMemo(() => {
    return (
      <View
        padding="12 16 12 16"
        margin="16 0 0 0"
        cardType="baseType"
        layoutStrategy="flexRowBetweenCenter">
        <View>
          <Image margin="0 0 8 0" name="_google" />
          <Text
            i18nKey="accountBindingString.gmailDes"
            category="c1"
            style={{
              flex: 1,
              color: 'text-color-600',
            }}
          />
        </View>
        <Button
          onPress={handGoogleBindng}
          style={{ borderRadius: 99 }}
          disabled={google}
          disabledTipTextI18nKey="accountBindingString.accountBinded"
          textI18nKey={google ? 'btnString.linked' : 'btnString.link'}></Button>
      </View>
    );
  }, [google]);

  /** 绑定提额的引导文案 */
  const $topCard = useMemo(() => {
    return (
      <View
        padding="8 16 8 16"
        style={{ backgroundColor: 'primary-color-100', borderRadius: 8 }}
        layoutStrategy="flexRowBetweenCenter">
        <Text
          i18nKey="accountBindingString.firstTopTip"
          category="p1"
          style={{
            flex: 1,
          }}
        />
      </View>
    );
  }, []);

  const $Button = useMemo(() => {
    if (google) {
      return (
        <View
          padding="16 16 16 16"
          style={{
            backgroundColor: 'background-color-0',
            // borderColor: 'line-color-200',
            // borderTopWidth: 1,
          }}>
          <Button status="primary" onPress={handleNext} textI18nKey="btnString.continue" />
        </View>
      );
    }
    return (
      <View
        padding="16 16 16 16"
        style={{
          backgroundColor: 'background-color-0',
          // borderColor: 'line-color-200',
          // borderTopWidth: 1,
        }}>
        <Button
          status="primary"
          appearance="outline"
          onPress={handleSkip}
          textI18nKey="btnString.ignore"
        />
      </View>
    );
  }, [google]);

  return (
    <>
      <Layout pLevel="0" level="1">
        <TopNavigation titleKey="accountBindingString.title" bottomLine />
        <ScrollView fadingEdgeLength={10} style={{ marginHorizontal: 16, marginVertical: 16 }}>
          {$topCard}
          {/* {$facebokBindingCard} */}
          {$gmailBindingCard}
        </ScrollView>
        {$Button}
      </Layout>
    </>
  );
};

export default memo(Account3rdBindingPage);
