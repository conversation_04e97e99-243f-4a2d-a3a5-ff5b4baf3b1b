/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react-native/no-inline-styles */
/* eslint-disable react/react-in-jsx-scope */
import {
  AmountSlider,
  Button,
  Image,
  Layouts,
  Text,
  View,
  LoanDatePickerInput,
  DashedLine,
} from '@/components';
import { HitPointEnumsSpace } from '@/enums';
import { Strings, useNameSpace } from '@/i18n';
import { OrderResponseSpace } from '@/server';
import { trackCommonEvent } from '@/trackEvent';
import _ from 'lodash';
import { memo, useCallback, useMemo } from 'react';
import { GestureResponderEvent, ScrollView, TouchableOpacity } from 'react-native';
import { IState } from '../../useData';
import React from 'react';
import { OrderVOSpace } from '@/types';
import { modalDataStoreInstance, ModalList, BaseInfoManager } from '@/managers';
import { Colors } from '@/themes';
import IncreaseCouponAmountBadge from '../../../../components/coupon/IncreaseCouponAmountBadge';

interface IProps extends IState {
  // onChangeIsReadContract: (isReadContract: boolean) => void;
  onChangeAmount: (defaultAmount: number) => void;
  onUpdateAmount: (defaultAmount: number) => void;
  amount: string;
  term: string;
  // handleClick: (event: GestureResponderEvent) => void;
  onGetHomePageAmout: (event: GestureResponderEvent) => void;
  isPending: boolean;
  loanAmount: number;
  loanAmountConfigList: IState[];
  selectedAmountConfigNumber: number;
  onChangeAmountConfigNumber: (num: number) => void;
  calculateCost: OrderVOSpace.CalculateCostOnFirstLoanType;
  /** 可选日期 */
  loanRepayDays: OrderVOSpace.LoanRepayDays;
  /** 还款日期 */
  repayDate: string;
  /** 变更还款日期 */
  onChangeRepayDate: (repayDate: string) => void;
  onToggleRepayDateModal: () => void;
  calendarExtraData?: any;
}
const AmountSliderView = function AmountSliderView(props: IProps) {
  const {
    // isReadContract,
    // onChangeIsReadContract,
    days = 0,
    minAmount = 0,
    maxAmount = 0,
    defaultAmount = 0,
    loanAmount = 0,
    increment = 0,
    onChangeAmount,
    onUpdateAmount,
    // handleClick,
    onGetHomePageAmout,
    isPending,
    loanAmountConfigList,
    selectedAmountConfigNumber,
    onChangeAmountConfigNumber,
    calculateCost,
    repayDate,
    onChangeRepayDate,
    loanRepayDays,
    onToggleRepayDateModal,
    calendarExtraData,
  } = props;

  const onSlidingComplete = (value: number) => {
    trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_AMOUNT,
        e: HitPointEnumsSpace.EEventKey.E_BTN_CHOOSE_FIRST_AMOUNT,
      },
      String(value),
    );
    onChangeAmount(value);
  };

  const onValueChange = (value: number) => {
    onUpdateAmount(value);
  };

  const handleOpenHotline = useCallback(() => {
    modalDataStoreInstance.openModal({
      key: ModalList.HOT_LINE,
    });
  }, []);

  const currentSelAmount = useMemo(() => {
    if (loanAmount != 0) {
      return String(loanAmount).toFormatFinance();
    } else {
      return '--'.toFormatFinance();
    }
  }, [loanAmount]);

  const t = useNameSpace().t;

  const onSelectTerm = ({
    value,
    index,
    enabled,
    seleted,
  }: {
    value: number;
    index: number;
    enabled: boolean;
    seleted: boolean;
  }) => {
    trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_AMOUNT,
        e: HitPointEnumsSpace.EEventKey.BTN_DATE_CHANGE,
      },
      String(value),
    );
    if (!enabled) {
      modalDataStoreInstance.openModal({
        key: ModalList.INFO_PROMPT_CONFIRM,
        i18nKey: Strings.directPaymentString.pls_locked_days,
        imageKey: '_epModalNormalIcon',
        confirmBtnName: 'btnString.approve',
        isBackdropClose: false,
      });
      // Toast(t('homeString.lockLoanTremClickTip'));
    }
    if (enabled && !seleted) {
      // 记录用户选择额度的事件
      onChangeAmountConfigNumber(index);
    }
  };

  const renderTermButtonView = useCallback(
    (loanAmountConfig: IState, index: number) => {
      const { days, enable, productType, installmentCount } = loanAmountConfig;

      const enabled = enable === 'YES';

      const seleted = selectedAmountConfigNumber === index;
      // const onSelect = _.throttle(() => {
      //   trackCommonEvent(
      //     {
      //       p: HitPointEnumsSpace.EPageKey.P_AMOUNT,
      //       e: HitPointEnumsSpace.EEventKey.BTN_DATE_CHANGE,
      //     },
      //     String(days),
      //     enable ? '1' : '0',
      //   );
      //   if (!enabled) {
      //     modalDataStoreInstance.openModal({
      //       key: ModalList.INFO_PROMPT_CONFIRM,
      //       i18nKey: 'homeString.lockTermPressTips',
      //       imageKey: '_epModalNormalIcon',
      //       confirmBtnName: 'btnString.approve',
      //       isBackdropClose: false,
      //     });
      //     // Toast(t('homeString.lockLoanTremClickTip'));
      //   }
      //   if (enabled && !seleted) {
      //     // 记录用户选择额度的事件

      //     onChangeAmountConfigNumber(index);
      //   }
      // }, 300);

      // const onDisabledCallback = () => {
      //   modalDataStoreInstance.openModal({
      //     key: ModalList.INFO_PROMPT_CONFIRM,
      //     i18nKey: 'multiPeriodString.disabledTips',
      //     imageKey: '_epModalNormalIcon',
      //     confirmBtnName: 'btnString.agree',
      //     isBackdropClose: false,
      //   });
      // };

      const textColor = seleted ? '#FFF' : 'primary-color-300';
      return (
        <Button
          margin="0 3 0 3"
          padding="0 12 0 12"
          height={40}
          width={80}
          onPress={() => onSelectTerm({ value: days, index, enabled, seleted })}
          // onDisabledCallback={onDisabledCallback}
          key={index.toString() + days}
          style={{
            position: 'relative',
            borderColor: seleted ? Colors.PRIMARY_COLOR_500 : Colors.PRIMARY_COLOR_300,
            borderWidth: 1,
            borderRadius: 8,
            minWidth: undefined,
            backgroundColor: seleted ? Colors.PRIMARY_COLOR_500 : Colors.BACKGROUND_COLOR_0,
          }}>
          {!enabled && (
            <Image
              name="_lockTipIcon"
              style={{
                position: 'absolute',
                top: 6,
                right: 14,
                tintColor: 'primary-color-300',
              }}
            />
          )}
          <View layoutStrategy="flexColumnCenterCenter">
            <Text
              category={selectedAmountConfigNumber === index ? 'p1' : 'p2'}
              bold="bold"
              style={{ color: textColor }}
              status="primary"
              textContent={String(days).toFormatDay()}
            />
            {/* {productType === 'INSTALLMENT' && (
              <Text
                category="c2"
                bold="bold"
                style={{color: textColor}}
                status="primary"
                textContent={String(installmentCount).toFormatPeriod()}
              />
            )} */}
          </View>
        </Button>
      );
    },
    [selectedAmountConfigNumber, onChangeAmountConfigNumber, t],
  );

  const $selectTermView = useMemo(() => {
    const amountConfigListLen = loanAmountConfigList.length;
    if (amountConfigListLen > 3) {
      return (
        <ScrollView horizontal fadingEdgeLength={10} keyboardShouldPersistTaps="always">
          {(loanAmountConfigList || []).map((loanAmountConfig, index) => {
            return renderTermButtonView(loanAmountConfig, index);
          })}
        </ScrollView>
      );
    } else {
      return (
        <View padding="8 0 8 0" layoutStrategy="flexRowBetweenCenter">
          {(loanAmountConfigList || []).map((loanAmountConfig, index) => {
            return renderTermButtonView(loanAmountConfig, index);
          })}
        </View>
      );
    }
  }, [loanAmountConfigList, selectedAmountConfigNumber, onChangeAmountConfigNumber]);

  /** 刷新选择额度 view */
  const $selectAmountView = useMemo(() => {
    /** @todo 数据加载时默认展示, 后续可优化加载过程 */
    if (!isPending) {
      return (
        <>
          <AmountSlider
            animationType="spring"
            value={defaultAmount}
            minimumValue={minAmount}
            maximumValue={maxAmount}
            onSlidingComplete={value => onSlidingComplete(Number(value))}
            onValueChange={value => onValueChange(Number(value))}
            step={increment}
          />
        </>
      );
    }

    if (!days || !defaultAmount) {
      return (
        <>
          <View
            margin="4 0 0 0"
            style={{
              width: '100%',
              display: 'flex',
              flexDirection: 'row',
              justifyContent: 'center',
              alignItems: 'center',
              flexWrap: 'wrap',
            }}>
            <Button
              onPress={onGetHomePageAmout}
              style={{
                width: 144,
              }}>
              <Image name="_reCycle" />
              <Text
                i18nKey="homeString.update"
                style={{
                  color: 'text-color-0',
                  zIndex: 1,
                }}
              />
            </Button>
          </View>
          <View
            margin="12 0 0 0"
            padding="8 12 8 12"
            style={{
              backgroundColor: 'secondary-color-100',
              borderRadius: 10,
            }}>
            <Text
              isCenter={true}
              i18nKey="homeString.update_select_amount"
              style={{
                color: 'text-color-800',
                zIndex: 1,
              }}
            />
          </View>
        </>
      );
    }
    return (
      <>
        <AmountSlider
          animationType="spring"
          value={defaultAmount}
          minimumValue={minAmount}
          maximumValue={maxAmount}
          onSlidingComplete={value => onSlidingComplete(Number(value))}
          onValueChange={value => onValueChange(Number(value))}
          step={increment}
        />
      </>
    );
  }, [days, defaultAmount, isPending]);

  return (
    <>
      <View
        margin="0 16 0 16"
        style={{
          backgroundColor: 'background-color-0',
          borderRadius: 8,
          overflow: 'hidden',
        }}>
        {/* <View
          padding="4 8 4 8"
          style={{
            backgroundColor: 'primary-color-500',
            borderRadius: 8,
            overflow: 'hidden',
          }}
          layoutStrategy="flexRowBetweenCenter">
          <Image margin="0 10 0 0" name="_soundBlackIcon" />
          <Text
            style={{flex: 1, color: 'text-color-700'}}
            category="c1"
            i18nKey={'homeString.reloanTip'}
          />
        </View> */}
        <View padding={'12 16 16 16'}>
          <Text
            style={{ color: 'text-color-700', fontSize: 14, alignSelf: 'center' }}
            category="p1"
            i18nKey={Strings.homeString.loanAmountUpToNew}
          />
          <View layoutStrategy="flexRowCenterCenter">
            {/* <Image margin="8 8 0 0" name="_amountIcon" /> */}
            <Text
              category="h1"
              isCenter={true}
              textContent={currentSelAmount}
              bold={'bold'}
              margin={'12 12 0 0'}
              style={{
                color: 'text-color-800',
              }}
            />
            <IncreaseCouponAmountBadge
              couponUseType={calculateCost.couponUseType}
              amount={
                calculateCost?.couponUseType === 'amount'
                  ? calculateCost.creditCouponAmount
                  : (Number(calculateCost.creditCouponPercent) * 100).toString()
              }
            />
          </View>
          {$selectAmountView}
          <DashedLine dashColor={Colors.TEXT_COLOR_400} style={{ paddingTop: 28 }} />
          <Text
            category="p1"
            style={{
              color: 'text-color-700',
              fontSize: 14,
              alignSelf: 'center',
            }}
            i18nKey={
              BaseInfoManager.context.baseModel.repayDateSwitch
                ? 'homeString.repayDateTip'
                : 'directPaymentString.loan_days_tips'
            }
            margin={'12 0 6 0'}
          />
          {BaseInfoManager.context.baseModel.repayDateSwitch ? (
            <>
              <LoanDatePickerInput
                repayDate={repayDate || ''}
                onChangeRepayDate={onChangeRepayDate}
                loanRepayDays={loanRepayDays}
                isFirstLoan={true}
                onFocus={onToggleRepayDateModal}
                extraData={calendarExtraData}
              />
              <View
                margin="9 0 0 0"
                layoutStrategy="flexRowStartCenter"
                style={{ alignItems: 'flex-start' }}>
                <Image name="_grayInfo" resizeMode="contain" margin="2 0 0 0" />
                <Text
                  margin="0 0 0 4"
                  category="c1"
                  i18nKey={'homeString.repayDateSelectTips'}
                  style={{
                    color: 'text-color-500',
                    flex: 1,
                  }}
                />
              </View>
            </>
          ) : (
            $selectTermView
          )}
        </View>
      </View>
    </>
  );
};

export default memo(AmountSliderView);
