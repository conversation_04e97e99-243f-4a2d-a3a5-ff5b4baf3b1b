/* eslint-disable react/react-in-jsx-scope */
/**
 * @description 滑块额度选择页，开关为firstSelectAmountSlideSwitch
 */

import React from 'react';
import { Layout, TopNavigation, View, Button, Text, Image } from '@/components';
import { ScreenProps } from '@/types';
import { ReactElement } from 'react';
import { Dimensions, RefreshControl, ScrollView } from 'react-native';
import AmountSliderView from './components/AmountSliderView';
import useData from './useData';

import { HitPointEnumsSpace } from '@/enums';
import { useTheme } from '@/hooks';
import _ from 'lodash';
import RenderHtml from 'react-native-render-html';
import CouponHelpModal from '../../components/coupon/CouponHelpModal';
import CouponSelectCard from '../../components/coupon/CouponSelectCard';
import CouponSelectModal from '../../components/coupon/CouponSelectModal';
import LoanDetailCard from '../../components/coupon/LoanDetailCard';
import LoanDetailModal from '../../components/coupon/LoanDetailModal';
import { Colors } from '@/themes';

export default ({}: ScreenProps<{}>): ReactElement => {
  const theme = useTheme();
  const {
    isPending,
    refreshing,
    onRefresh,
    handleClick,
    loanAmount,
    loanAmountConfigList,
    selectedAmountConfigNumber,
    tips,
    calculateCost,
    couponSelected,
    couponList,
    showCouponHelpModal,
    showCouponSelectModal,
    onCloseCouponHelpModal,
    onCloseCouponSelectModal,
    onConfirmSelectCoupon,
    onShowCouponHelpModal,
    onShowCouponSelectModal,
    onShowLoanDetailModal,
    onCloseLoanDetailModal,
    showLoanDetailModal,
    disable,
    ...props
  } = useData();

  return (
    <>
      <Layout
        pLevel="0"
        level="1"
        topCompensateColor="transprant"
        topBgImageName="_evidenceSelectAmountHeader">
        <TopNavigation
          showLogoAction={true}
          bottomLine={false}
          pageKey={HitPointEnumsSpace.EPageKey.P_AMOUNT}
          showMessage
          type="primary"
          isShowVip={false}
        />
        <ScrollView
          keyboardShouldPersistTaps="always"
          refreshControl={
            <RefreshControl
              colors={[theme['primary-color-500']]}
              refreshing={refreshing}
              onRefresh={onRefresh}
            />
          }>
          <View margin="20 0 0 0">
            <AmountSliderView
              loanAmount={loanAmount}
              loanAmountConfigList={loanAmountConfigList}
              selectedAmountConfigNumber={selectedAmountConfigNumber}
              isPending={isPending}
              calculateCost={calculateCost}
              {...props}
            />
            <CouponSelectCard
              couponList={couponList}
              selectCouponList={couponSelected}
              onOpenCouponHelp={onShowCouponHelpModal}
              onOpenCouponSelect={onShowCouponSelectModal}
            />
            <LoanDetailCard data={calculateCost} onOpenLoanDetailModal={onShowLoanDetailModal} />
            <View
              margin="16 16 40 16"
              padding="12 12 12 12"
              style={{
                backgroundColor: 'background-color-0',
                borderRadius: 8,
              }}>
              <View layoutStrategy="flexRowStartCenter">
                <Image name="_blackNotice" />
                <Text
                  margin="0 10 0 10"
                  i18nKey={'loanConfirmString.loan_notice_title'}
                  style={{ color: Colors.TEXT_COLOR_700 }}
                />
              </View>
              <RenderHtml
                contentWidth={Dimensions.get('window').width - 56}
                source={{ html: tips || '' }}
              />
            </View>

            {/* <VipUpLevelTipCard
                pageKey={HitPointEnumsSpace.EPageKey.P_HOMEPAGE}
              />
              <ActivitySwiper
                // @ts-ignore
                ref={activitySwiperRef}
                margin="0 16 12 16"
                location={BaseEnumsSpace.EBannerLocationType.RELOAN_HOME}
              /> */}
          </View>
        </ScrollView>
        <View
          padding={'8 32 32 32'}
          style={{
            backgroundColor: 'background-color-0',
            borderBottomLeftRadius: 8,
            borderBottomRightRadius: 8,
          }}>
          <Button
            margin="8 0 0 0"
            padding="12 0 12 0"
            status="primary"
            onPress={handleClick}
            textI18nKey="btnString.next"
            disabled={disable}
          />
        </View>
        <CouponHelpModal visible={showCouponHelpModal} onClose={onCloseCouponHelpModal} />
        <CouponSelectModal
          visible={showCouponSelectModal}
          onCancel={onCloseCouponSelectModal}
          couponList={couponList}
          availableCouponList={couponList}
          couponSelected={couponSelected}
          onConfirm={onConfirmSelectCoupon}
          pageKey={HitPointEnumsSpace.EPageKey.P_AMOUNT}
        />
        <LoanDetailModal
          visible={showLoanDetailModal}
          onClose={onCloseLoanDetailModal}
          data={calculateCost as any}
        />
      </Layout>
    </>
  );
};
