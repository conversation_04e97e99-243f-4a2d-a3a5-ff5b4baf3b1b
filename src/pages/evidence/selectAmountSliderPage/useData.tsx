/** 使用滑块选择 */

/* eslint-disable @typescript-eslint/no-shadow */
import { BaseEnumsSpace, HitPointEnumsSpace, UserEnumsSpace, EProductType } from '@/enums';
import { useCheckAllPermissionAndRequestPermission, useOnInit } from '@/hooks';
import { BaseInfoManager, ModalList, UserInfoManager, modalDataStoreInstance } from '@/managers';
import { Toast } from '@/nativeComponents';
import { RouterConfig } from '@/routes';
import {
  OrderResponseSpace,
  fetchAmountConfigOnFirstLoanNew,
  fetchCalculateCostOnFirstLoan,
  fetchFirstPreLoanApply,
  getOnFirstLoanAmountConfigDataInfo,
  saveFirstLoanAmountConfig,
  fetchCouponsByConditions,
  fetchLoanFixedDateHoliday,
} from '@/server';
import { OrderVOSpace, UserVOSpace } from '@/types';
import { TrackEvent, isHasAllPermission, nav } from '@/utils';
import { useMemo, useRef, useState, useTransition, useCallback } from 'react';
import { trackCommonEvent } from '@/trackEvent';

export interface IState extends OrderResponseSpace.RooutAmountData, OrderVOSpace.ProductPriceItem {
  loanAmount: number;
  calculateCost: OrderVOSpace.CalculateCostOnFirstLoanType;
  repayDate: string;
}

const defaultCalculateCost: OrderVOSpace.CalculateCostOnFirstLoanType = {
  loanAmount: '--',
  transferFee: '--',
  transferFeeVat: '--',
  processFee: '--',
  processFeeVat: '--',
  repaymentAmount: '--',
  days: '--',
  loanDate: '--',
  loanDateNewFormat: '--',
  repayDate: '--',
  repayDateNewFormat: '--',
  showApplyCost: 'NO',
  realAmount: '--',
  realRepaymentAmount: '--',
  creditCouponAmount: '--',
  creditCouponPercent: '--',
  couponUseType: 'amount',
};

const defaultAmountState: IState = {
  amount: '---',
  term: '---',
  days: 0,
  minAmount: 0,
  maxAmount: 0,
  loanAmount: 0,
  defaultAmount: 0,
  increment: 0,
  enable: 'YES',
  calculateCost: defaultCalculateCost,
  installmentCount: 0,
  productType: EProductType.FIXED_DAY,
  repayDate: '',
};

export default function useData() {
  const [isPending, startTransition] = useTransition();
  // 选中的额度配置编号
  const [selectedAmountConfigNumber, setSelectedAmountConfigNumber] = useState<number>(0);

  const [loanAmountConfigList, setLoanAmountConfigList] = useState<IState[]>([]);
  // 复贷额度或还款日期是否变化，用于判断是否需要刷新，true 则不刷新
  const reloanAmountConfigChangedRef = useRef<boolean>(false);

  // 新户
  const [state, setState] = useState<IState>(defaultAmountState);

  /** 提示 */
  const [tips, setTips] = useState<string>('');
  // 优惠券
  const [couponList, setCouponList] = useState<UserVOSpace.CouponsItem[]>([]);
  // 选择的优惠券
  const [couponSelected, setCouponSelected] = useState<UserVOSpace.CouponsItem[]>([]);
  const couponSelectedRef = useRef<UserVOSpace.CouponsItem[]>([]);

  const [showCouponHelpModal, setShowCouponHelpModal] = useState<boolean>(false);
  const [showCouponSelectModal, setShowCouponSelectModal] = useState<boolean>(false);
  const [showLoanDetailModal, setShowLoanDetailModal] = useState<boolean>(false);

  // 可选日期范围和不可选的节假日
  const [loanRepayDays, setLoanRepayDays] = useState<OrderVOSpace.LoanRepayDays>({
    startDate: '',
    endDate: '',
    holidays: [],
  });
  const [repayDate, setRepayDate] = useState<string>('');

  // 复贷
  const amountState = useMemo(() => {
    if (loanAmountConfigList.length === 0) {
      // 新户或者老户未获取额度
      return state;
    } else {
      // 老用户已经获取额度配置
      return loanAmountConfigList[selectedAmountConfigNumber];
    }
  }, [selectedAmountConfigNumber, loanAmountConfigList, state]);

  const onInit = async (refreshing: boolean = false) => {
    BaseInfoManager.changeLoadingModalVisible(true);
    /** 进入首页上报一次数据 */
    await TrackEvent.uploadEventLog();
    if (!(await UserInfoManager.updateUserState())) {
      loading.current = false;
      BaseInfoManager.changeLoadingModalVisible(false);
      return;
    }
    onGetLoanHoliday();
    /** 没有修改过额度的老用户获取滚动条额度 */
    if (!(await onGetFirstLoanAmountConfig()) && !reloanAmountConfigChangedRef.current) {
      loading.current = false;
      BaseInfoManager.changeLoadingModalVisible(false);
      return;
    }

    loading.current = false;
    BaseInfoManager.changeLoadingModalVisible(false);
  };

  /** 初始化方法 */
  const { loading, refreshing, onRefresh } = useOnInit({
    callback: onInit,
    pageKey: HitPointEnumsSpace.EPageKey.P_AMOUNT,
  });
  const getCouponSerialNumbers = (
    couponList: UserVOSpace.CouponsItem[] = couponSelectedRef.current,
  ) => {
    const serialNumbers = couponList.map(i => i.serialNumber).join(',') || '';
    return serialNumbers;
  };
  /** 获取可用的还款优惠券 */
  const getRepayCouponList = useCallback(async () => {
    const { code, data } = await fetchCouponsByConditions({
      /** 优惠券状态 */
      status: UserEnumsSpace.ECouponsStatus.AVAILABLE,
    });
    if (code === 0) {
      // 这里根据data需要判断是不是需要打开添加还款日历提醒的弹窗
      if (
        data.length > 0
        // && !UserInfoManager.context.userModel.isCreditSuccessWaitFace
      ) {
        const _data = data.filter(item => item.canCheck === 'YES');
        const selectCoupons = _data.filter(
          item => item.couponStatus === UserEnumsSpace.ECouponsStatus.BINDING,
        );
        const _selectCoupons = couponSelectedRef.current.length
          ? couponSelectedRef.current
          : selectCoupons;
        setCouponSelected(_selectCoupons);
        setCouponList(_data);
      }
    }
    return true;
  }, []);

  /** 获取首页信息 */
  const onGetHomePageAmout = async () => {
    onGetFirstLoanAmountConfig();
  };
  /** 获取可选日期范围和节假日（日历组件不可选） */
  const onGetLoanHoliday = async () => {
    const result = await fetchLoanFixedDateHoliday();
    if (result?.code === BaseEnumsSpace.ENetworkStatus.SUCCESS) {
      setLoanRepayDays(result.data);
    }
  };
  /** 创建预申请单 */
  const onSaveProductAmount = (): Promise<string> => {
    TrackEvent.trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_AMOUNT,
        e: HitPointEnumsSpace.EEventKey.BTN_FIRST_LOAN_APPLY,
      },
      '1',
    );

    return new Promise(async resolve => {
      const theFirstAmountConfig: any = {
        amount: amountState.loanAmount,
        productType: amountState.productType,
        couponSerialNumber: getCouponSerialNumbers(),
      };
      switch (amountState.productType) {
        case EProductType.FIXED_DAY:
          theFirstAmountConfig.loanTerm = amountState.days;
          break;
        case EProductType.FIXED_DATE:
          theFirstAmountConfig.repayDate = repayDate;
          break;
      }
      const result = await saveFirstLoanAmountConfig(theFirstAmountConfig);
      if (result?.code === BaseEnumsSpace.ENetworkStatus.SUCCESS) {
        await UserInfoManager.updateUserState();
        nav.nextToTopRouter(RouterConfig.SELECT_AMOUNT_SLIDER);
        BaseInfoManager.changeLoadingModalVisible(false);
      }
      resolve('');
    });
  };

  /** 获取复贷额度配置 */
  const onGetFirstLoanAmountConfig = async () => {
    const DEFAULT_MIN_AMOUNT = 200;
    const DEFAULT_MAX_AMOUNT = 500;
    const DEFAULT_INCREMENT = 100;
    if (loanAmountConfigList.length === 0) {
      let result = await fetchAmountConfigOnFirstLoanNew({
        productType: BaseInfoManager.context.baseModel.repayDateSwitch
          ? EProductType.FIXED_DATE
          : EProductType.FIXED_DAY,
      });
      if (result?.code === BaseEnumsSpace.ENetworkStatus.SUCCESS) {
        setTips(result?.data.tips || '');
        if (result?.data.productPriceList?.length !== 0) {
          let amountConfigList: IState[] = [];
          result.data.productPriceList.forEach((amountConfig: OrderVOSpace.ProductPriceItem) => {
            let {
              days,
              minAmount,
              maxAmount,
              increment,
              defaultAmount,
              installmentCount,
              productType,
            } = amountConfig;
            // 步进值纠正
            increment = Math.floor(increment ? DEFAULT_INCREMENT : Number(increment));
            // 最小值修正
            minAmount = Math.floor(Number(minAmount));
            // 最大值修正
            maxAmount = Math.floor(Number(maxAmount));
            // 最小值整除于步进值
            if (minAmount % increment != 0) {
              minAmount = Math.floor(minAmount) * increment;
            }
            // 最大值整除于步进值
            if (maxAmount % increment != 0) {
              maxAmount = Math.floor(maxAmount / increment) * increment;
            }
            // 最小金额最小值修正
            if (minAmount < DEFAULT_MIN_AMOUNT) {
              minAmount = DEFAULT_MIN_AMOUNT;
            }
            // 最大金额最小值修正;
            if (maxAmount < DEFAULT_MAX_AMOUNT) {
              maxAmount = DEFAULT_MAX_AMOUNT;
            }
            // 最大值必须大于最小值一个步进值
            if (minAmount >= maxAmount) {
              maxAmount = minAmount + increment;
            }
            const defaultMinAmount = minAmount || DEFAULT_MIN_AMOUNT;
            const defaultSelectedAmount =
              Number(defaultAmount) >= minAmount && Number(defaultAmount) <= maxAmount
                ? defaultAmount
                : maxAmount - increment;
            amountConfigList.push({
              ...defaultAmountState,
              ...amountConfig,
              days,
              minAmount: defaultMinAmount,
              maxAmount,
              defaultAmount: defaultSelectedAmount,
              increment,
              calculateCost: defaultCalculateCost,
              installmentCount,
              productType,
            });
          });
          onChangeAmount(
            amountConfigList[0].defaultAmount,
            amountConfigList[0].days,
            amountConfigList[0].installmentCount,
          );
          startTransition(() => {
            setState((preState: IState) => ({
              ...preState,
              ...amountConfigList[0],
            }));
            setLoanAmountConfigList(amountConfigList);
          });
        }
      }
      return result?.code === BaseEnumsSpace.ENetworkStatus.SUCCESS;
    }
    return true;
  };

  /** 获取复贷试算 */
  const onGetCalculateCost = async (
    selectedAmount: number = 0,
    selectedDays: number = 8,
    selectedPeriods: number = 1,
    selectedRepayDate: string = '',
  ) => {
    if (!selectedAmount) {
      return;
    }
    /** 还款日期没有选择 */
    if (BaseInfoManager.context.baseModel.repayDateSwitch && selectedRepayDate === '') {
      return;
    }
    const couponSerialNumber = getCouponSerialNumbers();
    BaseInfoManager.changeLoadingModalVisible(true);
    let result = await fetchCalculateCostOnFirstLoan({
      selectedAmount: String(selectedAmount),
      selectedDays: String(selectedDays),
      selectedPeriods: String(selectedPeriods),
      couponSerialNumber,
      repayDate: selectedRepayDate,
    });
    BaseInfoManager.changeLoadingModalVisible(false);
    if (result?.code === BaseEnumsSpace.ENetworkStatus.SUCCESS) {
      setLoanAmountConfigList((preState: IState[]) => {
        preState[selectedAmountConfigNumber] = {
          ...preState[selectedAmountConfigNumber],
          calculateCost: result.data,
        };
        return [...preState];
      });
      await getRepayCouponList();
      return result?.data;
    }
    return undefined;
  };

  /** 刷新首贷额度配置 */
  const onReferFirstLoanAmountConfig = async () => {
    loading.current = true;
    BaseInfoManager.changeLoadingModalVisible(true);
    await onGetFirstLoanAmountConfig();
    loading.current = false;
    BaseInfoManager.changeLoadingModalVisible(false);
  };

  const [checkPermission] = useCheckAllPermissionAndRequestPermission();

  const handleNext = async () => {
    BaseInfoManager.changeLoadingModalVisible(true);

    if (!(await UserInfoManager.updateUserState())) {
      BaseInfoManager.changeLoadingModalVisible(false);
      return;
    }

    nextLogic();
  };

  /** 开始贷款点击 */
  const handleClick = async () => {
    loading.current = true;

    // if (UserInfoManager.context.userModel.isUserTypeNew) {
    //   // 请求风控数据
    //   fetchRKPushQuestionnaire();
    // }

    if (!(await isHasAllPermission())) {
      let modalId = modalDataStoreInstance.openModal({
        key: ModalList.PERMISSION_APPLICATION,
        confirmBtnCallback: async () => {
          TrackEvent.trackCommonEvent(
            {
              p: HitPointEnumsSpace.EPageKey.P_AMOUNT,
              e: HitPointEnumsSpace.EEventKey.BTN_POST_PERMISSION,
            },
            '1',
          );

          if (await checkPermission()) {
            await handleNext();
          }
        },
        cancelBtnCallback: () => {
          TrackEvent.trackCommonEvent(
            {
              p: HitPointEnumsSpace.EPageKey.P_AMOUNT,
              e: HitPointEnumsSpace.EEventKey.BTN_POST_PERMISSION,
            },
            '0',
          );
          modalDataStoreInstance.openModal({
            key: ModalList.INFO_PROMPT_CONFIRM,
            i18nKey: 'permissionAgreeString.peso_per_apply_again_tips',
            imageKey: '_notify',
            confirmBtnName: 'btnString.accept',
            cancelBtnName: 'btnString.noAccept',
            isBackdropClose: false,
            confirmBtnCallback: async () => {
              modalDataStoreInstance.closeModal(modalId);
              TrackEvent.trackCommonEvent(
                {
                  p: HitPointEnumsSpace.EPageKey.P_AMOUNT,
                  e: HitPointEnumsSpace.EEventKey.BTN_POST_PERMISSION,
                },
                '1',
              );

              if (await checkPermission()) {
                await handleNext();
              }
            },
            cancelBtnCallback: () => {
              modalDataStoreInstance.closeModal(modalId);
              TrackEvent.trackCommonEvent(
                {
                  p: HitPointEnumsSpace.EPageKey.P_AMOUNT,
                  e: HitPointEnumsSpace.EEventKey.BTN_POST_PERMISSION,
                },
                '0',
              );
            },
          });
        },
      });
      loading.current = false;
      return;
    } else {
      await handleNext();
    }
  };

  /** 点击下一步逻辑 */
  const nextLogic = async () => {
    const { loanAmount, increment } = amountState;
    if (increment == 0 || loanAmount === 0) {
      modalDataStoreInstance.openModal({
        key: ModalList.INFO_PROMPT_CONFIRM,
        imageKey: '_refreshModal',
        i18nKey: 'homeString.update_select_amount',
        confirmBtnName: 'btnString.OK',
        isBackdropClose: false,
        confirmBtnCallback: () => {
          if (loanAmountConfigList.length === 0) {
            // 只有额度配置为空时，才去获取额度配置
            onReferFirstLoanAmountConfig();
          }
        },
      });
      BaseInfoManager.changeLoadingModalVisible(false);
      return;
    }
    await onSaveProductAmount();
    BaseInfoManager.changeLoadingModalVisible(false);
  };

  /** 修改选择额度 */
  const onChangeAmount = async (defaultAmount: number, day?: number, periods?: number) => {
    couponSelectedRef.current = [];
    let result = await onGetCalculateCost(
      defaultAmount,
      day || amountState.days,
      periods || amountState.installmentCount,
      repayDate,
    );
    setLoanAmountConfigList((preState: IState[]) => {
      preState[selectedAmountConfigNumber] = {
        ...preState[selectedAmountConfigNumber],
        calculateCost: result || defaultCalculateCost,
        defaultAmount,
        loanAmount: defaultAmount,
      };
      return [...preState];
    });
    setCouponSelected([]);
  };

  const onUpdateAmount = async (defaultAmount: number) => {
    reloanAmountConfigChangedRef.current = true;
    setLoanAmountConfigList((preState: IState[]) => {
      preState[selectedAmountConfigNumber] = {
        ...preState[selectedAmountConfigNumber],
        defaultAmount,
        loanAmount: defaultAmount,
      };
      return [...preState];
    });
  };

  const onChangeAmountConfigNumber = async (index: number) => {
    couponSelectedRef.current = [];
    reloanAmountConfigChangedRef.current = true;
    let amountConfig = loanAmountConfigList[index];
    let loanAmount = amountState.loanAmount;
    let defaultAmount = amountConfig.defaultAmount;
    let maxAmount = amountConfig.maxAmount;
    let minAmount = amountConfig.minAmount;
    let defaultDays = amountConfig.days;
    let installmentCount = amountConfig.installmentCount;
    let selectAmount = 0;

    if (loanAmount != 0 && loanAmount <= maxAmount && loanAmount >= minAmount) {
      selectAmount = loanAmount;
    } else if (defaultAmount >= minAmount && defaultAmount <= maxAmount) {
      selectAmount = defaultAmount;
    } else {
      selectAmount = minAmount;
    }

    // if (loanAmount != 0) {
    loanAmount = selectAmount;
    // }

    let result = await onGetCalculateCost(loanAmount, defaultDays, installmentCount, repayDate);

    setLoanAmountConfigList((preState: IState[]) => {
      preState[index] = {
        ...preState[index],
        calculateCost: result || defaultCalculateCost,
        defaultAmount: selectAmount,
        loanAmount: loanAmount,
        installmentCount: installmentCount,
      };
      return [...preState];
    });

    setSelectedAmountConfigNumber(index);
    setCouponSelected([]);
  };
  const onCloseCouponHelpModal = useCallback(() => {
    setShowCouponHelpModal(false);
  }, []);
  const onCloseCouponSelectModal = useCallback(() => {
    setShowCouponSelectModal(false);
  }, []);
  const onCloseLoanDetailModal = useCallback(() => {
    setShowLoanDetailModal(false);
  }, []);
  const onShowCouponHelpModal = useCallback(() => {
    setShowCouponHelpModal(true);
  }, []);
  const onShowCouponSelectModal = useCallback(() => {
    TrackEvent.trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_AMOUNT,
        e: HitPointEnumsSpace.EEventKey.BTN_USE_COUPON,
      },
      '1',
    );
    setShowCouponSelectModal(true);
  }, []);
  const onShowLoanDetailModal = useCallback(() => {
    setShowLoanDetailModal(true);
  }, []);
  const onConfirmSelectCoupon = useCallback(
    async (coupons?: UserVOSpace.CouponsItem[]) => {
      coupons = coupons || [];
      couponSelectedRef.current = coupons;
      setCouponSelected(coupons);
      onGetCalculateCost(
        Number(amountState.loanAmount),
        amountState.days,
        amountState.installmentCount,
        repayDate,
      );
    },
    [amountState, repayDate],
  );
  /**
   * 更改还款日期
   */
  const onChangeRepayDate = async (selectedRepayDate: string = '') => {
    trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_AMOUNT,
        e: HitPointEnumsSpace.EEventKey.BTN_CALENDAR_SELECT_DATE,
      },
      repayDate,
    );
    reloanAmountConfigChangedRef.current = true;
    let result = await onGetCalculateCost(
      Number(amountState.loanAmount),
      Number(amountState.days),
      Number(amountState.installmentCount),
      selectedRepayDate,
    );
    setLoanAmountConfigList((preState: IState[]) => {
      preState[selectedAmountConfigNumber] = {
        ...preState[selectedAmountConfigNumber],
        calculateCost: result || defaultCalculateCost,
      };
      return [...preState];
    });
    setRepayDate(selectedRepayDate);
  };

  const onToggleRepayDateModal = useCallback(() => {
    TrackEvent.trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_AMOUNT,
        e: HitPointEnumsSpace.EEventKey.F_SELECT_REPAYMENT_DATE,
      },
      '',
    );
  }, []);

  const disable = BaseInfoManager.context.baseModel.repayDateSwitch ? !repayDate : false;
  return {
    ...amountState,
    calculateCost: amountState.calculateCost || defaultCalculateCost,
    selectedAmountConfigNumber,
    tips,
    onChangeAmountConfigNumber,
    loanAmountConfigList,
    isPending,
    handleClick,
    refreshing,
    onRefresh,
    onChangeAmount,
    onUpdateAmount,
    onGetHomePageAmout,
    couponList,
    couponSelected,
    onCloseCouponHelpModal,
    onCloseCouponSelectModal,
    showCouponHelpModal,
    showCouponSelectModal,
    onConfirmSelectCoupon,
    onShowCouponHelpModal,
    onShowCouponSelectModal,
    showLoanDetailModal,
    onCloseLoanDetailModal,
    onShowLoanDetailModal,
    onChangeRepayDate,
    loanRepayDays,
    repayDate,
    disable,
    onToggleRepayDateModal,
  };
}
