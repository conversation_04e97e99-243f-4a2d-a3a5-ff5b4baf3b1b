/* eslint-disable react-native/no-inline-styles */

import { Button, Image, Layout, ProcessNav, Text, TopNavigation, View } from '@/components';
import { UserInfoManager } from '@/managers';
import { ScreenProps } from '@/types';
import React, { ReactElement, useMemo } from 'react';
import { Image as RNImage, ScrollView } from 'react-native';
import useData from './useData';
import ProgressView from '../../components/ProgressView';
import { convert2Progress } from '../utils/dataUtils';
import { EProcessStatus } from '../constants/enums';
import { Colors } from '@/themes';
import { Strings } from '@/i18n';

export default ({ navigation, route }: ScreenProps<{}>): ReactElement => {
  const { file, handleCancel, handleNext, handleGoBack, isNeedSupply } = useData({
    navigation,
    route,
  });
  const showProcessView = useMemo(() => !isNeedSupply, [isNeedSupply]);

  const TipsInfoView = (
    <View
      layoutStrategy="flexRowCenterCenter"
      margin="0 0 16 0"
      padding="12 12 12 12"
      style={{
        borderRadius: 4,
        backgroundColor: Colors.PRIMARY_COLOR_100,
        zIndex: 2,
      }}>
      <Text
        style={{ flex: 1, color: Colors.TEXT_COLOR_800 }}
        category="p1"
        i18nKey={Strings.takePhotoString.takePhotoNote}
      />
    </View>
  );

  const ImageAndDesciptionView = (
    <View
      margin="16 0 0 0"
      style={{
        backgroundColor: 'background-color-0',
        borderRadius: 8,
        overflow: 'hidden',
      }}>
      {TipsInfoView}
      <View
        margin="8 0 0 0"
        padding={'16 16 16 16'}
        style={{
          backgroundColor: 'background-color-0',
        }}>
        <View
          margin="16 0 0 0"
          style={{
            flexDirection: 'column',
            alignItems: 'center',
          }}>
          {file ? (
            <RNImage
              source={{ uri: file }}
              style={{
                height: 320,
                width: 250,
                borderRadius: 8,
              }}
            />
          ) : (
            <View
              style={{
                height: 320,
                width: 250,
                borderRadius: 8,
                backgroundColor: 'background-color-200',
              }}
            />
          )}

          <Button
            margin="24 0 0 0"
            status="primary"
            textI18nKey="btnString.takePhotoCancel"
            size="small"
            textCategory="c1"
            onPress={handleCancel}
            accessoryLeft={<Image name="_evidenceCameraIcon" margin="0 8 0 0" />}
            style={{
              marginTop: 16,
              width: 250,
            }}
          />
        </View>
      </View>
    </View>
  );

  const ButtonView = (
    <View
      padding="16 16 16 16"
      style={{
        backgroundColor: 'background-color-0',
        elevation: 2,
        // borderColor: 'line-color-200',
        // borderTopWidth: 1,
      }}>
      {!UserInfoManager.context.userModel.hasApplyOrderId && (
        <View margin="0 8 0 8" layoutStrategy="flexRowCenterCenter" style={{ alignSelf: 'center' }}>
          <Image margin="0 10 0 0" name="_safeTipIcon" />
          <Text
            category="p1"
            style={{ fontSize: 14 }}
            i18nKey={Strings.basicInfoString.protect_your_privacy}
          />
        </View>
      )}
      <Button
        margin="8 32 0 32"
        status="primary"
        onPress={handleNext}
        textI18nKey={Strings.btnString.next}
      />
    </View>
  );

  return (
    <>
      <Layout pLevel="0" level="1">
        <TopNavigation
          titleKey={'takePhotoString.takePhoto'}
          isBack={false}
          goBack={handleGoBack}
          bottomLine
        />
        {showProcessView ? (
          <ProgressView current={convert2Progress(EProcessStatus.TAKE_PHOTO_RESULT)} />
        ) : null}
        <ScrollView
          fadingEdgeLength={10}
          // keyboardShouldPersistTaps="always"
          style={{ paddingHorizontal: 16 }}>
          {ImageAndDesciptionView}
        </ScrollView>
        {ButtonView}
      </Layout>
    </>
  );
};
