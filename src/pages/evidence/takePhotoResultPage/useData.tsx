import { BaseEnumsSpace, HitPointEnumsSpace } from '@/enums';
import { useCheckAllPermissionAndRequestPermission, useDeviceDataReport, useOnInit } from '@/hooks';
import { BaseInfoManager, ModalList, UserInfoManager, modalDataStoreInstance } from '@/managers';
import { Toast } from '@/nativeComponents';
import { RouterConfig } from '@/routes';
import { fetchSelfieImage, fetchSelfieInfo, firstCancelConfirmApply } from '@/server';
import { TrackEvent, nav } from '@/utils';
import { useEffect, useMemo, useState } from 'react';
import { BackHandler } from 'react-native';
import { useNameSpace } from '../../../i18n';
import { Strings } from "@/i18n"
interface IProps {
  navigation: any;
  route: any;
}
export default function useData(props: IProps) {
  const { route } = props;
  const [file, setFile] = useState('');
  const [checkPermission] = useCheckAllPermissionAndRequestPermission();
  const { onApplyReportDeviceData } = useDeviceDataReport();
  const t = useNameSpace().t;

  /** 初始化方法 */
  useOnInit({
    callback: async () => {
      // 审核人员页面回填信息
      if (!route?.params?.uri) {
        const { code, data } = await fetchSelfieInfo();
        if (code === 0 && data) {
          setFile(data);
        }
      }
    },
    refreshCallback() { },
    pageKey: HitPointEnumsSpace.EPageKey.P_SELFIE,
  });

  useEffect(() => {
    initRouterParams();
  });

  const initRouterParams = () => {
    let uri = route?.params?.uri;
    if (uri) {
      onSetFile(uri);
    }
  };

  const isNeedSupply = useMemo(() => {
    return UserInfoManager.context.userModel.isNeedSupply;
  }, []);

  const onSetFile = (_file: string) => {
    setFile(_file);
  };

  /** 取消本次自拍照, 重新自拍 */
  const handleCancel = async () => {
    nav.replace(RouterConfig.SELFIE);
  };

  /** 下一步, 进入clabe号填写 */
  const handleNext = async () => {
    if (!((await checkPermission()) === 'agree')) {
      return;
    }
    const modalId = modalDataStoreInstance.openModal({
      key: ModalList.COUNT_UP,
      i18nKey: Strings.takePhotoString.takePhotoUploadLoadingTip,
      titleKey: Strings.takePhotoString.takePhotoUploadTimeoutTip,
      confirmBtnCallback: async () => {
        handleNext();
      }
    })
    let url = file;
    if (file.includes('http')) {
      url = '';
    }
    let result = await fetchSelfieImage(url);
    if (result?.code === BaseEnumsSpace.ENetworkStatus.SUCCESS) {
      if (!(await UserInfoManager.updateUserState())) {
        modalDataStoreInstance.closeModal(modalId);
        return;
      }
      const { isSupplyCompleted, isLoanFailNeedSupply, isHasActiveOrder, isUserTypeNew } =
        UserInfoManager.context.userModel;
      /** OCR补件场景, 创建订单 */

      /** 自拍照补件场景, 创建订单 */
      if (
        isSupplyCompleted &&
        !isLoanFailNeedSupply &&
        !isLoanFailNeedSupply &&
        !isHasActiveOrder &&
        isUserTypeNew
      ) {
        let applyId: string = await onFirstCancelConfirmApply();
        if (!applyId) {
          modalDataStoreInstance.closeModal(modalId);
          return;
        }

        await onApplyReportDeviceData(applyId);
        Toast(t('messageString.submit_success'));

        if (!(await UserInfoManager.updateUserState())) {
          modalDataStoreInstance.closeModal(modalId);
          return;
        }
      }
      try {
        await TrackEvent.uploadEventLog();
      } catch (error) {
        modalDataStoreInstance.closeModal(modalId);
      }

      modalDataStoreInstance.closeModal(modalId);
      nav.nextToTopRouter(RouterConfig.TAKE_PHOTO_RESULT);
    } else {
      modalDataStoreInstance.closeModal(modalId);
    }
  };

  /** 点击返回 */
  const handleGoBack = () => {
    if (UserInfoManager.context.userModel.isNeedSupply) {
      return true;
    } else {
      modalDataStoreInstance.openModal({
        key: ModalList.INFO_PROMPT_CONFIRM,
        i18nKey: 'basicInfoString.get_loan',
        imageKey: '_modalCry',
        confirmBtnName: 'btnString.continueOn',
        cancelBtnName: 'btnString.exit',
        isBackdropClose: false,
        confirmBtnCallback: async () => {
          TrackEvent.trackCommonEvent(
            {
              p: HitPointEnumsSpace.EPageKey.P_INTERCEPT_POPUP,
              e: HitPointEnumsSpace.EEventKey.BTN_POPUP,
            },
            '1',
          );
          await TrackEvent.uploadEventLog();
        },
        cancelBtnCallback: async () => {
          TrackEvent.trackCommonEvent(
            {
              p: HitPointEnumsSpace.EPageKey.P_INTERCEPT_POPUP,
              e: HitPointEnumsSpace.EEventKey.BTN_POPUP,
            },
            '0',
          );
          nav.navigationGoBack();
        },
      });
      return true;
    }
  };

  /** 首贷取消补件后主动提交申请 */
  const onFirstCancelConfirmApply = async (): Promise<string> => {
    let result = await firstCancelConfirmApply();
    if (result.code !== 0) {
      return '';
    }
    UserInfoManager.updateApplyId(result.data);
    return result.data;
  };

  return {
    isNeedSupply,
    file,
    onSetFile,
    handleCancel,
    handleNext,
    handleGoBack,
  };
}
