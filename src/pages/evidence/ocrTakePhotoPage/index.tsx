/**
 *@description ocr相机拍照页面
 */

import React, { ReactElement } from 'react';
import { CameraView, Image, View, Text } from '@/components';
import { ScreenProps } from '@/types';
import { PhotoFile } from 'react-native-vision-camera';
import { nav } from '@/utils';
import { LayoutAnimation, Pressable, Image as RNImage } from 'react-native';

// 身份证类型，frontCard正面，backCard反面
export enum CardType {
  frontCard = 'frontCard',
  backCard = 'backCard',
}

export default function OcrTakePhotoPage({
  navigation,
  route,
}: ScreenProps<{
  cardType: CardType;
  onTakePhoto: (params: { fileUri: string; error: string }) => void;
}>) {
  const { cardType } = route?.params || {};
  const [cardUri, setCardUri] = React.useState<string>();
  const onTakePhoto = (photo: PhotoFile) => {
    LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
    setCardUri(`file://${photo.path}`);
  };

  const onTakePhotoError = (error: Error) => {
    route?.params?.onTakePhoto?.({ error: error.message });
    console.error('onTakePhotoError', error);
    nav.navigationGoBack();
  };

  const onReTakePhoto = () => {
    LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
    setCardUri('');
  };

  const onConfirmPhoto = () => {
    route?.params?.onTakePhoto?.({ fileUri: cardUri });
    nav.navigationGoBack();
  };

  return (
    <View style={{ flex: 1, backgroundColor: '#000' }}>
      {cardUri ? null : (
        <CameraView
          onTakePhoto={onTakePhoto}
          onTakePhotoError={onTakePhotoError}
          cameraType={'back'}
        />
      )}
      <View
        style={{
          position: 'absolute',
          alignSelf: 'center',
          top: '21%',
          // 旋转 90度
          transform: [{ rotate: '90deg' }],
        }}
        pointerEvents="none">
        {cardUri ? (
          <RNImage source={{ uri: cardUri }} style={{ width: 562, height: 335 }} />
        ) : (
          <Image
            margin="0 0 0 0"
            name={
              cardType === CardType.frontCard
                ? '_evidenceCameraFrontCardMaskview'
                : '_evidenceCameraBackCardMaskview'
            }
          />
        )}
      </View>
      {cardUri ? (
        <View
          layoutStrategy="flexRowBetweenCenter"
          style={{
            position: 'absolute',
            bottom: 40,
            alignSelf: 'center',
            paddingHorizontal: 20,
            paddingVertical: 10,
            borderRadius: 20,
            width: '70%',
            zIndex: 2,
          }}>
          <Pressable onPress={onReTakePhoto}>
            <Image name={'_ocrPhotoRetake'} />
          </Pressable>
          <Pressable onPress={onConfirmPhoto}>
            <Image name={'_ocrPhotoSuccess'} />
          </Pressable>
        </View>
      ) : null}
    </View>
  );
}
