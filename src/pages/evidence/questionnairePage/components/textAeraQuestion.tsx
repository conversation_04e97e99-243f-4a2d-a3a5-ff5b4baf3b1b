import { PrefixInput, Text, View } from '@/components';
import { EvidenceVOSpace } from '@/types';
import React, { useMemo, useState } from 'react';
import { useNameSpace } from '@/i18n';

type IProps = {
  questionItem: EvidenceVOSpace.TQuestionItem;
  index: number;
  onChangeAnswer: (questionId: number, answer: any) => void;
};

const TextAeraQuestion = React.memo(({ questionItem, index, onChangeAnswer }: IProps) => {
  const [value, setValue] = useState<string>('');
  const onChageValue = (value: string = '') => {
    setValue(value);
    onChangeAnswer(questionItem.questionId, value);
  };

  const t = useNameSpace().t;

  /** 必填的提示标识 */
  const $renderRequireTip = useMemo(() => {
    return questionItem.required === 'YES' ? (
      <Text status="danger">*</Text>
    ) : (
      <Text category="c1" style={{ color: 'text-color-600' }} i18nKey="rkQustionString.opcional" />
    );
  }, []);

  const $renderTextLengthTip = useMemo(() => {
    const maxLength = questionItem?.limit?.textMaxLength;
    const textTip = maxLength ? `${value.length}/${maxLength}` : `${value.length}`;
    return (
      <Text
        style={{
          position: 'absolute',
          bottom: 0,
          right: 0,
          color: 'text-color-600',
        }}
        category="c1"
        textContent={textTip}
      />
    );
  }, [value, questionItem]);

  return (
    <View padding="0 0 16 0">
      <View margin="12 0 6 0" layoutStrategy="flexRowStartCenter">
        <Text style={{ lineHeight: 22 }}>
          <Text
            category="p1"
            status="basic"
            textContent={index + '. ' + questionItem.questionName}
          />
          {$renderRequireTip}
        </Text>
      </View>
      <PrefixInput
        style={{ height: 120 }}
        textStyle={{
          textAlignVertical: 'top',
        }}
        placeholder={t('rkQustionString.placeholder')}
        multiline
        numberOfLines={5}
        maxLength={questionItem?.limit?.textMaxLength}
        setValue={onChageValue}
        value={value}
      />
      {$renderTextLengthTip}
    </View>
  );
});

export default TextAeraQuestion;
