import { FromUI, Text, View } from '@/components';
import { EvidenceVOSpace } from '@/types';
import React, { useMemo, useState } from 'react';

const { CheckGroup, CheckBox, RadioGroup, RadioButton } = FromUI;

type IProps = {
  questionItem: EvidenceVOSpace.TQuestionItem;
  index: number;
  onChangeAnswer: (questionId: number, answer: any) => void;
};

const RaidoBoxQuestion = React.memo(({ questionItem, index, onChangeAnswer }: IProps) => {
  const [value, setValue] = useState<any>([]);
  const onChageValue = (values: EvidenceVOSpace.TQuestionAnswerLabelItem[]) => {
    setValue(values);
    if (values) {
      const v = values.map(item => item.value).join(';');
      onChangeAnswer(questionItem.questionId, v);
    }
  };

  /** 必填的提示标识 */
  const $renderRequireTip = useMemo(() => {
    return questionItem.required === 'YES' ? (
      <Text status="danger">*</Text>
    ) : (
      <Text category="c1" style={{ color: 'text-color-600' }} i18nKey="rkQustionString.opcional" />
    );
  }, []);

  return (
    <>
      <View margin="12 0 6 0" layoutStrategy="flexRowStartCenter">
        <Text style={{ lineHeight: 22 }}>
          <Text textContent={index + '. ' + questionItem.questionName} />
          <Text i18nKey="rkQustionString.multi" />
          {$renderRequireTip}
        </Text>
      </View>

      <CheckGroup checkedItems={value} onCheckedItemsChanged={onChageValue}>
        {questionItem.answer.map(item => (
          <CheckBox
            key={item.label}
            item={item}
            style={{
              marginTop: 8,
            }}
          />
        ))}
      </CheckGroup>
    </>
  );
});

export default RaidoBoxQuestion;
