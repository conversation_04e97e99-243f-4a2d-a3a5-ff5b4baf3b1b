import { Button, Layout, Text, TopNavigation, View } from '@/components';
import { memo, ReactElement, useCallback, useMemo } from 'react';
import { ScrollView } from 'react-native';

import { EvidenceVOSpace } from '@/types';
import CheckBoxQuestion from './components/checkBoxQuestion';
import RadioBoxQuestion from './components/radioBoxQuestion';
import TextAeraQuestion from './components/textAeraQuestion';
import TextInputQuestion from './components/textInputQuestion';

import { useData } from './useData';

const ProcessQuestionnairePage = (): ReactElement => {
  const { questionList, questionAnswer, onChangeAnswer, handleNext } = useData();

  const renderQuestion = useCallback(
    (
      questionItem: EvidenceVOSpace.TQuestionItem,
      index: number,
      onChangeAnswer: (questionId: number, answer: any) => void,
    ) => {
      switch (questionItem.questionType) {
        // 多选题
        case 'MULTI':
          return (
            <CheckBoxQuestion
              index={index}
              key={questionItem.questionId}
              questionItem={questionItem}
              onChangeAnswer={onChangeAnswer}
            />
          );
        // 单选题
        case 'SINGLE':
          return (
            <RadioBoxQuestion
              index={index}
              key={questionItem.questionId}
              questionItem={questionItem}
              onChangeAnswer={onChangeAnswer}
            />
          );
        // 单行文本框
        case 'TEXT':
          return (
            <TextInputQuestion
              index={index}
              key={questionItem.questionId}
              questionItem={questionItem}
              onChangeAnswer={onChangeAnswer}
            />
          );
        // 多行文本框
        case 'TEXTAREA':
          return (
            <TextAeraQuestion
              index={index}
              key={questionItem.questionId}
              questionItem={questionItem}
              onChangeAnswer={onChangeAnswer}
            />
          );
      }
    },
    [],
  );

  const $questionList = useMemo(() => {
    let index = 0;
    return questionList.map(questionItem => {
      let enable: boolean = true;
      // 题目的启用状态
      if (questionItem.enable) {
        const value = questionAnswer[questionItem.enable.parentId];
        enable = String(value).includes(questionItem.enable.parentOptionValue);
      }
      if (enable) {
        index++;
        return renderQuestion(questionItem, index, onChangeAnswer);
      } else {
        if (!enable) {
          // 如果是不启用的状态，把问题的答案同时置空
          return null;
        }
      }
    });
  }, [questionList, questionAnswer, onChangeAnswer]);

  return (
    <>
      <Layout pLevel="0" level="1">
        <TopNavigation titleKey="rkQustionString.title" />
        <ScrollView>
          <View
            margin="12 12 0 12"
            style={{
              backgroundColor: 'background-color-0',
              borderRadius: 8,
              // borderWidth: 1,
              // borderColor: 'line-color-200',
            }}>
            <View
              padding="8 16 8 16"
              style={{ backgroundColor: 'primary-color-100', borderRadius: 8 }}
              layoutStrategy="flexRowBetweenCenter">
              <Text
                i18nKey="rkQustionString.topTip"
                category="p1"
                style={{
                  flex: 1,
                }}
              />
            </View>
            <View padding="0 16 16 16">{$questionList}</View>
          </View>
        </ScrollView>
        <View
          padding="16 16 16 16"
          style={{
            backgroundColor: 'background-color-0',
            // borderColor: 'line-color-200',
            // borderTopWidth: 1,
          }}>
          <Button status="primary" onPress={handleNext} textI18nKey="btnString.send" />
        </View>
      </Layout>
    </>
  );
};

export default memo(ProcessQuestionnairePage);
