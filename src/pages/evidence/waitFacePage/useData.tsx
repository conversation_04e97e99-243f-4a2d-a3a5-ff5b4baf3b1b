import { HitPointEnumsSpace } from '@/enums';
import { useFaceLive, useGetUserStateAndNextRouterOrOtherCallBack, useOnInit } from '@/hooks';
import { BaseInfoManager } from '@/managers';
import { Toast } from '@/nativeComponents';
import { faceVerify, fetchGetBizToken, fetchUploadImage } from '@/server';
import { FileUtils, TrackEvent, log } from '@/utils';
import { useCallback, useRef } from 'react';
import { useNameSpace } from '../../../i18n';
import { EFaceLiveEventType, IFaceEventData } from '../../../native/module/faceLive';

export default function useData() {
  const pointParam = useRef<TrackEvent.IEventPoint>({
    p: HitPointEnumsSpace.EPageKey.P_LIVE,
    e: HitPointEnumsSpace.EEventKey.E_LIVE_ACTION,
  });

  const t = useNameSpace('loanConfirmString').t;
  const bizToken = useRef('');

  const { loading, refreshing, onRefresh } = useOnInit({
    async callback() {},
    async refreshCallback() {
      await getUserStateAndNextRouterOrOtherCallBack(true);
    },
    isBackAutoRefresh: true,
    pageKey: HitPointEnumsSpace.EPageKey.P_LIVE,
  });

  const [getUserStateAndNextRouterOrOtherCallBack] = useGetUserStateAndNextRouterOrOtherCallBack();

  const faceLiveExamineResultCallback = useCallback(async (event: IFaceEventData) => {
    log.debug('# faceLiveExamineResultCallback', event);
    const { eventType, faceLivenessFilePath, megliveData } = event;
    switch (eventType) {
      case EFaceLiveEventType.FACE_LIVE_PRE_START:
        break;
      case EFaceLiveEventType.FACE_LIVE_PRE_DETECT:
        break;
      case EFaceLiveEventType.FACE_LIVE_PRE_FINISH:
        break;
      case EFaceLiveEventType.FACE_LIVE_START_DETECT:
        BaseInfoManager.changeLoadingModalVisible(false);
        break;
      // 关闭loading
      case EFaceLiveEventType.FACE_LIVE_START_DETECT_STATE_SUCCESS:
        // 活体成功
        onStartLiveCompare(faceLivenessFilePath, megliveData);
        break;
      case EFaceLiveEventType.FACE_LIVE_PRE_DETECT_STATE_FAIL:
      case EFaceLiveEventType.FACE_LIVE_PRE_FINISH_STATE_FAIL:
      case EFaceLiveEventType.FACE_LIVE_START_DETECT_STATE_FAIL:
        // 关闭loading
        Toast(`${t('face_failed_pls_retry')}`);
        await TrackEvent.uploadEventLog();
        await getUserStateAndNextRouterOrOtherCallBack(true);
        BaseInfoManager.changeLoadingModalVisible(false);
        break;
    }
  }, []);

  const [startFaceLiveVerification] = useFaceLive(
    pointParam.current,
    faceLiveExamineResultCallback,
  );

  const handleNext = async () => {
    TrackEvent.trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_LIVE,
        e: HitPointEnumsSpace.EEventKey.BTN_LIVE_VERIFY,
      },
      '1',
    );
    await onStartLiveExamine();
  };

  /** 开始活体检测 */
  const onStartLiveExamine = async () => {
    if (!loading.current) {
      // 启用loading
      loading.current = true;
      // 1、先获取biztoken
      BaseInfoManager.changeLoadingModalVisible(true);
      const resp = await fetchGetBizToken({ get_liveness_video: 1 });
      if (resp.code === 0) {
        const { biz_token } = resp.data;
        if (biz_token) {
          bizToken.current = biz_token;
          // 2、调用faceLive的方法
          startFaceLiveVerification(biz_token);
        }
      } else {
        BaseInfoManager.changeLoadingModalVisible(false);
      }
      loading.current = false;
    }
  };

  const onStartLiveCompare = async (
    faceLivenessFilePath: string = '',
    megliveData: string = '',
  ) => {
    if (megliveData !== '') {
      const faceVerifyParam = {
        bizToken: bizToken.current,
        megliveData: megliveData,
      };
      BaseInfoManager.changeLoadingModalVisible(true);
      const faceVerifyResp = await faceVerify(faceVerifyParam);
      if (faceVerifyResp.code === 0) {
        const { passFlag } = faceVerifyResp.data;
        if (passFlag.includes('YES')) {
          // 活体验证通过直接下一步
        }
        const absoluteFilePath = FileUtils.getPlatformPathDir(faceLivenessFilePath);
        if (faceLivenessFilePath !== '') {
          // 上传原始的活体加密文件
          log.info(`Upload live encrypted files ${absoluteFilePath}`);
          const uploadResp = await fetchUploadImage(absoluteFilePath, {
            cardType: 'original_face',
            fileType: 'megvii',
          });
          if (uploadResp.code === 0) {
            log.info('Uploading live encrypted files successfully');
          }
        }
        await TrackEvent.uploadEventLog();
        await getUserStateAndNextRouterOrOtherCallBack(true);
      }
      BaseInfoManager.changeLoadingModalVisible(false);
    }
  };

  return { handleNext, loading, refreshing, onRefresh };
}
