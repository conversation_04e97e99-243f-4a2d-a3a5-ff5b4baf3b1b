/* eslint-disable react-native/no-inline-styles */
/* eslint-disable react/react-in-jsx-scope */

import { Button, Image, Layout, Text, View } from '@/components';
import React, { ReactElement } from 'react';
import { RefreshControl, ScrollView } from 'react-native';
import useData from './useData';

export default (): ReactElement => {
  const { handleNext, refreshing, onRefresh } = useData();
  return (
    <Layout pLevel="8">
      <ScrollView
        refreshControl={
          <RefreshControl colors={['#4C6EFF']} refreshing={refreshing} onRefresh={onRefresh} />
        }>
        <View layoutStrategy="flexColumnStartCenter">
          <Image margin="50 0 32 0" name="_faceModal" />
          <Text isCenter i18nKey="loanConfirmString.faceLiveVerifyTip" category="p1" />
        </View>
      </ScrollView>
      <Button margin="0 0 32 0" status="primary" onPress={handleNext} textI18nKey="btnString.OK" />
    </Layout>
  );
};
