/**
 *@description 自拍相机页面
 */

import React, { ReactElement } from 'react';
import { CameraView, Image, View } from '@/components';
import { ScreenProps } from '@/types';
import { PhotoFile } from 'react-native-vision-camera';
import { nav } from '@/utils';
import { RouterConfig } from '@/routes';
import { Dimensions } from 'react-native';

export default ({ navigation, route }: ScreenProps<{}>): ReactElement => {
  const { cameraType } = route?.params || {};
  const onTakePhoto = (photo: PhotoFile) => {
    console.log('photo', photo);
    nav.replace(RouterConfig.TAKE_PHOTO_RESULT as any, {
      uri: `file://${photo.path}`,
    });
  };

  const onTakePhotoError = (error: Error) => {
    console.error('onTakePhotoError', error);
    nav.navigationGoBack();
  };

  return (
    <View style={{ flex: 1, justifyContent: 'center' }}>
      <CameraView
        onTakePhoto={onTakePhoto}
        onTakePhotoError={onTakePhotoError}
        cameraType={cameraType}
      />
      <Image
        style={{
          position: 'absolute',
          alignSelf: 'center',
          top: '20%',
        }}
        name="_evidenceCameraFaceMaskview"
      />
    </View>
  );
};
