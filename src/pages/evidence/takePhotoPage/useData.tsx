import { HitPointEnumsSpace } from '@/enums';
import { useCheckCameraPermissionAndRequestPermission, useOnInit } from '@/hooks';
import { ModalList, UserInfoManager, modalDataStoreInstance } from '@/managers';
import { RouterConfig } from '@/routes';
import { nav } from '@/utils';
import { useEffect, useMemo } from 'react';
import { BackHandler } from 'react-native';

export default function useData() {
  const checkCameraPermission = useCheckCameraPermissionAndRequestPermission()[0];

  const isNeedSupply = useMemo(() => {
    return UserInfoManager.context.userModel.isNeedSupply;
  }, []);

  const handleNext = async () => {
    nav.navigate(RouterConfig.SELFIE as any, {
      cameraType: 'front',
    });
  };
  useOnInit({
    pageKey: HitPointEnumsSpace.EPageKey.P_SELFIE,
  });

  /** 点击返回 */
  const handleGoBack = () => {
    if (UserInfoManager.context.userModel.isNeedSupply) {
      return true;
    } else {
      modalDataStoreInstance.openModal({
        key: ModalList.INFO_PROMPT_CONFIRM,
        i18nKey: 'basicInfoString.get_loan',
        imageKey: '_modalCry',
        confirmBtnName: 'btnString.continueOn',
        cancelBtnName: 'btnString.exit',
        isBackdropClose: false,
        confirmBtnCallback: () => {},
        cancelBtnCallback: async () => {
          nav.navigationGoBack();
        },
      });
      return true;
    }
  };

  return { handleNext, handleGoBack, isNeedSupply };
}
