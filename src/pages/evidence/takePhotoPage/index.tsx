/* eslint-disable react-native/no-inline-styles */

import {
  Button,
  EProcessStatus,
  Image,
  Layout,
  ProcessNav,
  Text,
  TopNavigation,
  View,
} from '@/components';
import { UserInfoManager } from '@/managers';
import React, { ReactElement, memo, useMemo } from 'react';
import { GestureResponderEvent, ScrollView } from 'react-native';
import useData from './useData';
import { convert2Progress } from '../utils/dataUtils';
import ProgressView from '../../components/ProgressView';
import { Colors } from '@/themes';
import { Strings } from '@/i18n';

export default (): ReactElement => {
  const { handleNext, handleGoBack, isNeedSupply } = useData();
  const showProcessView = useMemo(() => !isNeedSupply, [isNeedSupply]);

  const TipsInfoView = (
    <View
      layoutStrategy="flexRowCenterCenter"
      margin="0 0 16 0"
      padding="12 12 12 12"
      style={{
        borderRadius: 4,
        backgroundColor: Colors.PRIMARY_COLOR_100,
        zIndex: 2,
      }}>
      <Text
        style={{ flex: 1, color: Colors.TEXT_COLOR_800 }}
        category="p1"
        i18nKey={Strings.takePhotoString.takePhotoNote}
      />
    </View>
  );

  const TakePhotoDesciptionView = () => {
    const errorSelfieIconList = [
      {
        icon: '_evidenceSelfieErrorIcon1',
        text: 'takePhotoString.ensureAllFace',
      },
      {
        icon: '_evidenceSelfieErrorIcon2',
        text: 'takePhotoString.coveredFace',
      },
      {
        icon: '_evidenceSelfieErrorIcon3',
        text: 'takePhotoString.tooDark',
      },
    ];
    return (
      <View layoutStrategy="flexRowBetweenTop" margin="24 0 0 0">
        {errorSelfieIconList.map(item => (
          <View width={114} key={item.text} layoutStrategy="flexColumnCenterCenter">
            {/* @ts-ignore */}
            <Image name={item.icon} />
            <Image margin="12 0 0 0" name="_evidenceSelfieErrorIcon" />
            <Text
              margin="8 0 0 0"
              category="p2"
              style={{ color: 'text-color-800' }}
              i18nKey={item.text}
              isCenter
            />
          </View>
        ))}
      </View>
    );
  };

  return (
    <>
      <Layout pLevel="0" level="1">
        <TopNavigation
          titleKey={'takePhotoString.takePhoto'}
          isBack={!isNeedSupply}
          goBack={handleGoBack}
          bottomLine
        />
        {showProcessView ? (
          <ProgressView current={convert2Progress(EProcessStatus.TAKE_PHOTO)} />
        ) : null}
        <SupplyDesciptionView isNeedSupply={isNeedSupply} />

        <ScrollView
          fadingEdgeLength={10}
          keyboardShouldPersistTaps="always"
          contentContainerStyle={{
            alignItems: 'center',
            paddingHorizontal: 16,
          }}>
          <View
            margin="24 0 0 0"
            padding="0 0 16 0"
            style={{
              backgroundColor: Colors.BACKGROUND_COLOR_0,
              borderRadius: 8,
            }}>
            {TipsInfoView}
            <Image name="_evidencePhoto" style={{ alignSelf: 'center' }} />
            <TakePhotoDesciptionView />
          </View>
        </ScrollView>
        <ButtonView handleNext={handleNext} />
      </Layout>
    </>
  );
};

interface ButtonViewProp {
  handleNext: (event: GestureResponderEvent) => void;
}
const ButtonView = memo(function ButtonView({ handleNext }: ButtonViewProp) {
  return (
    <View
      padding="16 16 16 16"
      style={{
        backgroundColor: 'background-color-0',
        // borderColor: 'line-color-200',
        // borderTopWidth: 1,
      }}>
      {!UserInfoManager.context.userModel.hasApplyOrderId && (
        <View
          margin="0 8 0 8"
          layoutStrategy="flexRowBetweenCenter"
          style={{ alignSelf: 'center' }}>
          <Image margin="0 10 0 0" name="_safeTipIcon" />
          <Text
            category="p1"
            style={{ fontSize: 14 }}
            i18nKey={Strings.basicInfoString.protect_your_privacy}
          />
        </View>
      )}
      <Button
        margin="16 0 0 0"
        status="primary"
        onPress={handleNext}
        textI18nKey="btnString.next"
      />
    </View>
  );
});

interface SupplyDesciptionViewProp {
  isNeedSupply: boolean;
}
/** 自拍照补件描述 */
const SupplyDesciptionView = memo(function SupplyDesciptionView({
  isNeedSupply,
}: SupplyDesciptionViewProp) {
  /** 不需要补件的情况不展示 */
  if (!isNeedSupply) {
    return <></>;
  }

  return (
    <View
      margin="12 16 0 16"
      padding="8 8 8 8"
      style={{
        flexDirection: 'row',
        borderRadius: 8,
        // borderWidth: 1,
        // borderColor: 'line-color-200',
        backgroundColor: 'warn-color-100',
      }}>
      <Image margin="0 4 0 0" name="_infoIcon" />
      <View margin="0 16 0 0">
        <Text
          category="p2"
          i18nKey={'supplyString.selfie'}
          style={{
            color: 'text-color-800',
          }}
        />
      </View>
    </View>
  );
});
