import {
  Button,
  Image,
  Layout,
  SmsCodeInput,
  SmsCodeInputRefType,
  Text,
  TopNavigation,
  View,
} from '@/components';
import { CallYouSendSmsModal, ReciveOtpByCallModal } from '@/modals';
import { ScreenProps } from '@/types';
import { isStringExist } from '@/utils';
import React, { ReactElement, Ref, memo } from 'react';
import { ScrollView } from 'react-native';
import useData from './useData';

export default ({ navigation, route }: ScreenProps<{}>): ReactElement => {
  const {
    smsCode,
    onSetSmsCode,
    computedButtonDisable,
    isNeedSupply,
    phoneNumber,
    smsCodeInputRef,
    handleNext,
    onClickSendPreMethod,
  } = useData();

  return (
    <>
      <Layout pLevel="0">
        <TopNavigation titleKey={'otpString.formTitle'} isBack={!isNeedSupply} bottomLine />
        <SupplyDesciptionView />
        <ScrollView fadingEdgeLength={10} style={{ marginHorizontal: 16 }}>
          <OtpFormView
            phoneNumber={phoneNumber}
            smsCode={smsCode}
            smsCodeInputRef={smsCodeInputRef}
            onSetSmsCode={onSetSmsCode}
            onClickSendPreMethod={onClickSendPreMethod}
          />
          <ButtonView
            smsCode={smsCode}
            handleNext={handleNext}
            computedButtonDisable={computedButtonDisable}
          />
        </ScrollView>
      </Layout>
      <ReciveOtpByCallModal />
      <CallYouSendSmsModal />
    </>
  );
};

/** OTP补件描述 */
const SupplyDesciptionView = memo(function SupplyDesciptionView() {
  return (
    <View
      margin="12 16 0 16"
      padding="8 8 8 8"
      style={{
        flexDirection: 'row',
        borderRadius: 8,
        // borderWidth: 1,
        // borderColor: 'line-color-200',
        backgroundColor: 'warn-color-100',
      }}>
      <Image margin="0 4 0 0" name="_infoIcon" />
      <View margin="0 16 0 0">
        <Text
          category="p2"
          i18nKey={'supplyString.otp'}
          style={{
            color: 'text-color-800',
          }}
        />
      </View>
    </View>
  );
});

/**
 * otp发送填写组件
 * @param phoneNumber 验证码
 * @param smsCode 验证码
 * @function onSetSmsCode 设置验证码
 * @function onClickSendPreMethod 点击发送按钮回调
 * */
const OtpFormView = memo(function OtpFormView({
  phoneNumber,
  smsCode,
  smsCodeInputRef,
  onSetSmsCode,
  onClickSendPreMethod,
}: {
  phoneNumber: string;
  smsCode: string;
  smsCodeInputRef: Ref<SmsCodeInputRefType>;
  onSetSmsCode: (value: any) => void;
  onClickSendPreMethod: () => Promise<boolean>;
}) {
  return (
    <>
      <View margin="24 0 0 0" layoutStrategy="flexColumnStartCenter">
        <Text
          category="p1"
          style={{
            color: 'text-color-800',
          }}
          i18nKey="otpString.otpSendTo"
        />
        <Text
          margin="12 0 0 0"
          category="p1"
          style={{
            color: 'text-color-800',
          }}
          textContent={`${String(phoneNumber).toPrefixPhoneNumber()}`}
        />
      </View>
      <SmsCodeInput
        type="line"
        ref={smsCodeInputRef}
        prefixMargin={'24 0 0 0'}
        placeholderKey={'otpString.code'}
        smsCode={smsCode}
        setSmsCode={onSetSmsCode}
        clickSendPreMethod={onClickSendPreMethod}
        scenesId="first_loan"
        validateConfig={[
          {
            condition: isStringExist(smsCode),
            info: '',
            status: 'danger',
          },
        ]}
      />
    </>
  );
});

/**
 * 按钮区域
 * @function handleNext 验证验证码,进行路由跳转等后续逻辑
 */
const ButtonView = memo(function ButtonView({
  handleNext,
  smsCode,
  computedButtonDisable,
}: {
  smsCode: string;
  handleNext: Function;
  computedButtonDisable: boolean;
}) {
  return (
    <Button
      margin="24 0 0 0"
      status="primary"
      onPress={() => {
        handleNext(smsCode);
      }}
      disabled={computedButtonDisable}
      padding={'16 0 16 0'}
      textI18nKey="btnString.next"
    />
  );
});
