import { SmsCodeInputRefType } from '@/components';
import { BaseEnumsSpace, HitPointEnumsSpace, UserEnumsSpace } from '@/enums';
import { useDeviceDataReport, useLoginInfo, useOnInit, useSubscribeFilter } from '@/hooks';
import {
  BaseInfoManager,
  ModalList,
  UserInfoContextType,
  UserInfoManager,
  modalDataStoreInstance,
} from '@/managers';
import { Toast } from '@/nativeComponents';
import {
  fetchFirstLoanOtp,
  fetchGetAppConfig,
  fetchVerifyFirstLoanOtp,
  firstCancelConfirmApply,
} from '@/server';
import { nav } from '@/utils';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useNameSpace } from '../../../i18n';
import { BackHandler } from 'react-native';
import { useSmsCodeHandler } from '../../../hooks/useSmsCodeHandler';

export default function useData() {
  const loginInfoState = useLoginInfo();
  const t = useNameSpace().t;

  /** 初始化方法 */
  useOnInit({
    pageKey: HitPointEnumsSpace.EPageKey.P_REMEDY_OTP,
  });

  useEffect(() => {
    BackHandler.addEventListener('hardwareBackPress', handleBackButton);
    return () => {
      BackHandler.removeEventListener('hardwareBackPress', handleBackButton);
    };
  }, []);

  useSmsCodeHandler(loginInfoState.onSetSmsCode);

  const handleBackButton = () => {
    return true;
  };

  /** 按钮状态 */
  const computedButtonDisable = useMemo(() => {
    return loginInfoState.smsCode.length !== 4;
  }, [loginInfoState.smsCode]);
  const { onApplyReportDeviceData } = useDeviceDataReport();

  const [smsType, setSmsType] = useState<UserEnumsSpace.ReLoanOptSmsType>(
    UserEnumsSpace.ReLoanOptSmsType.SMS_MOBILE,
  );
  /** 发送次数记录 */
  let sendCount = useRef<number>(0).current;
  const smsCodeInputRef = useRef<SmsCodeInputRefType>(null);
  const phoneNumber = useSubscribeFilter({
    subject: UserInfoManager.messageCenter,
    filter: (subject: UserInfoContextType) => {
      return subject.userModel.mobile;
    },
  }) as string;

  /** 是否需要补件 */
  const isNeedSupply = useMemo(() => {
    return UserInfoManager.context.userModel.isNeedSupply;
  }, []);

  /** 获取App Config */
  const onFetchGetAppConfig = async (): Promise<boolean> => {
    BaseInfoManager.changeLoadingModalVisible(true);
    let result = await fetchGetAppConfig();
    BaseInfoManager.changeLoadingModalVisible(false);
    return new Promise(async resolve => {
      if (result?.code === BaseEnumsSpace.ENetworkStatus.SUCCESS) {
        const { voiceSmsSwitchStatus } = result?.data;

        if (voiceSmsSwitchStatus === 'YES') {
          onOpenModal();
        } else {
          setSmsType(UserEnumsSpace.ReLoanOptSmsType.SMS_MOBILE);
          let result = await onSendCode(UserEnumsSpace.ReLoanOptSmsType.SMS_MOBILE);
          result && smsCodeInputRef.current?.reStartCountDown();
        }
      }
      resolve(false);
    });
  };

  /** 打开弹框,选择OTP验证码或者短信验证码 */
  const onOpenModal = () => {
    modalDataStoreInstance.openModal({
      key: ModalList.CALL_YOU_SEND_SMS,
      i18nKey: 'modalString.reciveOTPByVoiceCall',
      confirmBtnName: 'btnString.OK',
      cancelBtnName: 'btnString.continueSMS',
      isBackdropClose: true,
      confirmBtnCallback: async () => {
        setSmsType(UserEnumsSpace.ReLoanOptSmsType.SMS_VOICE_MOBILE);
        let result = await onSendCode(UserEnumsSpace.ReLoanOptSmsType.SMS_VOICE_MOBILE);
        result && smsCodeInputRef.current?.reStartCountDown();
      },
      cancelBtnCallback: async () => {
        setSmsType(UserEnumsSpace.ReLoanOptSmsType.SMS_MOBILE);
        let result = await onSendCode(UserEnumsSpace.ReLoanOptSmsType.SMS_MOBILE);
        result && smsCodeInputRef.current?.reStartCountDown();
      },
    });
  };

  /** 发送验证码,成功发送OTP验证码,弹出提示弹框 */
  const onSendCode = useCallback(
    async (smsType: UserEnumsSpace.ReLoanOptSmsType): Promise<boolean> => {
      let params = {
        mobile: phoneNumber,
        smsType,
      };
      BaseInfoManager.changeLoadingModalVisible(true);
      let result = await fetchFirstLoanOtp(params);
      BaseInfoManager.changeLoadingModalVisible(false);

      return new Promise(resolve => {
        if (
          smsType === UserEnumsSpace.ReLoanOptSmsType.SMS_VOICE_MOBILE &&
          result?.code === BaseEnumsSpace.ENetworkStatus.SUCCESS
        ) {
          modalDataStoreInstance.openModal({
            key: ModalList.RECIVE_OTP_BY_CALL,
            i18nKey: 'modalString.callYouSendOTP',
            confirmBtnName: 'btnString.OK',
            confirmBtnCallback: () => {
              resolve(result?.code === BaseEnumsSpace.ENetworkStatus.SUCCESS);
            },
          });
        }

        if (
          smsType === UserEnumsSpace.ReLoanOptSmsType.SMS_MOBILE &&
          result?.code === BaseEnumsSpace.ENetworkStatus.SUCCESS
        ) {
          Toast(`${t('homeString.peso_otp_success_tip')}${phoneNumber}`);
        }

        resolve(result?.code === BaseEnumsSpace.ENetworkStatus.SUCCESS);
      });
    },
    [],
  );

  /** 首贷验证,进行路由跳转 */
  const handleNext = useCallback(async (smsCode: string) => {
    let params = { mobile: phoneNumber, smsCode };
    BaseInfoManager.changeLoadingModalVisible(true);
    let result = await fetchVerifyFirstLoanOtp(params);

    if (result?.code === BaseEnumsSpace.ENetworkStatus.SUCCESS) {
      if (!(await UserInfoManager.updateUserState())) {
        BaseInfoManager.changeLoadingModalVisible(false);
        return;
      }

      /** OTP补件场景, 创建订单 */
      if (UserInfoManager.context.userModel.isSupplyCompleted) {
        let applyId: string = await onFirstCancelConfirmApply();

        if (!applyId) {
          BaseInfoManager.changeLoadingModalVisible(false);
          return;
        }
        Toast(t('messageString.submit_success'));
        await onApplyReportDeviceData(applyId);

        if (!(await UserInfoManager.updateUserState())) {
          BaseInfoManager.changeLoadingModalVisible(false);
          return;
        }
      }

      nav.nextToTopRouter();
    } else {
      loginInfoState.onSetSmsCode('');
      smsCodeInputRef.current?.blur();
      BaseInfoManager.changeLoadingModalVisible(false);
    }

    return result?.code === BaseEnumsSpace.ENetworkStatus.SUCCESS;
  }, []);

  /** 首贷取消补件后主动提交申请 */
  const onFirstCancelConfirmApply = async (): Promise<string> => {
    let result = await firstCancelConfirmApply();
    if (result.code !== 0) {
      return '';
    }
    UserInfoManager.updateApplyId(result.data);
    return result.data;
  };

  /** smsCode点击按钮前回调函数 */
  const onClickSendPreMethod = useCallback(async (): Promise<boolean> => {
    if (sendCount > 0) {
      return await onFetchGetAppConfig();
    } else {
      setSmsType(UserEnumsSpace.ReLoanOptSmsType.SMS_MOBILE);
      let result = await onSendCode(UserEnumsSpace.ReLoanOptSmsType.SMS_MOBILE);
      sendCount = sendCount + 1;
      result && smsCodeInputRef.current?.reStartCountDown();
      return result;
    }
  }, []);

  return {
    computedButtonDisable,
    ...loginInfoState,
    isNeedSupply,
    phoneNumber,
    smsCodeInputRef,
    onClickSendPreMethod,
    handleNext,
  };
}
