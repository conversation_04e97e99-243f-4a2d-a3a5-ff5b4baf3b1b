/* eslint-disable react-hooks/exhaustive-deps */
import { BaseEnumsSpace, HitPointEnumsSpace } from '@/enums';
import { useDeviceDataReport, useOnInit } from '@/hooks';
import { useNameSpace } from '@/i18n';
import { BaseInfoManager, UserInfoManager } from '@/managers';
import { Toast } from '@/nativeComponents';
import {
  fetchAmountConfigOnFirstLoan,
  fetchCalculateCostOnFirstLoan,
  fetchFirstPreLoanApply,
} from '@/server';
import { OrderVOSpace } from '@/types';
import { TrackEvent, nav } from '@/utils';
import { useEffect, useState } from 'react';

interface IState extends OrderVOSpace.AmountConfigOnFirstLoanType {
  calculateCost: OrderVOSpace.CalculateCostOnFirstLoanType;
  preApplyId: string;
  selectAmount: string;
  selectDays: number;
}

const defaultCalculateCost: OrderVOSpace.CalculateCostOnFirstLoanType = {
  loanAmount: '--',
  transferFee: '--',
  transferFeeVat: '--',
  processFee: '--',
  processFeeVat: '--',
  repaymentAmount: '--',
  days: '--',
  loanDate: '--',
  loanDateNewFormat: '--',
  repayDate: '--',
  repayDateNewFormat: '--',
  showApplyCost: 'NO',
  realAmount: '--',
  realRepaymentAmount: '--',
  creditCouponAmount: '--',
  creditCouponPercent: '--',
  couponUseType: 'amount',
};

const useData = () => {
  const t = useNameSpace().t;
  useOnInit({
    pageKey: HitPointEnumsSpace.EPageKey.P_AMOUNT,
  });
  const { onPreApplyReportDeviceData } = useDeviceDataReport();

  const [state, setState] = useState<IState>({
    amountList: [],
    days: [],
    selectDays: 61,
    defaultDays: 61,
    canSelectDays: 'NO',
    calculateCost: defaultCalculateCost,
    preApplyId: '',
    selectAmount: '',
  });
  useEffect(() => {
    onInit();
  }, []);

  const onSelectAmountChange = async (selectAmount: string) => {
    TrackEvent.trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_AMOUNT,
        e: HitPointEnumsSpace.EEventKey.BTN_FIRST_LOAN_CHONOSE,
      },
      selectAmount,
    );
    setState((preState: IState) => ({
      ...preState,
      selectAmount,
    }));
    const { selectDays } = state;
    await fetchLoanDetail(selectAmount, String(selectDays)).then(() => {
      BaseInfoManager.changeLoadingModalVisible(false);
    });
  };

  const onInit = async () => {
    await fetchAmountList().then(() => {
      BaseInfoManager.changeLoadingModalVisible(false);
    });
    return true;
  };

  // 获取可选金额列表
  const fetchAmountList = async () => {
    if (BaseInfoManager.context.baseModel.isAccUser) {
      BaseInfoManager.changeLoadingModalVisible(true);
      let result = await fetchAmountConfigOnFirstLoan();

      if (result.code === BaseEnumsSpace.ENetworkStatus.SUCCESS) {
        const { amountList, days, defaultDays } = result.data;
        setState((preState: IState) => ({
          ...preState,
          amountList,
          days,
          selectDays: defaultDays,
          defaultDays,
          canSelectDays: 'YES',
        }));
      }

      return result.code === BaseEnumsSpace.ENetworkStatus.SUCCESS;
    }

    BaseInfoManager.changeLoadingModalVisible(true);
    let result = await fetchAmountConfigOnFirstLoan();

    if (result.code === BaseEnumsSpace.ENetworkStatus.SUCCESS) {
      const { amountList, defaultDays, days, canSelectDays } = result.data;
      setState((preState: IState) => ({
        ...preState,
        amountList,
        defaultDays,
        days,
        selectDays: defaultDays,
        canSelectDays,
      }));
    }

    return result.code === BaseEnumsSpace.ENetworkStatus.SUCCESS;
  };

  // 获取贷款明细
  const fetchLoanDetail = async (selectedAmount: string, selectedDays: string) => {
    BaseInfoManager.changeLoadingModalVisible(true);
    if (!selectedAmount || !selectedDays) {
      BaseInfoManager.changeLoadingModalVisible(false);
      return;
    }

    let result = await fetchCalculateCostOnFirstLoan({
      selectedAmount,
      selectedDays,
    });

    if (result.code === BaseEnumsSpace.ENetworkStatus.SUCCESS) {
      if (result.data) {
        setState((preState: IState) => ({
          ...preState,
          calculateCost: result.data,
        }));
      } else {
      }
    }

    return result.code === BaseEnumsSpace.ENetworkStatus.SUCCESS;
  };

  // 创建订单
  const fetchCreateApplyOrder = async () => {
    const { selectAmount, selectDays } = state;

    let result = await fetchFirstPreLoanApply({
      amount: selectAmount,
      days: String(selectDays),
    });

    if (result.code === BaseEnumsSpace.ENetworkStatus.SUCCESS) {
      UserInfoManager.updatePreApplyId(result.data || '');
      // 首贷预申请
      await onPreApplyReportDeviceData(result.data);
      setState((preState: IState) => ({
        ...preState,
        preApplyId: result.data,
      }));
    }

    return result.code === BaseEnumsSpace.ENetworkStatus.SUCCESS;
  };

  const onValueChange = (value: number) => {
    setState((preState: IState) => ({
      ...preState,
      selectDays: value,
    }));
  };

  const onSlidingComplete = async (value: number) => {
    const { selectAmount } = state;
    await fetchLoanDetail(selectAmount, String(value)).then(() => {
      BaseInfoManager.changeLoadingModalVisible(false);
    });
  };

  const onNext = async () => {
    const { selectAmount } = state;
    BaseInfoManager.changeLoadingModalVisible(true);
    TrackEvent.trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_AMOUNT,
        e: HitPointEnumsSpace.EEventKey.BTN_FIRST_LOAN_APPLY,
      },
      '',
    );
    await TrackEvent.uploadEventLog();
    BaseInfoManager.changeLoadingModalVisible(true);

    if (!selectAmount) {
      Toast(t('directPaymentString.pls_select_amount'));
      BaseInfoManager.changeLoadingModalVisible(false);
      return;
    }

    BaseInfoManager.changeLoadingModalVisible(true);
    await fetchCreateApplyOrder().then(async createApplyResult => {
      BaseInfoManager.changeLoadingModalVisible(false);
      if (!createApplyResult) {
        return;
      }

      BaseInfoManager.changeLoadingModalVisible(true);
      await UserInfoManager.updateUserState().then(res => {
        BaseInfoManager.changeLoadingModalVisible(false);
        res && nav.nextToTopRouter();
      });
    });
  };

  const { amountList, calculateCost, defaultDays, selectDays, days, canSelectDays } = state;
  return {
    selectAmountCardProps: {
      data: amountList.map(amountItem => {
        return {
          label: `$ ${amountItem}`,
          value: `${amountItem}`,
        };
      }),
      onChange: onSelectAmountChange,
      onValueChange,
      onSlidingComplete,
      days,
      selectDays,
      defaultDays,
      canSelectDays,
    },
    loanDetailCardProps: calculateCost,
    tipsCardProps: {},
    onNext,
  };
};

export default useData;
