/* eslint-disable react-native/no-inline-styles */
import React, { ReactElement, memo } from 'react';
import { Image, Text, View } from '@/components';

/**
 * 提示文案组件
 */
const TipsCard = (props: TipsCardType): ReactElement => {
  const {} = props;
  return (
    <View
      margin="12 0 12 0"
      padding="12 8 12 8"
      style={{
        backgroundColor: 'background-color-0',
        borderRadius: 8,
        // borderWidth: 1,
        // borderColor: 'line-color-200',
      }}>
      <View layoutStrategy="flexRowStartCenter">
        <Image name="_blackNotice" />
        <Text
          margin="0 10 0 10"
          style={{ color: 'text-color-800' }}
          category="p1"
          i18nKey={'directPaymentString.loan_notice_title'}
        />
      </View>
      <View margin="12 10 0 10" layoutStrategy="flexRowStart">
        <Text style={{ color: 'text-color-600' }} category="p1" textContent="1." />
        <Text
          style={{ color: 'text-color-600' }}
          category="p1"
          i18nKey={'directPaymentString.loan_notice_first'}
        />
      </View>
      <View margin="12 10 0 10" layoutStrategy="flexRowStart">
        <Text style={{ color: 'text-color-600' }} category="p1" textContent="2." />
        <Text
          style={{ color: 'text-color-600' }}
          category="p1"
          i18nKey={'directPaymentString.loan_notice_second'}
        />
      </View>
      <View margin="12 10 0 10" layoutStrategy="flexRowStart">
        <Text style={{ color: 'text-color-600' }} category="p1" textContent="3." />
        <Text
          style={{ color: 'text-color-600' }}
          category="p1"
          i18nKey={'directPaymentString.loan_notice_third'}
        />
      </View>
      <View margin="12 10 0 10" layoutStrategy="flexRowStart">
        <Text style={{ color: 'text-color-600' }} category="p1" textContent="4." />
        <Text
          style={{ color: 'text-color-600' }}
          category="p1"
          i18nKey={'directPaymentString.loan_notice_forth'}
        />
      </View>
    </View>
  );
};

export type TipsCardType = {};
export default memo(TipsCard);
