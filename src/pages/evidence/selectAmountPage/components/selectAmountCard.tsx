/* eslint-disable react-native/no-inline-styles */
import React, {
  ReactElement,
  forwardRef,
  memo,
  useCallback,
  useRef,
  Ref,
  useImperativeHandle,
  useMemo,
} from 'react';
import { Button, Image, Text, View } from '@/components';
import Select, { RefType as SelectRefType, SelectType } from './select';
import { ModalList, modalDataStoreInstance } from '@/managers';
import { useNameSpace } from '@/i18n';
import { Toast } from '@/nativeComponents';

/**
 * 下拉选择卡片
 */
const SelectAmountCard = forwardRef(
  (props: SelectAmountCardType, ref: Ref<SelectRefType>): ReactElement => {
    const {
      data,
      onChange,
      days,
      defaultDays,
      selectDays,
      canSelectDays,
      onValueChange,
      onSlidingComplete,
    } = props;

    const selectRef = useRef<SelectRefType>(null);

    const t = useNameSpace().t;

    useImperativeHandle(ref, () => ({
      resetValue() {
        selectRef.current?.resetValue();
      },
      setValue(val: string) {
        selectRef.current?.setValue(val);
      },
    }));

    const $days = useMemo(() => {
      return (
        <>
          {days.map(day => {
            const locked = day !== selectDays && canSelectDays === 'NO';
            return (
              <View width={'33.3%'} key={day} layoutStrategy="flexColumnCenterCenter">
                <View style={{ position: 'relative' }}>
                  <Button
                    padding="10 6 10 6"
                    width={'20%'}
                    appearance={day === selectDays ? 'filled' : 'outline'}
                    status="primary"
                    style={{
                      opacity: locked ? 0.6 : 1,
                    }}
                    text={String(day).toFormatDay()}
                    onPress={() => {
                      if (locked) {
                        Toast(t('directPaymentString.pls_locked_days'));
                      } else if (day !== selectDays) {
                        onValueChange(day);
                        onSlidingComplete(day);
                      }
                    }}
                  />
                  {locked && (
                    <Image
                      name="_lockTipIcon"
                      style={{
                        tintColor: 'primary-color-500',
                        position: 'absolute',
                        top: 5,
                        opacity: 0.6,
                        right: 8,
                      }}
                    />
                  )}
                </View>
              </View>
            );
          })}
        </>
      );
    }, [days, selectDays, canSelectDays, onValueChange, onSlidingComplete, t]);

    return (
      <>
        <View
          margin="12 0 0 0"
          style={{
            backgroundColor: 'background-color-0',
            borderRadius: 8,
          }}
          width={'100%'}
          height={'auto'}>
          <View padding="12 12 12 12">
            <Text
              isCenter={true}
              style={{ color: 'text-color-700' }}
              category="p1"
              i18nKey={'directPaymentString.title'}
            />
            <View layoutStrategy="flexRowCenterCenter">
              <Select ref={selectRef} data={data} onChange={onChange} />
            </View>
            <Text
              isCenter={true}
              category="p1"
              i18nKey={'directPaymentString.loan_days_tips'}
              style={{ color: 'text-color-700' }}
            />
            {/* <Text
              margin="12 0 0 0"
              isCenter={true}
              category="h3"
              textContent={String(selectDays).toFormatDay()}
              style={{color: 'text-color-800'}}
            /> */}

            {/* <DaySlider
              animationType="spring"
              disabled={canSelectDays !== 'YES'}
              value={Number(selectDays)}
              minimumValue={Number(minDays)}
              maximumValue={Number(maxDays)}
              onSlidingComplete={value => onSlidingComplete(Number(value))}
              onValueChange={value => {
                onValueChange(Number(value));
              }}
              onSlidingStart={() => {
                canSelectDays !== 'YES' &&
                  Toast(t('directPaymentString.pls_locked_days'));
              }}
              step={Number(stepIncrementDays)}
            /> */}
            <View margin="12 0 0 0" layoutStrategy="flexRowStartCenter">
              {$days}
            </View>
          </View>
        </View>
      </>
    );
  },
);

/** 布局相关自定义参数 */
type LayoutType = {};
/** 逻辑相关自定义参数 */
type LogicType = {
  selectDays: number;
  defaultDays: number;
  canSelectDays: 'YES' | 'NO' | string;
  days: number[];
  onValueChange: (value: number) => void;
  onSlidingComplete: (value: number) => void;
} & SelectType;
export type SelectAmountCardType = LogicType & LayoutType;
export default memo(SelectAmountCard);
export type { RefType as SelectRefType, SelectType } from './select';
