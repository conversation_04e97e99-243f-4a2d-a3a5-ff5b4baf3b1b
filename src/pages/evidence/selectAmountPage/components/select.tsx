import { Image } from '@/components';
import { customTheme } from '@/themes';
import React, { Ref, forwardRef, memo, useImperativeHandle, useState } from 'react';
import { StyleSheet, Text, View } from 'react-native';
import { Dropdown } from 'react-native-element-dropdown';
import { useTheme } from '@/hooks';
const baseColor = customTheme.defaultTheme;
const lightColor = customTheme.defaultThemForLight;

const renderCustomRightIcon = () => {
  const theme = useTheme();
  return <Image tintColor={theme['primary-color-500']} name="_evidenceSelectIcon" />;
};
const renderCustomReRightIcon = () => {
  const theme = useTheme();
  return <Image tintColor={theme['primary-color-500']} name="_evidenceReSelectIcon" />;
};
const DropdownComponent = forwardRef((props: SelectType, ref: Ref<RefType>) => {
  const { data = [], onChange } = props;
  const [value, setValue] = useState<string>('');
  const [isFocus, setIsFocus] = useState(false);

  const onBlur = () => {
    setIsFocus(false);
  };
  const onFocus = () => {
    setIsFocus(true);
  };

  const trigglerChange = (item: string) => {
    setValue(item);
    setIsFocus(false);
    typeof onChange === 'function' && onChange(item);
  };

  useImperativeHandle(ref, () => ({
    resetValue() {
      setValue('');
      typeof onChange === 'function' && onChange('');
    },
    setValue(val: string) {
      // 设置的值是否在下拉列表中存在
      if (data.some(item => item.value === val)) {
        setValue(val);
        typeof onChange === 'function' && onChange(val);
      }
    },
  }));

  return (
    <View style={styles.container}>
      <Dropdown
        style={[styles.dropdown]}
        placeholderStyle={styles.placeholderStyle}
        selectedTextStyle={styles.selectedTextStyle}
        inputSearchStyle={styles.inputSearchStyle}
        itemContainerStyle={styles.itemContainerStyle}
        itemTextStyle={styles.itemTextStyle}
        data={data}
        search={false}
        maxHeight={300}
        minHeight={100}
        labelField="label"
        valueField="value"
        searchField="search"
        placeholder={!isFocus ? '$ --' : '$ --'}
        searchPlaceholder="Search..."
        value={value}
        onFocus={() => {
          onFocus();
        }}
        onBlur={() => {
          onBlur();
        }}
        renderRightIcon={isFocus ? renderCustomReRightIcon : renderCustomRightIcon}
        renderLeftIcon={() => <Text style={styles.iconStyle}>{}</Text>}
        onChange={item => {
          trigglerChange(item.value);
        }}
      />
    </View>
  );
});

export type RefType = {
  resetValue: () => void;
  setValue: (val: string) => void;
};
export type selectItemType = {
  label: string;
  value: string;
  search?: string;
};
export type SelectType = {
  data: selectItemType[];
  onChange?: (str: string) => void;
};
export default memo(DropdownComponent);

const styles = StyleSheet.create({
  container: {
    padding: 16,
  },
  dropdown: {
    height: 50,
    minWidth: 200,
    borderColor: lightColor['primary-color-500'],
    backgroundColor: lightColor['background-color-0'],
    borderWidth: 2,
    borderRadius: 8,
    paddingHorizontal: 8,
  },
  placeholderStyle: {
    fontSize: 28,
    lineHeight: 48,
    height: 48,
    color: baseColor['primary-color-500'],
  },
  selectedTextStyle: {
    fontSize: 28,
    lineHeight: 48,
    height: 48,
    color: baseColor['primary-color-500'],
  },
  itemContainerStyle: {},
  itemTextStyle: {
    fontSize: 16,
    color: baseColor['primary-color-500'],
  },
  inputSearchStyle: {
    fontSize: 32,
    lineHeight: 48,
    height: 48,
  },
  iconStyle: {
    fontSize: 32,
    lineHeight: 48,
    height: 48,
    color: baseColor['text-color-800'],
    marginRight: 8,
  },
});
