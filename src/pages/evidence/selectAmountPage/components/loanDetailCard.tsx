/* eslint-disable react/self-closing-comp */
/* eslint-disable react-hooks/rules-of-hooks */
/* eslint-disable react-native/no-inline-styles */
import { DashedLine, Divider, Image, Text, View } from '@/components';
import { HitPointEnumsSpace } from '@/enums';
import { useNameSpace } from '@/i18n';
import { Toast } from '@/nativeComponents';
import { OrderVOSpace } from '@/types';
import { TrackEvent } from '@/utils';
import React, { ReactElement, memo, useCallback, useMemo, useState } from 'react';
import { TouchableWithoutFeedback } from 'react-native';

/**
 * 贷款详细卡片
 */
const loanDetailCard = (
  props: OrderVOSpace.CalculateCostOnFirstLoanType & LoanDetailLayoutType,
): ReactElement => {
  const {
    loanAmount = '--',
    processFee = '--',
    processFeeVat = '--',
    repaymentAmount = '--',
    showApplyCost = 'NO',
  } = props;
  const t = useNameSpace().t;

  // 设置
  const [isSpreadOutLoanDetail, setIsSpreadOutLoanDetail] = useState<boolean>(false);

  const handleOpenTips = () => {
    Toast(t('directPaymentString.loan_amount_calc_tips'));
  };

  const onChangeIsSpreadOutLoanDetail = useCallback(() => {
    setIsSpreadOutLoanDetail(preState => {
      // 选择埋点
      TrackEvent.trackCommonEvent(
        {
          p: HitPointEnumsSpace.EPageKey.P_AMOUNT,
          e: HitPointEnumsSpace.EEventKey.BTN_CREDIT_DETAILS,
        },
        preState ? '1' : '0',
      );
      return !preState;
    });
  }, []);

  const $loanDetail = useMemo(() => {
    return (
      <>
        <View margin="12 0 0 0" layoutStrategy="flexRowBetweenCenter">
          <Text
            style={{ color: 'text-color-600' }}
            category="p1"
            i18nKey={'directPaymentString.loan_amount'}
          />
          <Text
            style={{ color: 'text-color-800' }}
            category="p1"
            textContent={String(loanAmount).toFormatFinance()}
          />
        </View>
        <View margin="12 0 0 0" layoutStrategy="flexRowBetweenCenter">
          <Text
            style={{ color: 'text-color-600' }}
            category="p1"
            i18nKey={'directPaymentString.loan_fee'}
          />
          <Text
            style={{ color: 'text-color-800' }}
            category="p1"
            textContent={String(processFee).toFormatFinance()}
          />
        </View>
        <View
          margin="12 0 0 0"
          layoutStrategy="flexRowBetweenCenter"
          style={{
            paddingBottom: 12,
          }}>
          <Text
            style={{ color: 'text-color-600' }}
            category="p1"
            i18nKey={'directPaymentString.loan_iva'}
          />
          <Text
            style={{ color: 'text-color-800' }}
            category="p1"
            textContent={String(processFeeVat).toFormatFinance()}
          />
        </View>
      </>
    );
  }, [loanAmount, processFee, processFeeVat]);

  const $renderHideLoanDetailInfo = useMemo(() => {
    return (
      <View
        margin="12 0 0 0"
        padding="12 12 12 12"
        layoutStrategy="flexRowStartCenter"
        style={{
          backgroundColor: 'background-color-0',
          borderRadius: 8,
          // borderWidth: 1,
          // borderColor: 'line-color-200',
        }}>
        <Image margin="0 8 0 0" name="_infoIcon" width={16} height={16} />
        <Text
          margin="0 8 0 0"
          style={{ flex: 1 }}
          category="p2"
          i18nKey={'directPaymentString.loan_detail_hide_tips'}
        />
      </View>
    );
  }, [loanAmount, processFee, processFeeVat, showApplyCost, repaymentAmount]);

  const $renderShowLoanDetailInfo = useMemo(() => {
    return (
      <View
        margin="12 0 12 0"
        padding="0 12 12 12"
        style={{
          backgroundColor: 'background-color-0',
          borderRadius: 8,
        }}>
        <View layoutStrategy="flexRowBetweenCenter">
          <TouchableWithoutFeedback onPress={handleOpenTips}>
            <View margin="12 0 0 0" layoutStrategy="flexRowStartCenter">
              <Text
                margin="0 8 0 0"
                style={{ color: 'text-color-800' }}
                category="p1"
                i18nKey={'directPaymentString.loan_amount_calc'}
              />
              <Image name="_question" />
            </View>
          </TouchableWithoutFeedback>
          <TouchableWithoutFeedback onPress={onChangeIsSpreadOutLoanDetail}>
            <View margin="12 0 0 0" width={24} height={24}>
              {!isSpreadOutLoanDetail && <Image name="_suffix" />}
            </View>
          </TouchableWithoutFeedback>
        </View>
        {isSpreadOutLoanDetail && $loanDetail}
        <Divider style={{ backgroundColor: 'line-color-100' }} margin="8 0 0 0" />
        <View margin="6 0 0 0" layoutStrategy="flexRowBetweenCenter">
          <Text
            style={{ color: 'text-color-600' }}
            category="p1"
            i18nKey={'directPaymentString.loan_total'}
          />
          <Text
            style={{ color: 'text-color-800' }}
            category="p1"
            textContent={String(repaymentAmount).toFormatFinance()}
          />
        </View>
      </View>
    );
  }, [
    loanAmount,
    processFee,
    processFeeVat,
    repaymentAmount,
    isSpreadOutLoanDetail,
    onChangeIsSpreadOutLoanDetail,
  ]);

  const $renderLoanDetailInfo = useMemo(() => {
    if (showApplyCost === 'YES') {
      return $renderShowLoanDetailInfo;
    } else {
      return $renderHideLoanDetailInfo;
    }
  }, [
    loanAmount,
    processFee,
    processFeeVat,
    repaymentAmount,
    showApplyCost,
    isSpreadOutLoanDetail,
    onChangeIsSpreadOutLoanDetail,
  ]);

  return <>{$renderLoanDetailInfo}</>;
};
export type LoanDetailLayoutType = {};
export default memo(loanDetailCard);
