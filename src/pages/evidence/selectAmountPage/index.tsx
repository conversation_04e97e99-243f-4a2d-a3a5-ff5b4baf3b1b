import { Button, Layout, TopNavigation, View } from '@/components';
import React, { ReactElement, memo } from 'react';
import { ScrollView } from 'react-native';
import LoanDetailCard from './components/loanDetailCard';
import SelectAmountCard from './components/selectAmountCard';
import TipsCard from './components/tipsCard';
import useData from './useData';
import { HitPointEnumsSpace } from '@/enums';

/**
 * 直接打款页面
 */
const SelectAmountPage = (): ReactElement => {
  const { selectAmountCardProps, loanDetailCardProps, tipsCardProps, onNext } = useData();
  return (
    <Layout pLevel="0" level="1" topCompensateColor="primary-color-500">
      <TopNavigation
        showLogoAction={true}
        pageKey={HitPointEnumsSpace.EPageKey.P_AMOUNT}
        showMessage
        type="primary"
        bottomLine={false}
      />
      <ScrollView fadingEdgeLength={10}>
        <View
          style={{
            height: 200,
            backgroundColor: 'primary-color-500',
          }}
        />
        <View
          padding="0 16 0 16"
          style={{
            transform: [{ translateY: -180 }],
          }}>
          <SelectAmountCard {...selectAmountCardProps} />
          <LoanDetailCard {...loanDetailCardProps} />
          <TipsCard {...tipsCardProps} />
        </View>
      </ScrollView>
      <View
        padding="16 16 16 16"
        style={{
          backgroundColor: 'background-color-0',
          // borderColor: 'line-color-200',
          // borderTopWidth: 1,
        }}>
        <Button status="primary" onPress={onNext} textI18nKey="btnString.next" />
      </View>
    </Layout>
  );
};

export default memo(SelectAmountPage);
