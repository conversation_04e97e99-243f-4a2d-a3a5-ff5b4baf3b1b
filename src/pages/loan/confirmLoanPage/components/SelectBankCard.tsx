import { memo } from 'react'
import { View, Text, Image } from "@/components"
import { Colors } from "@/themes"
import { Strings } from "@/i18n"
import { Pressable } from "react-native"
import { EvidenceVOSpace } from "@/types"

interface Props {
    bankCard: EvidenceVOSpace.MeBankcardDataType;
    onShowBankCardSheet?: () => void;
}
const SelectBankCard = (props: Props) => {
    const {
        bankCard,
        onShowBankCardSheet
    } = props
    if (!bankCard?.cardNo) {
        return null
    }
    return (
        <View
            padding='8 16 8 16'
            margin='16 16 0 16'
            style={{ backgroundColor: Colors.BACKGROUND_COLOR_0, borderRadius: 8 }}
            layoutStrategy="flexRowBetweenCenter"
        >
            <Text i18nKey={Strings.loanConfirmString.selectBankCardTitle} />
            <Pressable onPress={onShowBankCardSheet}>
                <View layoutStrategy="flexRowStartCenter">
                    <Text
                        textContent={bankCard.bankName}
                        style={{ color: Colors.TEXT_COLOR_600 }}
                        category='p2'
                    />
                    <Text
                        padding='0 0 0 6'
                        textContent={bankCard.cardNo.toFormatClabe(true)}
                        style={{ color: Colors.TEXT_COLOR_600 }}
                        category='p2'
                    />
                    <Image name='_arrowRight' />
                </View>
            </Pressable>
        </View>
    )
}
export default memo(SelectBankCard)