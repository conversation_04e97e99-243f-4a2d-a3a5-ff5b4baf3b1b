/**
 * @description 信用额度特权说明弹窗
 */
import { CommonModal, Image, LinearGradient, Text, View, ImageBackground } from '@/components';
import React, { ReactElement, memo, useCallback, useMemo } from 'react';
import { Colors } from '@/themes';
import { Strings } from '@/i18n';
import { Pressable } from 'react-native';

type CreditAmountRightModalProps = {
  visible: boolean;
  /** 下一个信用额度 */
  nextCreditAmount: string;
  changeDialogVisibe: (visible: boolean) => void;
};

const CreditAmountRightModal = (props: CreditAmountRightModalProps): ReactElement => {
  const { visible, changeDialogVisibe, nextCreditAmount } = props;

  const handleClose = useCallback(() => {
    changeDialogVisibe(false);
  }, [visible]);

  /** 弹窗内容 */
  const $content = useMemo(() => {
    return (
      <ImageBackground name="_repayDateBg" resizeMode="stretch">
        <View style={{ height: 55 }} />
        <View padding="24 16 0 26" layoutStrategy="flexColumnCenterCenter">
          <Text
            category="h4"
            style={{ color: 'tertiary-color-600', textAlign: 'center' }}
            i18nKey="loanConfirmString.credit_amount_right_modal_title"
          />
          {/* <Text
            category="h4"
            padding='8 0 0 0'
            style={{ color: 'tertiary-color-600', textAlign: 'center' }}
            i18nKey="loanConfirmString.credit_amount_right_modal_sub_title"
          /> */}
          <View margin="14 0 0 0" layoutStrategy="flexRowCenterCenter">
            <Text style={{ flex: 1 }}>
              <Text
                style={{ color: 'text-color-700' }}
                i18nKey="loanConfirmString.credit_amount_right_modal_content1"
              />
              <Text
                style={{ color: Colors.TERTIARY_COLOR_500 }}
                i18nKey="loanConfirmString.credit_amount_right_modal_content2"
              />
            </Text>
            <Image name="_happyBoy" />
          </View>

          <Image
            style={{ width: '100%', marginTop: 12 }}
            name="_progressBar"
            resizeMode="stretch"
          />
          <View
            margin="0 12 0 0"
            style={{ width: '66.66%', alignSelf: 'flex-end' }}
            layoutStrategy="flexRowBetweenCenter">
            <Text
              category="c1"
              style={{ color: 'text-color-700' }}
              textContent={`$${nextCreditAmount}`}
            />
            <Text category="c1" style={{ color: 'text-color-700' }} textContent={`$10000`} />
            <Text category="c1" style={{ color: 'text-color-700' }} textContent={`$25000`} />
          </View>
          <ImageBackground name="_repayDateModalBtn" style={{ marginTop: 20 }}>
            <Pressable onPress={handleClose}>
              <View
                layoutStrategy="flexRowCenterCenter"
                padding="0 0 4 0"
                style={{ height: '100%' }}>
                <Text
                  i18nKey={Strings.btnString.OK}
                  style={{
                    color: Colors.TEXT_COLOR_0,
                  }}
                />
              </View>
            </Pressable>
          </ImageBackground>
        </View>
      </ImageBackground>
    );
  }, [nextCreditAmount]);

  return (
    <CommonModal
      visible={visible}
      cancelCallback={handleClose}
      hasLinearGradient={false}
      style={{ backgroundColor: 'transparent' }}
      isBottomIconColse>
      {$content}
    </CommonModal>
  );
};

export default memo(CreditAmountRightModal);
