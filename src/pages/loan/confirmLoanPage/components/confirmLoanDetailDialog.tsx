import { Image, CommonModal, Text, View } from '@/components';
import { OrderVOSpace } from '@/types';
import React, { useCallback, useMemo } from 'react';

interface IProps {
  changeDialogVisibe: (visible: boolean) => void;
  visible: boolean;
  loanCreditCalculationData: OrderVOSpace.LoanDetailDataType;
}

export default React.memo((props: IProps) => {
  const { visible, loanCreditCalculationData, changeDialogVisibe } = props;

  const handleClose = useCallback(() => {
    changeDialogVisibe(false);
  }, [visible]);

  const $comfirmLoanDetailInfo = useMemo(() => {
    const {
      loanAmount,
      realAmount,
      repaymentAmount,
      processFee,
      processFeeVat,
      realRepaymentAmount,
      couponFlag,
    } = loanCreditCalculationData;
    return (
      <View margin="24 16 12 16">
        <Text
          margin="0 10 0 10"
          category="h3"
          isCenter={true}
          i18nKey={'loanConfirmString.confirm_loan_dialog_title'}
        />
        <View margin="16 10 0 10" layoutStrategy="flexRowBetweenCenter">
          <Text
            style={{ color: 'text-color-600' }}
            category="p1"
            i18nKey={'loanConfirmString.confirm_loan_dialog_amount'}
          />
          <View layoutStrategy="flexRowStartCenter">
            {realAmount !== loanAmount && (
              <>
                <Text
                  margin="0 4 0 0"
                  category="p1"
                  textContent={String(loanAmount).toFormatFinance()}
                  style={{
                    color: 'text-color-600',
                    opacity: 0.6,
                    textDecorationLine: 'line-through',
                  }}
                />
                <Image name="_upSuccessIcon" style={{ marginRight: 4 }} />
              </>
            )}
            <Text
              style={{ color: 'text-color-800' }}
              category="p1"
              textContent={String(realAmount).toFormatFinance()}
            />
          </View>
        </View>
        <View margin="16 10 0 10" layoutStrategy="flexRowBetweenCenter">
          <Text
            style={{ color: 'text-color-600' }}
            category="p1"
            i18nKey={'loanConfirmString.confirm_loan_dialog_fee'}
          />
          <Text
            style={{ color: 'text-color-800' }}
            category="p1"
            textContent={String(processFee).toFormatFinance()}
          />
        </View>
        <View
          padding="0 0 12 0"
          margin="16 10 0 10"
          layoutStrategy="flexRowBetweenCenter"
          style={{
            borderBottomWidth: 1,
            borderBottomColor: 'line-color-100',
            borderStyle: 'solid',
          }}>
          <Text
            style={{ color: 'text-color-600' }}
            category="p1"
            i18nKey={'loanConfirmString.confirm_loan_dialog_iva'}
          />
          <Text
            style={{ color: 'text-color-800' }}
            category="p1"
            textContent={String(processFeeVat).toFormatFinance()}
          />
        </View>
        <View padding="0 0 0 0" margin="16 10 0 10" layoutStrategy="flexRowBetweenCenter">
          <Text
            style={{ color: 'text-color-600' }}
            category="p1"
            i18nKey={'loanConfirmString.confirm_loan_dialog_total_new'}
          />
          <View layoutStrategy="flexRowStartCenter" margin="9 0 0 0">
            {realRepaymentAmount !== repaymentAmount && (
              <>
                <Text
                  margin="0 8 0 0"
                  category="h3"
                  textContent={String(repaymentAmount).toFormatFinance()}
                  style={{
                    color: 'text-color-600',
                    opacity: 0.6,
                    textDecorationLine: 'line-through',
                  }}
                />
                <Image name="_mountDownIcon" style={{ marginRight: 6 }} />
              </>
            )}
            <Text
              style={{ color: 'text-color-800' }}
              category="h3"
              textContent={String(realRepaymentAmount).toFormatFinance()}
            />
          </View>
        </View>
        {/* {couponFlag === 'YES' && (
          <>
            <View width={'100%'} layoutStrategy="flexRowBetweenCenter">
              <View />
              <Image
                margin="0 12 0 0"
                name="_polygonIcon"
                style={{
                  tintColor: 'primary-color-100',
                }}
              />
            </View>
            <View
              width={'100%'}
              margin="-3 0 0 0"
              padding="6 12 6 12"
              layoutStrategy="flexRowStartCenter"
              style={{
                borderRadius: 6,
                backgroundColor: 'primary-color-100',
              }}>
              <Image margin="0 12 0 0" name={'_infoIconYellow'} />
              <Text
                style={{ flex: 1 }}
                category="c1"
                i18nKey="couponString.couponEffectiveRestrictions1"
              />
            </View>
          </>
        )} */}
      </View>
    );
  }, [props.loanCreditCalculationData]);
  return (
    <CommonModal
      visible={visible}
      onBackdropPress={handleClose}
      hasLinearGradient={false}
      confirmBtnName="btnString.OK"
      confirmCallback={handleClose}>
      {$comfirmLoanDetailInfo}
    </CommonModal>
  );
});
