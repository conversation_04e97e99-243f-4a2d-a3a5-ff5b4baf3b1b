import { Check, Text, View } from '@/components';

interface ILoanContractProps {
  inputEnterDisabled: boolean;
  agreeLoanContractBool: boolean;
  onChangeAgreeLoanContractBool: () => void;
  toLoanContractPage: () => Promise<void>;
}

/** 用信合同确认 */
const LoanContract = (props: ILoanContractProps) => {
  const {
    inputEnterDisabled,
    agreeLoanContractBool,
    onChangeAgreeLoanContractBool,
    toLoanContractPage,
  } = props;
  return (
    <View layoutStrategy="flexRowStartCenter" margin="12 0 12 0">
      <Check
        margin={'0 8 0 0'}
        checked={agreeLoanContractBool}
        onChange={onChangeAgreeLoanContractBool}
      />
      <View layoutStrategy="flexRowStartCenter">
        <Text
          category="p2"
          i18nKey={'loanConfirmString.check_content'}
          style={{
            color: 'text-color-800',
          }}
        />
        <Text
          category="p2"
          onPress={toLoanContractPage}
          i18nKey={'loanConfirmString.check_content_important'}
          style={{
            color: 'primary-color-500',
            textDecorationLine: 'underline',
          }}
        />
      </View>
    </View>
  );
};

export default LoanContract;
