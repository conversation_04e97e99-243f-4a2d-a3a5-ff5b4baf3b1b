/* eslint-disable react-native/no-inline-styles */
import React, { ReactElement, memo, useCallback, useMemo } from 'react';
import { Image, ImageBackground, Text, View } from '@/components';
import { OrderVOSpace } from '@/types';
import { TouchableOpacity, TouchableWithoutFeedback } from 'react-native';
import { BaseInfoManager, modalDataStoreInstance, ModalList } from '@/managers';
import { HitPointEnumsSpace } from '@/enums';
import { TrackEvent } from '@/utils';
import { Colors } from '@/themes';
import { ImageNames } from '@/config';
import { Strings } from '@/i18n';

interface IMainInfoCardProps {
  loanCreditCalculationData: OrderVOSpace.LoanDetailDataType;
  changeConfirmLoanDialog: (visible: boolean) => void;
  changeCreditAmountRightModal: (visible: boolean) => void;
}
/** 主要信息展示卡片 */
const MainInfoCard = (props: IMainInfoCardProps) => {
  const { loanCreditCalculationData, changeConfirmLoanDialog, changeCreditAmountRightModal } =
    props;
  const {
    loanAmount,
    realAmount,
    days,
    expiryDateNewFormat,
    repaymentAmount,
    realRepaymentAmount,
    productPriceChanged,
    repayType,
    repayTypeStatus,
  } = loanCreditCalculationData;

  const openConfirmLoanDialog = useCallback(() => {
    changeConfirmLoanDialog(true);
  }, []);
  const onPressPayDayRejectTips = useCallback(() => {
    TrackEvent.trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_CREDIT_TBC,
        e: HitPointEnumsSpace.EEventKey.BTN_VIEW_LOAN_RESERVATION,
      },
      '1',
    );
    changeCreditAmountRightModal(true);
  }, []);
  const $payDayRejectIcon = useMemo(() => {
    if (productPriceChanged && productPriceChanged === 'YES') {
      return (
        <TouchableOpacity onPress={onPressPayDayRejectTips}>
          <Image name="_loanCreditTips" />
        </TouchableOpacity>
      );
    }

    return null;
  }, [repayType, repayTypeStatus]);

  /**
   * INE 过期提示信息
   */
  const renderINEExpiredTips = () => {
    /* TODO: 新增接口字段告知是否 INE 过期 */
    return (
      <View
        layoutStrategy="flexRowStart"
        padding="0 8 0 8"
        style={{
          backgroundColor: Colors.WARN_COLOR_100,
        }}>
        <Image margin="4 8 0 0" name={ImageNames._infoIconYellow} width={16} height={16} />
        <Text
          style={{ flex: 1 }}
          category="p2"
          i18nKey={Strings.loanConfirmString.ine_expired_tips}
        />
      </View>
    );
  };

  return (
    <View margin="24 0 0 0" cardType="baseType" style={{ overflow: 'hidden' }}>
      {renderINEExpiredTips()}
      <View padding="0 8 12 8">
        <Text
          margin="12 10 0 10"
          style={{ color: 'text-color-600' }}
          category="p1"
          i18nKey={'loanConfirmString.loan_amount'}
        />
        <View margin={'12 0 0 0'} layoutStrategy="flexRowStartCenter">
          <Text
            margin="0 10 0 10"
            category="h1"
            bold="bold"
            textContent={String(realAmount).toFormatFinance()}
            style={{ color: 'text-color-800' }}
          />
          {realAmount !== loanAmount && (
            <>
              <Text
                margin="0 12 0 0"
                category="h3"
                textContent={String(loanAmount).toFormatFinance()}
                style={{
                  color: 'text-color-600',
                  opacity: 0.6,
                  textDecorationLine: 'line-through',
                }}
              />
              <Image margin="0 5 0 5" name="_coinAddIcon" />
            </>
          )}
          {/* { BaseInfoManager.context.baseModel.isWithholdSwitch && enableWithhold === 'YES' && (
          <View>
            <ImageBackground
              style={{
                position: 'absolute',
                left: '100%',
                bottom: 10,
                justifyContent: 'center',
                alignItems: 'flex-start',
              }}
              name="_loanCreditIncreaseCreditRewardBg">
              <Text
                padding="0 0 0 4"
                style={{
                  flex: 1,
                  lineHeight: 11,
                  textAlign: 'left',
                  textAlignVertical: 'bottom',
                }}>
                <Text status="control" category="c2" i18nKey="loanConfirmString.increase" />
                <Text status="control" category="c2" textContent={`$${upAmountForWithhold}`} />
              </Text>
            </ImageBackground>
          </View>
        )} */}
        </View>
        <View
          padding="0 0 12 0"
          margin="12 10 0 10"
          layoutStrategy="flexRowBetweenCenter"
          style={{
            borderBottomWidth: 1,
            borderBottomColor: 'line-color-200',
            borderStyle: 'dashed',
          }}>
          <Text
            style={{ color: 'text-color-600' }}
            category="p1"
            i18nKey={'loanConfirmString.loan_days'}
          />
          <Text
            style={{ color: 'text-color-800' }}
            category="p1"
            textContent={String(days).toFormatDay()}
          />
        </View>
        <View margin="12 10 0 10" layoutStrategy="flexRowBetweenCenter">
          <Text
            style={{ color: 'text-color-600' }}
            category="p1"
            i18nKey={'loanConfirmString.repay_date'}
          />

          <View layoutStrategy="flexRowCenterCenter">
            <Text
              style={{ color: 'text-color-800' }}
              category="p1"
              textContent={expiryDateNewFormat}
            />
            {$payDayRejectIcon}
          </View>
        </View>
        <View margin="12 10 0 10" layoutStrategy="flexRowBetweenCenter">
          <Text
            style={{ color: 'text-color-600' }}
            category="p1"
            i18nKey={'loanConfirmString.repay_amount'}
          />
          <TouchableWithoutFeedback onPress={openConfirmLoanDialog}>
            <View layoutStrategy="flexRowBetweenCenter">
              <View layoutStrategy="flexRowStartCenter">
                {realRepaymentAmount !== repaymentAmount && (
                  <>
                    <Text
                      margin="0 6 0 0"
                      category="p1"
                      textContent={String(repaymentAmount).toFormatFinance()}
                      style={{
                        color: 'text-color-600',
                        opacity: 0.6,
                        textDecorationLine: 'line-through',
                      }}
                    />
                    <Image name="_mountDownIcon" style={{ marginRight: 6 }} />
                  </>
                )}
                <Text
                  style={{ color: 'text-color-800' }}
                  category="p1"
                  textContent={String(realRepaymentAmount).toFormatFinance()}
                />
              </View>
              <Image name="_detailIcon" margin="0 0 0 8" />
            </View>
          </TouchableWithoutFeedback>
        </View>
      </View>
    </View>
  );
};

export default MainInfoCard;
