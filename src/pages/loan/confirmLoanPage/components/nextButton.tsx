import { Button, CountDown, CountDownRefType, Divider, Image, Layouts, View } from '@/components';
import { OrderVOSpace } from '@/types';
import { TouchableOpacity } from 'react-native';
import { Text } from '@/components';
import { useCallback } from 'react';
import { modalDataStoreInstance, ModalList, UserInfoManager } from '@/managers';
import { nav, TrackEvent } from '@/utils';
import { BaseEnumsSpace, HitPointEnumsSpace } from '@/enums';
import { RouterConfig } from '@/routes';
import { trackCommonEvent } from '@/trackEvent';
import { fetchCancelLoanApply } from '@/server';
import { Strings } from '@/i18n';

interface INextButtonProps {
  handleNext: () => void;
  loanCreditCalculationData: OrderVOSpace.LoanDetailDataType;
  countdownRef: React.RefObject<CountDownRefType>;
  onToggleRejectModal: (visible: boolean) => void;
}

/** 下一步按钮 */
const NextButton = (props: INextButtonProps) => {
  const {
    handleNext,
    loanCreditCalculationData: { countdownFlag, countdown, showRejectBtn, showModifyBtn },
    countdownRef,
    onToggleRejectModal,
  } = props;

  const onHandleReject = useCallback(() => {
    TrackEvent.trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_CREDIT_TBC,
        e: HitPointEnumsSpace.EEventKey.BTN_CREDIT_REFUSE,
      },
      '1',
    );
    return onToggleRejectModal?.(true);
  }, []);

  /** 修改我的申请，取消我的申请，回到复贷选择额度首页 */
  const onModifyMyApply = async () => {
    modalDataStoreInstance.openModal({
      key: ModalList.APPLY_RECHECK,
      confirmBtnCallback: () => {
        trackCommonEvent(
          {
            p: HitPointEnumsSpace.EPageKey.P_CREDIT_TBC,
            e: HitPointEnumsSpace.EEventKey.BTN_EDIT_LOAN_CHOOSE,
          },
          '1',
        );
        handleNext();
      },
      cancelBtnCallback: async () => {
        trackCommonEvent(
          {
            p: HitPointEnumsSpace.EPageKey.P_CREDIT_TBC,
            e: HitPointEnumsSpace.EEventKey.BTN_EDIT_LOAN_CHOOSE,
          },
          '0',
        );
        const resp = await fetchCancelLoanApply({
          applyOrderId: UserInfoManager.context.userModel.applyOrderId,
        });
        if (resp.code === BaseEnumsSpace.ENetworkStatus.SUCCESS) {
          await UserInfoManager.updateUserState();
          // 回到复贷选择额度首页
          nav.nextToTopRouter();
        }
      },
    });
  };

  return (
    <View
      padding="0 0 12 0"
      style={{
        backgroundColor: 'background-color-0',
      }}
      cardType="baseType">
      <View
        margin="12 16 0 16"
        padding="8 12 8 12"
        style={{ borderRadius: 8, backgroundColor: 'secondary-color-100' }}
        layoutStrategy="flexRowStartCenter">
        <Image margin="0 8 0 0" name="_sound" style={{ tintColor: 'text-color-700' }} />
        <Text style={{ flex: 1 }} category="c2" i18nKey="multiPeriodString.countDownTips" />
      </View>
      <CountDown countdownFlag={countdownFlag === 'YES'} ref={countdownRef} time={countdown} />
      <Button
        margin="16 16 24 16"
        onPress={handleNext}
        status="primary"
        textI18nKey="btnString.acceptNow"
      />
      <View layoutStrategy={Layouts.flexRowCenterCenter}>
        {showRejectBtn ? (
          <TouchableOpacity onPress={onHandleReject}>
            <Text
              category="c1"
              i18nKey={Strings.btnString.rejectMyLoan}
              style={{ textDecorationLine: 'underline', color: 'text-color-500' }}
            />
          </TouchableOpacity>
        ) : null}
        {showModifyBtn === 'YES' && showRejectBtn ? (
          <Divider margin="0 12 0 12" direction="vertical" />
        ) : null}
        {showModifyBtn === 'YES' ? (
          <TouchableOpacity onPress={onModifyMyApply}>
            <Text
              category="c1"
              i18nKey={Strings.btnString.modifyMyApply}
              style={{ textDecorationLine: 'underline', color: 'text-color-500' }}
            />
          </TouchableOpacity>
        ) : null}
      </View>
    </View>
  );
};

export default NextButton;
