import { ActionSheet, View, Image, Text, Button } from "@/components"
import { memo } from 'react'
import { Colors } from "@/themes"
import { Strings } from "@/i18n"
import { FlatList, Pressable } from "react-native"
import { nav } from '@/utils';
import { RouterConfig } from '@/routes';
import { EvidenceVOSpace } from "@/types"


type BankCard = EvidenceVOSpace.MeBankcardDataType;
interface Props {
    visible: boolean;
    bankCardList: BankCard[];
    bankCard: BankCard
    onClose: () => void;
    onSelectBankCard: (bankCard: BankCard) => void
}
interface BankCardProps {
    item: BankCard;
    defaultBankCard: BankCard;
    onSelectBankCard: () => void
}
const BankCard = memo((props: BankCardProps) => {
    const { item, defaultBankCard, onSelectBankCard } = props
    const disabled = defaultBankCard.cardNo === item.cardNo
    return (
        <View
            padding="12 12 12 12"
            margin="0 0 12 0"
            style={{ backgroundColor: Colors.BACKGROUND_COLOR_0, borderRadius: 8 }}
        >
            <View layoutStrategy="flexRowBetweenCenter">
                <View layoutStrategy="flexRowStartCenter">
                    <Image name="_couponModalCloseIcon" style={{ width: 40, height: 40, marginRight: 14 }} />
                    <View>
                        <Text textContent={item.bankName} />
                        <Text textContent={item.cardNo} />
                    </View>
                </View>
                <Button
                    size="custom"
                    textI18nKey={Strings.loanConfirmString[disabled ? "selectedBtn" : "selectBtn"]}
                    padding="8 8 8 8"
                    textCategory="c1"
                    disabled={disabled}
                    onPress={onSelectBankCard}
                />
            </View>
            <View margin="8 0 0 10" layoutStrategy="flexRowStartCenter">
                <Image name="_grayInfo" />
                <Text
                    category="c1"
                    padding="0 0 0 8"
                    i18nKey={Strings.loanConfirmString.selectBankCardActionSheetHolder}
                    style={{ color: Colors.TEXT_COLOR_600 }}
                />
                <Text
                    category="c1"
                    padding="0 0 0 4"
                    style={{ color: Colors.TEXT_COLOR_600, flex: 1 }}
                    textContent={item.isSelf}
                    ellipsizeMode="tail"
                    numberOfLines={1}
                />
            </View>
        </View>
    )
})
const AddBankCard = memo((props: { onAddBackCard: () => void }) => {
    const { onAddBackCard } = props
    return (
        <Pressable onPress={onAddBackCard}>
            <View
                padding="12 12 12 12"
                margin="24 0 12 0"
                style={{ backgroundColor: Colors.BACKGROUND_COLOR_0, borderRadius: 8 }}
            >
                <View layoutStrategy="flexRowCenterCenter">
                    <Image name="_addBankCardIcon" />
                    <Text padding="0 0 0 14" i18nKey={Strings.loanConfirmString.selectBankCardActionSheetAddTitle} />
                </View>
            </View>
        </Pressable>
    )
})
const SelectBankCardActionSheet = (props: Props) => {
    const { visible, onClose, bankCardList, bankCard, onSelectBankCard } = props
    const handleAddBackCard = () => {
        onClose()
        nav.navigate(RouterConfig.CLABE_BASIC_INFO as any, { pageKey: RouterConfig.COMFIRM_LOAN });
    }
    const handleSelectBankCard = (bankCard: BankCard) => {
        onClose()
        onSelectBankCard(bankCard)
    }
    return (
        <ActionSheet visible={visible} onClose={onClose}>
            <View
                style={{ height: 64, backgroundColor: Colors.BACKGROUND_COLOR_0 }}
                layoutStrategy="flexRowBetweenCenter"
                padding="0 18 0 18"
            >
                <Text
                    i18nKey={Strings.loanConfirmString.selectBankCardActionSheetTitle}
                    category="h3"
                />
                <Pressable onPress={onClose}>
                    <Image name="_couponModalCloseIcon" />
                </Pressable>
            </View>
            <FlatList
                data={bankCardList}
                renderItem={({ item }) => <BankCard
                    item={item}
                    defaultBankCard={bankCard}
                    onSelectBankCard={() => handleSelectBankCard(item)}
                />}
                keyExtractor={(item) => item.cardNo}
                style={{ backgroundColor: "#EDF0F5", maxHeight: 412, paddingHorizontal: 16 }}
                ListHeaderComponent={<AddBankCard onAddBackCard={handleAddBackCard} />}
            />
        </ActionSheet>
    )
}
export default memo(SelectBankCardActionSheet)