import { CommonModal } from '@/components';
import React from 'react';

interface IProps {
  onConfrim: () => void;
  onCancel: () => void;
  visible: boolean;
}

export default React.memo((props: IProps) => {
  const { visible, onCancel, onConfrim } = props;

  return (
    <CommonModal
      visible={visible}
      onBackdropPress={onCancel}
      hasLinearGradient={false}
      i18nKey="loanConfirmString.argeeLoanContrackTip"
      confirmBtnName="btnString.OK"
      cancelBtnName="btnString.No"
      cancelCallback={onCancel}
      confirmCallback={onConfrim}
    />
  );
});
