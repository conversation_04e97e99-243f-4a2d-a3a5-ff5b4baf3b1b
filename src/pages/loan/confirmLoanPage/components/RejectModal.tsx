/**
 * @description 确认用信申请更改再次提醒弹窗
 */
/* eslint-disable react-native/no-inline-styles */
import React, { useMemo } from 'react';
import _ from 'lodash';
import { modalDataStoreInstance, ModalContextType, ModalList } from '@/managers';
import { Button, CommonModal, Image, LinearGradient, Text, View } from '@/components';
import { Colors } from '@/themes';
import { Strings } from '@/i18n';
import { nav, TrackEvent } from '@/utils';
import { BaseEnumsSpace, HitPointEnumsSpace } from '@/enums';
import { RouterConfig } from '@/routes';

interface Props {
  visible: boolean;
  onToggleRejectModal: (visible: boolean) => void;
}
const RejectModal = (props: Props): React.ReactElement => {
  const { visible, onToggleRejectModal } = props;
  const onClose = () => {
    onToggleRejectModal(false);
  };
  const onConfirm = () => {
    TrackEvent.trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_CREDIT_TBC,
        e: HitPointEnumsSpace.EEventKey.BTN_REMIND_CHOOSE,
      },
      '1',
    );
    onClose();
  };
  const onCancel = () => {
    TrackEvent.trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_CREDIT_TBC,
        e: HitPointEnumsSpace.EEventKey.BTN_REMIND_CHOOSE,
      },
      '0',
    );
    onClose();
    nav.navigate(RouterConfig.CREDIT_REFUSE_QUESTION as any);
  };
  const ButtonGroup = () => {
    return (
      <View layoutStrategy={'flexColumnStartCenter'} margin="24 16 16 16">
        <Button
          width={'90%'}
          appearance="filled"
          status="primary"
          onPress={onConfirm}
          textI18nKey={Strings.btnString.acceptMyLoan}
        />
        <Button
          style={{
            paddingTop: 0,
            paddingBottom: 0,
            minHeight: undefined,
          }}
          appearance="ghost"
          margin={'24 0 0 0'}
          onPress={onCancel}>
          <Text
            status="basic"
            style={{
              textDecorationLine: 'underline',
              color: 'text-color-500',
            }}
            category="c1"
            bold="bold"
            i18nKey={Strings.btnString.rejectMyLoan}
          />
        </Button>
      </View>
    );
  };

  return (
    <CommonModal
      hasLinearGradient={false}
      visible={visible}
      isBottomIconColse={true}
      onBackdropPress={onClose}
      cancelCallback={onClose}
      style={{ backgroundColor: 'transparent' }}
      buttonType={'vertical-special-1'}>
      <View
        style={{
          borderRadius: 16,
          backgroundColor: '#FFF',
          overflow: 'hidden',
          paddingBottom: 12,
        }}>
        <LinearGradient
          style={{ height: 120, alignItems: 'center', justifyContent: 'center' }}
          start={{ x: 0, y: 0 }}
          end={{ x: 0, y: 1 }}
          colors={['rgba(219, 228, 255, 1)', 'rgba(255, 255, 255, 0.8)']}>
          <Image name="_modalRefuseIcon" />
        </LinearGradient>
        <Text
          margin="0 16 0 16"
          category="p1"
          style={{ color: Colors.TEXT_COLOR_800 }}
          i18nKey="loanConfirmString.refuse_credit_tip"
          isCenter
        />
        <ButtonGroup />
      </View>
    </CommonModal>
  );
};
export default RejectModal;
