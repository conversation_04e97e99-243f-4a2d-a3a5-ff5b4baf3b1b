import { CommonModal } from '@/components';
import React from 'react';

interface IProps {
  onConfrim: () => void;
  onCancel: () => void;
  visible: boolean;
}

export default React.memo((props: IProps) => {
  const { visible, onCancel, onConfrim } = props;

  return (
    <CommonModal
      visible={visible}
      onBackdropPress={onCancel}
      hasLinearGradient={false}
      imageKey="_faceModal"
      i18nKey="loanConfirmString.faceLiveVerifyTip"
      confirmBtnName="btnString.OK"
      cancelBtnName="btnString.No"
      cancelCallback={onCancel}
      confirmCallback={onConfrim}
    />
  );
});
