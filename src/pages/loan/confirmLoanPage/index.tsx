/* eslint-disable react-native/no-inline-styles */

import { BusinessUI, Image, Layout, Text, TopNavigation, View } from '@/components';
import { HitPointEnumsSpace } from '@/enums';
import { useTheme } from '@/hooks';
import { useNameSpace } from '@/i18n';
import { KVManager, modalDataStoreInstance, ModalList, UserInfoManager } from '@/managers';
import { submitCreditRefuseRetain } from '@/server';
import { ScreenProps } from '@/types';
import { log, TrackEvent } from '@/utils';
import React, { ReactElement, useMemo } from 'react';
import { Dimensions, RefreshControl, ScrollView, TouchableOpacity } from 'react-native';
import RenderHtml from 'react-native-render-html';
import CouponRetainModal from '../../../pages/components/coupon/CouponRetainModal';
import ConfirmLoanDetailDialog from './components/confirmLoanDetailDialog';
import CouponHelpsModal from './components/couponHelpsModal';
import LoanContract from './components/loanContract';
import LoanContractConfirmDialog from './components/loanContractConfirmDialog';
import MainInfoCard from './components/mainInfoCard';
import NeedFaceDialog from './components/needFaceDialog';
import NextButton from './components/nextButton';
import useData from './useData';
import { Colors } from '@/themes';
import CouponSelectCard from '../../components/coupon/CouponSelectCard';
import CouponSelectModal from '../../components/coupon/CouponSelectModal';
import CreditAmountRightModal from './components/CreditAmountRightModal';
import RejectModal from './components/RejectModal';
import SelectBankCard from './components/SelectBankCard';
import BankCardActionSheet from './components/BankCardActionSheet';

const { AutoWithholdCardAutoBind } = BusinessUI;

export default ({
  route,
  navigation,
}: ScreenProps<{ agreeLoanContract: boolean }>): ReactElement => {
  const {
    countdownRef,
    toLoanContractPage,
    onRefresh,
    handleNext,
    refreshing,
    loanCreditCalculationData,
    agreeLoanContractBool,
    onChangeAgreeLoanContractBool,
    setAgreeLoanContractBool,
    needAgreeContractDialogVisible,
    closeNeedAgreeContractDialog,
    agreeLoanContractDialogConfirm,
    inputEnterDisabled,
    faceLiveDialogVisible,
    faceDialogConfirm,
    closeFaceLiveDialog,
    changeConfirmDetialDialogVisible,
    confirmLoanDetailDialogVisible,
    onOpenCouponHelp,
    onCloseCouponHelp,
    couponHelpModalVisible,
    couponSelectModalVisible,
    couponList,
    availableCouponList,
    onOpenCouponSelect,
    onCloseCouponSelect,
    getRepayCouponList,
    autoWithhold,
    openAutoWithholdContract,
    onOpenAutoWithhold,
    onCloseAutoWithhold,
    couponSelected,
    onConfirmSelectCoupon,
    creditAmountRightModalVisible,
    changeCreditAmountRightModal,
    isShowRejectModal,
    onToggleRejectModal,
    bankCard,
    bankCardList,
    isBankCardSheetVisible,
    handleShowBankCardSheet,
    handleHiddenBankCardSheet,
    handleSelectBankCard,
  } = useData();
  const theme = useTheme();
  const t = useNameSpace().t;

  React.useEffect(() => {
    if (route.params?.agreeLoanContract) {
      setAgreeLoanContractBool(true);
    }
  }, [route.params]);

  React.useEffect(() => {
    try {
      const { isRetain, retainMethod, originalQuota, currentQuota, couponAmount, couponType } =
        route.params || { isRetain: '' };
      if (isRetain === 'YES') {
        const eventKey =
          couponType === 'DEDUCTION'
            ? HitPointEnumsSpace.EEventKey.BTN_INTEREST_DISCOUNT
            : HitPointEnumsSpace.EEventKey.BTN_CREDIT_INCREASE;

        modalDataStoreInstance.openModal({
          key: ModalList.COUPON_RETAIN,
          extra: {
            data: {
              couponType: couponType === 'DEDUCTION' ? 'discount' : 'increase',
              originalQuota,
              currentQuota,
              couponAmount,
            },
          },
          confirmBtnCallback: async () => {
            // 触发埋点直接关闭弹窗
            TrackEvent.trackCommonEvent(
              {
                p: HitPointEnumsSpace.EPageKey.P_CREDIT_TBC,
                e: eventKey,
              },
              '1',
            );
            const { code } = await submitCreditRefuseRetain({
              acceptRetain: 'ACCEPT',
            });
            if (code === 0) {
            }
            getRepayCouponList();
          },
        });
      }
    } catch (error) {
      log.error('not get credit refuse route params fail', { error });
    }
  }, [route.params]);

  /** 自动代扣服务绑定的参数回调 */
  React.useEffect(() => {
    const { agreeAutoWithHold } = route.params || { agreeAutoWithHold: false };
    const { realAmount, enableWithhold } = loanCreditCalculationData;
    /** 用于提示开通自动代扣提额弹窗 */
    const localCacheKey = `_enable_automatic_deduction_rewards_${UserInfoManager.context.userModel.applyOrderId}`;
    if (
      agreeAutoWithHold &&
      enableWithhold === 'YES' &&
      !KVManager.action.getBoolean(localCacheKey)
    ) {
      const children = (
        <View margin="12 12 12 12">
          <Text isCenter category="p2" i18nKey="loanConfirmString.increaseCredit" />
          <Text
            isCenter
            category="h3"
            style={{ color: 'danger-color-500' }}
            textContent={String(realAmount).toFormatFinance()}
          />
        </View>
      );

      modalDataStoreInstance.openModal({
        key: ModalList.INFO_PROMPT_CONFIRM,
        isBottomIconColse: false,
        imageKey: '_loanCreditIncreaseCreditRewardIcon',
        children,
        confirmBtnName: 'btnString.close',
        confirmBtnCallback: async () => {
          // 触发埋点直接关闭弹窗
        },
      });
      KVManager.action.setBoolean(localCacheKey, true);
    }
  }, [loanCreditCalculationData.enableWithhold, route.params]);
  return (
    <>
      <Layout pLevel="0" level="1" topCompensateColor="primary-color-500">
        <TopNavigation
          showLogoAction={true}
          isShowVip
          showMessage
          type="primary"
          bottomLine={false}
        />
        <ScrollView
          keyboardShouldPersistTaps="always"
          showsVerticalScrollIndicator={false}
          overScrollMode={'never'}
          refreshControl={
            <RefreshControl colors={['#4C6EFF']} refreshing={refreshing} onRefresh={onRefresh} />
          }>
          <View
            style={{
              height: 180,
              backgroundColor: 'primary-color-500',
            }}
          />
          <View margin="-180 0 0 0">
            <View margin="0 0 16 0">
              <View margin="0 16 0 16">
                <MainInfoCard
                  loanCreditCalculationData={loanCreditCalculationData}
                  changeConfirmLoanDialog={changeConfirmDetialDialogVisible}
                  changeCreditAmountRightModal={changeCreditAmountRightModal}
                />
              </View>
              {loanCreditCalculationData.showWithholdBtn === 'YES' && (
                <AutoWithholdCardAutoBind
                  cardNo={loanCreditCalculationData.cardNo}
                  withholdState={autoWithhold}
                  openAutoWithholdContract={openAutoWithholdContract}
                  openWithholdAuthorization={onOpenAutoWithhold}
                  closeWithholdAuthorization={onCloseAutoWithhold}
                />
              )}
              <SelectBankCard
                bankCard={bankCard as any}
                onShowBankCardSheet={handleShowBankCardSheet}
              />
              <CouponSelectCard
                couponList={availableCouponList}
                selectCouponList={couponSelected}
                onOpenCouponSelect={onOpenCouponSelect}
                onOpenCouponHelp={onOpenCouponHelp}
              />
              <View margin="0 16 0 16">
                <LoanContract
                  inputEnterDisabled={inputEnterDisabled}
                  agreeLoanContractBool={agreeLoanContractBool}
                  onChangeAgreeLoanContractBool={onChangeAgreeLoanContractBool}
                  toLoanContractPage={toLoanContractPage}
                />
                <View margin="12 0 24 0" padding="12 8 12 8" cardType="baseType">
                  <View layoutStrategy="flexRowStartCenter">
                    <Image name="_blackNotice" />
                    <Text
                      margin="0 10 0 10"
                      style={{ color: 'text-color-600' }}
                      category="p1"
                      i18nKey={'loanConfirmString.loan_notice_title'}
                    />
                  </View>
                  <RenderHtml
                    contentWidth={Dimensions.get('window').width - 56}
                    source={{ html: loanCreditCalculationData.loanTips || '' }}
                  />
                </View>
              </View>
            </View>
          </View>
        </ScrollView>
        <NextButton
          handleNext={handleNext}
          loanCreditCalculationData={loanCreditCalculationData}
          //@ts-ignore
          countdownRef={countdownRef}
          onToggleRejectModal={onToggleRejectModal}
        />
      </Layout>
      <NeedFaceDialog
        visible={faceLiveDialogVisible}
        onCancel={closeFaceLiveDialog}
        onConfrim={faceDialogConfirm}
      />
      <LoanContractConfirmDialog
        visible={needAgreeContractDialogVisible}
        onCancel={closeNeedAgreeContractDialog}
        onConfrim={agreeLoanContractDialogConfirm}
      />
      <ConfirmLoanDetailDialog
        visible={confirmLoanDetailDialogVisible}
        changeDialogVisibe={changeConfirmDetialDialogVisible}
        loanCreditCalculationData={loanCreditCalculationData}
      />
      <CouponSelectModal
        availableCouponList={availableCouponList}
        visible={couponSelectModalVisible}
        onCancel={onCloseCouponSelect}
        couponList={couponList}
        couponSelected={couponSelected}
        onConfirm={onConfirmSelectCoupon}
        pageKey={HitPointEnumsSpace.EPageKey.P_CREDIT_TBC}
      />
      <CouponHelpsModal visible={couponHelpModalVisible} onCancel={onCloseCouponHelp} />
      <CouponRetainModal />
      <CreditAmountRightModal
        visible={creditAmountRightModalVisible}
        changeDialogVisibe={changeCreditAmountRightModal}
        nextCreditAmount={loanCreditCalculationData?.nextCreditAmount}
      />
      <RejectModal visible={isShowRejectModal} onToggleRejectModal={onToggleRejectModal} />
      <BankCardActionSheet
        visible={isBankCardSheetVisible}
        bankCardList={bankCardList}
        bankCard={bankCard as any}
        onClose={handleHiddenBankCardSheet}
        onSelectBankCard={handleSelectBankCard}
      />
    </>
  );
};
