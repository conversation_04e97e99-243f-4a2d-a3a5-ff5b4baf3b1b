import { CountDownRefType } from '@/components';
import { BaseEnumsSpace, HitPointEnumsSpace, UserEnumsSpace } from '@/enums';
import {
  useCheckAllPermissionAndRequestPermission,
  useCheckCameraPermissionAndRequestPermission,
  useGetUserStateAndNextRouterOrOtherCallBack,
  useOnInit,
} from '@/hooks';
import { BaseInfoManager, modalDataStoreInstance, ModalList, UserInfoManager } from '@/managers';
import { RouterConfig } from '@/routes';
import {
  confirmApplyConfirm,
  fetchApplyContract,
  fetchCouponsByConditions,
  fetchDisbursingPageShowTipList,
  fetchLoanCreditCalculation,
  fetchWithholdContract,
  fetchUserBankcard,
  responseHandler
} from '@/server';
import { OrderVOSpace, UserVOSpace, EWithholderTipType, EvidenceVOSpace } from '@/types';
import { TrackEvent, nav } from '@/utils';
import CryptoJS from 'crypto-js';
import _, { set } from 'lodash';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { AdjustTools } from '../../../business/_export_';
import useFaceLiveVerify from './useFaceLiveVerify';
import { useSetState } from "ahooks"
import { State } from "./type"


export default function useData() {
  const [agreeLoanContractBool, setAgreeLoanContractBool] = useState<boolean>(true);
  const countdownRef = useRef<CountDownRefType>(null);

  /** 确认用信贷款试算 */
  const [loanCreditCalculationData, setLoanCreditCalculation] =
    useState<OrderVOSpace.LoanDetailDataType>({
      /** 还款金额 */
      repaymentAmount: '0',
      /** 真实还款金额(减免之后) */
      realRepaymentAmount: '0',
      /** 卡类型 */
      cardType: '*',
      /** 银行名称 */
      bankName: '*',
      /** 卡号 */
      cardNo: '*',
      /** 贷款金额 */
      loanAmount: '',
      /** 到账金额 */
      realAmount: '0',
      /** 新的格式化到期时间 */
      loanDate: '*',
      /** 新的格式化到期时间 */
      expiryDateNewFormat: '*',
      /** 到期日期 */
      expiryDate: '*',
      /** 利息 */
      interest: '0',
      /** 手续费vat */
      processFeeVat: '0',
      /** 手续费 */
      processFee: '0',
      /** 天数 */
      days: '',
      /** 用信倒计时 */
      countdown: 0,
      /** 用信倒计时开关 */
      countdownFlag: 'NO',
      /** 是否用券 */
      couponFlag: 'NO',
      /** 优惠券类型 */
      couponType: '',
      /** 优惠券金额  */
      couponAmount: '0',
      /** 是否显示挽留入口 */
      showRejectBtn: false,
      /** 是否展示自动代扣 */
      showWithholdBtn: 'NO',
      /** 是否已经开通代扣提额 */
      enableWithhold: 'NO',
      /**  开通代扣提额的额度 */
      upAmountForWithhold: 0,
      /** 产品变更提示 */
      productChangeTips: '',
      /** 提示文案 */
      loanTips: '',
      /** 是否显示修改入口 */
      showModifyBtn: 'NO',
      /** 下一个信用额度 */
      nextCreditAmount: '0',
      /** 是否显示信用额度特权说明 */
      productPriceChanged: 'NO',
    });

  const [couponList, setCouponList] = useState<UserVOSpace.CouponsItem[]>([]);

  const autoSelectedCouponRef = useRef<boolean>(true);

  const [couponHelpModalVisible, setCouponHelpModalVisible] = useState<boolean>(false);

  const [couponSelectModalVisible, setCouponSelectModalVisible] = useState<boolean>(false);

  const [couponSelected, setCouponSelected] = useState<UserVOSpace.CouponsItem[]>([]);
  const couponSelectedRef = useRef<UserVOSpace.CouponsItem[]>([]);

  /** 是否开通自动代扣 */
  const [autoWithhold, setAutoWithhold] = useState<boolean>(
    BaseInfoManager.context.baseModel.autoWithholdFirstLoanContractPageDefaultValue,
  );

  const [creditAmountRightModalVisible, setCreditAmountRightModalVisible] =
    useState<boolean>(false);

  const [isShowRejectModal, setShowRejectModal] = useState<boolean>(false);
  const [state, setState] = useSetState<State>({
    bankCard: {},
    bankCardList: [],
    isBankCardSheetVisible: false
  })
  useEffect(() => {
    couponSelectedRef.current = couponSelected;
  }, [couponSelected]);

  const onOpenAutoWithhold = useCallback(() => {
    modalDataStoreInstance.openModal({
      key: ModalList.WITHHOLDER_TIP,
      extra: EWithholderTipType.OPEN,
      confirmBtnCallback: async () => {
        TrackEvent.trackCommonEvent(
          {
            p: HitPointEnumsSpace.EPageKey.P_CREDIT_TBC,
            e: HitPointEnumsSpace.EEventKey.BTN_SELECT_AUTODEBIT,
          },
          '1',
        );
        setAutoWithhold(true);
        getLoanCreditCalculation({
          withholdAuthorize: true,
        });
      },
    });

  }, [autoWithhold, loanCreditCalculationData]);

  useEffect(() => {
    if (loanCreditCalculationData.productChangeTips !== '') {
      modalDataStoreInstance.openModal({
        key: ModalList.INFO_PROMPT_CONFIRM,
        topImageKey: '_vipTipIcon',
        titleKey: 'loanConfirmString.loanPayDayRejectTitle',
        // i18nKey: 'loanConfirmString.loanPayDayRejectTips',
        content: loanCreditCalculationData.productChangeTips,
        confirmBtnName: 'btnString.agree',
      });
    }
  }, [loanCreditCalculationData.productChangeTips]);

  const onCloseAutoWithhold = useCallback(() => {
    modalDataStoreInstance.openModal({
      key: ModalList.WITHHOLDER_TIP,
      extra: EWithholderTipType.CLOSE,
      confirmBtnCallback: () => {
        TrackEvent.trackCommonEvent(
          {
            p: HitPointEnumsSpace.EPageKey.P_CREDIT_TBC,
            e: HitPointEnumsSpace.EEventKey.BTN_SELECT_AUTODEBIT,
          },
          '0',
        );
        setAutoWithhold(false);
        getLoanCreditCalculation({
          withholdAuthorize: false,
        });
      },
    });
  }, [autoWithhold, loanCreditCalculationData]);

  /** 跳转到自动代扣协议 */
  const openAutoWithholdContract = async () => {
    TrackEvent.trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_CREDIT_TBC,
        e: HitPointEnumsSpace.EEventKey.BTN_NEGOTIATE_CHECK,
      },
      '1',
    );
    let result = await fetchWithholdContract({
      cardNo: loanCreditCalculationData.cardNo,
      bankName: loanCreditCalculationData.bankName,
    });
    if (result.code === BaseEnumsSpace.ENetworkStatus.SUCCESS) {
      nav.navigate(RouterConfig.AUTOMATIC_WITHHOLD_PROTOCOL as any, {
        html: CryptoJS.enc.Base64.parse(String(result.data)).toString(CryptoJS.enc.Utf8),
        currentRoute: RouterConfig.COMFIRM_LOAN,
        acceptHandle: onOpenAutoWithhold,
        rejectHandle: onCloseAutoWithhold,
      });
    }

    return result?.code === BaseEnumsSpace.ENetworkStatus.SUCCESS;
  };
  // 获取银行卡列表
  const getBankCardList = async () => {
    const { error, data } = await responseHandler(fetchUserBankcard())
    if (error) {
      return
    }
    const bankCard = data?.find(item => item.isDef === "YES") || {}
    setState({
      bankCardList: data,
      bankCard
    })
  };
  const onOpenCouponHelp = useCallback(() => {
    setCouponHelpModalVisible(true);
  }, []);

  const onCloseCouponHelp = useCallback(() => {
    setCouponHelpModalVisible(false);
  }, []);

  const availableCouponList = useMemo(() => {
    const { couponFlag } = loanCreditCalculationData;

    if (couponFlag === 'YES' && UserInfoManager.context.userModel.isCreditSuccessWaitFace) {
      return [];
    } else {
      return couponList;
    }
  }, [couponList, loanCreditCalculationData]);

  const onOpenCouponSelect = useCallback(() => {
    TrackEvent.trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_CREDIT_TBC,
        e: HitPointEnumsSpace.EEventKey.BTN_USE_COUPON,
      },
      '1',
    );
    // todo 选择优惠券时，需要判断是否已经绑定了优惠券，和是否有可用的优惠券可以使用。
    if (UserInfoManager.context.userModel.isCreditSuccessWaitFace) {
      // todo 已经绑定优惠券
      if (loanCreditCalculationData.couponFlag === 'YES') {
        modalDataStoreInstance.openModal({
          key: ModalList.INFO_PROMPT_CONFIRM,
          imageKey: '_modalCouponSelectIcon',
          i18nKey: 'couponString.couponHasBeenBound',
          confirmBtnName: 'btnString.agree',
        });
      }
    } else {
      setCouponSelectModalVisible(true);
    }
  }, [availableCouponList, loanCreditCalculationData]);
  const getCouponSerialNumbers = (
    couponList: UserVOSpace.CouponsItem[] = couponSelectedRef.current,
  ) => {
    const serialNumbers = couponList.map(i => i.serialNumber).join(',') || '';
    return serialNumbers;
  };
  const onCloseCouponSelect = useCallback(() => {
    setCouponSelectModalVisible(false);
  }, []);
  const onConfirmSelectCoupon = useCallback(
    async (coupons?: UserVOSpace.CouponsItem[], index?: number) => {
      autoSelectedCouponRef.current = false;
      coupons = coupons || [];
      couponSelectedRef.current = coupons;
      setCouponSelected(coupons);
      const serialNumbers = getCouponSerialNumbers(coupons);
      await getLoanCreditCalculation({
        couponSerialNumber: serialNumbers,
      });
    },
    [availableCouponList],
  );

  /** 获取可用的还款优惠券 */
  const getRepayCouponList = useCallback(async () => {
    const { code, data } = await fetchCouponsByConditions({
      /** 优惠券状态 */
      status: UserEnumsSpace.ECouponsStatus.AVAILABLE,
      optimal: 'YES',
    });
    let couponSerialNumber = '';
    if (code === 0) {
      // 这里根据data需要判断是不是需要打开添加还款日历提醒的弹窗
      if (
        data.length > 0 &&
        autoSelectedCouponRef.current
        // && !UserInfoManager.context.userModel.isCreditSuccessWaitFace
      ) {
        const _data = data.filter(item => item.canCheck === 'YES');
        const selectCoupons = _data.filter(
          item => item.couponStatus === UserEnumsSpace.ECouponsStatus.BINDING,
        );
        setCouponSelected(selectCoupons);
        setCouponList(_data);
        couponSerialNumber = getCouponSerialNumbers(selectCoupons);
      }
    }
    await getLoanCreditCalculation({ couponSerialNumber });
  }, []);

  // 按钮禁用状态
  const [inputEnterDisabled, setInputEnterDisabled] = useState<boolean>(false);

  /** 初始化方法 */
  const { refreshing, setRefreshing, loading, onRefresh } = useOnInit({
    callback: async () => {
      loading.current = true;
      BaseInfoManager.changeLoadingModalVisible(true);
      await getRepayCouponList();
      await getBankCardList()
      faceLiveVerify();
      BaseInfoManager.changeLoadingModalVisible(false);
      loading.current = false;
    },
    refreshCallback: async () => {
      setRefreshing(true);
      loading.current = true;
      await getRepayCouponList();
      await getBankCardList()
      await getUserStateAndNextRouterOrOtherCallBack(true, faceLiveVerify);
      loading.current = false;
      setRefreshing(false);
    },
    isBackAutoRefresh: true,
    pageKey: HitPointEnumsSpace.EPageKey.P_CREDIT_TBC,
  });

  // /** 活体提示弹窗 */
  const [faceLiveDialogVisible, setFaceLiveDialogVisible] = useState<boolean>(false);

  // /** 确认用信详情弹窗 */
  const [confirmLoanDetailDialogVisible, setConfirmLoanDetailDialogVisible] =
    useState<boolean>(false);

  /** 关闭活体验证提示弹窗 */
  const closeFaceLiveDialog = useCallback(() => {
    // 关闭活体
    TrackEvent.trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_CREDIT_TBC,
        e: HitPointEnumsSpace.EEventKey.BTN_LIVE_VERIFY,
      },
      '0',
    );
    setFaceLiveDialogVisible(false);
  }, []);
  /** 开启活体验证提示弹窗 */
  const openFaceLiveDialog = useCallback(() => {
    setFaceLiveDialogVisible(true);
  }, []);

  /** 需要同意贷款合同提示弹窗 */
  const [needAgreeContractDialogVisible, setNeedAgreeContractDialogVisible] =
    useState<boolean>(false);
  /** 开启同意贷款合同提示弹窗 */
  const closeNeedAgreeContractDialog = useCallback(() => {
    setNeedAgreeContractDialogVisible(false);
  }, []);
  /** 关闭贷款合同提示弹窗 */
  const openNeedAgreeContractDialogVisible = useCallback(() => {
    setNeedAgreeContractDialogVisible(true);
  }, []);
  /** 改变确认用信详情提示弹窗 */
  const changeConfirmDetialDialogVisible = useCallback(
    (visible: boolean) => {
      TrackEvent.trackCommonEvent(
        {
          p: HitPointEnumsSpace.EPageKey.P_CREDIT_TBC,
          e: HitPointEnumsSpace.EEventKey.BTN_CREDIT_DETAILS,
        },
        visible ? '1' : '0',
      );
      setConfirmLoanDetailDialogVisible(visible);
    },
    [confirmLoanDetailDialogVisible],
  );
  /** 关闭确认用信详情提示弹窗 */

  const faceDialogConfirm = useCallback(
    _.debounce(async () => {
      TrackEvent.trackCommonEvent(
        {
          p: HitPointEnumsSpace.EPageKey.P_CREDIT_TBC,
          e: HitPointEnumsSpace.EEventKey.BTN_LIVE_VERIFY,
        },
        '1',
      );
      if (await checkCameraPermission()) {
        // 判断活体功能是否异常
        if (UserInfoManager.context.userModel.isFaceSceneError) {
          nav.navigate(RouterConfig.MAINTAINER as any);
        } else {
          loading.current = true;
          // 在这里先获取appConfig 配置接口在接口中查看是采用哪种活体方式。
          await startLiveVerifyForConfig();
        }
      }
    }, 300),
    [],
  );

  /** 获取appConfig判断是采用哪种活体方式 */
  const startLiveVerifyForConfig = async () => {
    onFaceStartLiveExamine();
  };

  /** 用户做活体验证 */
  const faceLiveVerify = (status: boolean = true) => {
    // 判断当前的条件是不是需要做活体
    if (UserInfoManager.context.userModel.isCreditSuccessWaitFace && status) {
      setInputEnterDisabled(true);
      // 需要弹出做活体提示弹窗
      openFaceLiveDialog();
      return true;
    }
    loading.current = false;
    return false;
  };

  /** face++ 活体验证方法 */
  const onFaceStartLiveExamine = useFaceLiveVerify(faceLiveVerify)[0];

  const checkCameraPermission = useCheckCameraPermissionAndRequestPermission()[0];

  const [getUserStateAndNextRouterOrOtherCallBack] = useGetUserStateAndNextRouterOrOtherCallBack(
    RouterConfig.COMFIRM_LOAN,
  );

  useEffect(() => {
    // adjust 首次进入确认用信场景打点
    AdjustTools.AdjustEventPointTools.trackEventOfCustReview();

    return () => {
      countdownRef.current?.clearCountDown();
    };
  }, []);

  /** 跳转到贷款合同 */
  const toLoanContractPage = async () => {
    TrackEvent.trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_CREDIT_TBC,
        e: HitPointEnumsSpace.EEventKey.BTN_VIEW_LOAN_CONTRACT,
      },
      '1',
    );
    await openContract();
  };

  const getParams = () => {
    let withholdAuthorizeStatus =
      loanCreditCalculationData.showWithholdBtn === 'YES' ? (autoWithhold ? 'YES' : 'NO') : '';
    return {
      /** 选择额度 */
      selectedAmount: loanCreditCalculationData.loanAmount,
      /** 优惠券编号 */
      couponSerialNumber: getCouponSerialNumbers(),
      selectedDays: loanCreditCalculationData.days,
      withholdAuthorizeStatus,
      cardNo: loanCreditCalculationData.cardNo,
    };
  };

  const openContract = async () => {
    let result = await fetchApplyContract(getParams());

    if (result.code === BaseEnumsSpace.ENetworkStatus.SUCCESS) {
      nav.navigate(RouterConfig.LOAN_CONTRACT as any, {
        html: CryptoJS.enc.Base64.parse(String(result.data)).toString(CryptoJS.enc.Utf8),
      });
    }

    return result?.code === BaseEnumsSpace.ENetworkStatus.SUCCESS;
  };

  const onCreateLoanApplyComfirmOrFaceLive = _.throttle(async () => {
    let faceLiveVerifyResult = faceLiveVerify();
    BaseInfoManager.changeLoadingModalVisible(true);
    if (UserInfoManager.context.userModel.isCreditWait) {
      // 如果需要确认用信先确认用信
      await onCreateLoanApplyComfirm();
    } else if (faceLiveVerifyResult) {
    } else {
      await getUserStateAndNextRouterOrOtherCallBack(true, faceLiveVerify);
    }
    BaseInfoManager.changeLoadingModalVisible(false);
    // 成功之后获取用户的状态，然后判断是不是需要让用户做活体
  }, 300);

  /** 用户确认用信 */
  const onCreateLoanApplyComfirm = async () => {
    // 提交申请贷款逻辑
    const { code } = await confirmApplyConfirm(getParams());
    if (code === 0) {
      setInputEnterDisabled(true);
      // adjust 用信打点
      AdjustTools.AdjustEventPointTools.trackEventOfAccept();
      await getUserStateAndNextRouterOrOtherCallBack(true, faceLiveVerify);
    }
    getRepayCouponList();
  };

  /** 获取贷款计算信息 */
  const getLoanCreditCalculation = async ({
    couponSerialNumber,
    withholdAuthorize,
  }: {
    couponSerialNumber?: string;
    withholdAuthorize?: Boolean;
  }): Promise<string> => {
    const couponNum = couponSerialNumber || getCouponSerialNumbers();
    let withholdAuthorizeStatus =
      loanCreditCalculationData.showWithholdBtn === 'YES'
        ? (withholdAuthorize !== undefined ? withholdAuthorize : autoWithhold)
          ? 'YES'
          : 'NO'
        : '';

    const { data, code } = await fetchLoanCreditCalculation({
      ...getParams(),
      couponSerialNumber: couponNum,
      withholdAuthorizeStatus,
    });
    if (code === 0) {
      setLoanCreditCalculation(data);
      countdownRef.current?.startCountDown(data.countdown);
      return data.countdownFlag;
    }
    return '';
  };
  const checkAllPermission = useCheckAllPermissionAndRequestPermission()[0];

  const agreeLoanContractDialogConfirm = async () => {
    // 用户通过直接提交贷款订单
    setAgreeLoanContractBool(true);
    onCreateLoanApplyComfirmOrFaceLive();
  };

  /** 下一步 */
  const handleNext = async () => {
    // 确认用信按钮点击事件
    TrackEvent.trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_CREDIT_TBC,
        e: HitPointEnumsSpace.EEventKey.BTN_CONFIRM_CREDIT,
      },
      '1',
    );
    // 先判断是否有所有权限
    if ((await checkAllPermission()) === 'agree') {
      // 在判断是否同意了合同
      if (agreeLoanContractBool) {
        // 提交用信
        onCreateLoanApplyComfirmOrFaceLive();
      } else {
        // 弹出合同弹窗让用户确认
        openNeedAgreeContractDialogVisible();
      }
    }
  };

  const onChangeAgreeLoanContractBool = useCallback(() => {
    if (!UserInfoManager.context.userModel.isCreditSuccessWaitFace) {
      setAgreeLoanContractBool(preState => {
        TrackEvent.trackCommonEvent(
          {
            p: HitPointEnumsSpace.EPageKey.P_CREDIT_TBC,
            e: HitPointEnumsSpace.EEventKey.BTN_LOAN_CONTRACT_CHECK,
          },
          !preState ? '1' : '0',
        );
        return !preState;
      });
    }
  }, []);
  const changeCreditAmountRightModal = useCallback((visible: boolean) => {
    TrackEvent.trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_CREDIT_TBC,
        e: HitPointEnumsSpace.EEventKey.BTN_CREATE_LIMIT_INCREASE_TEASER,
      },
      '1',
    );
    setCreditAmountRightModalVisible(visible);
  }, []);
  const onToggleRejectModal = useCallback((visible: boolean) => {
    setShowRejectModal(visible);
  }, []);
  const handleShowBankCardSheet = useCallback(() => {
    setState({
      isBankCardSheetVisible: true
    })
  }, [])
  const handleHiddenBankCardSheet = useCallback(() => {
    setState({
      isBankCardSheetVisible: false
    })
  }, [])
  const handleSelectBankCard = (bankCard: EvidenceVOSpace.MeBankcardDataType) => {
    setState({ bankCard })
  }
  return {
    countdownRef,
    toLoanContractPage,
    refreshing,
    handleNext,
    onRefresh,
    loanCreditCalculationData,
    agreeLoanContractBool,
    onChangeAgreeLoanContractBool,
    setAgreeLoanContractBool,
    faceLiveDialogVisible,
    needAgreeContractDialogVisible,
    faceDialogConfirm,
    closeFaceLiveDialog,
    closeNeedAgreeContractDialog,
    agreeLoanContractDialogConfirm,
    inputEnterDisabled,
    changeConfirmDetialDialogVisible,
    confirmLoanDetailDialogVisible,
    onOpenCouponHelp,
    onCloseCouponHelp,
    couponHelpModalVisible,
    couponSelectModalVisible,
    couponList,
    availableCouponList,
    onOpenCouponSelect,
    onCloseCouponSelect,
    getRepayCouponList,
    getLoanCreditCalculation,
    autoWithhold,
    openAutoWithholdContract,
    onOpenAutoWithhold,
    onCloseAutoWithhold,
    couponSelected,
    onConfirmSelectCoupon,
    changeCreditAmountRightModal,
    creditAmountRightModalVisible,
    onToggleRejectModal,
    isShowRejectModal,
    handleShowBankCardSheet,
    handleHiddenBankCardSheet,
    handleSelectBankCard,
    ...state
  };
}
