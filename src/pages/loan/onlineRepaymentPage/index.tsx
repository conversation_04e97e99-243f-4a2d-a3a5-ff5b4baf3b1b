/* eslint-disable react-native/no-inline-styles */

import { Card, Image, Layout, Text, TopNavigation, View } from '@/components';
import { ScreenProps } from '@/types';
import React, { ReactElement } from 'react';
import { ScrollView, TouchableOpacity } from 'react-native';
import useData from './useData';
import { useTheme } from '@/hooks';

export default ({ navigation, route }: ScreenProps<{}>): ReactElement => {
  const { onlineRepayDetail, onCopyClabe, refreshing, onRefresh } = useData({
    navigation,
  });
  const theme = useTheme();

  return (
    <Layout level="1" pLevel="0">
      <TopNavigation titleKey={'repaymentString.useSPEI'} bottomLine />
      <ScrollView
        fadingEdgeLength={10}
        keyboardShouldPersistTaps="always"
        style={{
          paddingHorizontal: 16,
        }}>
        {/* <View
          margin="16 0 12 0"
          padding="10 8 10 8"
          layoutStrategy="flexRowStartCenter"
          style={{
            backgroundColor: 'warn-color-100',
            borderRadius: 8,
          }}>
          <Image margin="0 12 0 0" name="_onlineRepayIcon" />
          <Text
            category="p1"
            i18nKey={'repaymentString.onlineDesc'}
            bold={'500'}
            style={{
              fontSize: 12,
              width: '90%',
              lineHeight: 18,
            }}
          />
        </View> */}
        <View cardType="baseType" margin="16 0 0 0" padding="16 12 16 12">
          <Text category="p2" textContent={onlineRepayDetail.channelTips} />
          <Text
            category="p2"
            margin="24 0 0 0"
            i18nKey={'repaymentString.onlineCard1_4'}
            style={{
              color: 'text-color-600',
            }}
          />
          <View margin="16 0 0 0" layoutStrategy="flexRowBetweenCenter">
            <Card backgroundColor="background-color-100">
              <Text
                category="p1"
                bold={'bold'}
                textContent={String(onlineRepayDetail.clabe).toFormatClabe()}
              />
            </Card>
            <TouchableOpacity onPress={onCopyClabe}>
              <Image padding="0 4 0 4" name="_rePaymentcopyIcon" />
            </TouchableOpacity>
          </View>
          <View margin="24 0 0 0" layoutStrategy="flexRowBetweenCenter">
            <Text
              category="p2"
              i18nKey={'repaymentString.onlineCard1_5'}
              style={{
                color: 'text-color-600',
              }}
            />
            <Text
              category="p1"
              textContent={String(onlineRepayDetail.totalAmount).toFormatFinance()}
            />
          </View>
        </View>
        <View cardType="baseType" margin="16 0 0 0" padding="16 12 16 12">
          <View layoutStrategy="flexRowStartCenter">
            <Image tintColor={theme['text-color-800']} margin="0 8 0 0" name="_sound" />
            <Text
              category="p1"
              width={'90%'}
              i18nKey={'repaymentString.onlineCard2_1'}
              style={{
                color: 'text-color-800',
              }}
            />
          </View>
          <View margin="12 0 0 0">
            <Text
              category="p2"
              i18nKey={'repaymentString.onlineCard2_2'}
              style={{
                color: 'text-color-600',
              }}
            />
          </View>
          <View margin="12 0 0 0">
            <Text
              category="p2"
              i18nKey={'repaymentString.onlineCard2_3'}
              style={{
                color: 'text-color-600',
              }}
            />
          </View>
          <View margin="12 0 0 0">
            <Text
              category="p2"
              i18nKey={'repaymentString.onlineCard2_4'}
              style={{
                color: 'text-color-600',
              }}
            />
          </View>
          <View margin="12 0 0 0">
            <Text
              category="p2"
              i18nKey={'repaymentString.onlineCard2_5'}
              style={{
                color: 'text-color-600',
              }}
            />
          </View>
          <View margin="24 0 0 0">
            <Text
              category="p1"
              i18nKey={'repaymentString.onlineCard2_6'}
              style={{
                color: 'text-color-800',
              }}
            />
          </View>
        </View>
      </ScrollView>
    </Layout>
  );
};
