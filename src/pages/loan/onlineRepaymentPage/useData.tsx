import { HitPointEnumsSpace, UserEnumsSpace } from '@/enums';
import { useOnInit } from '@/hooks';
import { BaseInfoManager } from '@/managers';
import { Toast } from '@/nativeComponents';
import { fetchOnlineRepayMethodDetailAllChannel } from '@/server';
import { trackCommonEvent } from '@/trackEvent';
import { OrderVOSpace, ScreenProps } from '@/types';
import Clipboard from '@react-native-clipboard/clipboard';
import { useCallback, useState } from 'react';
import { useNameSpace } from '../../../i18n';
import { removeAllSpaces } from '@/utils';

interface IProps {}
export default function useData(props: IProps) {
  const t = useNameSpace().t;

  /** 页面初始化 */
  const { loading, refreshing, setRefreshing, onRefresh } = useOnInit({
    callback: async () => {
      BaseInfoManager.changeLoadingModalVisible(true);
      loading.current = true;
      await getOnlieRepayDetail();
      loading.current = false;
      BaseInfoManager.changeLoadingModalVisible(false);
    },
    refreshCallback: async () => {
      setRefreshing(true);
      loading.current = true;
      await getOnlieRepayDetail();
      loading.current = false;
      setRefreshing(false);
    },
    buryCallback: async () => {
      trackCommonEvent(
        {
          p: HitPointEnumsSpace.EPageKey.P_ONLINE,
          e: HitPointEnumsSpace.EEventKey.IN_PAGE,
        },
        '1',
      );
    },
    buryBlurCallback: () => {
      trackCommonEvent(
        {
          p: HitPointEnumsSpace.EPageKey.P_ONLINE,
          e: HitPointEnumsSpace.EEventKey.OUT_PAGE,
        },
        '1',
      );
    },
  });

  const [onlineRepayDetail, setOnlieRepayDetail] = useState<OrderVOSpace.RepayOnlineDataType>({
    /**账号*/
    clabe: '*',
    /**待还金额*/
    totalAmount: '*',
    /**服务费*/
    repayFee: '*',
    /**服务费vat*/
    repayFeeVat: '*',
    /** 不同渠道的文案说明 **/
    channelTips: '*',
  });

  const getOnlineRepayDetailAllChannel = useCallback(async () => {
    const { code, data } = await fetchOnlineRepayMethodDetailAllChannel();
    if (code === 0) {
      setOnlieRepayDetail((prevState: OrderVOSpace.RepayOnlineDataType) => ({
        ...prevState,
        clabe: data?.clabe || '*',
        totalAmount: data?.totalAmount || '*',
        repayFee: data?.repayFee || '*',
        repayFeeVat: data?.repayFeeVat || '*',
        channelTips: data?.channelTips || '*',
      }));
    }
  }, []);

  const getOnlieRepayDetail = useCallback(async () => {
    await getOnlineRepayDetailAllChannel();
  }, []);

  /** 复制 clabe 号 */
  const onCopyClabe = useCallback(() => {
    const clabe = removeAllSpaces(onlineRepayDetail.clabe);
    Clipboard.setString(clabe);
    Toast(t('messageString.copy_success'), 'SHORT');
  }, [onlineRepayDetail.clabe]);

  return { onlineRepayDetail, onCopyClabe, onRefresh, refreshing };
}
