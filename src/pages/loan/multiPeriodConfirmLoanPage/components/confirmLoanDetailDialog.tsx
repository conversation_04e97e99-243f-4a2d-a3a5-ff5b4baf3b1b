import { Image, CommonModal, Text, View, DashedLine, Divider } from '@/components';
import { useNameSpace } from '@/i18n';
import { OrderVOSpace } from '@/types';
import React, { useCallback, useMemo } from 'react';

interface IProps {
  changeDialogVisibe: (visible: boolean) => void;
  visible: boolean;
  loanCreditCalculationData: OrderVOSpace.MultiPeriodCalculateConfirmLoanType;
}

export default React.memo((props: IProps) => {
  const { visible, loanCreditCalculationData, changeDialogVisibe } = props;

  const handleClose = useCallback(() => {
    changeDialogVisibe(false);
  }, [visible]);

  const t = useNameSpace().t;

  const $comfirmLoanDetailInfo = useMemo(() => {
    const { loanPlan } = loanCreditCalculationData;
    return (
      <View margin="24 16 0 16">
        <Text
          margin="0 10 0 10"
          style={{ color: 'text-color-600' }}
          category="h3"
          isCenter={true}
          i18nKey={'multiPeriodString.loanPlan'}
        />
        <Divider margin="8 0 0 0" />
        <View
          padding="12 8 12 8"
          height={100 * (loanPlan.length - 1) - (loanPlan.length - 3) * 20}
          layoutStrategy="flexRowStartCenter">
          <View height={'100%'} layoutStrategy="flexRowStartCenter">
            <View height={'100%'} layoutStrategy="flexColumnStartCenter">
              {loanPlan.map((item, index) => {
                if (index === 0) {
                  return (
                    <Text
                      margin="6 0 0 0"
                      key={index.toString()}
                      category="c1"
                      textContent={item.curPeriodRepayDate}
                      style={{ color: 'text-color-600' }}
                    />
                  );
                } else {
                  return (
                    <Text
                      key={index.toString()}
                      category="c1"
                      margin="50 0 0 0"
                      textContent={item.curPeriodRepayDate}
                      style={{ color: 'text-color-600' }}
                    />
                  );
                }
              })}
            </View>
            {/* 中心圆点 */}
            <View height={'100%'} margin="0 12 0 12" layoutStrategy="flexColumnStartCenter">
              {loanPlan.map((item, index) => {
                if (index === 0) {
                  return (
                    <View
                      margin="6 0 0 0"
                      key={index.toString()}
                      style={{
                        width: 12,
                        height: 12,
                        borderRadius: 99,
                        backgroundColor: 'primary-color-500',
                      }}
                    />
                  );
                } else {
                  return (
                    <View layoutStrategy="flexColumnCenterCenter" key={index.toString()}>
                      <DashedLine
                        axis="vertical"
                        width={0.5}
                        height={
                          (100 * (loanPlan.length - 1) -
                            (loanPlan.length - 3) * 20 -
                            (loanPlan.length - 1) * 12 +
                            12) /
                            loanPlan.length -
                          1
                        }
                      />
                      <View
                        margin={index === loanPlan.length - 1 ? '0 0 6 0' : '0 0 0 0'}
                        style={{
                          width: 12,
                          height: 12,
                          borderRadius: 99,
                          backgroundColor: 'primary-color-500',
                        }}
                      />
                    </View>
                  );
                }
              })}
            </View>
          </View>

          {/* 金额和税费 */}
          <View height={'100%'} layoutStrategy="flexColumnBetweenStart">
            {loanPlan.map((item, index) => {
              if (index === 0) {
                return (
                  <View
                    // margin={index === 0 ? '0 0 12 0' : '12 0 12 0'}
                    layoutStrategy="flexColumnStart"
                    key={index.toString()}>
                    <Text
                      width={180}
                      numberOfLines={2}
                      category="c2"
                      textContent={
                        t('multiPeriodString.amount') +
                        item.curPeriodPrincipal +
                        ' + ' +
                        t('multiPeriodString.serverFee') +
                        item.curPeriodProcessFee +
                        ' + ' +
                        t('multiPeriodString.serverIVA') +
                        item.curPeriodProcessFeeVat
                      }
                      style={{ color: 'text-color-600' }}
                    />
                  </View>
                );
              } else {
                return (
                  <View layoutStrategy="flexColumnStart" key={index.toString()}>
                    <Text
                      category="c2"
                      width={180}
                      numberOfLines={2}
                      textContent={
                        t('multiPeriodString.amount') +
                        item.curPeriodPrincipal +
                        ' + ' +
                        t('multiPeriodString.serverFee') +
                        item.curPeriodProcessFee +
                        ' + ' +
                        t('multiPeriodString.serverIVA') +
                        item.curPeriodProcessFeeVat
                      }
                      style={{ color: 'text-color-600' }}
                    />
                  </View>
                );
              }
            })}
          </View>
        </View>
      </View>
    );
  }, [props.loanCreditCalculationData]);
  return (
    <CommonModal
      visible={visible}
      onBackdropPress={handleClose}
      hasLinearGradient={false}
      confirmBtnName="btnString.OK"
      confirmCallback={handleClose}>
      {$comfirmLoanDetailInfo}
    </CommonModal>
  );
});
