import { Button, CountDown, CountDownRefType, Image, View } from '@/components';
import { OrderVOSpace } from '@/types';
import { TouchableOpacity } from 'react-native';
import { Text } from '@/components';
import { useCallback } from 'react';
import { modalDataStoreInstance, ModalList } from '@/managers';
import { nav, TrackEvent } from '@/utils';
import { HitPointEnumsSpace } from '@/enums';
import { RouterConfig } from '@/routes';

interface INextButtonProps {
  handleNext: () => void;
  loanCreditCalculationData: OrderVOSpace.MultiPeriodCalculateConfirmLoanType;
  countdownRef: React.RefObject<CountDownRefType>;
}

/** 下一步按钮 */
const NextButton = (props: INextButtonProps) => {
  const {
    handleNext,
    loanCreditCalculationData: { countdownFlag, countdown },
    countdownRef,
  } = props;

  const onHandleReject = useCallback(() => {
    TrackEvent.trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_INSTALLMENT_CREDIT_TBC,
        e: HitPointEnumsSpace.EEventKey.BTN_CREDIT_REFUSE,
      },
      '1',
    );
    modalDataStoreInstance.openModal({
      key: ModalList.INFO_PROMPT_CONFIRM,
      buttonType: 'vertical-special-1',
      isBottomIconColse: true,
      // titleKey: 'loanConfirmString.refuse_title',
      imageKey: '_modalRefuseIcon',
      i18nKey: 'loanConfirmString.refuse_credit_tip',
      confirmBtnName: 'btnString.acceptMyLoan',
      isBackdropClose: true,
      cancelBtnCallback: () => {
        TrackEvent.trackCommonEvent(
          {
            p: HitPointEnumsSpace.EPageKey.P_INSTALLMENT_CREDIT_TBC,
            e: HitPointEnumsSpace.EEventKey.BTN_REMIND_CHOOSE,
          },
          '0',
        );
        // todo 跳转到拒绝用信文案提交页面
        nav.navigate(RouterConfig.CREDIT_REFUSE_QUESTION as any);
      },
      cancelBtnName: 'btnString.rejectMyLoan',
      confirmBtnCallback: () => {
        // 触发埋点直接关闭弹窗
        TrackEvent.trackCommonEvent(
          {
            p: HitPointEnumsSpace.EPageKey.P_INSTALLMENT_CREDIT_TBC,
            e: HitPointEnumsSpace.EEventKey.BTN_REMIND_CHOOSE,
          },
          '1',
        );
      },
    });
  }, []);

  return (
    <View
      padding="0 0 24 0"
      style={{
        backgroundColor: 'background-color-0',
      }}
      cardType="baseType">
      <View
        margin="12 16 0 16"
        padding="8 12 8 12"
        style={{ borderRadius: 8, backgroundColor: 'secondary-color-100' }}
        layoutStrategy="flexRowStartCenter">
        <Image margin="0 8 0 0" name="_sound" style={{ tintColor: 'text-color-700' }} />
        <Text style={{ flex: 1 }} category="c2" i18nKey="multiPeriodString.countDownTips" />
      </View>
      <CountDown countdownFlag={countdownFlag === 'YES'} ref={countdownRef} time={countdown} />
      <Button
        margin="16 16 16 16"
        onPress={handleNext}
        status="primary"
        textI18nKey="btnString.acceptNow"
      />
    </View>
  );
};

export default NextButton;
