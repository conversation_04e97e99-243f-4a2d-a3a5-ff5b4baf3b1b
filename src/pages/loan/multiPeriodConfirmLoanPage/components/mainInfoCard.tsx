/* eslint-disable react-native/no-inline-styles */
import React, { useCallback } from 'react';
import { Image, Text, View } from '@/components';
import { OrderVOSpace } from '@/types';
import { TouchableWithoutFeedback } from 'react-native';

interface IMainInfoCardProps {
  loanCreditCalculationData: OrderVOSpace.MultiPeriodCalculateConfirmLoanType;
  changeConfirmLoanDialog: (visible: boolean) => void;
}
/** 主要信息展示卡片 */
const MainInfoCard = (props: IMainInfoCardProps) => {
  const { loanCreditCalculationData, changeConfirmLoanDialog } = props;
  const {
    loanAmount,
    loanDate,
    // realAmount,
    loanPlan,
    days,
    loanPeriods,
    // expiryDateNewFormat,
    // repaymentAmount,
    realRepaymentAmount,
    firstRepayDate,
    // enableWithhold,
    // upAmountForWithhold,
  } = loanCreditCalculationData;

  const openConfirmLoanDialog = useCallback(() => {
    changeConfirmLoanDialog(true);
  }, []);

  return (
    <View margin="24 0 0 0" padding="12 8 12 8" cardType="baseType">
      <Text
        margin="0 10 0 10"
        style={{ color: 'text-color-600' }}
        category="p1"
        i18nKey={'loanConfirmString.loan_amount'}
      />
      <View margin={'12 0 0 0'} layoutStrategy="flexRowStartCenter">
        <Text
          margin="0 10 0 10"
          category="h1"
          bold="bold"
          textContent={String(loanAmount).toFormatFinance()}
          style={{ color: 'text-color-800' }}
        />
        <Image margin="0 8 0 12" name="_coin" />
      </View>
      <View
        padding="0 0 12 0"
        margin="12 10 0 10"
        layoutStrategy="flexRowBetweenCenter"
        style={{
          borderBottomWidth: 1,
          borderBottomColor: 'line-color-200',
          borderStyle: 'dashed',
        }}>
        <Text
          style={{ color: 'text-color-600' }}
          category="p1"
          i18nKey={'multiPeriodString.days'}
        />
        <Text
          style={{ color: 'text-color-800' }}
          category="p1"
          textContent={String(days).toFormatMonth()}
        />
      </View>
      <View margin="12 10 0 10" layoutStrategy="flexRowBetweenCenter">
        <Text
          style={{ color: 'text-color-600' }}
          category="p1"
          i18nKey={'multiPeriodString.period'}
        />
        <View layoutStrategy="flexRowStartCenter">
          <Text
            style={{ color: 'text-color-800' }}
            category="p1"
            textContent={String(loanPeriods)}
          />
        </View>
      </View>
      <View margin="12 10 0 10" layoutStrategy="flexRowBetweenCenter">
        <Text
          style={{ color: 'text-color-600' }}
          category="p1"
          i18nKey={'multiPeriodString.currentAmount'}
        />
        <Text
          style={{ color: 'text-color-800' }}
          category="p1"
          textContent={'MXN' + String(loanPlan[0]?.curPeriodRepaymentAmount).toFormatFinance(false)}
        />
      </View>
      <View margin="12 10 0 10" layoutStrategy="flexRowBetweenCenter">
        <Text
          style={{ color: 'text-color-600' }}
          category="p1"
          i18nKey={'multiPeriodString.loanDate'}
        />
        <Text style={{ color: 'text-color-800' }} category="p1" textContent={loanDate} />
      </View>

      <View margin="12 10 0 10" layoutStrategy="flexRowBetweenCenter">
        <Text
          style={{ color: 'text-color-600' }}
          category="p1"
          i18nKey={'multiPeriodString.currentRepayDate'}
        />
        <TouchableWithoutFeedback onPress={openConfirmLoanDialog}>
          <View layoutStrategy="flexRowBetweenCenter">
            <Text textContent={String(firstRepayDate)} />
            <Image name="_detailIcon" margin="0 0 0 8" />
          </View>
        </TouchableWithoutFeedback>
      </View>
      {/* <View margin="12 10 0 10" layoutStrategy="flexRowBetweenCenter">
        <Text
          style={{color: 'text-color-600'}}
          category="p1"
          i18nKey={'multiPeriodString.loanPlan'}
        />
        <TouchableWithoutFeedback onPress={openConfirmLoanDialog}>
          <View layoutStrategy="flexRowBetweenCenter">
            <View layoutStrategy="flexColumnCenterCenter">
              <Text
                textContent={
                  'MXN' + String(realRepaymentAmount).toFormatFinance()
                }
              />
              <Text textContent={String(firstRepayDate)} />
            </View>
            <Image name="_detailIcon" margin="0 0 0 8" />
          </View>
        </TouchableWithoutFeedback>
      </View> */}
    </View>
  );
};

export default MainInfoCard;
