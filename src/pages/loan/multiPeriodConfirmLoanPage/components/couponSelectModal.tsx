/** 贷款详情弹窗 */

import { ActionSheet, Button, Image, Text, View } from '@/components';
import { HitPointEnumsSpace } from '@/enums';
import { OrderVOSpace, UserVOSpace } from '@/types';
import { TrackEvent } from '@/utils';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import {
  Dimensions,
  FlatList,
  TouchableNativeFeedback,
  TouchableWithoutFeedback,
} from 'react-native';

interface IProps {
  selectedIndex?: number;
  onCancel: () => void;
  onConfrim: (item?: UserVOSpace.CouponsItem, index?: number) => void;
  couponList: UserVOSpace.CouponsItem[];
  availableCouponList: UserVOSpace.CouponsItem[];
  loanCreditData: OrderVOSpace.LoanDetailDataType;
  visible: boolean;
}

export default (props: IProps) => {
  const { visible, onCancel, onConfrim, couponList, availableCouponList, selectedIndex } = props;

  const [selectIndex, setSelectIndex] = useState<number | undefined>(selectedIndex);

  useEffect(() => {
    setSelectIndex(selectedIndex);
  }, [selectedIndex]);

  const onConfrimHandle = useCallback(() => {
    let coupon = undefined;
    if (selectIndex !== undefined) {
      coupon = availableCouponList[selectIndex];
      const { serialNumber } = coupon;
      TrackEvent.trackCommonEvent(
        {
          p: HitPointEnumsSpace.EPageKey.P_INSTALLMENT_CREDIT_TBC,
          e: HitPointEnumsSpace.EEventKey.BTN_USE_DISCOUNT_COUPON,
        },
        serialNumber,
      );
    }
    onConfrim && onConfrim(coupon, selectIndex);
    onCancel && onCancel();
  }, [selectIndex, availableCouponList, onConfrim, onCancel]);

  const onSeletIndex = useCallback(
    (index: number) => {
      if (index !== selectIndex) {
        const { serialNumber, type } = availableCouponList[index];
        TrackEvent.trackCommonEvent(
          {
            p: HitPointEnumsSpace.EPageKey.P_INSTALLMENT_CREDIT_TBC,
            e: HitPointEnumsSpace.EEventKey.BTN_CHOOSE_DISCOUNT_COUPON,
          },
          serialNumber,
        );
        setSelectIndex(index);
      } else {
        setSelectIndex(undefined);
      }
    },
    [setSelectIndex, selectIndex, availableCouponList],
  );

  const renderItem = ({ item, index }: { item: UserVOSpace.CouponsItem; index: number }) => {
    const { amount, serialNumber, expirationDate } = item;
    const selected = index === selectIndex;
    return (
      <TouchableWithoutFeedback onPress={() => onSeletIndex(index)} key={serialNumber}>
        <View
          padding="12 16 12 16"
          margin="0 16 0 16"
          style={{
            borderRadius: 8,
            backgroundColor: 'background-color-0',
          }}>
          <View layoutStrategy="flexRowBetweenCenter">
            <View layoutStrategy="flexRowStartCenter">
              <Text>
                <Text
                  style={{
                    color: 'secondary-color-500',
                  }}
                  category="p1"
                  textContent="$"
                />
                <Text
                  category="h2"
                  margin="0 0 0 6"
                  style={{
                    color: 'secondary-color-500',
                  }}
                  bold="bold"
                  textContent={String(Math.floor(Number(amount)))}
                />
              </Text>
              <View margin="12 0 12 12">
                <Text margin="0 0 6 0" category="p2" i18nKey="couponString.discountCouponTitle" />
                <Text category="c1" i18nKey="couponString.couponDate">
                  {expirationDate}
                </Text>
              </View>
            </View>
            <Image name={selected ? '_radioChecked' : '_radioUnchecked'} />
          </View>
          <View
            style={{
              borderColor: 'text-color-600',
              borderStyle: 'dotted',
              borderTopWidth: 1,
            }}>
            <Text
              margin="6 0 0 0"
              category="c2"
              i18nKey="couponString.discountCouponTip"
              style={{ color: 'text-color-600' }}
            />
          </View>
        </View>
      </TouchableWithoutFeedback>
    );
  };

  const $renderFooter = useMemo(() => {
    let textI18nKey = '';
    if (couponList.length === 0) {
      textI18nKey = 'couponString.noHasCoupones';
    } else if (availableCouponList.length === 0) {
      textI18nKey = 'couponString.noHasAvailableCoupones';
    }
    return textI18nKey === '' ? null : (
      <View
        margin="0 16 24 16"
        padding="6 12 6 12"
        layoutStrategy="flexRowStart"
        style={{
          borderRadius: 8,
          backgroundColor: 'background-color-0',
        }}>
        <Image margin="0 12 0 0" name={'_infoIconYellow'} style={{ tintColor: 'fill-color-500' }} />
        <Text style={{ flex: 1 }} category="p2" i18nKey={textI18nKey} />
      </View>
    );
  }, [availableCouponList, couponList]);

  const $renderHeader = useMemo(() => {
    if (availableCouponList.length !== 0) {
      return (
        <View
          margin="0 16 24 16"
          padding="6 12 6 12"
          layoutStrategy="flexRowStart"
          style={{
            borderRadius: 8,
            backgroundColor: 'background-color-0',
          }}>
          <Image
            margin="0 12 0 0"
            name={'_infoIconYellow'}
            style={{ tintColor: 'fill-color-500' }}
          />
          <Text style={{ flex: 1 }} category="p2" i18nKey="couponString.couponUseTips" />
        </View>
      );
    }
    return null;
  }, [availableCouponList]);

  const [height, setHeight] = useState(
    (Dimensions.get('window').height * 3) / 4 > 700
      ? 700
      : (Dimensions.get('window').height * 3) / 4,
  );

  return (
    <ActionSheet visible={visible} onClose={onCancel}>
      <View
        onLayout={e => {
          // setHeight(e.nativeEvent.layout.height);
        }}
        height={height}
        style={{
          borderTopLeftRadius: 12,
          borderTopRightRadius: 12,
          width: Dimensions.get('window').width,
          backgroundColor: 'background-color-100',
        }}>
        <View padding="16 16 16 16" layoutStrategy="flexRowBetweenCenter">
          <Text i18nKey="couponString.coupon" />
          <TouchableNativeFeedback onPress={onCancel}>
            <Image name="_modalCloseIcon" />
          </TouchableNativeFeedback>
        </View>
        <View style={{ flex: 1, backgroundColor: 'background-color-100' }}>
          <FlatList
            ItemSeparatorComponent={() => <View height={10} />}
            style={{ height: '100%', marginVertical: 10 }}
            renderItem={renderItem}
            ListHeaderComponent={$renderHeader}
            ListFooterComponent={$renderFooter}
            data={availableCouponList}
          />
        </View>
        <View width={'100%'} padding="16 32 24 32" layoutStrategy="flexColumnCenterCenter">
          <Button
            width={'100%'}
            status="primary"
            textI18nKey="btnString.OK"
            onPress={onConfrimHandle}
          />
        </View>
      </View>
    </ActionSheet>
  );
};
