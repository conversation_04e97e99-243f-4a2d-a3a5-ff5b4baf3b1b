import { HitPointEnumsSpace, UserEnumsSpace } from '@/enums';
import { useFaceLive, useGetUserStateAndNextRouterOrOtherCallBack } from '@/hooks';
import { BaseInfoManager, UserInfoManager } from '@/managers';
import { Toast } from '@/nativeComponents';
import {
  faceVerify,
  fetchGetBizToken,
  fetchUploadImage,
  faceSkip,
  fetchReloanGetBizToken,
  reloanFaceVerify,
  EvidenceResponseSpace,
} from '@/server';
import { FileUtils, TrackEvent, log } from '@/utils';
import { useCallback, useRef } from 'react';
import { useNameSpace } from '../../../i18n';
import { EFaceLiveEventType, IFaceEventData } from '../../../native/module/faceLive';

export default function useData(faceVerifyCallback: () => void) {
  const pointParam = useRef<TrackEvent.IEventPoint>({
    p: HitPointEnumsSpace.EPageKey.P_LIVE,
    e: HitPointEnumsSpace.EEventKey.E_LIVE_ACTION,
  });

  const t = useNameSpace('loanConfirmString').t;
  const bizToken = useRef('');

  const [getUserStateAndNextRouterOrOtherCallBack] = useGetUserStateAndNextRouterOrOtherCallBack();

  const faceLiveExamineResultCallback = useCallback(async (event: IFaceEventData) => {
    log.debug('# faceLiveExamineResultCallback', event);
    const { eventType, faceLivenessFilePath, megliveData } = event;
    switch (eventType) {
      case EFaceLiveEventType.FACE_LIVE_PRE_START:
        break;
      case EFaceLiveEventType.FACE_LIVE_PRE_DETECT:
        break;
      case EFaceLiveEventType.FACE_LIVE_PRE_FINISH:
        break;
      case EFaceLiveEventType.FACE_LIVE_START_DETECT:
        BaseInfoManager.changeLoadingModalVisible(false);
        break;
      // 关闭loading
      case EFaceLiveEventType.FACE_LIVE_START_DETECT_STATE_SUCCESS:
        // 活体成功
        onStartLiveCompare(faceLivenessFilePath, megliveData);
        break;
      case EFaceLiveEventType.FACE_LIVE_START_DETECT_STATE_FAIL:
        // 关闭loading
        Toast(`${t('face_failed_pls_retry')}`);
        await TrackEvent.uploadEventLog();
        await getUserStateAndNextRouterOrOtherCallBack(false, faceVerifyCallback);
        BaseInfoManager.changeLoadingModalVisible(false);
        break;
      case EFaceLiveEventType.FACE_LIVE_PRE_DETECT_STATE_FAIL:
      case EFaceLiveEventType.FACE_LIVE_PRE_FINISH_STATE_FAIL:
      case EFaceLiveEventType.FACE_LIVE_START_DETECT_STATE_ERROR:
        // 客户端调用faceid sdk异常, 直接跳过人脸
        onSkipLive(UserEnumsSpace.ESkipLiveScene.CLIENT_CALL_FACEID_ERROR);
        break;
    }
  }, []);

  const [startFaceLiveVerification] = useFaceLive(
    pointParam.current,
    faceLiveExamineResultCallback,
  );

  /** 开始活体检测 */
  const onStartLiveExamine = async () => {
    // 启用loading
    // 1、先获取biztoken
    BaseInfoManager.changeLoadingModalVisible(true);
    let resp: EvidenceResponseSpace.ResponseFaceBizTokenData;
    if (UserInfoManager.context.userModel.isUserTypeOld) {
      resp = await fetchReloanGetBizToken({ get_liveness_video: 1 });
    } else {
      resp = await fetchGetBizToken({ get_liveness_video: 1 });
    }
    BaseInfoManager.changeLoadingModalVisible(false);
    if (resp.code === 0) {
      const { biz_token, skippedLive } = resp.data;

      if (skippedLive?.includes('YES')) {
        // 服务端调用第三方失败
        await TrackEvent.uploadEventLog();
        await getUserStateAndNextRouterOrOtherCallBack();
        return true;
      }

      if (biz_token) {
        bizToken.current = biz_token;
        // 2、调用faceLive的方法
        startFaceLiveVerification(biz_token);
      } else {
        // 客户端获取token异常
        onSkipLive(UserEnumsSpace.ESkipLiveScene.CLIENT_GET_TOKEN_ERROR);
      }
    } else {
      // 客户端获取token异常
      onSkipLive(UserEnumsSpace.ESkipLiveScene.CLIENT_GET_TOKEN_ERROR);
    }
  };

  const onStartLiveCompare = async (
    faceLivenessFilePath: string = '',
    megliveData: string = '',
  ) => {
    if (megliveData !== '') {
      const faceVerifyParam = {
        bizToken: bizToken.current,
        megliveData: megliveData,
      };
      BaseInfoManager.changeLoadingModalVisible(true);
      let faceVerifyResp: EvidenceResponseSpace.ResponseFaceVerifyData;
      if (UserInfoManager.context.userModel.isUserTypeOld) {
        faceVerifyResp = await reloanFaceVerify(faceVerifyParam);
      } else {
        faceVerifyResp = await faceVerify(faceVerifyParam);
      }
      // BaseInfoManager.changeLoadingModalVisible(false);
      if (faceVerifyResp.code === 0) {
        const { passFlag, skippedLive } = faceVerifyResp.data;

        if (skippedLive?.includes('YES')) {
          // 服务端调用第三方失败
          await TrackEvent.uploadEventLog();
          await getUserStateAndNextRouterOrOtherCallBack();
          BaseInfoManager.changeLoadingModalVisible(false);
          return true;
        }

        if (passFlag?.includes('YES')) {
          // 活体验证通过直接下一步
        }

        const absoluteFilePath = FileUtils.getPlatformPathDir(faceLivenessFilePath);
        if (faceLivenessFilePath !== '') {
          // 上传原始的活体加密文件
          log.info(`上传活体加密文件 ${absoluteFilePath}`);
          const uploadResp = await fetchUploadImage(absoluteFilePath, {
            cardType: 'original_face',
            fileType: 'megvii',
            bizType: UserInfoManager.context.userModel.isUserTypeOld
              ? UserEnumsSpace.BizType.RELOAN
              : UserEnumsSpace.BizType.FIRST_LOAN_VERIFY,
          });
          if (uploadResp.code === 0) {
            log.info('上传活体加密文件成功');
          }
        }
        await TrackEvent.uploadEventLog();
        await getUserStateAndNextRouterOrOtherCallBack(true, faceVerifyCallback);
        BaseInfoManager.changeLoadingModalVisible(false);
      } else {
        // 客户端调用服务端失败
        onSkipLive(UserEnumsSpace.ESkipLiveScene.CLIENT_VERIFY_ERROR);
      }
    } else {
      // 客户端调用服务端失败
      onSkipLive(UserEnumsSpace.ESkipLiveScene.CLIENT_VERIFY_ERROR);
    }
  };

  const onSkipLive = async (skipStatus: UserEnumsSpace.ESkipLiveScene) => {
    BaseInfoManager.changeLoadingModalVisible(true);
    const faceSkipResp = await faceSkip({
      skipStatus,
      applyOrderId: UserInfoManager.context.userModel.applyOrderId,
    });

    if (faceSkipResp.code === 0) {
      await TrackEvent.uploadEventLog();
      BaseInfoManager.changeLoadingModalVisible(false);
      Toast(`${t('peso_face_verify_err_and_skip_live_tip')}`);
      await getUserStateAndNextRouterOrOtherCallBack();
    } else {
      await TrackEvent.uploadEventLog();
      BaseInfoManager.changeLoadingModalVisible(false);
      await getUserStateAndNextRouterOrOtherCallBack();
    }
  };

  return [onStartLiveExamine];
}
