import { CountDownRefType } from '@/components';
import { BaseEnumsSpace, HitPointEnumsSpace, UserEnumsSpace } from '@/enums';
import {
  useCheckAllPermissionAndRequestPermission,
  useCheckCameraPermissionAndRequestPermission,
  useGetUserStateAndNextRouterOrOtherCallBack,
  useOnInit,
} from '@/hooks';
import { BaseInfoManager, UserInfoManager } from '@/managers';
import { RouterConfig } from '@/routes';
import {
  fetchApplyContract,
  fetchMultiPeriodLoanApplyCalculate,
  fetchWithholdContract,
  multiPeriodConfirmApplyConfirm,
} from '@/server';
import { OrderVOSpace } from '@/types';
import { TrackEvent, nav } from '@/utils';
import CryptoJS from 'crypto-js';
import _ from 'lodash';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { AdjustTools } from '../../../business/_export_';
import useFaceLiveVerify from './useFaceLiveVerify';

export default function useData() {
  const [agreeLoanContractBool, setAgreeLoanContractBool] = useState<boolean>(true);
  const countdownRef = useRef<CountDownRefType>(null);

  /** 分期场景确认用信贷款试算 */
  const [multiLoanCreditCalculationData, setMultiLoanCreditCalculation] =
    useState<OrderVOSpace.MultiPeriodCalculateConfirmLoanType>({
      countdownFlag: 'NO',
      countdown: 0,
      loanDate: '', //放款日期
      loanAmount: '', // 放款总金额
      realRepaymentAmount: '', // 实际应还总金额
      days: 0, // 总天数
      loanPeriods: 0,
      firstRepayDate: '',
      firstRepaymentAmount: '',
      loanPlan: [],
      loanTips: '',
      cardNo: '',
      bankName: '',
      showWithholdBtn: 'NO',
    });

  // 按钮禁用状态
  const [inputEnterDisabled, setInputEnterDisabled] = useState<boolean>(false);

  /** 初始化方法 */
  const { refreshing, setRefreshing, loading, onRefresh } = useOnInit({
    callback: async () => {
      loading.current = true;
      BaseInfoManager.changeLoadingModalVisible(true);
      // await getRepayCouponList();
      await getLoanCreditCalculation();
      faceLiveVerify();
      BaseInfoManager.changeLoadingModalVisible(false);
      loading.current = false;
    },
    refreshCallback: async () => {
      setRefreshing(true);
      loading.current = true;
      // await getRepayCouponList();
      await getLoanCreditCalculation();
      await getUserStateAndNextRouterOrOtherCallBack(true, faceLiveVerify);
      loading.current = false;
      setRefreshing(false);
    },
    isBackAutoRefresh: true,
    pageKey: HitPointEnumsSpace.EPageKey.P_INSTALLMENT_CREDIT_TBC,
  });

  /** 是否开通自动代扣 */
  const [autoWithhold, setAutoWithhold] = useState<boolean>(
    BaseInfoManager.context.baseModel.autoWithholdFirstLoanContractPageDefaultValue,
  );

  const onOpenAutoWithhold = useCallback(() => {
    TrackEvent.trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_INSTALLMENT_CREDIT_TBC,
        e: HitPointEnumsSpace.EEventKey.BTN_SELECT_AUTODEBIT,
      },
      '1',
    );
    setAutoWithhold(true);
    getLoanCreditCalculation();
  }, [autoWithhold, multiLoanCreditCalculationData]);

  const onCloseAutoWithhold = useCallback(() => {
    TrackEvent.trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_INSTALLMENT_CREDIT_TBC,
        e: HitPointEnumsSpace.EEventKey.BTN_SELECT_AUTODEBIT,
      },
      '0',
    );
    setAutoWithhold(false);
    getLoanCreditCalculation();
  }, [autoWithhold, multiLoanCreditCalculationData]);

  /** 跳转到自动代扣协议 */
  const openAutoWithholdContract = async () => {
    TrackEvent.trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_INSTALLMENT_CREDIT_TBC,
        e: HitPointEnumsSpace.EEventKey.BTN_NEGOTIATE_CHECK,
      },
      '1',
    );
    let result = await fetchWithholdContract({
      cardNo: multiLoanCreditCalculationData.cardNo,
      bankName: multiLoanCreditCalculationData.bankName,
    });
    if (result.code === BaseEnumsSpace.ENetworkStatus.SUCCESS) {
      nav.navigate(RouterConfig.AUTOMATIC_WITHHOLD_PROTOCOL as any, {
        html: CryptoJS.enc.Base64.parse(String(result.data)).toString(CryptoJS.enc.Utf8),
        currentRoute: RouterConfig.MULTI_PERIOD_COMFIRM_LOAN,
        acceptHandle: onOpenAutoWithhold,
        rejectHandle: onCloseAutoWithhold,
      });
    }

    return result?.code === BaseEnumsSpace.ENetworkStatus.SUCCESS;
  };

  // /** 活体提示弹窗 */
  const [faceLiveDialogVisible, setFaceLiveDialogVisible] = useState<boolean>(false);

  // /** 确认用信详情弹窗 */
  const [confirmLoanDetailDialogVisible, setConfirmLoanDetailDialogVisible] =
    useState<boolean>(false);

  /** 关闭活体验证提示弹窗 */
  const closeFaceLiveDialog = useCallback(() => {
    // 关闭活体
    TrackEvent.trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_INSTALLMENT_CREDIT_TBC,
        e: HitPointEnumsSpace.EEventKey.BTN_LIVE_VERIFY,
      },
      '0',
    );
    setFaceLiveDialogVisible(false);
  }, []);
  /** 开启活体验证提示弹窗 */
  const openFaceLiveDialog = useCallback(() => {
    // 开启活体
    TrackEvent.trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_INSTALLMENT_CREDIT_TBC,
        e: HitPointEnumsSpace.EEventKey.BTN_LIVE_VERIFY,
      },
      '1',
    );
    setFaceLiveDialogVisible(true);
  }, []);

  /** 需要同意贷款合同提示弹窗 */
  const [needAgreeContractDialogVisible, setNeedAgreeContractDialogVisible] =
    useState<boolean>(false);
  /** 开启同意贷款合同提示弹窗 */
  const closeNeedAgreeContractDialog = useCallback(() => {
    setNeedAgreeContractDialogVisible(false);
  }, []);
  /** 关闭贷款合同提示弹窗 */
  const openNeedAgreeContractDialogVisible = useCallback(() => {
    setNeedAgreeContractDialogVisible(true);
  }, []);
  /** 改变确认用信详情提示弹窗 */
  const changeConfirmDetialDialogVisible = useCallback(
    (visible: boolean) => {
      TrackEvent.trackCommonEvent(
        {
          p: HitPointEnumsSpace.EPageKey.P_INSTALLMENT_CREDIT_TBC,
          e: HitPointEnumsSpace.EEventKey.BTN_VIEW_REPAYMENT_PLAN,
        },
        visible ? '1' : '0',
      );
      setConfirmLoanDetailDialogVisible(visible);
    },
    [confirmLoanDetailDialogVisible],
  );
  /** 关闭确认用信详情提示弹窗 */

  const faceDialogConfirm = useCallback(
    _.debounce(async () => {
      TrackEvent.trackCommonEvent(
        {
          p: HitPointEnumsSpace.EPageKey.P_INSTALLMENT_CREDIT_TBC,
          e: HitPointEnumsSpace.EEventKey.BTN_LIVE_VERIFY,
        },
        '1',
      );
      if (await checkCameraPermission()) {
        // 判断活体功能是否异常
        if (UserInfoManager.context.userModel.isFaceSceneError) {
          nav.navigate(RouterConfig.MAINTAINER as any);
        } else {
          loading.current = true;
          // 在这里先获取appConfig 配置接口在接口中查看是采用哪种活体方式。
          await startLiveVerifyForConfig();
        }
      }
    }, 300),
    [],
  );

  /** 获取appConfig判断是采用哪种活体方式 */
  const startLiveVerifyForConfig = async () => {
    onFaceStartLiveExamine();
  };

  /** 用户做活体验证 */
  const faceLiveVerify = (status: boolean = true) => {
    // 判断当前的条件是不是需要做活体
    if (UserInfoManager.context.userModel.isCreditSuccessWaitFace && status) {
      setInputEnterDisabled(true);
      // 需要弹出做活体提示弹窗
      openFaceLiveDialog();
      return true;
    }
    loading.current = false;
    return false;
  };

  /** face++ 活体验证方法 */
  const onFaceStartLiveExamine = useFaceLiveVerify(faceLiveVerify)[0];

  const checkCameraPermission = useCheckCameraPermissionAndRequestPermission()[0];

  const [getUserStateAndNextRouterOrOtherCallBack] = useGetUserStateAndNextRouterOrOtherCallBack(
    RouterConfig.MULTI_PERIOD_COMFIRM_LOAN,
  );

  useEffect(() => {
    // adjust 首次进入确认用信场景打点
    AdjustTools.AdjustEventPointTools.trackEventOfCustReview();

    return () => {
      countdownRef.current?.clearCountDown();
    };
  }, []);

  /** 跳转到贷款合同 */
  const toLoanContractPage = async () => {
    TrackEvent.trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_INSTALLMENT_CREDIT_TBC,
        e: HitPointEnumsSpace.EEventKey.BTN_VIEW_LOAN_CONTRACT,
      },
      '1',
    );
    await openContract();
  };

  const getParams = () => ({
    // applyPeriods: multiLoanCreditCalculationData.loanPeriods,
    selectedAmount: String(multiLoanCreditCalculationData.loanAmount),
    selectedDays: String(multiLoanCreditCalculationData.days),
  });

  const openContract = async () => {
    let result = await fetchApplyContract(getParams());
    if (result.code === BaseEnumsSpace.ENetworkStatus.SUCCESS) {
      nav.navigate(RouterConfig.LOAN_CONTRACT as any, {
        html: CryptoJS.enc.Base64.parse(String(result.data)).toString(CryptoJS.enc.Utf8),
      });
    }
    return result?.code === BaseEnumsSpace.ENetworkStatus.SUCCESS;
  };

  const onCreateLoanApplyComfirmOrFaceLive = _.throttle(async () => {
    let faceLiveVerifyResult = faceLiveVerify();
    BaseInfoManager.changeLoadingModalVisible(true);
    if (UserInfoManager.context.userModel.isCreditWait) {
      // 如果需要确认用信先确认用信
      await onCreateLoanApplyComfirm();
    } else if (faceLiveVerifyResult) {
    } else {
      await getUserStateAndNextRouterOrOtherCallBack(true, faceLiveVerify);
    }
    BaseInfoManager.changeLoadingModalVisible(false);
    // 成功之后获取用户的状态，然后判断是不是需要让用户做活体
  }, 300);

  /** 用户确认用信 */
  const onCreateLoanApplyComfirm = async () => {
    // 提交申请贷款逻辑

    let withholdAuthorizeStatus =
      multiLoanCreditCalculationData.showWithholdBtn === 'YES' ? (autoWithhold ? 'YES' : 'NO') : '';

    const { code } = await multiPeriodConfirmApplyConfirm({
      cardNo: multiLoanCreditCalculationData.cardNo,
      withholdAuthorizeStatus,
    });

    if (code === 0) {
      setInputEnterDisabled(true);
      // adjust 用信打点
      AdjustTools.AdjustEventPointTools.trackEventOfAccept();
      await getUserStateAndNextRouterOrOtherCallBack(true, faceLiveVerify);
    }
    // getRepayCouponList();
  };

  /** 获取贷款计算信息 */
  const getLoanCreditCalculation = async (): Promise<string> => {
    const { data, code } = await fetchMultiPeriodLoanApplyCalculate({
      // ...getParams(),
    });
    if (code === 0) {
      setMultiLoanCreditCalculation(data);
      countdownRef.current?.startCountDown(data.countdown);
      return data.countdownFlag;
    }
    return '';
  };
  const checkAllPermission = useCheckAllPermissionAndRequestPermission()[0];

  const agreeLoanContractDialogConfirm = async () => {
    // 用户通过直接提交贷款订单
    setAgreeLoanContractBool(true);
    onCreateLoanApplyComfirmOrFaceLive();
  };

  /** 下一步 */
  const handleNext = async () => {
    // 确认用信按钮点击事件
    TrackEvent.trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_INSTALLMENT_CREDIT_TBC,
        e: HitPointEnumsSpace.EEventKey.BTN_CONFIRM_CREDIT,
      },
      '1',
    );
    // 先判断是否有所有权限
    if ((await checkAllPermission()) === 'agree') {
      // 在判断是否同意了合同
      if (agreeLoanContractBool) {
        // 提交用信
        onCreateLoanApplyComfirmOrFaceLive();
      } else {
        // 弹出合同弹窗让用户确认
        openNeedAgreeContractDialogVisible();
      }
    }
  };

  const onChangeAgreeLoanContractBool = useCallback(() => {
    if (!UserInfoManager.context.userModel.isCreditSuccessWaitFace) {
      setAgreeLoanContractBool(preState => {
        TrackEvent.trackCommonEvent(
          {
            p: HitPointEnumsSpace.EPageKey.P_INSTALLMENT_CREDIT_TBC,
            e: HitPointEnumsSpace.EEventKey.BTN_LOAN_CONTRACT_CHECK,
          },
          !preState ? '1' : '0',
        );
        return !preState;
      });
    }
  }, []);

  return {
    countdownRef,
    toLoanContractPage,
    refreshing,
    handleNext,
    onRefresh,
    // loanCreditCalculationData,
    multiLoanCreditCalculationData,
    agreeLoanContractBool,
    onChangeAgreeLoanContractBool,
    setAgreeLoanContractBool,
    faceLiveDialogVisible,
    needAgreeContractDialogVisible,
    faceDialogConfirm,
    closeFaceLiveDialog,
    closeNeedAgreeContractDialog,
    agreeLoanContractDialogConfirm,
    inputEnterDisabled,
    changeConfirmDetialDialogVisible,
    confirmLoanDetailDialogVisible,
    autoWithhold,
    openAutoWithholdContract,
    onOpenAutoWithhold,
    onCloseAutoWithhold,
  };
}
