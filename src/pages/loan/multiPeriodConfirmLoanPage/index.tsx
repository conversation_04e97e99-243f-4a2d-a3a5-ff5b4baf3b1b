/* eslint-disable react-native/no-inline-styles */

import { Image, Layout, Text, TopNavigation, View, BusinessUI } from '@/components';
import { ScreenProps } from '@/types';
import React, { ReactElement } from 'react';
import { Dimensions, RefreshControl, ScrollView } from 'react-native';
import ConfirmLoanDetailDialog from './components/confirmLoanDetailDialog';
import LoanContract from './components/loanContract';
import LoanContractConfirmDialog from './components/loanContractConfirmDialog';
import MainInfoCard from './components/mainInfoCard';
import NeedFaceDialog from './components/needFaceDialog';
import NextButton from './components/nextButton';
import useData from './useData';
import RenderHtml from 'react-native-render-html';

const { AutoWithholdCardAutoBind } = BusinessUI;

export default ({
  route,
  navigation,
}: ScreenProps<{ agreeLoanContract: boolean }>): ReactElement => {
  const {
    countdownRef,
    toLoanContractPage,
    onRefresh,
    handleNext,
    refreshing,
    multiLoanCreditCalculationData,
    agreeLoanContractBool,
    onChangeAgreeLoanContractBool,
    setAgreeLoanContractBool,
    needAgreeContractDialogVisible,
    closeNeedAgreeContractDialog,
    agreeLoanContractDialogConfirm,
    inputEnterDisabled,
    faceLiveDialogVisible,
    faceDialogConfirm,
    closeFaceLiveDialog,
    changeConfirmDetialDialogVisible,
    confirmLoanDetailDialogVisible,
    autoWithhold,
    openAutoWithholdContract,
    onOpenAutoWithhold,
    onCloseAutoWithhold,
  } = useData();

  React.useEffect(() => {
    if (route.params?.agreeLoanContract) {
      setAgreeLoanContractBool(true);
    }
  }, [route.params]);

  return (
    <>
      <Layout pLevel="0" level="1" topCompensateColor="primary-color-500">
        <TopNavigation
          showLogoAction={true}
          isShowVip
          showMessage
          type="primary"
          bottomLine={false}
        />
        <ScrollView
          keyboardShouldPersistTaps="always"
          overScrollMode={'never'}
          refreshControl={
            <RefreshControl colors={['#4C6EFF']} refreshing={refreshing} onRefresh={onRefresh} />
          }>
          <View
            style={{
              height: 180,
              backgroundColor: 'primary-color-500',
            }}></View>
          <View margin="-180 0 0 0">
            <View margin="0 0 16 0">
              <View margin="0 16 0 16">
                <MainInfoCard
                  loanCreditCalculationData={multiLoanCreditCalculationData}
                  changeConfirmLoanDialog={changeConfirmDetialDialogVisible}
                />
              </View>

              {multiLoanCreditCalculationData.showWithholdBtn === 'YES' && (
                <AutoWithholdCardAutoBind
                  cardNo={multiLoanCreditCalculationData.cardNo}
                  withholdState={autoWithhold}
                  openAutoWithholdContract={openAutoWithholdContract}
                  openWithholdAuthorization={onOpenAutoWithhold}
                  closeWithholdAuthorization={onCloseAutoWithhold}
                />
              )}

              <View margin="0 16 0 16">
                <LoanContract
                  inputEnterDisabled={inputEnterDisabled}
                  agreeLoanContractBool={agreeLoanContractBool}
                  onChangeAgreeLoanContractBool={onChangeAgreeLoanContractBool}
                  toLoanContractPage={toLoanContractPage}
                />
                <View margin="12 0 24 0" padding="12 8 12 8" cardType="baseType">
                  <View layoutStrategy="flexRowStartCenter">
                    <Image name="_blackNotice" />
                    <Text
                      margin="0 10 0 10"
                      style={{ color: 'text-color-600' }}
                      category="p1"
                      i18nKey={'loanConfirmString.loan_notice_title'}
                    />
                  </View>
                  <RenderHtml
                    contentWidth={Dimensions.get('window').width - 56}
                    source={{
                      html: multiLoanCreditCalculationData.loanTips || '',
                    }}
                  />
                </View>
              </View>
            </View>
          </View>
        </ScrollView>
        <NextButton
          handleNext={handleNext}
          loanCreditCalculationData={multiLoanCreditCalculationData}
          //@ts-ignore
          countdownRef={countdownRef}
        />
      </Layout>
      <NeedFaceDialog
        visible={faceLiveDialogVisible}
        onCancel={closeFaceLiveDialog}
        onConfrim={faceDialogConfirm}
      />
      <LoanContractConfirmDialog
        visible={needAgreeContractDialogVisible}
        onCancel={closeNeedAgreeContractDialog}
        onConfrim={agreeLoanContractDialogConfirm}
      />
      <ConfirmLoanDetailDialog
        visible={confirmLoanDetailDialogVisible}
        changeDialogVisibe={changeConfirmDetialDialogVisible}
        loanCreditCalculationData={multiLoanCreditCalculationData}
      />
    </>
  );
};
