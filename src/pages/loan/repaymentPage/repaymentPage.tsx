/* eslint-disable react-native/no-inline-styles */

import {
  Card,
  DashedLine,
  Image,
  Layout,
  Swiper,
  Text,
  TopNavigation,
  View,
  BusinessUI,
  Button,
  ImageBackground,
  LinearGradient,
  Divider,
  Layouts,
} from '@/components';

import { Strings, useNameSpace } from '@/i18n';
import { GpReviewModal } from '@/modals';
import React, { ReactElement, useMemo } from 'react';
import {
  Dimensions,
  Pressable,
  RefreshControl,
  ScrollView,
  TouchableOpacity,
  TouchableWithoutFeedback,
} from 'react-native';
import CouponHelpsModal from './components/couponHelpsModal';
// import CouponSelectModal from './components/couponSelectModal';
import RepayDetailInfoDialog from './components/repayDetailInfoDialog';
import ExtModal from './components/extModal';
import useData from './useData';
import { useVipFuncSwitch } from '@/hooks';
import { HitPointEnumsSpace } from '@/enums';
import { BaseInfoManager } from '@/managers';
import CountDown from './components/CountDown';
import AccountUpdateModal from './components/AccountUpdateModal';
import { Colors } from '@/themes';
import { EDirectDebitStatus, ERepaymentChannel } from '@/types';
import { useProcessColorStyles } from '@/utils';
import { Toast } from '@/nativeComponents';
import PayTypeItemView from './components/PayTypeItemView';
const { VipUpLevelTipCard } = BusinessUI;
import { CouponSelectCard, CouponSelectModal } from '../../components/coupon/index';

export default (): ReactElement => {
  const isVipFuncSwitch = useVipFuncSwitch();
  const {
    refreshing,
    onRefresh,
    repayDetailModalVisible,
    onChangeRepayDetailModalState,
    onlineRepayDetail,
    repayDetail,
    getRepayChannelConfigAndJumpOnline,
    getRepayChannelConfigAndJumpOffline,
    onCopyClabe,
    repayShowTipTextList,
    fetchOverdueRepaymentReduction,
    showOverdueRepaymentReducionTip,
    onOpenCouponHelp,
    onCloseCouponHelp,
    couponHelpModalVisible,
    extModalVisible,
    onOpenExtModal,
    onCloseExtModal,
    countdown,
    couponList,
    availableCouponList,
    onOpenCouponSelect,
    onCloseCouponSelect,
    couponSelectModalVisible,
    onSelectCoupont,
    handleGoVipRelus,
    repayMentAccountVisible,
    onCloseRepayMentAccount,
    jumpDirectDebit,
    couponSelected,
    onSelectCouponSubmit,
  } = useData();

  const t = useNameSpace().t;
  const colorStyles = useProcessColorStyles({
    TEXT_COLOR_700: Colors.TEXT_COLOR_700,
    PRIMARY_COLOR_500: Colors.PRIMARY_COLOR_500,
    DANGER_COLOR_500: Colors.DANGER_COLOR_500,
    TEXT_COLOR_0: Colors.TEXT_COLOR_0,
  });

  const $withholdTip = useMemo(() => {
    const { withholdCopywriting } = repayDetail;
    if (
      BaseInfoManager.context.baseModel.isWithholdSwitch &&
      withholdCopywriting &&
      withholdCopywriting.length > 0
    ) {
      return (
        <View
          margin="10 0 0 0"
          padding="6 8 6 8"
          style={{ backgroundColor: 'primary-color-100', borderRadius: 4 }}>
          {withholdCopywriting.map((tip, index) => {
            return (
              <Text key={index.toString()}>
                <Text padding="0 6 0 0" status="danger" textContent="* " />
                <Text textContent={tip} />
              </Text>
            );
          })}
        </View>
      );
    } else {
      return null;
    }
  }, [repayDetail]);

  /** 还款详情卡片 */
  const $repayDetailCard = useMemo(() => {
    const {
      remindRepayAmount,
      realRemindRepayAmount,
      repayDateNewFormat,
      overdueDay,
      withholding,
      withholdCopywriting,
    } = repayDetail;
    const isOverdue = repayDetail.overdueDay && Number(overdueDay) > 0;

    return (
      <View
        style={{
          backgroundColor: 'primary-color-900',
          borderRadius: 8,
        }}>
        <View
          style={{
            backgroundColor: 'primary-color-800',
            borderTopLeftRadius: 8,
            borderTopRightRadius: 8,
          }}
          layoutStrategy="flexRowBetweenCenter">
          <Swiper
            style={{
              borderTopLeftRadius: 8,
              borderTopRightRadius: 8,
              width: '100%',
            }}
            category="c1"
            canableShowBottom={false}
            bottomType={'none'}
            backgroundColor="primary-color-800"
            leftIconName="_sound"
            list={repayShowTipTextList}
          />
        </View>
        <View padding="12 16 12 16">
          <Text category="p2" i18nKey={'multiPeriodString.totalRepayAmount'} status="control" />
          <TouchableWithoutFeedback onPress={onChangeRepayDetailModalState}>
            <View layoutStrategy="flexRowStartCenter" margin="9 0 0 0">
              <Text
                margin="0 12 0 0"
                category="h1"
                bold={'bold'}
                textContent={String(realRemindRepayAmount).toFormatFinance()}
                status="control"
              />
              {realRemindRepayAmount !== remindRepayAmount && (
                <Text
                  margin="0 12 0 0"
                  category="h3"
                  textContent={String(remindRepayAmount).toFormatFinance()}
                  status="control"
                  style={{
                    opacity: 0.6,
                    textDecorationLine: 'line-through',
                  }}
                />
              )}
              <Image name="_detailWhiteIcon" />
            </View>
          </TouchableWithoutFeedback>
          <DashedLine
            height={0.5}
            isRotate={false}
            dashGap={6}
            dashColor="line-color-200"
            style={{
              marginTop: 8,
            }}
          />
          <View layoutStrategy="flexRowBetweenCenter" margin="12 0 0 0">
            <Text category="p2" i18nKey={'repaymentString.cardLimit'} status="control" />
            <Text
              category="p2"
              margin="8 0 0 0"
              textContent={repayDateNewFormat}
              status="control"
            />
          </View>
          {isOverdue && (
            <View layoutStrategy="flexRowBetweenCenter">
              <Text
                margin="12 0 0 0"
                padding="4 6 4 6"
                style={{
                  borderRadius: 6,
                  borderWidth: 1,
                  borderColor: 'danger-color-500',
                  backgroundColor: 'danger-color-500',
                  color: 'text-color-0',
                }}
                category="p2"
                textContent={`Vencido: ${overdueDay} días`}
              />
            </View>
          )}
          {BaseInfoManager.context.baseModel.isWithholdSwitch && withholding === 'YES' && (
            <View layoutStrategy="flexRowBetweenCenter">
              <Text
                margin="12 0 0 0"
                padding="4 6 4 6"
                style={{
                  borderRadius: 6,
                  borderWidth: 1,
                  borderColor: 'success-color-500',
                  backgroundColor: 'success-color-500',
                }}
                status="control"
                category="p2"
                i18nKey="repaymentString.automaticDeduction"
              />
            </View>
          )}
          {$withholdTip}
        </View>
      </View>
    );
  }, [
    repayDetail,
    repayDetailModalVisible,
    onChangeRepayDetailModalState,
    repayShowTipTextList,
    $withholdTip,
  ]);

  // 一键代扣状态
  const $directDebitTipView = useMemo(() => {
    if (
      !repayDetail?.directDebitStatus ||
      repayDetail?.directDebitStatus === EDirectDebitStatus.LIMITED
    ) {
      return null;
    }
    let startColor = '#FFF';
    let middleColor = '#FFF';
    let endColor = '#FFF';
    let textColor = colorStyles.TEXT_COLOR_0;
    let imageName = '';
    let text = '';
    switch (repayDetail.directDebitStatus) {
      case EDirectDebitStatus.PROCESSING:
        startColor = '#DBE4FF';
        middleColor = '#D7E1FF';
        endColor = '#FFF';
        textColor = colorStyles.PRIMARY_COLOR_500;
        imageName = '_directDebitProcess';
        text = t(Strings.repaymentString.directDebitProcessing, {
          amount: repayDetail.directDebitAmount || '',
        });
        break;
      case EDirectDebitStatus.FAILED:
        startColor = '#FFECE3';
        middleColor = '#FFE9DE';
        endColor = '#FFF';
        textColor = colorStyles.DANGER_COLOR_500;
        imageName = '_directDebitFail';
        text = t(Strings.repaymentString.directDebitFailed);
        break;
    }
    return (
      <LinearGradient
        style={{
          paddingHorizontal: 8,
          paddingVertical: 4,
          borderRadius: 4,
        }}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
        locations={[0, 0.45, 1]}
        colors={[startColor, middleColor, endColor]}>
        <View layoutStrategy="flexRowStartCenter">
          {repayDetail.directDebitStatus !== EDirectDebitStatus.FAILED ? (
            <Image margin="0 4 0 0" name={imageName} />
          ) : null}
          <Text
            category="c1"
            style={{
              color: textColor,
            }}
            textContent={text}
          />
          {repayDetail.directDebitStatus === EDirectDebitStatus.FAILED ? (
            <Pressable
              onPress={() => {
                Toast(repayDetail.directDebitFailedReason ?? '', 'LONG');
              }}>
              <Image name={imageName} margin="0 0 0 4" />
            </Pressable>
          ) : null}
        </View>
      </LinearGradient>
    );
  }, [repayDetail, colorStyles]);

  // 线上还款
  const $onlineRepay = useMemo(() => {
    return (
      <PayTypeItemView
        key={'online'}
        titleI18nKey={Strings.repaymentString.switch_online}
        noteI18nKey={Strings.repaymentString.freeFee}
        onPress={getRepayChannelConfigAndJumpOnline}>
        <View layoutStrategy={Layouts.flexRowBetweenCenter}>
          <View
            height={40}
            padding="4 8 4 8"
            layoutStrategy="flexColumnCenterCenter"
            style={{ backgroundColor: 'background-color-100', borderRadius: 8 }}>
            <Text
              category="p1"
              bold={'600'}
              textContent={String(onlineRepayDetail.clabe).toFormatClabe()}
            />
          </View>
          <Button status="primary" onPress={onCopyClabe} textI18nKey="btnString.copy" />
        </View>
      </PayTypeItemView>
    );
  }, [onlineRepayDetail.clabe]);

  // 线下还款
  const $offlineRepay = useMemo(() => {
    return (
      <PayTypeItemView
        key={'offline'}
        titleI18nKey={Strings.repaymentString.cardThreeTitle}
        onPress={getRepayChannelConfigAndJumpOffline}></PayTypeItemView>
    );
  }, []);

  // 一键代扣还款
  const $directDebitRepay = useMemo(() => {
    return (
      <PayTypeItemView
        key={'directDebit'}
        titleI18nKey={Strings.repaymentString.directDebitTitle}
        noteI18nKey={Strings.repaymentString.freeFee}
        disabled={repayDetail?.directDebitStatus === EDirectDebitStatus.PROCESSING}
        onPress={jumpDirectDebit}>
        {$directDebitTipView}
      </PayTypeItemView>
    );
  }, [repayDetail, $directDebitTipView]);

  const $repayChannelCard = useMemo(() => {
    // 展示的渠道卡片
    const channelCard = [];
    if (!!repayDetail?.repayTypeChannels?.length) {
      for (let repayChannel of repayDetail?.repayTypeChannels) {
        switch (repayChannel) {
          case ERepaymentChannel.ONLINE:
            channelCard.push($onlineRepay);
            break;
          case ERepaymentChannel.OFFLINE:
            channelCard.push($offlineRepay);
            break;
          case ERepaymentChannel.DIRECT_DEBIT:
            channelCard.push($directDebitRepay);
            break;
        }
      }
    }

    return (
      <View margin="16 0 0 0" style={{ overflow: 'hidden' }} cardType="baseType">
        <LinearGradient
          style={{
            marginTop: 1,
            marginHorizontal: 1,
            paddingHorizontal: 11,
            borderRadius: 8,
            paddingTop: 12,
          }}
          start={{ x: 0, y: 0 }}
          end={{ x: 0, y: 1 }}
          colors={['#F0F4FF', '#ffffff']}>
          <Text
            category="p1"
            style={{
              color: 'text-color-800',
            }}
            i18nKey={'repaymentString.cardTwoTitle'}
          />
        </LinearGradient>
        <Divider style={{ width: undefined }} margin="12 12 12 12" />

        <View padding="0 12 8 12">
          {channelCard}

          <View layoutStrategy="flexRowStartCenter" padding="0 0 8 0">
            <Image
              margin="0 4 0 0"
              style={{
                tintColor: 'fill-color-500',
              }}
              name="_vector"
            />
            <Text
              category="p2"
              margin="0 0 0 0"
              padding="0 0 0 0"
              style={{
                color: 'text-color-600',
              }}
              i18nKey={'repaymentString.cardTwoSubTitle'}
            />
          </View>
        </View>
      </View>
    );
  }, [repayDetail, onlineRepayDetail]);

  /** 逾期还款减免申请入口 */
  const $overdueRepayReduceTip = useMemo(() => {
    const {
      dunRegistrationLabel,
      /** 减免文案 */
      annulCopywriting,
      /** 减免登记状态 */
      dunRegistration,
    } = repayDetail;
    if (dunRegistrationLabel === 1) {
      return (
        <Card
          margin="16 0 0 0"
          padding="6 6 6 6"
          style={{
            borderColor: 'primary-color-500',
            borderWidth: 1,
            borderRadius: 8,
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}>
          <Text style={{ flex: 1 }} category="c1" status="basic" textContent={annulCopywriting} />
          {dunRegistration === 'NO' && (
            <View layoutStrategy="flexColumnCenterCenter" margin="6 8 6 8">
              <Image name="_likeIcon" />
              <Text
                category="p2"
                i18nKey="repaymentString.request"
                status="primary"
                onPress={fetchOverdueRepaymentReduction}
              />
            </View>
          )}
          {dunRegistration === 'YES' && (
            <View layoutStrategy="flexColumnCenterCenter" margin="6 8 6 8">
              <Image name="_successIcon" />
              <Text
                category="p2"
                style={{ color: 'fill-color-400' }}
                onPress={showOverdueRepaymentReducionTip}
                i18nKey="repaymentString.requested"
              />
            </View>
          )}
        </Card>
      );
    } else {
      return null;
    }
  }, [repayDetail]);

  /** 展期卡片 */
  const $extertionCard = useMemo(() => {
    const { extStatus, extAmount, extPaidAmount, extRemainAmount } = repayDetail;

    if (extStatus === 'WAIT_PAY') {
      return (
        <View
          layoutStrategy="flexRowBetweenCenter"
          style={{
            borderRadius: 12,
            overflow: 'hidden',
            backgroundColor: 'primary-color-500',
          }}
          margin="12 0 0 0">
          <ImageBackground padding="14 8 14 8" resizeMode="stretch" name="_extCardBgLeft">
            <View height={'100%'} layoutStrategy="flexColumnBetweenStart">
              <View layoutStrategy="flexRowStartCenter">
                <Image margin="0 6 0 0" name="_extAlarmIcon" />
                <Text status="control" i18nKey="extString.cardLabel" />
              </View>
              <Text
                margin="0 0 0 6"
                status="control"
                category="p2"
                textContent={String(extRemainAmount).toFormatFinance()}
              />
            </View>
          </ImageBackground>
          <View height={80} layoutStrategy="flexColumnBetweenCenter">
            <View
              padding="4 8 4 8"
              style={{
                backgroundColor: 'background-color-0',
                borderBottomLeftRadius: 16,
              }}>
              <CountDown time={countdown} />
            </View>
            <Text
              onPress={onOpenExtModal}
              margin="0 12 14 0"
              status="control"
              bold="bold"
              category="p2"
              i18nKey="extString.payNow"
            />
          </View>
        </View>
      );
    }
    return null;
  }, [repayDetail, countdown]);

  /** 优惠券入口 */
  const $couponLabel = useMemo(() => {
    const { deductionFlag, deductionAmount, overdueDay } = repayDetail;

    // 优惠券文本颜色
    let textColor = 'text-color-600';
    let textDecorationLine: any = undefined;
    if (
      (deductionFlag === 'YES' && Number(deductionAmount) > 0) ||
      availableCouponList.length > 0
    ) {
      if (Number(overdueDay) <= 0) {
        textColor = 'danger-color-500';
      }
    }

    let text = '';
    if (deductionFlag === 'YES' && Number(overdueDay) <= 0) {
      text = '- ' + deductionAmount.toFormatFinance();
    } else {
      text = t('couponString.couponNum', {
        num: Number(overdueDay) > 0 ? 0 : availableCouponList.length,
      });
    }
    return (
      <View margin="16 0 0 0" padding="8 12 8 12" cardType="baseType">
        <View>
          <View layoutStrategy="flexRowBetweenCenter">
            <Text category="p1" i18nKey={'couponString.coupon'} />
            <TouchableOpacity
              onPress={onOpenCouponSelect}
              style={{
                flexDirection: 'row',
                justifyContent: 'center',
                alignItems: 'center',
              }}>
              <Text
                category="p2"
                textContent={text}
                style={{
                  color: textColor,
                  textDecorationLine: textDecorationLine,
                }}
              />
              <Image
                style={{
                  tintColor: 'primary-color-500',
                }}
                margin="0 0 0 6"
                name="_evidenceSelectIcon"
              />
            </TouchableOpacity>
          </View>
          <View margin="6 0 0 0">
            <TouchableOpacity onPress={onOpenCouponHelp}>
              <Text
                style={{
                  color: 'text-color-600',
                  textDecorationLine: 'underline',
                }}
                category="p1"
                i18nKey={'couponString.couponHelpsTip'}
              />
            </TouchableOpacity>
          </View>
        </View>
      </View>
    );
  }, [t, onOpenCouponHelp, onOpenCouponSelect, repayDetail, couponList, availableCouponList]);

  /** 返现文案说明 */
  const $cashbackTip = useMemo(() => {
    if (!isVipFuncSwitch) {
      return <></>;
    }

    if (!repayDetail.cashbackCopywriting) {
      return <></>;
    }

    return (
      <View
        padding="8 12 8 12"
        margin="16 0 0 0"
        style={{ backgroundColor: 'tertiary-color-500', borderRadius: 8 }}
        layoutStrategy="flexRowStartCenter">
        <Image margin="0 12 0 0" resizeMode="contain" name="_vipIcon" />
        <Text category="p1" style={{ flex: 1 }}>
          <Text status="control" textContent={repayDetail.cashbackCopywriting?.firstHalf} />
          <Text
            style={{
              color: 'danger-color-500',
            }}
            textContent={repayDetail.cashbackCopywriting?.amount}
          />
          <Text status="control" textContent={repayDetail.cashbackCopywriting?.secondHalf} />
        </Text>
        <TouchableOpacity onPress={handleGoVipRelus}>
          <Image margin="0 0 0 12" name="_vipView" />
        </TouchableOpacity>
      </View>
    );
  }, [repayDetail.cashbackCopywriting, isVipFuncSwitch, handleGoVipRelus]);

  return (
    <>
      <Layout level="1" pLevel="0" topCompensateColor="primary-color-500">
        <TopNavigation
          pageKey={HitPointEnumsSpace.EPageKey.P_REPAY}
          showLogoAction={true}
          type="primary"
          bottomLine={false}
          isShowVip={true}
          showMessage
        />
        {/* {$addRepayRemindCalendarTag} */}
        <ScrollView
          overScrollMode={'never'}
          refreshControl={
            <RefreshControl refreshing={refreshing} colors={['#4C6EFF']} onRefresh={onRefresh} />
          }>
          <View>
            <View
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                height: 150,
                backgroundColor: 'primary-color-500',
              }}></View>
            <View margin="0 16 12 16">
              {$repayDetailCard}
              {$overdueRepayReduceTip}
              {$cashbackTip}
              {/* {$couponLabel} */}
            </View>
            <View style={{ marginTop: -10 }}>
              <CouponSelectCard
                couponList={availableCouponList}
                selectCouponList={couponSelected}
                onOpenCouponSelect={onOpenCouponSelect}
                repayDetail={repayDetail}
              />
            </View>
            <View margin="0 16 12 16">
              {$extertionCard}
              {$repayChannelCard}
            </View>
            <VipUpLevelTipCard pageKey={HitPointEnumsSpace.EPageKey.P_REPAY} />
          </View>
        </ScrollView>
        {/* 还款详情弹窗 */}
        <RepayDetailInfoDialog
          visible={repayDetailModalVisible}
          repayDetail={repayDetail}
          changeDialogVisibe={onChangeRepayDetailModalState}
        />
      </Layout>
      <GpReviewModal delay={repayMentAccountVisible} />
      {/* <CouponSelectModal
        availableCouponList={availableCouponList}
        repayDetail={repayDetail}
        visible={couponSelectModalVisible}
        onCancel={onCloseCouponSelect}
        onConfrim={onSelectCoupont}
        couponList={couponList}
      /> */}
      <CouponSelectModal
        visible={couponSelectModalVisible}
        onCancel={onCloseCouponSelect}
        onConfirm={onSelectCouponSubmit}
        couponList={couponList}
        availableCouponList={availableCouponList}
        couponSelected={couponSelected}
        pageKey={HitPointEnumsSpace.EPageKey.P_REPAY}
      />
      <CouponHelpsModal visible={couponHelpModalVisible} onCancel={onCloseCouponHelp} />
      <ExtModal
        visible={extModalVisible}
        repayDetail={repayDetail}
        onlineRepayDetail={onlineRepayDetail}
        countdown={countdown}
        getRepayChannelConfigAndJumpOnline={getRepayChannelConfigAndJumpOnline}
        onCopyClabe={onCopyClabe}
        changeDialogVisibe={onCloseExtModal}
      />
      <AccountUpdateModal
        visible={repayMentAccountVisible}
        onClose={onCloseRepayMentAccount}
        clabe={onlineRepayDetail.clabe}
      />
    </>
  );
};
