import { HitPointEnumsSpace, UserEnumsSpace } from '@/enums';
import {
  useDeviceDataReport,
  useGetUserStateAndNextRouterOrOtherCallBack,
  useOnInit,
  useShowGPReviewStatus,
} from '@/hooks';
import { useNameSpace } from '@/i18n';
import { BaseInfoManager, ModalList, UserInfoManager, modalDataStoreInstance } from '@/managers';
import { Toast } from '@/nativeComponents';
import { RouterConfig } from '@/routes';
import {
  fetchCouponsByConditions,
  fetchDunRegistration,
  fetchOnlineRepayMethodDetailAllChannel,
  fetchRepayBindCoupon,
  fetchRepayDetail,
  fetchRepayPageShowTipList,
} from '@/server';
import { EDirectDebitStatus, ERepaymentChannel, OrderVOSpace, UserVOSpace } from '@/types';
import { TrackEvent, nav, removeAllSpaces } from '@/utils';
import Clipboard from '@react-native-clipboard/clipboard';
import { useUpdateEffect } from 'ahooks';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';

export default function useData() {
  const t = useNameSpace().t;
  const { onRepaymentReportDeviceData } = useDeviceDataReport();
  /** 还款 */
  const [repayDetailModalVisible, setRepayDetailModalVisible] = useState<boolean>(false);

  const [countdown, setCountdown] = useState<number>(0);

  const [couponList, setCouponList] = useState<UserVOSpace.CouponsItem[]>([]);

  const [couponHelpModalVisible, setCouponHelpModalVisible] = useState<boolean>(false);

  const [couponSelectModalVisible, setCouponSelectModalVisible] = useState<boolean>(false);

  const [extModalVisible, setExtModalVisible] = useState<boolean>(false);

  const { showGooglePlayReviewOnOrder } = useShowGPReviewStatus({
    screen: 'LOAN',
  });

  /** 还款页面提示文案 */
  const [repayShowTipTextList, setRepayShowTipTextList] = useState<string[]>([]);
  const [repayMentAccountVisible, setRepayMentAccountVisible] = useState<boolean>(false);

  const [onlineRepayDetail, setOnlieRepayDetail] = useState<OrderVOSpace.RepayOnlineDataType>({
    /**账号*/
    clabe: '*',
    /**待还金额*/
    totalAmount: '*',
    /**服务费*/
    repayFee: '*',
    /**服务费vat*/
    repayFeeVat: '*',
  });

  /** 页面初始化 */
  const { loading, refreshing, setRefreshing, onRefresh } = useOnInit({
    callback: async () => {
      BaseInfoManager.changeLoadingModalVisible(true);

      loading.current = true;
      await getOnlineRepayDetailAllChannel();

      await getRepayDetailInfo();

      await getRepayCouponList();

      // await getUserStateAndNextRouterOrOtherCallBack();

      await onRepaymentReportDeviceData();

      await getRepayShowTipTextList();

      showGooglePlayReviewOnOrder();

      loading.current = false;

      BaseInfoManager.changeLoadingModalVisible(false);
    },
    refreshCallback: async () => {
      setRefreshing(true);

      loading.current = true;

      await getRepayDetailInfo();

      await getRepayCouponList();

      await getOnlineRepayDetailAllChannel();

      await getUserStateAndNextRouterOrOtherCallBack();

      showGooglePlayReviewOnOrder();

      loading.current = false;

      setRefreshing(false);
    },
    isBackAutoRefresh: true,
    pageKey: HitPointEnumsSpace.EPageKey.P_REPAY,
  });

  const [repayDetail, setRepayDetail] = useState<OrderVOSpace.BillDetailDataType>({
    orderId: '*',
    /** 本金 */
    remainPrincipal: '*',
    /** 放款手续费 */
    remainTransferFee: '*',
    /** 放款手续费VAT */
    remainTransferFeeVat: '*',
    /** 服务费 */
    remainProcessFee: '*',
    /** 服务费VAT */
    remainProcessFeeVat: '*',
    /** 利息 */
    remainInterest: '*',
    /** 利息vat */
    remainInterestVat: '*',
    /** 逾期天数*/
    overdueDay: '*',
    /** 逾期费 */
    remainOverdueFee: '*',
    /** 逾期费Vat */
    remainOverdueFeeVat: '*',
    /** 催收费 */
    remainCollectFee: '*',
    /** 催收费vat */
    remainCollectFeeVat: '*',
    /** 还款日期 */
    repayDate: '*',
    /** 还款日期已格式化 */
    repayDateNewFormat: '*',
    /** 剩余几天 */
    remindDay: '*',
    /** 已还金额 */
    realRepayAmount: '*',
    /** 待还金额 */
    remindRepayAmount: '*',
    /** 减免金额 */
    annulAmount: '*',
    /** 是否展示还款减免登记入口 */
    dunRegistrationLabel: 0,
    /** 减免文案 */
    annulCopywriting: '',
    /** 减免登记状态 */
    dunRegistration: 'NO',
    /** 抵扣金额 */
    deductionAmount: '0',
    /** 是否使用了抵扣券 */
    deductionFlag: 'NO',
    /** 按时还款是否有券 */
    couponBannerFlag: 'NO',
    /** 卷后真实还款金额 */
    realRemindRepayAmount: '',
    /** 是否可以展期 */
    allowExt: 'NO',
    /** 展期状态 */
    extStatus: '', // EXTENDING 展期中
    /** 贷款日期 */
    loanDate: '',
    /** 贷款天数 */
    days: '',
    /** 展期天数 */
    extDays: '',
    /** 展期支付倒计时 */
    extCountdown: 0,
    /** 展期金额 */
    extAmount: '',
    /** 返现文案 */
    cashbackCopywriting: null,
    /** 代扣文案 */
    withholdCopywriting: undefined,
    /** 是否在代扣中 */
    withholding: UserEnumsSpace.EStatusType.NO,
    /** 展期以支付费用 */
    extRemainAmount: '',
    /** 展期已支付费用 */
    extPaidAmount: '',
    /** 还款账号提醒 */
    repayAccountChanged: 'NO',
    directDebitStatus: undefined,
    repayTypeChannels: [ERepaymentChannel.ONLINE, ERepaymentChannel.OFFLINE],
    directDebitFailedReason: '',
  });
  const repayDetailRef = useRef<OrderVOSpace.BillDetailDataType>(repayDetail);
  const [couponSelected, setCouponSelected] = useState<UserVOSpace.CouponsItem[]>([]);

  useEffect(() => {
    let timer: number | null;
    let index = 0;
    if (repayDetail.extStatus === 'WAIT_PAY' && typeof repayDetail.extCountdown === 'number') {
      timer = setInterval(() => {
        index++;
        let countdown = repayDetail.extCountdown / 1000 - index;
        if (countdown <= 0) {
          timer && clearInterval(timer);
          timer = null;
          onRefresh && onRefresh();
        }
        setCountdown(countdown);
      }, 1000);
    }

    return () => {
      timer && clearInterval(timer);
      timer = null;
    };
  }, [repayDetail.extCountdown, repayDetail.extStatus]);

  useUpdateEffect(() => {
    if (repayDetail?.repayAccountChanged === 'YES') setRepayMentAccountVisible(true);
  }, [repayDetail?.repayAccountChanged]);

  const onOpenCouponHelp = useCallback(() => {
    setCouponHelpModalVisible(true);
  }, []);

  const onCloseCouponHelp = useCallback(() => {
    setCouponHelpModalVisible(false);
  }, []);

  const onOpenExtModal = useCallback(() => {
    setExtModalVisible(true);
  }, []);

  const onCloseExtModal = useCallback(() => {
    setExtModalVisible(false);
  }, []);

  const availableCouponList = useMemo(() => {
    const { realRemindRepayAmount, deductionFlag, overdueDay } = repayDetail;

    if (Number(overdueDay) >= 1 || deductionFlag === 'YES') {
      return [];
    } else {
      return couponList.filter(item => Number(item.amount) < Number(realRemindRepayAmount));
    }
  }, [couponList, repayDetail]);

  const onOpenCouponSelect = useCallback(() => {
    TrackEvent.trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_REPAY,
        e: HitPointEnumsSpace.EEventKey.BTN_USE_COUPON,
      },
      '1',
    );
    // todo 选择优惠券时，需要判断是否已经绑定了优惠券，和是否有可用的优惠券可以使用。
    if (repayDetail.deductionFlag === 'YES' && Number(repayDetail.overdueDay) <= 0) {
      // todo 已经绑定优惠券
      modalDataStoreInstance.openModal({
        key: ModalList.INFO_PROMPT_CONFIRM,
        imageKey: '_modalCouponSelectIcon',
        i18nKey: 'couponString.couponHasBeenBound',
        confirmBtnName: 'btnString.agree',
      });
    } else {
      setCouponSelectModalVisible(true);
    }
  }, [availableCouponList, repayDetail]);

  const onCloseCouponSelect = useCallback(() => {
    setCouponSelectModalVisible(false);
  }, []);

  const onSelectCoupont = useCallback(async (item: UserVOSpace.CouponsItem) => {
    modalDataStoreInstance.openModal({
      key: ModalList.INFO_PROMPT_CONFIRM,
      imageKey: '_modalCouponSelectIcon',
      i18nKey: 'couponString.couponBoundConfrim',
      confirmBtnName: 'btnString.confirm',
      cancelBtnName: 'btnString.cancel',
      cancelBtnCallback: () => {},
      confirmBtnCallback: async () => {
        const { code } = await fetchRepayBindCoupon({
          couponSN: item.serialNumber,
        });
        if (code == 0) {
          getRepayDetailInfo();
        }
      },
    });
  }, []);
  const onSelectCouponSubmit = useCallback(async (coupons?: UserVOSpace.CouponsItem[]) => {
    const item = coupons?.[0];
    if (!item) return;
    setCouponSelected(coupons || []);
    modalDataStoreInstance.openModal({
      key: ModalList.INFO_PROMPT_CONFIRM,
      imageKey: '_modalCouponSelectIcon',
      i18nKey: 'couponString.couponBoundConfrim',
      confirmBtnName: 'btnString.confirm',
      cancelBtnName: 'btnString.cancel',
      cancelBtnCallback: () => {},
      confirmBtnCallback: async () => {
        const { code } = await fetchRepayBindCoupon({
          couponSN: item?.serialNumber,
        });
        if (code == 0) {
          getRepayDetailInfo();
        }
      },
    });
  }, []);

  /** 获取还款详情 */
  const getRepayDetailInfo = useCallback(async () => {
    const { code, data } = await fetchRepayDetail();
    if (code === 0) {
      // 这里根据data需要判断是不是需要打开添加还款日历提醒的弹窗
      // initRepayRemindCalendarEventStatus(data);
      repayDetailRef.current = data;
      setRepayDetail(data);
      // setRepayMentAccountVisible(data.repayAccountChanged === 'YES');
    }
  }, []);

  /** 获取可用的还款优惠券 */
  const getRepayCouponList = useCallback(async () => {
    const { code, data } = await fetchCouponsByConditions({
      /** 优惠券类型 */
      type: UserEnumsSpace.ECouponsType.DEDUCTION,
      /** 优惠券状态 */
      status: UserEnumsSpace.ECouponsStatus.AVAILABLE,
    });
    if (code === 0) {
      // 这里根据data需要判断是不是需要打开添加还款日历提醒的弹窗
      const _data = data.filter(item => item.canCheck === 'YES');
      const selectCoupons = _data.filter(
        item => item.couponStatus === UserEnumsSpace.ECouponsStatus.BINDING,
      );
      setCouponList(_data);
      setCouponSelected(selectCoupons);
    }
  }, []);

  /** 获取线上还款信息 */
  const getOnlineRepayDetailAllChannel = useCallback(async () => {
    const { code, data } = await fetchOnlineRepayMethodDetailAllChannel();
    if (code === 0) {
      setOnlieRepayDetail((prevState: OrderVOSpace.RepayOnlineDataType) => ({
        ...prevState,
        clabe: data?.clabe || '*',
        totalAmount: data?.totalAmount || '*',
        repayFee: data?.repayFee || '*',
        repayFeeVat: data?.repayFeeVat || '*',
      }));
    }
  }, []);

  /** 获取还款页提示文案列表 */
  const getRepayShowTipTextList = useCallback(async () => {
    const { code, data } = await fetchRepayPageShowTipList();
    if (code === 0) {
      if (Array.isArray(data.repayPage)) {
        setRepayShowTipTextList(data.repayPage);
      }
    }
  }, []);

  /** 线上还款 */
  const getRepayChannelConfigAndJumpOnline = useCallback(() => {
    nav.navigate(RouterConfig.ONLINE_REPAYMENT as any);
  }, []);

  /** 引导框选择线上或线下还款 */
  const getRepayChannelConfigAndJumpOffline = useCallback(() => {
    modalDataStoreInstance.openModal({
      key: ModalList.INFO_PROMPT_CONFIRM,
      imageKey: '_repaymentGuideIcon',
      i18nKey: 'repaymentString.guide_to_offline_repayment',
      confirmBtnCallback: () => {
        const realRemindRepayAmount = repayDetailRef.current?.realRemindRepayAmount;
        nav.navigate(RouterConfig.OFFLINE_REPAYMENT as any, { realRemindRepayAmount });
      },
      cancelBtnCallback: () => {
        nav.navigate(RouterConfig.ONLINE_REPAYMENT as any);
      },
      cancelBtnName: 'repaymentString.switch_online',
      confirmBtnName: 'repaymentString.next',
    });
  }, []);

  const jumpDirectDebit = useCallback(() => {
    if (repayDetailRef.current?.directDebitStatus === EDirectDebitStatus.PROCESSING) {
      return;
    }
    TrackEvent.trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_REPAY,
        e: HitPointEnumsSpace.EEventKey.BTN_ONE_CLICK_REPAY,
      },
      '1',
    );
    nav.navigate(RouterConfig.DIRECT_DEBIT_PAGE as any, {
      realRemindRepayAmount: repayDetailRef.current?.realRemindRepayAmount,
      orderId: repayDetailRef.current?.orderId,
    });
  }, []);

  /** 复制 clabe 号 */
  const onCopyClabe = useCallback(() => {
    const clable = removeAllSpaces(onlineRepayDetail.clabe);
    Clipboard.setString(clable);
    Toast(t('basicInfoString.copyTip'), 'SHORT');
    TrackEvent.trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_REPAY,
        e: HitPointEnumsSpace.EEventKey.BTN_COPY_CODE,
      },
      '1',
    );
  }, [onlineRepayDetail.clabe, t]);

  /** 改变还款详情模态弹窗 */
  const onChangeRepayDetailModalState = useCallback(() => {
    setRepayDetailModalVisible(preState => {
      TrackEvent.trackCommonEvent(
        {
          p: HitPointEnumsSpace.EPageKey.P_REPAY,
          e: HitPointEnumsSpace.EEventKey.BTN_REPAYMENT_DETAILS,
        },
        !preState ? '1' : '0',
      );
      return !preState;
    });
  }, []);

  const [getUserStateAndNextRouterOrOtherCallBack] = useGetUserStateAndNextRouterOrOtherCallBack(
    RouterConfig.REPAYMENT,
  );

  /** 逾期还款减免登记 */
  const fetchOverdueRepaymentReduction = async () => {
    BaseInfoManager.changeLoadingModalVisible(true);
    const result = await fetchDunRegistration();
    await getRepayDetailInfo();
    if (result.code === 0) {
      Toast(t('repaymentString.request_success_tip'));
    }
    BaseInfoManager.changeLoadingModalVisible(false);
  };

  /** 用户重复点击逾期还款减免登记提示客服会在24小时联系 */
  const showOverdueRepaymentReducionTip = () => {
    Toast(t('repaymentString.request_success_tip'));
  };

  const handleGoVipRelus = useCallback(() => {
    UserInfoManager.getVipConfigAndNavigate({});
    TrackEvent.trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_REPAY,
        e: HitPointEnumsSpace.EEventKey.BTN_VIP_CASHBACK,
      },
      '1',
    );
  }, []);
  const onCloseRepayMentAccount = useCallback(() => {
    setRepayMentAccountVisible(false);
  }, []);
  return {
    refreshing,
    onRefresh,
    onChangeRepayDetailModalState,
    onlineRepayDetail,
    repayDetail,
    repayDetailModalVisible,
    getRepayChannelConfigAndJumpOnline,
    getRepayChannelConfigAndJumpOffline,
    onCopyClabe,
    repayShowTipTextList,
    fetchOverdueRepaymentReduction,
    showOverdueRepaymentReducionTip,
    onOpenCouponHelp,
    onCloseCouponHelp,
    couponHelpModalVisible,
    couponSelectModalVisible,
    extModalVisible,
    onOpenExtModal,
    onCloseExtModal,
    couponList,
    availableCouponList,
    onOpenCouponSelect,
    onCloseCouponSelect,
    onSelectCoupont,
    handleGoVipRelus,
    countdown,
    onCloseRepayMentAccount,
    repayMentAccountVisible,
    jumpDirectDebit,
    couponSelected,
    onSelectCouponSubmit,
  };
}
