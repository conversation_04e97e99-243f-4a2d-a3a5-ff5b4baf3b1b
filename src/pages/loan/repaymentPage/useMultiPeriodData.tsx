import { HitPointEnumsSpace } from '@/enums';
import {
  useDeviceDataReport,
  useGetUserStateAndNextRouterOrOtherCallBack,
  useOnInit,
  useShowGPReviewStatus,
} from '@/hooks';
import { useNameSpace } from '@/i18n';
import { BaseInfoManager, ModalList, UserInfoManager, modalDataStoreInstance } from '@/managers';
import { Toast } from '@/nativeComponents';
import { RouterConfig } from '@/routes';
import {
  fetchDunRegistration,
  fetchMultiPeriodRepayDetail,
  fetchOnlineRepayMethodDetailAllChannel,
  fetchRepayPageShowTipList,
} from '@/server';
import { EDirectDebitStatus, ERepaymentChannel, OrderVOSpace } from '@/types';
import { TrackEvent, nav } from '@/utils';
import Clipboard from '@react-native-clipboard/clipboard';
import { useUpdateEffect } from 'ahooks';
import { useCallback, useState, useRef } from 'react';

export default function useMultiPeriodData() {
  const { onRepaymentReportDeviceData } = useDeviceDataReport();
  /** 还款 */
  const [repayDetailModalVisible, setRepayDetailModalVisible] = useState<boolean>(false);

  const { showGooglePlayReviewOnOrder } = useShowGPReviewStatus({
    screen: 'LOAN',
  });

  /** 还款页面提示文案 */
  const [repayShowTipTextList, setRepayShowTipTextList] = useState<string[]>([]);

  const [onlineRepayDetail, setOnlieRepayDetail] = useState<OrderVOSpace.RepayOnlineDataType>({
    /**账号*/
    clabe: '*',
    /**待还金额*/
    totalAmount: '*',
    /**服务费*/
    repayFee: '*',
    /**服务费vat*/
    repayFeeVat: '*',
  });

  /** 页面初始化 */
  const { loading, refreshing, setRefreshing, onRefresh } = useOnInit({
    callback: async () => {
      BaseInfoManager.changeLoadingModalVisible(true);

      loading.current = true;
      await getOnlineRepayDetailAllChannel();
      await getRepayDetailInfo();

      // await getUserStateAndNextRouterOrOtherCallBack();

      await onRepaymentReportDeviceData();

      await getRepayShowTipTextList();

      showGooglePlayReviewOnOrder();

      loading.current = false;

      BaseInfoManager.changeLoadingModalVisible(false);
    },
    refreshCallback: async () => {
      setRefreshing(true);

      loading.current = true;

      await getRepayDetailInfo();

      await getOnlineRepayDetailAllChannel();

      await getUserStateAndNextRouterOrOtherCallBack();

      showGooglePlayReviewOnOrder();

      loading.current = false;

      setRefreshing(false);
    },
    isBackAutoRefresh: true,
    pageKey: HitPointEnumsSpace.EPageKey.P_INSTALLMENT_REPAY,
  });

  const [repayDetail, setRepayDetail] = useState<OrderVOSpace.MultiPeriodBillDetailDataType>({
    orderId: '*',
    /** 本金 */
    remainPrincipal: '*',
    /** 还款日期 */
    repayDate: '*',
    /** 待还金额 */
    remindRepayAmount: '*',
    /** 减免金额 */
    annulAmount: '*',
    /** 卷后真实还款金额 */
    realRemindRepayAmount: '',
    /** 贷款日期 */
    loanDate: '',
    /** 贷款天数 */
    days: 0,
    loanAmount: '',
    remainProcessFeeAndVat: '',
    remainInterestAndVat: '',
    remainOverdueFeeAndVat: '',
    remainCollectFeeAndVat: '',
    /** 线上还款渠道 */
    onlineType: '',
    /** 银行卡号 */
    clabe: '',
    /** 鲜虾还款渠道 */
    offlineType: '',
    /** 账单期限 */
    loanCostPerPeriods: [],
    /** 逾期天数 */
    overdueDay: '0',
    /** 还款账号变更 */
    repayAccountChanged: 'NO',
    directDebitStatus: undefined,
    repayTypeChannels: [ERepaymentChannel.ONLINE, ERepaymentChannel.OFFLINE],
    directDebitFailedReason: '',
  });
  const [repayMentAccountVisible, setRepayMentAccountVisible] = useState<boolean>(false);
  const repayDetailRef = useRef<OrderVOSpace.MultiPeriodBillDetailDataType>(repayDetail);

  useUpdateEffect(() => {
    if (repayDetail?.repayAccountChanged === 'YES') setRepayMentAccountVisible(true);
  }, [repayDetail?.repayAccountChanged]);

  /** 获取还款详情 */
  const getRepayDetailInfo = useCallback(async () => {
    const { code, data } = await fetchMultiPeriodRepayDetail();
    if (code === 0) {
      // 这里根据data需要判断是不是需要打开添加还款日历提醒的弹窗
      // initRepayRemindCalendarEventStatus(data);
      repayDetailRef.current = data;
      setRepayDetail(data);
      // setRepayMentAccountVisible(data.repayAccountChanged === 'YES');
    }
  }, []);

  /** 获取线上还款信息 */
  const getOnlineRepayDetailAllChannel = useCallback(async () => {
    const { code, data } = await fetchOnlineRepayMethodDetailAllChannel();
    if (code === 0) {
      setOnlieRepayDetail((prevState: OrderVOSpace.RepayOnlineDataType) => ({
        ...prevState,
        clabe: data?.clabe || '*',
        totalAmount: data?.totalAmount || '*',
        repayFee: data?.repayFee || '*',
        repayFeeVat: data?.repayFeeVat || '*',
      }));
    }
  }, []);

  /** 获取还款页提示文案列表 */
  const getRepayShowTipTextList = useCallback(async () => {
    const { code, data } = await fetchRepayPageShowTipList();
    if (code === 0) {
      if (Array.isArray(data.repayPage)) {
        setRepayShowTipTextList(data.repayPage);
      }
    }
  }, []);

  /** 线上还款 */
  const getRepayChannelConfigAndJumpOnline = useCallback(() => {
    nav.navigate(RouterConfig.ONLINE_REPAYMENT as any);
  }, []);

  /** 引导框选择线上或线下还款 */
  const getRepayChannelConfigAndJumpOffline = useCallback(() => {
    modalDataStoreInstance.openModal({
      key: ModalList.INFO_PROMPT_CONFIRM,
      imageKey: '_repaymentGuideIcon',
      i18nKey: 'repaymentString.guide_to_offline_repayment',
      confirmBtnCallback: () => {
        const realRemindRepayAmount = repayDetailRef.current?.realRemindRepayAmount;
        nav.navigate(RouterConfig.OFFLINE_REPAYMENT as any, { realRemindRepayAmount });
      },
      cancelBtnCallback: () => {
        nav.navigate(RouterConfig.ONLINE_REPAYMENT as any);
      },
      cancelBtnName: 'repaymentString.switch_online',
      confirmBtnName: 'repaymentString.next',
    });
  }, []);

  const t = useNameSpace().t;

  const jumpDirectDebit = useCallback(() => {
    if (repayDetailRef.current?.directDebitStatus === EDirectDebitStatus.PROCESSING) {
      return;
    }
    TrackEvent.trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_REPAY,
        e: HitPointEnumsSpace.EEventKey.BTN_ONE_CLICK_REPAY,
      },
      '1',
    );
    nav.navigate(RouterConfig.DIRECT_DEBIT_PAGE as any, {
      realRemindRepayAmount: repayDetailRef.current?.realRemindRepayAmount,
      orderId: repayDetailRef.current?.orderId,
    });
  }, []);

  /** 复制 clabe 号 */
  const onCopyClabe = useCallback(() => {
    Clipboard.setString(onlineRepayDetail.clabe);
    Toast(t('basicInfoString.copyTip'), 'SHORT');
    TrackEvent.trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_INSTALLMENT_REPAY,
        e: HitPointEnumsSpace.EEventKey.BTN_COPY_CODE,
      },
      '1',
    );
  }, [onlineRepayDetail.clabe, t]);

  /** 改变还款详情模态弹窗 */
  const onChangeRepayDetailModalState = useCallback(() => {
    setRepayDetailModalVisible(preState => {
      TrackEvent.trackCommonEvent(
        {
          p: HitPointEnumsSpace.EPageKey.P_INSTALLMENT_REPAY,
          e: HitPointEnumsSpace.EEventKey.BTN_REPAYMENT_DETAILS,
        },
        !preState ? '1' : '0',
      );
      return !preState;
    });
  }, []);

  const [getUserStateAndNextRouterOrOtherCallBack] = useGetUserStateAndNextRouterOrOtherCallBack(
    RouterConfig.REPAYMENT,
  );

  /** 逾期还款减免登记 */
  const fetchOverdueRepaymentReduction = async () => {
    BaseInfoManager.changeLoadingModalVisible(true);
    const result = await fetchDunRegistration();
    await getRepayDetailInfo();
    if (result.code === 0) {
      Toast(t('repaymentString.request_success_tip'));
    }
    BaseInfoManager.changeLoadingModalVisible(false);
  };

  /** 用户重复点击逾期还款减免登记提示客服会在24小时联系 */
  const showOverdueRepaymentReducionTip = () => {
    Toast(t('repaymentString.request_success_tip'));
  };

  const handleGoVipRelus = useCallback(() => {
    UserInfoManager.getVipConfigAndNavigate({});
    TrackEvent.trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_INSTALLMENT_REPAY,
        e: HitPointEnumsSpace.EEventKey.BTN_VIP_CASHBACK,
      },
      '1',
    );
  }, []);
  const onCloseRepayMentAccount = useCallback(() => {
    setRepayMentAccountVisible(false);
  }, []);
  return {
    refreshing,
    onRefresh,
    onChangeRepayDetailModalState,
    onlineRepayDetail,
    repayDetail,
    repayDetailModalVisible,
    getRepayChannelConfigAndJumpOnline,
    getRepayChannelConfigAndJumpOffline,
    onCopyClabe,
    repayShowTipTextList,
    fetchOverdueRepaymentReduction,
    showOverdueRepaymentReducionTip,
    handleGoVipRelus,
    repayMentAccountVisible,
    onCloseRepayMentAccount,
    jumpDirectDebit,
  };
}
