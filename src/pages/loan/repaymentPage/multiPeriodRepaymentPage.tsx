/* eslint-disable react-native/no-inline-styles */

import {
  BusinessUI,
  Button,
  DashedLine,
  Divider,
  Image,
  Layout,
  LinearGradient,
  Swiper,
  Text,
  TopNavigation,
  View,
} from '@/components';

import { GpReviewModal } from '@/modals';
import React, { ReactElement, useMemo } from 'react';
import { Dimensions, Pressable, RefreshControl, ScrollView, TouchableOpacity } from 'react-native';
import MultiPeriodRepayDetailInfoCard from './components/MultiPeriodRepayDetailInfoCard';

import { HitPointEnumsSpace } from '@/enums';
import AccountUpdateModal from './components/AccountUpdateModal';
import useMultiPeriodData from './useMultiPeriodData';
import { EDirectDebitStatus, ERepaymentChannel } from '@/types';
import { useProcessColorStyles } from '@/utils';
import PayTypeItemView from './components/PayTypeItemView';
import { Strings } from '@/i18n';
import { Colors } from '@/themes';
import { Toast } from '@/nativeComponents';
import { t } from 'i18next';

const { VipUpLevelTipCard } = BusinessUI;

export default (): ReactElement => {
  const {
    refreshing,
    onRefresh,
    repayDetailModalVisible,
    onChangeRepayDetailModalState,
    onlineRepayDetail,
    repayDetail,
    getRepayChannelConfigAndJumpOnline,
    getRepayChannelConfigAndJumpOffline,
    onCopyClabe,
    repayShowTipTextList,
    repayMentAccountVisible,
    onCloseRepayMentAccount,
    jumpDirectDebit,
  } = useMultiPeriodData();
  const colorStyles = useProcessColorStyles({
    TEXT_COLOR_700: Colors.TEXT_COLOR_700,
    PRIMARY_COLOR_500: Colors.PRIMARY_COLOR_500,
    DANGER_COLOR_500: Colors.DANGER_COLOR_500,
    TEXT_COLOR_0: Colors.TEXT_COLOR_0,
  });

  const $directDebitTipView = useMemo(() => {
    if (
      !repayDetail?.directDebitStatus ||
      repayDetail?.directDebitStatus === EDirectDebitStatus.LIMITED
    ) {
      return null;
    }
    let startColor = '#FFF';
    let middleColor = '#FFF';
    let endColor = '#FFF';
    let textColor = colorStyles.TEXT_COLOR_0;
    let imageName = '';
    let text = '';
    switch (repayDetail.directDebitStatus) {
      case EDirectDebitStatus.PROCESSING:
        startColor = '#DBE4FF';
        middleColor = '#D7E1FF';
        endColor = '#FFF';
        textColor = colorStyles.PRIMARY_COLOR_500;
        imageName = '_directDebitIimit';
        text = t(Strings.repaymentString.directDebitProcessing, {
          amount: repayDetail.directDebitAmount || '',
        });
        break;
      case EDirectDebitStatus.FAILED:
        startColor = '#FFECE3';
        middleColor = '#FFE9DE';
        endColor = '#FFF';
        textColor = colorStyles.DANGER_COLOR_500;
        imageName = '_directDebitFail';
        text = t(Strings.repaymentString.directDebitFailed);
        break;
    }
    return (
      <LinearGradient
        style={{
          paddingHorizontal: 8,
          paddingVertical: 4,
          borderRadius: 4,
        }}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
        locations={[0, 0.45, 1]}
        colors={[startColor, middleColor, endColor]}>
        <View layoutStrategy="flexRowStartCenter">
          {repayDetail.directDebitStatus !== EDirectDebitStatus.FAILED ? (
            <Image margin="0 4 0 0" name={imageName} />
          ) : null}
          <Text
            category="c1"
            style={{
              color: textColor,
            }}
            textContent={text}
          />
          {repayDetail.directDebitStatus === EDirectDebitStatus.FAILED ? (
            <Pressable
              onPress={() => {
                Toast(repayDetail.directDebitFailedReason ?? '', 'LONG');
              }}>
              <Image name={imageName} margin="0 0 0 4" />
            </Pressable>
          ) : null}
        </View>
      </LinearGradient>
    );
  }, [repayDetail, colorStyles]);

  /** 还款详情卡片 */
  const $repayDetailCard = useMemo(() => {
    const { remindRepayAmount, realRemindRepayAmount, repayDate, overdueDay } = repayDetail;
    const isOverdue = repayDetail.overdueDay && Number(overdueDay) > 0;

    return (
      <View
        style={{
          backgroundColor: 'primary-color-900',
          borderRadius: 8,
        }}>
        <View
          style={{
            backgroundColor: 'primary-color-800',
            borderTopLeftRadius: 8,
            borderTopRightRadius: 8,
          }}
          layoutStrategy="flexRowBetweenCenter">
          <Swiper
            style={{
              borderTopLeftRadius: 8,
              borderTopRightRadius: 8,
              width: '100%',
            }}
            category="c1"
            canableShowBottom={false}
            bottomType={'none'}
            backgroundColor="primary-color-800"
            leftIconName="_sound"
            list={repayShowTipTextList}
          />
        </View>
        <View padding="12 16 12 16">
          <Text category="p2" i18nKey={'multiPeriodString.repayAmount'} status="control" />
          <View layoutStrategy="flexRowStartCenter" margin="9 0 0 0">
            <Text
              margin="0 12 0 0"
              category="h1"
              bold={'bold'}
              textContent={String(realRemindRepayAmount).toFormatFinance()}
              status="control"
            />
            {realRemindRepayAmount !== remindRepayAmount && (
              <Text
                margin="0 12 0 0"
                category="h3"
                textContent={String(remindRepayAmount).toFormatFinance()}
                status="control"
                style={{
                  opacity: 0.6,
                  textDecorationLine: 'line-through',
                }}
              />
            )}
            {/* <Image name="_detailWhiteIcon" /> */}
          </View>
          {/* </TouchableWithoutFeedback> */}

          <DashedLine
            height={0.5}
            isRotate={false}
            dashGap={6}
            dashColor="line-color-100"
            style={{
              marginTop: 8,
              // marginBottom: 8,
            }}
          />

          <View layoutStrategy="flexRowBetweenCenter" margin="12 0 0 0">
            <Text category="p2" i18nKey={'multiPeriodString.repayDate'} status="control" />
            <Text category="p2" margin="8 0 0 0" textContent={repayDate} status="control" />
          </View>

          {isOverdue && (
            <View layoutStrategy="flexRowBetweenCenter">
              <Text
                margin="12 0 0 0"
                padding="4 6 4 6"
                style={{
                  borderRadius: 6,
                  borderWidth: 1,
                  borderColor: 'danger-color-500',
                  backgroundColor: 'danger-color-100',
                }}
                category="p2"
                status="danger"
                textContent={`Vencido: ${overdueDay} días`}
              />
            </View>
          )}
        </View>
      </View>
    );
  }, [repayDetail, repayDetailModalVisible, onChangeRepayDetailModalState, repayShowTipTextList]);

  // 线上还款
  const $onlineRepay = useMemo(() => {
    return (
      <PayTypeItemView
        key={'online'}
        titleI18nKey={Strings.repaymentString.switch_online}
        noteI18nKey={Strings.repaymentString.freeFee}
        onPress={getRepayChannelConfigAndJumpOnline}>
        <View layoutStrategy="flexRowBetweenCenter">
          <View
            height={40}
            padding="4 8 4 8"
            layoutStrategy="flexColumnCenterCenter"
            style={{ backgroundColor: 'background-color-100', borderRadius: 8 }}>
            <Text
              category="p1"
              bold={'600'}
              textContent={String(onlineRepayDetail.clabe).toFormatClabe()}
            />
          </View>
          <Button status="primary" onPress={onCopyClabe} textI18nKey="btnString.copy" />
        </View>
      </PayTypeItemView>
    );
  }, [onlineRepayDetail.clabe]);

  // 线下还款
  const $offlineRepay = useMemo(() => {
    return (
      <PayTypeItemView
        key={'offline'}
        titleI18nKey={Strings.repaymentString.cardThreeTitle}
        onPress={getRepayChannelConfigAndJumpOffline}></PayTypeItemView>
    );
  }, []);

  // 一键代扣还款
  const $directDebitRepay = useMemo(() => {
    return (
      <PayTypeItemView
        key={'directDebit'}
        titleI18nKey={Strings.repaymentString.directDebitTitle}
        noteI18nKey={Strings.repaymentString.freeFee}
        disabled={repayDetail?.directDebitStatus === EDirectDebitStatus.PROCESSING}
        onPress={jumpDirectDebit}>
        {$directDebitTipView}
      </PayTypeItemView>
    );
  }, [repayDetail, $directDebitTipView]);

  const $repayChannelCard = useMemo(() => {
    // 展示的渠道卡片
    const channelCard = [];
    if (!!repayDetail?.repayTypeChannels?.length) {
      for (let repayChannel of repayDetail?.repayTypeChannels) {
        switch (repayChannel) {
          case ERepaymentChannel.ONLINE:
            channelCard.push($onlineRepay);
            break;
          case ERepaymentChannel.OFFLINE:
            channelCard.push($offlineRepay);
            break;
          case ERepaymentChannel.DIRECT_DEBIT:
            channelCard.push($directDebitRepay);
            break;
        }
      }
    }

    return (
      <View margin="16 0 0 0" style={{ overflow: 'hidden' }} cardType="baseType">
        <LinearGradient
          style={{
            marginTop: 1,
            marginHorizontal: 1,
            paddingHorizontal: 11,
            borderRadius: 8,
            paddingTop: 12,
          }}
          start={{ x: 0, y: 0 }}
          end={{ x: 0, y: 1 }}
          colors={['#F0F4FF', '#ffffff']}>
          <Text
            category="p1"
            style={{
              color: 'text-color-800',
            }}
            i18nKey={'repaymentString.cardTwoTitle'}
          />
        </LinearGradient>
        <Divider style={{ width: undefined }} margin="12 12 12 12" />

        <View padding="0 12 8 12">
          {channelCard}

          <View layoutStrategy="flexRowStartCenter" padding="0 0 8 0">
            <Image
              margin="0 4 0 0"
              style={{
                tintColor: 'fill-color-500',
              }}
              name="_vector"
            />
            <Text
              category="p2"
              margin="0 0 0 0"
              padding="0 0 0 0"
              style={{
                color: 'text-color-600',
              }}
              i18nKey={'repaymentString.cardTwoSubTitle'}
            />
          </View>
        </View>
      </View>
    );
  }, [repayDetail, onlineRepayDetail]);

  return (
    <>
      <Layout level="1" pLevel="0" topCompensateColor="primary-color-500">
        <TopNavigation
          pageKey={HitPointEnumsSpace.EPageKey.P_INSTALLMENT_REPAY}
          showLogoAction={true}
          type="primary"
          bottomLine={false}
          isShowVip={true}
          showMessage
        />
        <ScrollView
          overScrollMode={'never'}
          refreshControl={
            <RefreshControl refreshing={refreshing} colors={['#4C6EFF']} onRefresh={onRefresh} />
          }>
          <View>
            <View
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                height: 150,
                backgroundColor: 'primary-color-500',
              }}></View>
            <View margin="0 16 12 16">
              {$repayDetailCard}
              <MultiPeriodRepayDetailInfoCard repayDetail={repayDetail} />
              {$repayChannelCard}
            </View>
            <VipUpLevelTipCard pageKey={HitPointEnumsSpace.EPageKey.P_INSTALLMENT_REPAY} />
          </View>
        </ScrollView>
        <AccountUpdateModal
          visible={repayMentAccountVisible}
          onClose={onCloseRepayMentAccount}
          clabe={onlineRepayDetail.clabe}
        />
      </Layout>
      <GpReviewModal delay={repayMentAccountVisible} />
    </>
  );
};
