import React, { ReactElement, forwardRef, memo, useMemo } from 'react';
import { View, Text } from '@/components';

interface IProp {
  time: number;
}

export type RefType = {
  startCountDown: Function;
  clearCountDown: Function;
};
const CountDown = (props: IProp): ReactElement => {
  const { time } = props;
  const hour = useMemo(() => {
    if (time == 0) {
      return '--';
    }
    return String(Math.floor(time / 3600).toFixed(0)).padStart(2, '0');
  }, [time]);

  const minute = useMemo(() => {
    if (time == 0) {
      return '--';
    }
    return String(Math.floor((time % 3600) / 60).toFixed(0)).padStart(2, '0');
  }, [time]);

  const second = useMemo(() => {
    if (time == 0) {
      return '--';
    }
    return String((time % 60).toFixed(0)).padStart(2, '0');
  }, [time]);

  // if (!isStart) {
  //   return <></>;
  // }

  return (
    <View layoutStrategy="flexRowStartCenter">
      <Text
        width={18}
        height={18}
        status="control"
        category="c1"
        textContent={hour}
        isCenter={true}
        style={{ borderRadius: 4, backgroundColor: 'danger-color-500' }}
      />
      <Text status="danger" margin="0 6 0 6" textContent={':'} />
      <Text
        category="c1"
        status="control"
        width={18}
        height={18}
        textContent={minute}
        isCenter={true}
        style={{ borderRadius: 4, backgroundColor: 'danger-color-500' }}
      />
      <Text status="danger" margin="0 6 0 6" textContent={':'} />
      <Text
        category="c1"
        status="control"
        width={18}
        height={18}
        textContent={second}
        isCenter={true}
        style={{ borderRadius: 4, backgroundColor: 'danger-color-500' }}
      />
    </View>
  );
};

export default memo(CountDown);
