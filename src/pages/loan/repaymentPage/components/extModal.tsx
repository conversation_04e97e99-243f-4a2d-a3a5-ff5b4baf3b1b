import { Image, CommonModal, Text, View, Button, ImageBackground, Divider } from '@/components';
import { OrderVOSpace } from '@/types';
import React, { useMemo } from 'react';
import { TouchableOpacity } from 'react-native';
import CountDown from './CountDown';
import { useNameSpace } from '@/i18n';

interface IProps {
  changeDialogVisibe: () => void;
  visible: boolean;
  repayDetail: OrderVOSpace.BillDetailDataType;
  onlineRepayDetail: OrderVOSpace.RepayOnlineDataType;
  getRepayChannelConfigAndJumpOnline: () => void;
  onCopyClabe: () => void;
  countdown: number;
}

export default React.memo((props: IProps) => {
  const {
    visible,
    repayDetail,
    onlineRepayDetail,
    getRepayChannelConfigAndJumpOnline,
    onCopyClabe,
    changeDialogVisibe,
    countdown,
  } = props;

  const t = useNameSpace('extString').t;

  const $repayDetailInfo = useMemo(() => {
    const { extPaidAmount, extRemainAmount } = repayDetail;

    return (
      <View>
        <ImageBackground
          padding="0 0 0 12"
          style={{
            borderTopLeftRadius: 8,
            borderTopRightRadius: 8,
            overflow: 'hidden',
            flexDirection: 'row',
            justifyContent: 'space-between',
          }}
          name="_extModalTopBg">
          <View margin="12 0 0 0" height={70} layoutStrategy="flexColumnBetweenStart">
            <Text category="p2" status="control" i18nKey="extString.modalTitle" />
            <Text style={{ verticalAlign: 'bottom' }}>
              <Text
                margin="0 6 0 0"
                status="control"
                category="h2"
                bold="bold"
                textContent={String(extRemainAmount).toFormatFinance()}
              />
              <Text
                status="control"
                category="c2"
                textContent={t('extAmount', {
                  amount: String(extPaidAmount).toFormatFinance(false),
                })}
              />
            </Text>
          </View>
          <View
            margin="12 0 0 0"
            padding="4 8 4 8"
            height={32}
            style={{
              backgroundColor: 'background-color-0',
              borderTopLeftRadius: 4,
              borderBottomLeftRadius: 4,
            }}>
            <CountDown time={countdown} />
          </View>
        </ImageBackground>

        <TouchableOpacity onPress={getRepayChannelConfigAndJumpOnline}>
          <ImageBackground
            margin="-8 0 0 0"
            padding="0 12 0 12"
            style={{
              borderTopLeftRadius: 8,
              borderTopRightRadius: 8,
              overflow: 'hidden',
              justifyContent: 'center',
            }}
            name="_extModalPayChannelBg">
            <View layoutStrategy="flexRowBetweenCenter">
              <Text bold={'600'} i18nKey="extString.trans" />
              <View layoutStrategy="flexRowStartCenter">
                <Image margin="0 8 0 0" resizeMode="contain" name="_speIcon" />
              </View>
            </View>
          </ImageBackground>
        </TouchableOpacity>
        <Text
          margin="12 12 12 12"
          category="c1"
          style={{ color: 'text-color-600' }}
          i18nKey="extString.transTip"
        />
        <Divider style={{ backgroundColor: 'line-color-100' }}></Divider>
        <Text margin="12 0 0 12" textContent="CLABE" />
        <View margin="16 12 24 12" layoutStrategy="flexRowBetweenCenter">
          <View
            height={40}
            padding="4 8 4 8"
            layoutStrategy="flexColumnCenterCenter"
            style={{ backgroundColor: 'background-color-100', borderRadius: 8 }}>
            <Text
              category="p1"
              bold={'600'}
              textContent={String(onlineRepayDetail.clabe).toFormatClabe()}
            />
          </View>
          <Button height={24} status="primary" onPress={onCopyClabe} textI18nKey="btnString.copy" />
        </View>
      </View>
    );
  }, [
    repayDetail,
    onlineRepayDetail,
    getRepayChannelConfigAndJumpOnline,
    onCopyClabe,
    changeDialogVisibe,
    countdown,
    t,
  ]);
  return (
    <CommonModal
      visible={visible}
      onBackdropPress={changeDialogVisibe}
      cancelCallback={changeDialogVisibe}
      isBottomIconColse={true}
      hasLinearGradient={false}>
      {$repayDetailInfo}
    </CommonModal>
  );
});
