/** 还款账号更新 */
import { Image, Text, View, Button, CommonModal } from '@/components';
import React, { useEffect, useState, useCallback } from 'react';
import { Colors } from '@/themes';
import { Strings } from '@/i18n';

interface IProps {
  visible: boolean;
  onClose: () => void;
  clabe: string;
}
/**
 * 还款账号更新提示
 */
export default React.memo((props: IProps) => {
  const { visible, onClose, clabe } = props;
  return (
    <CommonModal visible={visible}>
      <View style={{ borderRadius: 8 }}>
        <Image
          name="_repaymentAccountModalHeaderBg"
          style={{
            borderTopLeftRadius: 8,
            borderTopRightRadius: 8,
            width: '100%',
            position: 'absolute',
            left: 0,
            top: 0,
          }}
          resizeMode="cover"
        />
        <View style={{ height: 178 }} />
        <View padding="12 32 24 32" layoutStrategy="flexColumnCenterCenter">
          <Text
            i18nKey={Strings.repaymentString.accountModalTitle}
            category="h3"
            bold="bold"
            style={{ color: Colors.PRIMARY_COLOR_700 }}
          />
          <Text
            padding="16 0 0 0"
            style={{ color: Colors.TEXT_COLOR_800 }}
            i18nKey={Strings.repaymentString.accountModalDesc_1}
            isCenter
          />
          <Text textContent={clabe} style={{ color: Colors.DANGER_COLOR_500 }} isCenter />
          <View padding="8 0 0 0" layoutStrategy="flexRowStart">
            <Text
              padding="0 4 0 0"
              style={{ color: Colors.TEXT_COLOR_600, fontStyle: 'italic' }}
              textContent="*"
              category="c2"
            />
            <Text
              i18nKey={Strings.repaymentString.accountModalDesc_2}
              style={{ color: Colors.TEXT_COLOR_600, fontStyle: 'italic' }}
              category="c2"
            />
          </View>
          <Button
            style={{ width: '100%' }}
            margin="16 0 0 0"
            textI18nKey={Strings.btnString.OK}
            onPress={onClose}
          />
        </View>
      </View>
    </CommonModal>
  );
});
