/**
 * @description 支付方式item
 * @param {string} titleI18nKey 标题
 * @param {string} noteI18nKey 备注
 * @param {React.ReactNode} children 内容元素
 */
import { Pressable, Dimensions } from 'react-native';
import { View, Text, Image, DashedLine } from '@/components';
import { Colors } from '@/themes';

const PayTypeItemView = ({
  titleI18nKey,
  noteI18n<PERSON>ey,
  children,
  onPress,
  disabled = false,
}: {
  titleI18nKey: string;
  onPress?: () => void;
  noteI18nKey?: string;
  children?: React.ReactNode;
  disabled?: boolean;
}) => {
  return (
    <>
      <Pressable onPress={onPress} disabled={disabled}>
        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}>
          <View layoutStrategy="flexRowStartCenter">
            <View width={4} height={12} style={{ backgroundColor: Colors.PRIMARY_COLOR_500 }} />
            <Text
              margin="0 0 0 6"
              category="p1"
              i18nKey={titleI18nKey}
              style={{
                color: 'text-color-800',
                fontSize: 14,
              }}
            />
          </View>
          <View layoutStrategy="flexRowStartCenter">
            <Text
              margin="0 0 0 12"
              category="c1"
              i18nKey={noteI18nKey}
              style={{
                color: 'text-color-600',
                fontSize: 10,
              }}
            />
            <Image
              name="_greyRightIcon"
              style={{ tintColor: disabled ? Colors.BACKGROUND_COLOR_100 : Colors.TEXT_COLOR_800 }}
            />
          </View>
        </View>
      </Pressable>

      <View style={{ display: !!children ? 'flex' : 'none' }} margin="16 0 0 0">
        {children}
      </View>
      <DashedLine
        height={0.5}
        width={Dimensions.get('window').width * 0.88}
        isRotate={false}
        dashGap={6}
        dashColor="line-color-200"
        style={{
          marginTop: 16,
          marginBottom: 16,
        }}
      />
    </>
  );
};

export default PayTypeItemView;
