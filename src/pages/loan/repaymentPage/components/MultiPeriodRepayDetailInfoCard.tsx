/**
 * @description: 分期还款明细详情卡片
 */
import { DashedLine, Image, Text, View } from '@/components';
import { HitPointEnumsSpace } from '@/enums';
import { OrderVOSpace } from '@/types';
import { TrackEvent } from '@/utils';
import React, { useCallback, useState } from 'react';
import { TouchableWithoutFeedback } from 'react-native';

interface IProps {
  repayDetail: OrderVOSpace.MultiPeriodBillDetailDataType;
}

export default React.memo((props: IProps) => {
  const { repayDetail } = props;

  const [isSpreadOut, setIsSpreadOut] = useState<boolean>(true);

  const onChangeIsSpreadOut = useCallback(() => {
    setIsSpreadOut(preState => {
      TrackEvent.trackCommonEvent(
        {
          p: HitPointEnumsSpace.EPageKey.P_INSTALLMENT_REPAY,
          e: HitPointEnumsSpace.EEventKey.BTN_EXPAND_REPAYMENT_PLAN,
        },
        !preState ? '1' : '0',
      );
      return !preState;
    });
  }, []);

  const { loanDate, days, loanCostPerPeriods } = repayDetail;

  const renderRepayState = useCallback((status: string, overdueDay: string = '') => {
    switch (status) {
      case '0':
        return (
          <Text
            status="control"
            category="p2"
            padding="2 4 2 4"
            style={{ borderRadius: 4, backgroundColor: 'text-color-500' }}
            i18nKey="multiPeriodString.waiting"
          />
        );
      case '1':
        return (
          <Text
            status="control"
            category="p2"
            padding="2 4 2 4"
            style={{ borderRadius: 4, backgroundColor: 'info-color-500' }}
            i18nKey="multiPeriodString.process"
          />
        );
      case '2':
        return (
          <Text
            status="control"
            category="p2"
            padding="2 4 2 4"
            style={{ borderRadius: 4, backgroundColor: 'danger-color-500' }}
            i18nKey="multiPeriodString.failure"
            textContent={String(overdueDay).toFormatDay()}
          />
        );
      case '3':
        return (
          <Text
            status="control"
            category="p2"
            padding="2 4 2 4"
            style={{ borderRadius: 4, backgroundColor: 'text-color-500' }}
            i18nKey="multiPeriodString.success"
          />
        );
    }
  }, []);

  return (
    <View
      margin="12 0 0 0"
      padding="12 12 12 12"
      style={{ borderRadius: 8, backgroundColor: 'background-color-0' }}>
      <View margin="0 0 0 0" layoutStrategy="flexRowBetweenCenter">
        <Text isCenter i18nKey={'repaymentString.repayDetailTitle'} />
        <TouchableWithoutFeedback onPress={onChangeIsSpreadOut}>
          <View layoutStrategy="flexRowBetweenCenter">
            <Image name={isSpreadOut ? '_triangleUpIcon' : '_triangleDownIcon'} />
          </View>
        </TouchableWithoutFeedback>
      </View>

      {isSpreadOut && (
        <View>
          <View layoutStrategy="flexRowBetweenCenter" margin="16 0 0 0">
            <Text
              category="p2"
              i18nKey={'multiPeriodString.loanDate'}
              style={{ color: 'text-color-600' }}
            />
            <Text
              category="p2"
              textContent={String(loanDate)}
              style={{ color: 'text-color-800' }}
            />
          </View>
          <View layoutStrategy="flexRowBetweenCenter" margin="16 0 0 0">
            <Text
              category="p2"
              i18nKey={'multiPeriodString.days'}
              style={{ color: 'text-color-600' }}
            />
            <Text
              category="p2"
              textContent={String(days).toFormatMonth()}
              style={{ color: 'text-color-800' }}
            />
          </View>
          <View
            margin="12 0 0 0"
            padding="14 12 14 12"
            style={{
              borderRadius: 8,
              backgroundColor: 'info-color-100',
            }}
            layoutStrategy="flexColumnStart">
            {loanCostPerPeriods.map((item, index) => {
              return (
                <View key={index.toString()} layoutStrategy="flexRowStart">
                  <View margin="0 16 0 0" layoutStrategy="flexColumnStartCenter">
                    <View
                      key={index.toString()}
                      style={{
                        width: 12,
                        height: 12,
                        borderRadius: 99,
                        backgroundColor: 'primary-color-500',
                      }}
                    />
                    {index !== loanCostPerPeriods.length - 1 && (
                      <DashedLine axis="vertical" width={0.5} style={{ flex: 1 }} />
                    )}
                  </View>
                  <View margin="-6 0 0 0" style={{ flex: 1 }} layoutStrategy="flexColumnStart">
                    <View layoutStrategy="flexRowStartCenter">
                      <Text
                        margin="0 12 0 0"
                        category="p2"
                        style={{ color: 'text-color-600' }}
                        i18nKey="multiPeriodString.repayDate">
                        {item.curPeriodRepayDate}
                      </Text>
                      {renderRepayState(item.curStatus, item.curPeriodOverdueDay)}
                    </View>
                    <View layoutStrategy="flexRowBetweenCenter">
                      <Text category="p2" i18nKey="multiPeriodString.amount" />
                      <Text
                        category="p2"
                        style={{ color: 'text-color-600' }}
                        textContent={String(item.curPeriodPrincipal).toFormatFinance(false)}
                      />
                    </View>
                    <View layoutStrategy="flexRowBetweenCenter">
                      <Text
                        category="p2"
                        style={{ color: 'text-color-600' }}
                        i18nKey="multiPeriodString.serverFee"
                      />
                      <Text
                        category="p2"
                        style={{ color: 'text-color-600' }}
                        textContent={String(item.curPeriodProcessFee).toFormatFinance(false)}
                      />
                    </View>
                    <View layoutStrategy="flexRowBetweenCenter">
                      <Text
                        category="p2"
                        style={{ color: 'text-color-600' }}
                        i18nKey="multiPeriodString.serverIVA"
                      />
                      <Text
                        category="p2"
                        style={{ color: 'text-color-600' }}
                        textContent={String(item.curPeriodProcessFeeVat).toFormatFinance(false)}
                      />
                    </View>
                    {item.curStatus == '2' && (
                      <View layoutStrategy="flexRowBetweenCenter">
                        <Text
                          category="p2"
                          style={{ color: 'text-color-600' }}
                          i18nKey="multiPeriodString.overdueFee"
                        />
                        <Text
                          category="p2"
                          style={{ color: 'text-color-600' }}
                          textContent={String(item.curPeriodCollectFeeAndVat).toFormatFinance(
                            false,
                          )}
                        />
                      </View>
                    )}
                    {item.curStatus == '2' && (
                      <View layoutStrategy="flexRowBetweenCenter">
                        <Text
                          category="p2"
                          style={{ color: 'text-color-600' }}
                          i18nKey="multiPeriodString.overdueIVA"
                        />
                        <Text
                          category="p2"
                          style={{ color: 'text-color-600' }}
                          textContent={String(item.curPeriodOverdueFeeAndVat).toFormatFinance(
                            false,
                          )}
                        />
                      </View>
                    )}
                  </View>
                </View>
              );
            })}
          </View>
        </View>
      )}
    </View>
  );
});
