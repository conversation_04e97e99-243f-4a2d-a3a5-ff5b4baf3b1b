import { Image, CommonModal, Text, View } from '@/components';
import { OrderVOSpace } from '@/types';
import React, { useMemo } from 'react';

interface IProps {
  changeDialogVisibe: () => void;
  visible: boolean;
  repayDetail: OrderVOSpace.BillDetailDataType;
}

export default React.memo((props: IProps) => {
  const { visible, repayDetail, changeDialogVisibe } = props;

  const $repayDetailInfo = useMemo(() => {
    const {
      remainPrincipal,
      remainInterest,
      remainInterestVat,
      remainProcessFee,
      remainProcessFeeVat,
      remainCollectFee,
      remainCollectFeeVat,
      remainOverdueFee,
      remainOverdueFeeVat,
      realRepayAmount,
      remindRepayAmount,
      realRemindRepayAmount,
      deductionAmount,
      deductionFlag,
      overdueDay,
      annulAmount,
    } = repayDetail;
    return (
      <View margin="24 16 12 16">
        <Text category="h3" isCenter i18nKey={'repaymentString.repayDetailTitle'} />
        {Number(remainPrincipal) > 0 && (
          <View layoutStrategy="flexRowBetweenCenter" margin="16 0 0 0">
            <Text
              category="p2"
              i18nKey={'repaymentString.loanAmount'}
              style={{ color: 'text-color-600' }}
            />
            <Text
              category="p2"
              margin="16 0 0 0"
              textContent={String(remainPrincipal).toFormatFinance()}
              style={{ color: 'text-color-800' }}
            />
          </View>
        )}
        {Number(Number(remainInterest) + Number(remainInterestVat)) > 0 && (
          <View layoutStrategy="flexRowBetweenCenter" margin="16 0 0 0">
            <Text
              category="p2"
              i18nKey={'repaymentString.interest'}
              style={{ color: 'text-color-600' }}
            />
            <Text
              category="p2"
              textContent={String(
                Number(Number(remainInterest) + Number(remainInterestVat)),
              ).toFormatFinance()}
              style={{ color: 'text-color-800' }}
            />
          </View>
        )}
        {Number(Number(remainProcessFee) + Number(remainProcessFeeVat)) > 0 && (
          <View layoutStrategy="flexRowBetweenCenter" margin="16 0 0 0">
            <Text
              category="p2"
              i18nKey={'repaymentString.serviceFee'}
              style={{ color: 'text-color-600' }}
            />
            <Text
              category="p2"
              textContent={String(
                parseFloat((Number(remainProcessFee) + Number(remainProcessFeeVat)).toFixed(10)),
              ).toFormatFinance()}
              style={{ color: 'text-color-800' }}
            />
          </View>
        )}
        {Number(Number(remainCollectFee) + Number(remainCollectFeeVat)) > 0 && (
          <View layoutStrategy="flexRowBetweenCenter" margin="16 0 0 0">
            <Text
              category="p2"
              i18nKey={'repaymentString.onceTimePenaltyFee'}
              style={{ color: 'text-color-600' }}
            />
            <Text
              category="p2"
              textContent={String(
                Number(Number(remainCollectFee) + Number(remainCollectFeeVat)),
              ).toFormatFinance()}
              style={{ color: 'text-color-800' }}
            />
          </View>
        )}
        {Number(Number(remainOverdueFee) + Number(remainOverdueFeeVat)) > 0 && (
          <View layoutStrategy="flexRowBetweenCenter" margin="16 0 0 0">
            <Text
              category="p2"
              i18nKey={'repaymentString.totalOverdueFee'}
              style={{ color: 'text-color-600' }}
            />
            <Text
              category="p2"
              textContent={String(
                Number(Number(Number(remainOverdueFee) + Number(remainOverdueFeeVat))).toFixed(2),
              ).toFormatFinance()}
              style={{ color: 'text-color-800' }}
            />
          </View>
        )}
        <View
          margin="16 0 0 0"
          height={1}
          width={'100%'}
          style={{
            backgroundColor: 'line-color-100',
          }}
        />
        {/* {Number(annulAmount) > 0 && (
          <>
            <View layoutStrategy="flexRowBetweenCenter" margin="16 0 0 0">
              <Text
                category="p2"
                i18nKey={'repaymentString.discountAmount'}
                style={{ color: 'text-color-600' }}
              />
              <Text
                category="p2"
                textContent={String(annulAmount).toFormatFinance()}
                style={{
                  color: 'danger-color-500',
                  textDecorationLine: 'line-through',
                }}
              />
            </View>
          </>
        )} */}
        {/* {deductionFlag === 'YES' && Number(deductionAmount) > 0 && Number(overdueDay) <= 0 && (
          <View layoutStrategy="flexRowBetweenCenter" margin="16 0 0 0">
            <Text
              category="p2"
              i18nKey={'couponString.couponDeduction'}
              style={{ color: 'text-color-600' }}
            />
            <Text
              category="p2"
              textContent={String(deductionAmount).toFormatFinance()}
              style={{
                color: 'danger-color-500',
              }}
            />
          </View>
        )} */}
        <View layoutStrategy="flexRowBetweenCenter" margin="16 0 0 0">
          <Text
            category="p2"
            i18nKey={'repaymentString.realRepayAmount'}
            style={{ color: 'text-color-600' }}
          />
          <View layoutStrategy="flexRowStartCenter" margin="0 0 0 16">
            {(Number(annulAmount) > 0 ||
              (deductionFlag === 'YES' &&
                Number(deductionAmount) > 0 &&
                Number(overdueDay) <= 0)) && (
              <>
                <Text
                  margin="0 6 0 0"
                  category="c1"
                  textContent={String(remindRepayAmount).toFormatFinance()}
                  style={{
                    color: 'text-color-600',
                    textDecorationLine: 'line-through',
                  }}
                />
                <Image name="_mountDownIcon" style={{ marginRight: 6 }} />
              </>
            )}
            <Text
              category="p2"
              textContent={String(Number(realRemindRepayAmount)).toFormatFinance()}
              style={{ color: 'text-color-800' }}
            />
          </View>
        </View>
        {/* {deductionFlag === 'YES' && Number(deductionAmount) > 0 && Number(overdueDay) <= 0 && (
          <View margin="6 0 0 0" layoutStrategy="flexColumnStartCenter">
            <View width={'100%'} layoutStrategy="flexRowBetweenCenter">
              <View />
              <Image
                margin="0 12 0 0"
                name="_polygonIcon"
                style={{
                  tintColor: 'info-color-100',
                }}
              />
            </View>
            <View
              width={'100%'}
              margin="-3 0 0 0"
              padding="6 12 6 12"
              layoutStrategy="flexRowStartCenter"
              style={{
                borderRadius: 6,
                backgroundColor: 'info-color-100',
              }}>
              <Image margin="0 12 0 0" name={'_infoIconYellow'} />

              <Text
                style={{ flex: 1 }}
                category="c1"
                i18nKey="couponString.couponEffectiveRestrictions"
              />
            </View>
          </View>
        )} */}
      </View>
    );
  }, [repayDetail]);
  return (
    <CommonModal
      visible={visible}
      onBackdropPress={changeDialogVisibe}
      hasLinearGradient={false}
      confirmBtnName="btnString.OK"
      confirmCallback={changeDialogVisibe}>
      {$repayDetailInfo}
    </CommonModal>
  );
});
