import { HitPointEnumsSpace } from '@/enums';
import { useOnInit } from '@/hooks';
import { BaseInfoManager } from '@/managers';
import { Toast } from '@/nativeComponents';
import { RouterConfig } from '@/routes';
import { submitCreditRefuseReason } from '@/server';
import { useCallback, useState } from 'react';
import { useNameSpace } from '@/i18n';
import { nav, TrackEvent } from '@/utils';

export default function useData() {
  const [optionReason, setOptionReason] = useState<
    null | 'unsatisfied_amount' | 'unsatisfied_fee' | 'other'
  >(null);
  const [inputReason, setInputReason] = useState<string>('');

  const t = useNameSpace().t;
  /** 初始化方法 */
  const { loading, refreshing, setRefreshing, onRefresh } = useOnInit({
    callback: async () => {
      BaseInfoManager.changeLoadingModalVisible(true);
      loading.current = true;
      loading.current = false;
      BaseInfoManager.changeLoadingModalVisible(false);
    },
    refreshCallback: async () => {
      BaseInfoManager.changeLoadingModalVisible(true);
      loading.current = true;
      loading.current = false;
      BaseInfoManager.changeLoadingModalVisible(false);
    },
    isBackAutoRefresh: false,
    pageKey: HitPointEnumsSpace.EPageKey.P_CREDIT_REFUSE_QUESTION,
  });

  const handleBack = useCallback(() => {
    nav.navigationGoBack();
  }, []);

  const handleNext = useCallback(async () => {
    if (!loading.current) {
      loading.current = true;
      // 确认重新贷款
      if (optionReason !== null) {
        if (optionReason === 'other' && inputReason === '') {
          // 提示必填项未完成所有必填项
          Toast(t('rkQustionString.formNotCompletedTip'));
        } else {
          const content = (() => {
            switch (optionReason) {
              case 'unsatisfied_amount':
                return '0';
              case 'unsatisfied_fee':
                return '1';
              case 'other':
                return '2';
              default:
                return '';
            }
          })();
          TrackEvent.trackCommonEvent(
            {
              p: HitPointEnumsSpace.EPageKey.P_CREDIT_REFUSE_QUESTION,
              e: HitPointEnumsSpace.EEventKey.E_BTN_SUBMIT,
            },
            content,
          );
          BaseInfoManager.changeLoadingModalVisible(true);
          const { code, data } = await submitCreditRefuseReason({
            optionReason,
            inputReason: optionReason === 'other' ? inputReason : '',
          });
          if (code === 0) {
            const {
              isRetain,
              retainMethod,
              originalQuota,
              currentQuota,
              couponAmount,
              couponType,
            } = data;
            if (isRetain === 'YES') {
              nav.resetRouteNavigate(RouterConfig.COMFIRM_LOAN as any, {
                isRetain,
                retainMethod,
                originalQuota,
                currentQuota,
                couponAmount,
                couponType,
              });
            } else {
              nav.resetRouteNavigateCanGoback(RouterConfig.CREDIT_REFUSE_CONFRIM as any);
            }
          }
          BaseInfoManager.changeLoadingModalVisible(false);
        }
      } else {
        // 提示必填项未完成所有必填项
        Toast(t('rkQustionString.formNotCompletedTip'));
      }
    }
    loading.current = false;
  }, [t, optionReason, inputReason]);

  return {
    optionReason,
    inputReason,
    onRefresh,
    refreshing,
    handleBack,
    handleNext,
    setOptionReason,
    setInputReason,
  };
}
