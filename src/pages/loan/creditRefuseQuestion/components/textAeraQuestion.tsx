import { PrefixInput, Text, View } from '@/components';
import { EvidenceVOSpace } from '@/types';
import React, { useMemo, useState } from 'react';
import { useNameSpace } from '@/i18n';

type IProps = {
  onChangeAnswer: (answer: any) => void;
};

const TextAeraQuestion = React.memo(({ onChangeAnswer }: IProps) => {
  const [value, setValue] = useState<string>('');
  const onChageValue = (value: string = '') => {
    setValue(value);
    onChangeAnswer(value);
  };

  const t = useNameSpace().t;

  const $renderTextLengthTip = useMemo(() => {
    const maxLength = 100;
    const textTip = maxLength ? `${value.length}/${maxLength}` : `${value.length}`;
    return (
      <Text
        margin="8 0 0 0"
        style={{
          position: 'absolute',
          bottom: 0,
          right: 0,
          color: 'text-color-600',
        }}
        category="c1"
        textContent={textTip}
      />
    );
  }, [value]);

  return (
    <View width={'100%'} padding="0 0 16 0">
      <PrefixInput
        style={{ height: 120, backgroundColor: 'background-color-0' }}
        textStyle={{
          textAlignVertical: 'top',
        }}
        placeholder={t('creditRefuseQuestionString.inputPlaceholder')}
        multiline
        numberOfLines={5}
        maxLength={100}
        setValue={onChageValue}
        value={value}
      />
      {$renderTextLengthTip}
    </View>
  );
});

export default TextAeraQuestion;
