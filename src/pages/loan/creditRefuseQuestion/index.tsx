/* eslint-disable react-native/no-inline-styles */

import { Button, Image, Layout, Text, View, FromUI, TopNavigation, Card } from '@/components';
import React, { ReactElement, useState } from 'react';
import { RefreshControl, ScrollView } from 'react-native';
import useData from './useData';
import { EvidenceVOSpace } from '@/types';
import TextAeraQuestion from './components/textAeraQuestion';
import { useNameSpace } from '@/i18n';
const { RadioGroup, RadioButton } = FromUI;

const ANSWER_LIST = [
  {
    labelKey: 'creditRefuseQuestionString.answer1',
    value: 'unsatisfied_amount',
  },
  {
    labelKey: 'creditRefuseQuestionString.answer2',
    value: 'unsatisfied_fee',
  },
  { labelKey: 'creditRefuseQuestionString.answer3', value: 'other' },
];

export default (): ReactElement => {
  const { onRefresh, refreshing, handleBack, handleNext, setOptionReason, setInputReason } =
    useData();

  const $ButtonGroup = (
    <View layoutStrategy={'flexRowBetweenCenter'} margin="16 16 32 16">
      <Button
        appearance="outline"
        margin={'0 10 0 0'}
        onPress={handleBack}
        style={{ flex: 1 }}
        textI18nKey={'btnString.back'}
      />

      <Button
        appearance="filled"
        status="primary"
        onPress={handleNext}
        style={{ flex: 1 }}
        textI18nKey={'btnString.send'}
      />
    </View>
  );

  const [value, setValue] = useState<EvidenceVOSpace.TQuestionAnswerLabelItem | undefined>(
    undefined,
  );
  const onChageValue = (value?: EvidenceVOSpace.TQuestionAnswerLabelItem) => {
    setValue(value);
    if (value) {
      setOptionReason(value.value as any);
      if (value.value !== 'other') {
        setInputReason('');
      }
    }
  };

  const t = useNameSpace().t;

  const onChangeText = (text: string) => {
    setInputReason(text);
  };

  return (
    <>
      <Layout pLevel="0">
        <TopNavigation titleKey={'creditRefuseQuestionString.title'} bottomLine />
        <ScrollView
          fadingEdgeLength={10}
          refreshControl={
            <RefreshControl colors={['#4C6EFF']} refreshing={refreshing} onRefresh={onRefresh} />
          }>
          <View margin="16 16 16 16" cardType="baseType">
            <Card backgroundColor="primary-color-500" borderColor="primary-color-500">
              <Text status="control" category="p2" i18nKey="creditRefuseQuestionString.topTip" />
            </Card>
            <View
              margin="12 12 0 12"
              style={{
                flexDirection: 'column',
                alignItems: 'flex-start',
              }}>
              <Text
                margin="24 0 12 0"
                category="p1"
                style={{ color: 'text-color-700' }}
                i18nKey="creditRefuseQuestionString.questionTitle"
              />
              <RadioGroup checkedItem={value} onItemChecked={onChageValue}>
                {ANSWER_LIST.map((item, index) => {
                  const data = {
                    ...item,
                    label: t(item.labelKey),
                  };
                  return (
                    <View width={'100%'} padding="12 0 12 0" key={item.labelKey}>
                      <RadioButton
                        item={data}
                        style={{
                          marginTop: 13,
                        }}
                        labelStyle={{
                          marginBottom: item?.value === 'other' ? 0 : 24,
                        }}></RadioButton>
                    </View>
                  );
                })}
              </RadioGroup>
              {value?.value === 'other' && <TextAeraQuestion onChangeAnswer={onChangeText} />}
            </View>
          </View>
        </ScrollView>
        {$ButtonGroup}
      </Layout>
    </>
  );
};
