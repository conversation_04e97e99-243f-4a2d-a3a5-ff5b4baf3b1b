import { HitPointEnumsSpace } from '@/enums';
import { useOnInit } from '@/hooks';
import { useNameSpace } from '@/i18n';
import { BaseInfoManager, UserInfoManager } from '@/managers';
import { Toast } from '@/nativeComponents';
import { fetchReloanQuestionSubmit, fetchRLoanQuestionList } from '@/server';
import { nav, TrackEvent } from '@/utils';
import { useCallback, useState } from 'react';
import { EvidenceVOSpace } from '../../../types/VO/evidenceType';

const defalutQuestionList: EvidenceVOSpace.TQuestionItem[] = [];

export const useData = () => {
  const [questionList, setQuestionList] =
    useState<EvidenceVOSpace.TQuestionItem[]>(defalutQuestionList);
  const [questionGroupId, setQuestionGroupId] = useState<number>(0);
  const [questionAnswer, setQuestionAnswer] = useState<any>({});

  const t = useNameSpace().t;

  /** 初始化 */
  useOnInit({
    callback: async () => {
      // 获取问券列表
      await onGetRKQuestionList();
    },
    pageKey: HitPointEnumsSpace.EPageKey.P_INCREASE_CREDIT,
  });

  const onChangeAnswer = useCallback(
    (questionId: number, answer: any) => {
      // 应对于多级联场景, 需要删除多级联的子题目
      setQuestionAnswer((preState: any) => {
        const questionAnswerObj = {
          ...preState,
          [questionId]: answer,
        };
        questionList.map(questionItem => {
          let enable: boolean = true;
          // 题目的启用状态
          if (questionItem.enable) {
            const value = questionAnswerObj[questionItem.enable.parentId];
            if (value) {
              enable = String(value).includes(questionItem.enable.parentOptionValue);
            } else {
              enable = false;
            }
          }
          if (!enable) {
            // 如果是不启用的状态，把问题的答案同时置空
            questionAnswerObj[questionItem.questionId] = null;
          }
        });
        return questionAnswerObj;
      });
    },
    [questionList],
  );

  /** 获取问券列表 */
  const onGetRKQuestionList = async () => {
    BaseInfoManager.changeLoadingModalVisible(true);
    let result = await fetchRLoanQuestionList();

    if (result.code !== 0) {
      BaseInfoManager.changeLoadingModalVisible(false);
      return false;
    } else {
      setQuestionGroupId(result.data.qgId);
      setQuestionList(result.data.questions);
    }
    BaseInfoManager.changeLoadingModalVisible(false);
    return true;
  };

  const isAllCompleted = () => {
    let isComplete = true;
    questionList.forEach((questionItem: any) => {
      let enable: boolean = true;
      // 题目的启用状态
      if (questionItem.enable) {
        const value = questionAnswer[questionItem.enable.parentId];
        enable = String(value).includes(questionItem.enable.parentOptionValue);
      }
      if (enable && questionItem.required === 'YES') {
        // 启用并且为required的选项需要必填
        if (!questionAnswer[questionItem.questionId]) {
          isComplete = false;
        }
      }
    });

    return isComplete;
  };

  const handleNext = async () => {
    TrackEvent.trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_INCREASE_CREDIT,
        e: HitPointEnumsSpace.EEventKey.BTN_QUESTION_SUBMIT,
      },
      '1',
    );
    if (isAllCompleted()) {
      // 提交问券
      BaseInfoManager.changeLoadingModalVisible(true);
      const answerList = [];
      for (let id in questionAnswer) {
        if (questionAnswer[id]) {
          const answer: EvidenceVOSpace.TQuestionAnswerItem = {
            questionId: Number(id),
            optionKey: questionAnswer[id],
          };
          answerList.push(answer);
        }
      }
      const result = await fetchReloanQuestionSubmit({
        qgId: questionGroupId,
        answerList,
      });
      if (result.code === 0) {
        if (await UserInfoManager.updateUserState()) {
          nav.nextToTopRouter();
        }
      }
    } else {
      // 提示必填项未完成所有必填项
      Toast(t('rkQustionString.formNotCompletedTip'));
    }
    BaseInfoManager.changeLoadingModalVisible(false);
  };

  return {
    questionList,
    questionAnswer,
    onChangeAnswer,
    handleNext,
  };
};
