import { PrefixInput, Text, View } from '@/components';
import { EvidenceVOSpace } from '@/types';
import React, { useMemo, useState } from 'react';

type IProps = {
  questionItem: EvidenceVOSpace.TQuestionItem;
  index: number;
  onChangeAnswer: (questionId: number, answer: any) => void;
};

const TextInputQuestion = React.memo(({ questionItem, index, onChangeAnswer }: IProps) => {
  const [value, setValue] = useState<any>(null);
  const onChageValue = (value?: string) => {
    setValue(value);
    onChangeAnswer(questionItem.questionId, value);
  };
  /** 必填的提示标识 */
  const $renderRequireTip = useMemo(() => {
    return questionItem.required === 'YES' ? (
      <Text status="danger">*</Text>
    ) : (
      <Text category="c2" style={{ color: 'text-color-600' }} i18nKey="rkQustionString.opcional" />
    );
  }, []);

  const $renderTextLengthTip = useMemo(() => {
    const maxLength = questionItem?.limit?.textMaxLength;
    const textTip = maxLength ? `${value.length}/${maxLength}` : `${value.length}`;
    return (
      <Text
        style={{
          position: 'absolute',
          bottom: 0,
          right: 0,
          color: 'text-color-600',
        }}
        category="c1"
        textContent={textTip}
      />
    );
  }, [value, questionItem]);

  return (
    <View padding="0 0 16 0">
      <View margin="12 0 6 0" layoutStrategy="flexRowStartCenter">
        <Text
          category="h4"
          style={{ color: 'text-color-800', lineHeight: 22 }}
          status="basic"
          textContent={index + '. ' + questionItem.questionName}
        />
        {$renderRequireTip}
      </View>
      <PrefixInput
        setValue={onChageValue}
        value={value}
        maxLength={questionItem?.limit?.textMaxLength}
      />
      {$renderTextLengthTip}
    </View>
  );
});

export default TextInputQuestion;
