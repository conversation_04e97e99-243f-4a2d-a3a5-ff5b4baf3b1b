import { FromUI, Text, View } from '@/components';
import { EvidenceVOSpace } from '@/types';
import React, { useMemo, useState } from 'react';

const { RadioGroup, RadioButton } = FromUI;

type IProps = {
  questionItem: EvidenceVOSpace.TQuestionItem;
  index: number;
  onChangeAnswer: (questionId: number, answer: any) => void;
};

const RaidoBoxQuestion = React.memo(({ questionItem, index, onChangeAnswer }: IProps) => {
  const [value, setValue] = useState<any>(null);
  const onChageValue = (value?: EvidenceVOSpace.TQuestionAnswerLabelItem) => {
    setValue(value);
    if (value) {
      onChangeAnswer(questionItem.questionId, value.value);
    }
  };

  /** 必填的提示标识 */
  const $renderRequireTip = useMemo(() => {
    return questionItem.required === 'YES' ? (
      <Text status="danger">*</Text>
    ) : (
      <Text category="c2" style={{ color: 'text-color-600' }} i18nKey="rkQustionString.opcional" />
    );
  }, []);

  return (
    <>
      <View margin="12 0 6 0" layoutStrategy="flexRowStartCenter">
        <Text category="h4" style={{ color: 'text-color-800', lineHeight: 22 }}>
          {index + '. ' + questionItem.questionName}
          {$renderRequireTip}
        </Text>
      </View>
      <RadioGroup checkedItem={value} onItemChecked={onChageValue}>
        {questionItem.answer.map(item => (
          <RadioButton
            key={item.label}
            item={item}
            style={{
              marginTop: 8,
            }}
          />
        ))}
      </RadioGroup>
    </>
  );
});

export default RaidoBoxQuestion;
