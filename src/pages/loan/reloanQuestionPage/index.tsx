/**
 * @description 复贷问卷页
 */
import { Button, Layout, Text, TopNavigation, View } from '@/components';
import { EvidenceVOSpace, ScreenProps } from '@/types';
import { ReactElement, useCallback, useMemo, useState } from 'react';

import { ScrollView } from 'react-native';
import { useData } from './useData';

import CheckBoxQuestion from './components/checkBoxQuestion';
import RadioBoxQuestion from './components/radioBoxQuestion';
import TextAeraQuestion from './components/textAeraQuestion';
import TextInputQuestion from './components/textInputQuestion';

/** 复贷问卷页面 */
export default ({ navigation, route }: ScreenProps<{}>): ReactElement => {
  const { questionList, questionAnswer, onChangeAnswer, handleNext } = useData();

  /** 绑定提额的引导文案 */
  const $topCard = useMemo(() => {
    return (
      <View
        padding="8 16 8 16"
        style={{ backgroundColor: 'primary-color-500', borderRadius: 8 }}
        layoutStrategy="flexRowBetweenCenter">
        <Text
          i18nKey="reloanQuestionString.description"
          category="p1"
          status="control"
          style={{
            flex: 1,
          }}
        />
      </View>
    );
  }, []);

  const renderQuestion = useCallback(
    (
      questionItem: EvidenceVOSpace.TQuestionItem,
      index: number,
      onChangeAnswer: (questionId: number, answer: any) => void,
    ) => {
      switch (questionItem.questionType) {
        // 多选题
        case 'MULTI':
          return (
            <CheckBoxQuestion
              index={index}
              key={questionItem.questionId}
              questionItem={questionItem}
              onChangeAnswer={onChangeAnswer}
            />
          );
        // 单选题
        case 'SINGLE':
          return (
            <RadioBoxQuestion
              index={index}
              key={questionItem.questionId}
              questionItem={questionItem}
              onChangeAnswer={onChangeAnswer}
            />
          );
        // 单行文本框
        case 'TEXT':
          return (
            <TextInputQuestion
              index={index}
              key={questionItem.questionId}
              questionItem={questionItem}
              onChangeAnswer={onChangeAnswer}
            />
          );
        // 多行文本框
        case 'TEXTAREA':
          return (
            <TextAeraQuestion
              index={index}
              key={questionItem.questionId}
              questionItem={questionItem}
              onChangeAnswer={onChangeAnswer}
            />
          );
      }
    },
    [],
  );

  const $questionList = useMemo(() => {
    let index = 0;
    return questionList.map(questionItem => {
      let enable: boolean = true;
      // 题目的启用状态
      if (questionItem.enable) {
        const value = questionAnswer[questionItem.enable.parentId];
        enable = String(value).includes(questionItem.enable.parentOptionValue);
      }
      if (enable) {
        index++;
        return renderQuestion(questionItem, index, onChangeAnswer);
      } else {
        if (!enable) {
          // 如果是不启用的状态，把问题的答案同时置空
          return null;
        }
      }
    });
  }, [questionList, questionAnswer, onChangeAnswer]);

  return (
    <Layout pLevel="0" level="1">
      <TopNavigation titleKey="basicInfoString.generalInfo" />
      <View style={{ flex: 1, backgroundColor: 'background-color-200' }}>
        <ScrollView
          style={{
            marginHorizontal: 16,
            marginVertical: 16,
            backgroundColor: '#fff',
            borderBottomLeftRadius: 8,
            borderBottomRightRadius: 8,
          }}>
          {$topCard}
          <View margin="0 12 0 12">{$questionList}</View>
        </ScrollView>
      </View>
      <View
        padding="16 0 16 0"
        style={{
          backgroundColor: 'background-color-0',
        }}>
        <Button
          margin="0 32 0 32"
          status="primary"
          onPress={handleNext}
          textI18nKey="btnString.next"
        />
      </View>
    </Layout>
  );
};
