/* eslint-disable react/react-in-jsx-scope */
/* eslint-disable react-hooks/exhaustive-deps */

import { Button, Layout, TopNavigation, View } from '@/components';
import { BaseInfoManager } from '@/managers';
import { RouterConfig } from '@/routes';
import type { NavigationHelpers, Route } from '@react-navigation/core';
import { useCallback, useEffect, useMemo } from 'react';
import WebView from 'react-native-webview';
import { useOnInit } from '@/hooks';
import { HitPointEnumsSpace } from '@/enums';
import { trackCommonEvent } from '@/trackEvent';
import { nav } from '@/utils';

type RouteParams = {
  uri?: string;
  html?: string;
};

type ScreenProps = {
  route: Route<string, RouteParams>;
  navigation: NavigationHelpers<any>;
} & any;

/** 贷款合同页 */
export default ({ route, navigation }: ScreenProps) => {
  /** 初始化方法 */
  useOnInit({
    pageKey: HitPointEnumsSpace.EPageKey.P_CONTRACT,
  });

  const handleNext = useCallback(() => {
    trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_CREDIT_TBC,
        e: HitPointEnumsSpace.EEventKey.E_ACCEPT_LOAN,
      },
      '1',
    );
    nav.navigationGoBack();
  }, []);

  useEffect(() => {
    onLoadStart();
  }, []);

  const onLoadStart = useCallback(() => {
    BaseInfoManager.changeLoadingModalVisible(true);
  }, []);

  const onLoadEnd = useCallback(() => {
    BaseInfoManager.changeLoadingModalVisible(false);
  }, []);

  const $renderWebview = useMemo(() => {
    if (route.params.uri) {
      return (
        <WebView
          onLoadEnd={onLoadEnd}
          originWhitelist={['*']}
          javaScriptEnabled={true}
          useWebkit={true}
          source={{ uri: route.params.uri }}
        />
      );
    }

    if (route.params.html) {
      return (
        <WebView
          onLoadEnd={onLoadEnd}
          originWhitelist={['*']}
          javaScriptEnabled={true}
          useWebkit={true}
          source={{
            html: route.params.html,
            baseUrl: '',
          }}
        />
      );
    }
  }, []);

  return (
    <>
      <Layout>
        <TopNavigation titleKey="loanContractString.loanContractTitle" />
        {$renderWebview}
        <View
          padding="16 16 16 16"
          style={{
            backgroundColor: 'background-color-0',
            // borderColor: 'line-color-200',
            // borderTopWidth: 1,
          }}>
          <Button
            margin="16 16 0 16"
            onPress={handleNext}
            status="primary"
            textI18nKey="btnString.confirm"
          />
        </View>
      </Layout>
    </>
  );
};
