/* eslint-disable react-native/no-inline-styles */

import { Button, Image, Layout, Text, TopNavigation, View } from '@/components';
import React, { ReactElement, useCallback } from 'react';
import { RefreshControl, ScrollView } from 'react-native';
import useData from './useData';
import { useNameSpace } from '@/i18n';
export default (): ReactElement => {
  const { refreshing, onRefresh, handleNext, vaildDays } = useData();
  const t = useNameSpace().t;

  const rendTip = useCallback(() => {
    return (
      <>
        <Text
          margin="12 32 0 32"
          category="p1"
          isCenter
          i18nKey="creditRefuseConfirmString.topTip"></Text>
        <Text
          margin="24 32 0 32"
          category="p1"
          isCenter
          textContent={t('creditRefuseConfirmString.tip', {
            days: vaildDays,
          })}></Text>
      </>
    );
  }, [vaildDays, t]);

  return (
    <>
      <Layout pLevel="0">
        <TopNavigation showLogoAction={true} showMessage bottomLine />
        <ScrollView
          fadingEdgeLength={10}
          keyboardShouldPersistTaps="always"
          refreshControl={
            <RefreshControl colors={['#4C6EFF']} refreshing={refreshing} onRefresh={onRefresh} />
          }>
          <View
            style={{
              flexDirection: 'column',
              alignItems: 'center',
            }}>
            <Image margin="40 0 0 0" name="_loanCreditRefuseConfirm" />
            <View
              layoutStrategy="flexColumnBetweenCenter"
              margin="40 0 0 0"
              padding="8 8 8 8"
              cardType="baseType"
              style={{
                width: '80%',
              }}>
              {rendTip()}
            </View>
          </View>
        </ScrollView>
        <Button
          margin="16 32 32 32"
          onPress={handleNext}
          status="primary"
          textI18nKey="btnString.resume"
        />
      </Layout>
    </>
  );
};
