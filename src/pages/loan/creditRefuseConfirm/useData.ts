import { HitPointEnumsSpace } from '@/enums';
import {
  useGetUserStateAndNextRouterOrOtherCallBack,
  useOnInit,
  useSubscribeFilter,
} from '@/hooks';
import { BaseInfoManager, UserInfoContextType, UserInfoManager } from '@/managers';
import { RouterConfig } from '@/routes';
import { confirmUnlock, fetchCreditVaildDays } from '@/server';
import { nav } from '@/utils';
import { useCallback, useState } from 'react';

interface IFiletrData {
  lockMessage: string[];
  unLockTime: string;
  canUnLock: boolean;
}

export default function useData() {
  const [vaildDays, setVaildDays] = useState<string>('*');

  const [getUserStateAndNextRouterOrOtherCallBack] = useGetUserStateAndNextRouterOrOtherCallBack();

  /** 初始化方法 */
  const { loading, refreshing, setRefreshing, onRefresh } = useOnInit({
    callback: async () => {
      BaseInfoManager.changeLoadingModalVisible(true);
      loading.current = true;
      await getVaildDays();
      loading.current = false;
      BaseInfoManager.changeLoadingModalVisible(false);
    },
    refreshCallback: async () => {
      setRefreshing(true);
      loading.current = true;
      await getVaildDays();
      loading.current = false;
      setRefreshing(false);
    },
    isBackAutoRefresh: false,
    pageKey: HitPointEnumsSpace.EPageKey.P_CREDIT_REFUSE_CONFIRM,
  });

  const getVaildDays = useCallback(async () => {
    const { code, data } = await fetchCreditVaildDays();
    if (code === 0) {
      setVaildDays(String(data));
    }
  }, []);

  const handleNext = useCallback(async () => {
    if (!loading.current) {
      BaseInfoManager.changeLoadingModalVisible(true);
      loading.current = true;
      // 确认重新贷款
      await getUserStateAndNextRouterOrOtherCallBack();
      loading.current = false;
      BaseInfoManager.changeLoadingModalVisible(false);
    }
  }, [getUserStateAndNextRouterOrOtherCallBack]);

  return {
    onRefresh,
    refreshing,
    handleNext,
    vaildDays,
  };
}
