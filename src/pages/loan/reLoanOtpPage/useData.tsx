import { BaseEnumsSpace, UserEnumsSpace } from '@/enums';
import { useDeviceDataReport, useSubscribeFilter, useOnInit } from '@/hooks';
import {
  BaseInfoManager,
  ModalList,
  UserInfoContextType,
  UserInfoManager,
  modalDataStoreInstance,
} from '@/managers';
import { Toast } from '@/nativeComponents';
import { fetchReLoanOtp, fetchGetAppConfig, fetchVerifyReLoanOtp, fetchSkipVerify } from '@/server';
import { Log, nav, TrackEvent } from '@/utils';
import { useCallback, useRef, useState } from 'react';
import { useNameSpace } from '../../../i18n';
import { SmsCodeInputRefType } from '@/components';
import { HitPointEnumsSpace } from '@/enums';

export default function useData() {
  const t = useNameSpace().t;
  const [smsType, setSmsType] = useState<UserEnumsSpace.ReLoanOptSmsType>(
    UserEnumsSpace.ReLoanOptSmsType.SMS_MOBILE,
  );
  const [skipButtonVisible, setSkipButtonVisible] = useState<boolean>(false);
  const smsCodeInputRef = useRef<SmsCodeInputRefType>(null);
  const { onApplyReportDeviceData } = useDeviceDataReport();
  const phoneNumber = useSubscribeFilter({
    subject: UserInfoManager.messageCenter,
    filter: (subject: UserInfoContextType) => {
      return subject.userModel.mobile;
    },
  }) as string;
  /** 发送次数记录 */
  let sendCount = useRef<number>(0).current;
  /** 验证次数 */
  let verifyCount = useRef<number>(0).current;

  /** 初始化方法 */
  useOnInit({
    pageKey: HitPointEnumsSpace.EPageKey.P_RE_OTP,
  });

  /** 发送验证码,成功发送OTP验证码,弹出提示弹框 */
  const onSendCode = useCallback(
    async (smsType: UserEnumsSpace.ReLoanOptSmsType): Promise<boolean> => {
      let params = {
        mobile: phoneNumber,
        smsType,
      };
      BaseInfoManager.changeLoadingModalVisible(true);
      let result = await fetchReLoanOtp(params);
      BaseInfoManager.changeLoadingModalVisible(false);

      return new Promise(resolve => {
        if (
          smsType === UserEnumsSpace.ReLoanOptSmsType.SMS_VOICE_MOBILE &&
          result?.code === BaseEnumsSpace.ENetworkStatus.SUCCESS
        ) {
          modalDataStoreInstance.openModal({
            key: ModalList.RECIVE_OTP_BY_CALL,
            i18nKey: 'modalString.callYouSendOTP',
            confirmBtnName: 'btnString.OK',
            confirmBtnCallback: () => {
              resolve(true);
            },
          });
          smsCodeInputRef.current?.reStartCountDown();
        }

        if (
          smsType === UserEnumsSpace.ReLoanOptSmsType.SMS_MOBILE &&
          result?.code === BaseEnumsSpace.ENetworkStatus.SUCCESS
        ) {
          smsCodeInputRef.current?.reStartCountDown();
          Toast(`${t('loanConfirmString.peso_otp_success_tip')}${phoneNumber}`);
          resolve(true);
        }
      });
    },
    [],
  );

  /** 获取App Config */
  const onFetchGetAppConfig = async (): Promise<boolean> => {
    BaseInfoManager.changeLoadingModalVisible(true);
    let result = await fetchGetAppConfig();
    BaseInfoManager.changeLoadingModalVisible(false);
    return new Promise(async resolve => {
      if (result?.code === BaseEnumsSpace.ENetworkStatus.SUCCESS) {
        const { voiceSmsSwitchStatus } = result?.data;

        if (voiceSmsSwitchStatus === 'YES') {
          onOpenModal();
        } else {
          setSmsType(UserEnumsSpace.ReLoanOptSmsType.SMS_MOBILE);
          let result = await onSendCode(UserEnumsSpace.ReLoanOptSmsType.SMS_MOBILE);
        }
      }
      resolve(false);
    });
  };

  /** 打开弹框,选择OTP验证码或者短信验证码 */
  const onOpenModal = () => {
    modalDataStoreInstance.openModal({
      key: ModalList.CALL_YOU_SEND_SMS,
      i18nKey: 'modalString.reciveOTPByVoiceCall',
      confirmBtnName: 'btnString.OK',
      cancelBtnName: 'btnString.continueSMS',
      isBackdropClose: true,
      confirmBtnCallback: async () => {
        setSmsType(UserEnumsSpace.ReLoanOptSmsType.SMS_VOICE_MOBILE);
        await onSendCode(UserEnumsSpace.ReLoanOptSmsType.SMS_VOICE_MOBILE);
      },
      cancelBtnCallback: async () => {
        setSmsType(UserEnumsSpace.ReLoanOptSmsType.SMS_MOBILE);
        await onSendCode(UserEnumsSpace.ReLoanOptSmsType.SMS_MOBILE);
      },
    });
  };

  /** 验证复贷OTP验证码 */
  const onFetchVerifyOtp = async (params: { mobile: string; smsCode: string }) => {
    let result = await fetchVerifyReLoanOtp(params);

    if (result?.code === BaseEnumsSpace.ENetworkStatus.SUCCESS) {
    } else {
      Toast(result.msg);
      verifyCount++;
      BaseInfoManager.context.baseModel.otpVerifyFailedCount = verifyCount;
    }

    return result?.code === BaseEnumsSpace.ENetworkStatus.SUCCESS;
  };

  /** 跳过OTP验证 */
  const onFetchSkipVerify = async () => {
    BaseInfoManager.changeLoadingModalVisible(true);
    let { code } = await fetchSkipVerify();

    if (code === BaseEnumsSpace.ENetworkStatus.SUCCESS) {
      if (!(await UserInfoManager.updateUserState())) {
        BaseInfoManager.changeLoadingModalVisible(false);
        return;
      }

      nav.nextToTopRouter();
    }

    return code === BaseEnumsSpace.ENetworkStatus.SUCCESS;
  };

  /** 复贷验证,进行路由跳转 */
  const handleNext = useCallback(async (smsCode: string) => {
    TrackEvent.trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_RE_OTP,
        e: HitPointEnumsSpace.EEventKey.BTN_OTP_SUBMIT,
      },
      '1',
    );
    TrackEvent.uploadEventLog();

    let params = {
      mobile: phoneNumber,
      smsCode,
    };

    BaseInfoManager.changeLoadingModalVisible(true);
    if (!(await onFetchVerifyOtp(params))) {
      BaseInfoManager.changeLoadingModalVisible(false);
    }

    if (!(await UserInfoManager.updateUserState())) {
      BaseInfoManager.changeLoadingModalVisible(false);
      return;
    }

    BaseInfoManager.changeLoadingModalVisible(false);

    nav.nextToTopRouter();
  }, []);

  /** smsCode点击按钮前回调函数 */
  const onClickSendPreMethod = useCallback(async (): Promise<boolean> => {
    if (sendCount > 0) {
      return await onFetchGetAppConfig();
    } else {
      setSmsType(UserEnumsSpace.ReLoanOptSmsType.SMS_MOBILE);
      let result = await onSendCode(UserEnumsSpace.ReLoanOptSmsType.SMS_MOBILE);
      sendCount = sendCount + 1;
      return result;
    }
  }, []);

  /** 倒计时结束回调 */
  const onCountDownOverCallback = () => {
    setSkipButtonVisible(true);
  };

  return {
    phoneNumber,
    smsCodeInputRef,
    onClickSendPreMethod,
    handleNext,
    onFetchSkipVerify,
    onCountDownOverCallback,
    skipButtonVisible,
  };
}
