import { Button, Layout, SmsCodeInput, Text, TopNavigation, View } from '@/components';
import { CallYouSendSmsModal, ReciveOtpByCallModal } from '@/modals';
import { ScreenProps } from '@/types';
import React, { ReactElement, useMemo } from 'react';
import { ScrollView, TouchableWithoutFeedback } from 'react-native';
import useData from './useData';
import useFormData from './useFormData';
import { useSmsCodeHandler } from '../../../hooks/useSmsCodeHandler';

export default ({ navigation, route }: ScreenProps<{}>): ReactElement => {
  const { smsCode, onSetSmsCode } = useFormData();
  const {
    phoneNumber,
    smsCodeInputRef,
    handleNext,
    onClickSendPreMethod,
    onFetchSkipVerify,
    onCountDownOverCallback,
    skipButtonVisible,
  } = useData();
  useSmsCodeHandler(onSetSmsCode);

  /** 跳过按钮 */
  const $skipButton = useMemo(() => {
    if (!skipButtonVisible) {
      return <></>;
    }

    return (
      <>
        <TouchableWithoutFeedback onPress={onFetchSkipVerify}>
          <Text
            margin="16 0 0 0"
            category="p1"
            isCenter={true}
            style={{
              color: 'primary-color-500',
              textDecorationLine: 'underline',
            }}
            i18nKey="otpString.skipOtp"
          />
        </TouchableWithoutFeedback>
      </>
    );
  }, [onFetchSkipVerify, skipButtonVisible]);

  return (
    <>
      <Layout pLevel="0">
        <TopNavigation titleKey={'otpString.formTitle'} isBack={false} bottomLine />
        <View
          margin="12 16 0 16"
          padding="8 8 8 8"
          style={{
            flexDirection: 'row',
            borderRadius: 8,
            // borderWidth: 1,
            // borderColor: 'line-color-200',
            backgroundColor: 'info-color-100',
          }}>
          <Text
            category="p1"
            i18nKey={'otpString.formTitleDesc'}
            style={{
              color: 'text-color-800',
            }}
          />
        </View>
        <ScrollView fadingEdgeLength={10} keyboardShouldPersistTaps="always">
          <View margin="24 0 0 0" padding="0 16 0 16">
            {/* <Text
              category="h3"
              style={{
                color: 'text-color-600',
              }}
              i18nKey="otpString.otpSendTo"
            /> */}
            <Text
              margin="12 0 0 0"
              category="p1"
              bold={'bold'}
              style={{
                color: 'text-color-800',
              }}
              textContent={`Tu celular: ${String(phoneNumber).toPrefixPhoneNumber()}`}
            />
            <SmsCodeInput
              type="line"
              ref={smsCodeInputRef}
              prefixKey={'otpString.code'}
              prefixMargin={'24 0 0 0'}
              placeholderKey={'otpString.code'}
              smsCode={smsCode}
              setSmsCode={onSetSmsCode}
              scenesId="re_loan"
              initMethod={onClickSendPreMethod}
              countDownOverCallback={onCountDownOverCallback}
              clickSendPreMethod={onClickSendPreMethod}
            />
            <Button
              margin="24 0 0 0"
              status="primary"
              onPress={() => {
                handleNext(smsCode);
              }}
              padding={'16 0 16 0'}
              textI18nKey="btnString.submit"
            />
            {$skipButton}
          </View>
        </ScrollView>
      </Layout>
      <ReciveOtpByCallModal />
      <CallYouSendSmsModal />
    </>
  );
};
