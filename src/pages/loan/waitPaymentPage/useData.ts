import { BaseEnumsSpace, HitPointEnumsSpace } from '@/enums';
import { useGetUserStateAndNextRouterOrOtherCallBack, useOnInit } from '@/hooks';
import { BaseInfoManager, modalDataStoreInstance, ModalList } from '@/managers';
import { RouterConfig } from '@/routes';
import {
  fetchDisbursingPageShowTipList,
  fetchOrderDetail,
  fetchHasQualification,
  fetchIsInviteDuring,
  fetchWithholdAuthorizationOpen,
  fetchWithholdAuthorizationClose,
  fetchWithholdContract,
} from '@/server';
import { OrderVOSpace, EWithholderTipType } from '@/types';
import { useCallback, useMemo, useRef, useState } from 'react';
import { RefType as ActivitySwiperRefType } from '../../components/activitySwiper';
import { nav, TrackEvent } from '@/utils';
import CryptoJS from 'crypto-js';

export default function useData() {
  const [getUserStateAndNextRouterOrOtherCallBack] = useGetUserStateAndNextRouterOrOtherCallBack(
    RouterConfig.WAIT_PAYMENT,
  );
  let activitySwiperRef = useRef<ActivitySwiperRefType>(null);
  /** 页面初始化 */
  const { loading, refreshing, setRefreshing, onRefresh } = useOnInit({
    callback: async () => {
      BaseInfoManager.changeLoadingModalVisible(true);
      loading.current = true;
      await getLoanDetail();
      await getDisbursingShowTipTextList();
      // await getUserStateAndNextRouterOrOtherCallBack();
      await getHasQualification();
      loading.current = false;
      BaseInfoManager.changeLoadingModalVisible(false);
    },
    refreshCallback: async () => {
      setRefreshing(true);
      loading.current = true;
      await getLoanDetail();
      await getUserStateAndNextRouterOrOtherCallBack();
      await getHasQualification();
      activitySwiperRef.current?.resetValue();
      loading.current = false;
      setRefreshing(false);
    },
    isBackAutoRefresh: true,
    pageKey: HitPointEnumsSpace.EPageKey.P_DISBURSING,
  });

  const [pedentVisible, setPedentVisible] = useState<boolean>(false);
  const [loanDetail, setLoanDetail] = useState<OrderVOSpace.OrderDetailDataType>({
    /** 借款金额 */
    loanAmount: '--',
    /** 利息 */
    interest: '--',
    /** 手续费 */
    processFee: '--',
    /** 手续费vat */
    processFeeVat: '--',
    /** 银行卡类型 */
    cardType: '--',
    /** 银行名称 */
    bankName: '--',
    /** 卡号 */
    cardNo: '--',
    /** 是否是补件放款 */
    bankcardSupply: '',
    /** 是否开启自动代扣功能开关卡片 */
    withholdAuthorizeStatus: '',
    /** 是否展示代扣卡片 */
    showWithholdBtn: 'NO',
  });

  /** 是否补件后 */
  const isBankcardSupply = useMemo(() => {
    return loanDetail.bankcardSupply === 'YES';
  }, [loanDetail.bankcardSupply]);

  /** 放款页面提示文案 */
  const [disbursingShowTipTextList, setDisbursingShowTipTextList] = useState<string[]>([]);

  /** 用户自动代扣绑定状态 */
  const [withholdAuthorize, setWithholdAuthorize] = useState<boolean>(false);

  const getLoanDetail = useCallback(async () => {
    // todo 获取最新的放款订单，只有在有在途放款订单存在的时候才能去看。
    const { code, data } = await fetchOrderDetail();
    if (code === 0 && data) {
      if (data.withholdAuthorizeStatus === 'YES') {
        setWithholdAuthorize(true);
      }
      setLoanDetail(data);
    }
  }, []);

  const getHasQualification = useCallback(async () => {
    let result = await fetchIsInviteDuring();
    if (result.code === BaseEnumsSpace.ENetworkStatus.SUCCESS && result.data.isDuring) {
      const { code, data } = await fetchHasQualification();
      if (code === BaseEnumsSpace.ENetworkStatus.SUCCESS) {
        setPedentVisible(data.hasQualification);
      }
    }
  }, []);

  /** 获取还款页提示文案列表 */
  const getDisbursingShowTipTextList = useCallback(async () => {
    const { code, data } = await fetchDisbursingPageShowTipList();
    if (code === 0) {
      if (Array.isArray(data.waitLoanPage)) {
        setDisbursingShowTipTextList(data.waitLoanPage);
      }
    }
  }, []);

  /** 跳转到自动代扣协议 */
  const openAutoWithhold = async () => {
    TrackEvent.trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_DISBURSING,
        e: HitPointEnumsSpace.EEventKey.BTN_NEGOTIATE_CHECK,
      },
      '1',
    );
    let result = await fetchWithholdContract({
      cardNo: loanDetail.cardNo,
      bankName: loanDetail.bankName,
    });
    if (result.code === BaseEnumsSpace.ENetworkStatus.SUCCESS) {
      nav.navigate(RouterConfig.AUTOMATIC_WITHHOLD_PROTOCOL as any, {
        html: CryptoJS.enc.Base64.parse(String(result.data)).toString(CryptoJS.enc.Utf8),
        currentRoute: RouterConfig.WAIT_PAYMENT,
        acceptHandle: openWithholdAuthorization,
      });
    }

    return result?.code === BaseEnumsSpace.ENetworkStatus.SUCCESS;
  };

  /** 开启自动代扣功能 */
  const openWithholdAuthorization = async () => {
    modalDataStoreInstance.openModal({
      key: ModalList.WITHHOLDER_TIP,
      extra: EWithholderTipType.OPEN,
      confirmBtnCallback: async () => {
        BaseInfoManager.changeLoadingModalVisible(true);
        TrackEvent.trackCommonEvent(
          {
            p: HitPointEnumsSpace.EPageKey.P_DISBURSING,
            e: HitPointEnumsSpace.EEventKey.BTN_AUTOMATIC_REPAY_CLICK,
          },
          '1',
        );
        const { code } = await fetchWithholdAuthorizationOpen();
        if (code === 0) {
          modalDataStoreInstance.openModal({
            key: ModalList.INFO_PROMPT_CONFIRM,
            i18nKey: 'autoWithholdString.activateSuccess',
            imageKey: '_epModalSuccessIcon',
            confirmBtnName: 'btnString.agree',
            isBackdropClose: false,
          });
          getLoanDetail();
          // 提示修改成功
          setWithholdAuthorize(true);
        }
        BaseInfoManager.changeLoadingModalVisible(false);
      },
    });
  };

  /** 关闭自动代扣功能 */
  const closeWithholdAuthorization = async () => {
    modalDataStoreInstance.openModal({
      key: ModalList.WITHHOLDER_TIP,
      extra: EWithholderTipType.CLOSE,
      confirmBtnCallback: async () => {
        BaseInfoManager.changeLoadingModalVisible(true);
        /** 点击修改银行卡按钮事件 */
        TrackEvent.trackCommonEvent(
          {
            p: HitPointEnumsSpace.EPageKey.P_DISBURSING,
            e: HitPointEnumsSpace.EEventKey.BTN_AUTOMATIC_REPAY_CLICK,
          },
          '0',
        );
        const { code } = await fetchWithholdAuthorizationClose();
        if (code === 0) {
          setWithholdAuthorize(false);
        }
        BaseInfoManager.changeLoadingModalVisible(false);
      },
    });
  };

  const onChageAutoWithholdState = () => {
    if (withholdAuthorize) {
      // 关闭自动代扣
      closeWithholdAuthorization();
    } else {
      // 开启自动代扣
      openWithholdAuthorization();
    }
  };

  return {
    activitySwiperRef,
    isBankcardSupply,
    onRefresh,
    refreshing,
    loanDetail,
    disbursingShowTipTextList,
    pedentVisible,
    withholdAuthorize,
    setPedentVisible,
    openAutoWithhold,
    openWithholdAuthorization,
    closeWithholdAuthorization,
    onChageAutoWithholdState,
  };
}
