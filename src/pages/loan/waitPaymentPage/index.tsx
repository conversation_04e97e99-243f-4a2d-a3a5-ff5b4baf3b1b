/* eslint-disable react-native/no-inline-styles */

import {
  Button,
  Card,
  Image,
  ImageBackground,
  Layout,
  LinearGradient,
  Swiper,
  Text,
  TopNavigation,
  View,
} from '@/components';
import Pedent from './components/pedent';
import { ELocal<PERSON>ey } from '@/localStorage';
import { BaseInfoManager, KVManager, UserInfoManager } from '@/managers';
import React, { ReactElement, useMemo } from 'react';
import { Dimensions, RefreshControl, ScrollView, TouchableOpacity } from 'react-native';
import useData from './useData';
export default (): ReactElement => {
  const {
    onRefresh,
    refreshing,
    loanDetail,
    disbursingShowTipTextList,
    isBankcardSupply,
    pedentVisible,
    setPedentVisible,
    withholdAuthorize,
    openAutoWithhold,
    onChageAutoWithholdState,
  } = useData();

  const $disbursingTip = useMemo(() => {
    return (
      disbursingShowTipTextList.length != 0 && (
        <Swiper
          style={{
            width: Dimensions.get('window').width - 36,
            borderRadius: 8,
            // borderWidth: 1,
            // borderColor: 'line-color-200',
          }}
          type={2}
          canableShowBottom={true}
          bottomType={'dot'}
          backgroundColor="background-color-0"
          bottomMarkColor="fill-color-300"
          bottomMarkActiveColor="primary-color-500"
          textColor="text-color-800"
          margin="16 8 0 8"
          list={disbursingShowTipTextList}
        />
      )
    );
  }, [disbursingShowTipTextList]);

  const DesicptionView = useMemo(() => {
    const isUpdateClabe = KVManager.action.getBoolean(
      ELocalKey.CLABE_SUPPLY_UPDATED + '_' + UserInfoManager.context.userModel.applyOrderId,
    );

    return (
      <>
        {isBankcardSupply && (
          <View
            padding={'12 12 12 12'}
            layoutStrategy="flexRowStartCenter"
            style={{
              borderColor: 'success-color-100',
              backgroundColor: 'success-color-100',
              borderRadius: 8,
            }}>
            <Image name="_correctIcon" margin="0 8 0 0" />
            <Text
              category="p2"
              i18nKey={
                isUpdateClabe
                  ? 'waitCheckString.clabeSupplyAndChangeSubmit'
                  : 'waitCheckString.clabeSupplyAndNoneChangeSubmit'
              }
              style={{
                flex: 1,
                color: 'text-color-800',
              }}
            />
          </View>
        )}
      </>
    );
  }, [isBankcardSupply]);

  const $autoWithholdView = useMemo(() => {
    if (
      BaseInfoManager.context.baseModel.isWithholdSwitch &&
      loanDetail.showWithholdBtn === 'YES' &&
      loanDetail.withholdAuthorizeStatus !== 'YES'
    ) {
      return (
        <View margin="20 0 0 0" padding="0 20 0 20" width={'100%'} layoutStrategy="flexColumnStart">
          <LinearGradient
            style={{ borderRadius: 8 }}
            start={{ x: 0, y: 0.5 }} // 起点：顶部中心
            end={{ x: 1, y: 0.5 }} // 终点：底部中心
            colors={['#FFF5B5', '#FFFCEB']}>
            <View padding="16 16 16 16" layoutStrategy="flexRowBetweenCenter">
              <Text category="p2" style={{ flex: 1 }} i18nKey="waitCheckString.autoWithholdTip" />
              <Button
                onPress={onChageAutoWithholdState}
                style={[
                  { borderRadius: 99 },
                  withholdAuthorize
                    ? { borderColor: 'secondary-color-600' }
                    : { backgroundColor: 'secondary-color-600' },
                ]}
                status={'warning'}
                appearance={withholdAuthorize ? 'outline' : 'filled'}
                textI18nKey={withholdAuthorize ? 'btnString.cancel' : 'btnString.activar'}></Button>
            </View>
          </LinearGradient>

          <View margin="6 0 0 0" layoutStrategy="flexRowStartCenter">
            <Image
              margin="2 8 0 0"
              style={{
                tintColor: 'fill-color-500',
              }}
              name="_vector"></Image>
            <View layoutStrategy="flexRowStartCenter">
              <Text
                category="c2"
                i18nKey={'autoWithholdString.agree'}
                style={{
                  color: 'text-color-800',
                }}
              />
              <Text
                category="c2"
                onPress={openAutoWithhold}
                i18nKey={'autoWithholdString.automaticWithhold'}
                style={{
                  color: 'text-color-700',
                  textDecorationLine: 'underline',
                }}
              />
            </View>
          </View>
        </View>
      );
    } else {
      return null;
    }
  }, [withholdAuthorize, loanDetail, openAutoWithhold, onChageAutoWithholdState]);

  return (
    <Layout pLevel="0">
      {!isBankcardSupply && (
        <TopNavigation isShowVip showLogoAction={true} type="basic" showMessage bottomLine />
      )}
      {DesicptionView}
      <ScrollView
        fadingEdgeLength={10}
        keyboardShouldPersistTaps="always"
        refreshControl={
          <RefreshControl colors={['#4C6EFF']} refreshing={refreshing} onRefresh={onRefresh} />
        }>
        <View
          margin="40 0 0 0"
          style={{
            flexDirection: 'column',
            alignItems: 'center',
          }}>
          <Image name="_evidenceFaceSuccess" />
          <View
            layoutStrategy="flexColumnBetweenCenter"
            style={{
              width: '80%',
            }}>
            <Text
              margin="12 0 0 0"
              i18nKey={'waitCheckString.waitPaymentTitle'}
              category="h3"
              bold="500"
            />
            {UserInfoManager.context.userModel.isDirectLendingStatus && (
              <Text
                margin="12 0 0 0"
                i18nKey={'waitCheckString.waitPaymentSubTitle'}
                category="p2"
              />
            )}
            <Text
              margin="12 0 0 0"
              i18nKey={'waitCheckString.waitPaymentDesciption'}
              category="p2"
            />
          </View>
          <View
            margin="12 0 0 0"
            padding="8 16 8 16"
            style={{
              width: Dimensions.get('window').width * 0.9,
              backgroundColor: 'background-color-0',
              borderRadius: 8,
              // borderWidth: 1,
              // borderColor: 'line-color-200',
            }}>
            <View margin="8 0 0 0" layoutStrategy="flexRowBetweenCenterWrap">
              <Text
                i18nKey={'waitCheckString.waitPaymentActualTermOfLoan'}
                category="p2"
                bold="500"
                style={{
                  color: 'text-color-600',
                }}
              />
              <Text
                margin="8 0 0 0"
                textContent={String(loanDetail.loanAmount).toFormatFinance()}
                category="p2"
                bold="500"
                style={{
                  color: 'text-color-800',
                }}
              />
            </View>
            <View margin="8 0 0 0" layoutStrategy="flexRowBetweenCenterWrap">
              <Text
                margin="8 0 0 0"
                i18nKey={'waitCheckString.waitPaymentBankName'}
                category="p2"
                bold="500"
                style={{
                  color: 'text-color-600',
                }}
              />
              <Text
                margin="8 0 0 0"
                textContent={String(loanDetail.bankName)}
                category="p2"
                bold="500"
                style={{
                  color: 'text-color-800',
                }}
              />
            </View>
            <View margin="12 0 0 0" layoutStrategy="flexColumnStart">
              <Text
                margin="0 0 0 0"
                i18nKey={'waitCheckString.waitPaymentAccountToReceiveLoan'}
                category="p2"
                bold="500"
                style={{
                  color: 'text-color-600',
                }}
              />
              <View margin="12 0 0 0" layoutStrategy="flexRowBetweenCenter">
                <Text
                  textContent={`CLABE`}
                  category="p2"
                  bold="500"
                  style={{
                    color: 'text-color-800',
                  }}
                />
                <Text
                  textContent={String(loanDetail.cardNo).toFormatClabe()}
                  category="p1"
                  bold="700"
                  style={{
                    color: 'text-color-800',
                  }}
                />
              </View>
              {BaseInfoManager.context.baseModel.isWithholdSwitch && withholdAuthorize && (
                <Text
                  category="c2"
                  margin="12 0 0 0"
                  padding="2 4 2 4"
                  style={{
                    borderRadius: 4,
                    borderWidth: 1,
                    borderColor: 'primary-color-400',
                    color: 'primary-color-400',
                    alignSelf: 'flex-end',
                  }}
                  i18nKey="waitCheckString.activado"
                />
              )}
              {/* </View> */}
            </View>
          </View>
          {/* 自动代扣卡片 */}
          {$autoWithholdView}
          {$disbursingTip}
        </View>
      </ScrollView>
      <Pedent visible={pedentVisible} setVisible={setPedentVisible} />
    </Layout>
  );
};
