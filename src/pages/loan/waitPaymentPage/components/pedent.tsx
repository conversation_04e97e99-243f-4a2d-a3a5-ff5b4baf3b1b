import { Image, View } from '@/components';
import { HitPointEnumsSpace } from '@/enums';
import { UserInfoManager } from '@/managers';
import { trackCommonEvent } from '@/trackEvent';
import { TouchableHighlight, TouchableWithoutFeedback } from 'react-native';

/** 福袋 */
const Pedent = (props: { visible: boolean; setVisible: Function }) => {
  const { visible, setVisible } = props;
  if (!visible) {
    return <></>;
  }

  const onOpenPage = async () => {
    UserInfoManager.getInviteDataAndNavigateLand({});
    trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_DISBURSING,
        e: HitPointEnumsSpace.EEventKey.BTN_CLICK_LUCKYBAG,
      },
      '1',
    );
  };

  const onClose = () => {
    setVisible(false);
  };

  return (
    <>
      <View
        style={{
          position: 'absolute',
          right: '5%',
          bottom: '10%',
        }}>
        <TouchableWithoutFeedback onPress={onOpenPage}>
          <Image name="_pedentContainer" />
        </TouchableWithoutFeedback>
        <TouchableHighlight
          style={{
            position: 'absolute',
            top: -5,
            right: -10,
          }}
          onPress={onClose}>
          <Image name="_pedentClose" />
        </TouchableHighlight>
      </View>
    </>
  );
};

export default Pedent;
