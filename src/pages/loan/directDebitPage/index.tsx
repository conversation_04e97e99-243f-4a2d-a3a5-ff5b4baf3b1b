/**
 * @description 一键代扣页面，输入金额进行代扣
 */
import {
  Button,
  Image,
  Input,
  InputRefType,
  Layout,
  Radio,
  Text,
  TopNavigation,
  View,
} from '@/components';
import { Strings } from '@/i18n';
import { Colors } from '@/themes';
import { ScreenProps } from '@/types';
import { TrackEvent, useProcessColorStyles } from '@/utils';
import { useUpdateEffect } from 'ahooks';
import { t } from 'i18next';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import {
  Dimensions,
  InteractionManager,
  Pressable,
  RefreshControl,
  ScrollView,
} from 'react-native';
import RenderHtml from 'react-native-render-html';
import BankCardsModal from './components/BankCardsModal';
import useData from './useData';
import { HitPointEnumsSpace } from '@/enums';

export enum PaymentType {
  // 全部支付
  ALL = 'all',
  // 部分支付
  PART = 'part',
}
export type RouteParams = {
  // 券后还款金额
  realRemindRepayAmount: string;
  // 放款订单id
  orderId: string;
};

export default (props: ScreenProps<RouteParams>) => {
  const {
    handleNext,
    bankcardList,
    tips,
    refreshing,
    onRefresh,
    selectedBankCard,
    setSelectedBankCard,
    openAutoWithholdContract,
    minAmount,
    paymentType,
    partAmount,
    setPartAmount,
    partAmountRef,
    partAmountInputRef,
    setPaymentType,
  } = useData();
  const colorStyle = useProcessColorStyles({
    PRIMARY_COLOR_500: Colors.PRIMARY_COLOR_500,
  });

  // 银行卡列表是否显示
  const [bankCardListVisible, setBankCardListVisible] = useState(false);

  // 部分支付还是全部支付
  const onChangePaymentType = (value: PaymentType) => {
    TrackEvent.trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_ONE_CLICK_REPAY_APPLY_PAGE,
        e: HitPointEnumsSpace.EEventKey.BTN_SELECT_FULL_REPAYMENT_OPTION,
      },
      value === PaymentType.PART ? '0' : '1',
    );
    setPaymentType(value);
  };

  // 显示银行卡列表actionsheet
  const onShowBankCardList = () => {
    setBankCardListVisible(true);
  };

  useUpdateEffect(() => {
    if (paymentType === PaymentType.PART) {
      partAmountInputRef.current?.focus();
    } else {
      partAmountInputRef.current?.blur();
      // partAmountInputRef.current?.clearErrorStatus();
    }
  }, [paymentType]);

  useEffect(() => {
    partAmountRef.current = partAmount as number;
  }, [partAmount]);

  const checkPartAmountValid = () => {
    if (paymentType === PaymentType.ALL) {
      return true;
    }
    const amount = partAmount;
    if (!!amount) {
      return amount >= minAmount;
    }
    return false;
  };

  const handlePartAmountChange = useCallback(
    (text: string) => {
      if (text === '') {
        setPartAmount(undefined);
        return;
      }
      const amount = Number(text);
      if (amount > props.route?.params?.realRemindRepayAmount) {
        setPartAmount(Number(props.route.params.realRemindRepayAmount));
      } else {
        setPartAmount(amount);
      }
    },
    [props.route?.params?.realRemindRepayAmount],
  );

  // 支付金额和银行卡选择卡片
  const RepaymentCard = (
    <View margin="16 0 0 0" padding="16 12 16 12" cardType="baseType">
      <View layoutStrategy="flexRowStartCenter">
        <View
          margin="0 6 0 0"
          style={{ borderRadius: 4, backgroundColor: Colors.TEXT_COLOR_500 }}
          height={8}
          width={8}
        />
        <Text
          category="p1"
          i18nKey={Strings.repaymentString.amountToPay}
          style={{ color: Colors.TEXT_COLOR_800 }}
        />
      </View>
      <View padding="0 12 0 0" margin="16 0 0 0">
        <Radio
          checked={paymentType === PaymentType.ALL}
          onChange={checked => checked && onChangePaymentType(PaymentType.ALL)}
          labelI18nKey={Strings.repaymentString.payAll}
        />
        <Text
          margin="12 0 0 32"
          style={{ color: Colors.TEXT_COLOR_600, fontSize: 14 }}
          textContent={props.route?.params?.realRemindRepayAmount?.toFormatFinance() ?? '$-'}
        />
        <Radio
          margin="16 0 0 0"
          checked={paymentType === PaymentType.PART}
          onChange={checked => checked && onChangePaymentType(PaymentType.PART)}
          labelI18nKey={Strings.repaymentString.payPart}
        />
        <Input
          ref={partAmountInputRef}
          disabled={paymentType === PaymentType.ALL}
          value={partAmount ? partAmount.toString() : ''}
          onChangeText={handlePartAmountChange}
          onBlur={() => {
            // 小于最小值重置为最小值
            if (partAmountRef.current < minAmount) {
              setPartAmount(minAmount);
            }
          }}
          keyboardType="number-pad"
          margin="12 0 0 32"
          placeholder={t(Strings.repaymentString.minAmountToPay, { minAmount })}
          style={{
            borderWidth: 1,
            borderColor: Colors.LINE_COLOR_100,
            backgroundColor: Colors.BACKGROUND_COLOR_0,
            borderRadius: 4,
          }}
          dangerTextStyle={{
            fontSize: 14,
            lineHeight: 22,
            flex: 1,
          }}
          dangerStyle={{
            backgroundColor: 'background-color-0',
            borderColor: 'danger-color-500',
            borderRadius: 4,
          }}
          textStyle={{ color: Colors.TEXT_COLOR_800, fontSize: 14 }}
          validateConfig={
            [
              // {
              //   condition: checkPartAmountValid,
              //   info: t(Strings.verificationString.failed, {
              //     name: t(Strings.repaymentString.payPart),
              //   }),
              //   status: 'danger',
              // },
            ]
          }
        />
      </View>

      <View margin="32 0 0 0">
        <View layoutStrategy="flexRowStartCenter">
          <View
            margin="0 6 0 0"
            style={{ borderRadius: 4, backgroundColor: Colors.TEXT_COLOR_500 }}
            height={8}
            width={8}
          />
          <Text
            i18nKey={Strings.repaymentString.chooseCardToPay}
            category="p1"
            style={{ color: Colors.TEXT_COLOR_800 }}
          />
        </View>

        <Pressable onPress={onShowBankCardList}>
          <View
            margin="16 0 0 0"
            padding="0 12 0 12"
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
              borderRadius: 4,
              borderColor: Colors.LINE_COLOR_100,
              borderWidth: 1,
              backgroundColor: Colors.BACKGROUND_COLOR_0,
              height: 48,
            }}>
            <Text
              textContent={selectedBankCard?.cardNo ?? ''}
              style={{ color: Colors.TEXT_COLOR_800, fontSize: 14, lineHeight: 20 }}
            />
            <Image name="_dropDownIcon" />
          </View>
        </Pressable>
        <View
          margin="4 4 4 0"
          padding="4 8 4 0"
          style={{
            display: selectedBankCard?.isDirectBebitOpened === 'NO' ? 'flex' : 'none',
            borderBottomLeftRadius: 4,
            borderBottomRightRadius: 4,
            backgroundColor: 'background-color-0',
          }}
          layoutStrategy="flexRowStartCenter">
          <Image
            margin="2 8 0 0"
            style={{
              tintColor: 'fill-color-500',
            }}
            name="_vector"
          />
          <View layoutStrategy="flexRowStartCenter">
            <Text
              category="c2"
              i18nKey={'autoWithholdString.agree'}
              style={{
                color: 'text-color-600',
              }}
            />
            <Text
              category="c2"
              onPress={openAutoWithholdContract}
              i18nKey={'autoWithholdString.automaticWithhold'}
              style={{
                color: 'text-color-700',
                textDecorationLine: 'underline',
              }}
            />
          </View>
        </View>
      </View>
    </View>
  );

  const ButtonView = useMemo(() => {
    return (
      <View
        padding="12 16 16 16"
        style={{
          backgroundColor: 'background-color-0',
        }}>
        <Button
          disabled={paymentType === PaymentType.PART ? !checkPartAmountValid() : false}
          margin="0 16 0 16"
          status="primary"
          onPress={handleNext}
          textI18nKey={
            selectedBankCard?.isDirectBebitOpened === 'YES'
              ? Strings.repaymentString.directDebit
              : Strings.repaymentString.authAndDirectDebit
          }
        />
      </View>
    );
  }, [selectedBankCard, partAmount, paymentType, minAmount]);

  const NoteInfoView = useMemo(() => {
    if (!tips) {
      return null;
    }
    return (
      <View
        margin="16 0 40 0"
        padding="12 12 12 12"
        style={{
          backgroundColor: 'background-color-0',
          borderRadius: 8,
        }}>
        <View layoutStrategy="flexRowStartCenter">
          <Image name="_blackNotice" />
          <Text
            margin="0 10 0 10"
            category="p1"
            i18nKey={Strings.loanConfirmString.loan_notice_title}
          />
        </View>
        <RenderHtml
          contentWidth={Dimensions.get('window').width - 56}
          source={{ html: tips || '' }}
        />
      </View>
    );
  }, [tips]);

  return (
    <Layout pLevel="0" level="1">
      <TopNavigation
        type="basic"
        showRightAction
        titleKey={Strings.repaymentString.directDebitTitle}
      />
      <ScrollView
        fadingEdgeLength={10}
        keyboardShouldPersistTaps="always"
        contentContainerStyle={{ paddingHorizontal: 16 }}
        refreshControl={
          <RefreshControl
            colors={[colorStyle.PRIMARY_COLOR_500]}
            refreshing={refreshing}
            onRefresh={onRefresh}
          />
        }>
        {RepaymentCard}
        {NoteInfoView}
      </ScrollView>
      {ButtonView}
      <BankCardsModal
        data={bankcardList}
        selectData={selectedBankCard}
        visible={bankCardListVisible}
        onCancel={() => setBankCardListVisible(false)}
        onComfirm={bankcard => {
          TrackEvent.trackCommonEvent(
            {
              p: HitPointEnumsSpace.EPageKey.P_ONE_CLICK_REPAY_APPLY_PAGE,
              e: HitPointEnumsSpace.EEventKey.F_INPUT_REPAYMENT_CARD,
            },
            '1',
          );
          setSelectedBankCard(bankcard);
        }}
      />
    </Layout>
  );
};
