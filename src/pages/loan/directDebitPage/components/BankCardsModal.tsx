/**
 * 银行卡列表弹窗
 */
import { ActionSheet, Divider, Image, Text, View } from '@/components';
import { Strings } from '@/i18n';
import { Colors } from '@/themes';
import { OrderVOSpace } from '@/types';
import { useUpdateEffect } from 'ahooks';
import _ from 'lodash';
import React, { useMemo, useState } from 'react';
import { Dimensions, FlatList, Pressable } from 'react-native';

interface IProps {
  visible: boolean;
  data: OrderVOSpace.DirectDebitBank[];
  onCancel: () => void;
  onComfirm: (item: OrderVOSpace.DirectDebitBank) => void;
  selectData?: OrderVOSpace.DirectDebitBank;
}

export default React.memo((props: IProps) => {
  const { visible = false, onCancel, onComfirm, data = [], selectData } = props;
  const styles = useGetStyle();

  const [selectItem, setSelectItem] = useState<OrderVOSpace.DirectDebitBank | undefined>(
    selectData,
  );

  useUpdateEffect(() => {
    setSelectItem(selectData);
  }, [selectData]);

  const closeModal = _.debounce(() => {
    onCancel();
  }, 100);

  const [height, setHeight] = useState(
    (Dimensions.get('window').height * 3) / 4 > 700
      ? 700
      : (Dimensions.get('window').height * 3) / 4,
  );

  const onClickItem = (item: OrderVOSpace.DirectDebitBank) => {
    setSelectItem(item);
    onComfirm(item);
    closeModal();
  };

  const renderItem = (args: { item: OrderVOSpace.DirectDebitBank; index: number }) => {
    const { item, index } = args;
    return (
      <Pressable
        onPress={() => {
          onClickItem(item);
        }}>
        <Text
          margin="0 16 0 16"
          padding="19 0 19 0"
          status={item.cardNo === selectItem?.cardNo ? 'primary' : 'basic'}
          isCenter={true}
          textContent={item.cardNo || ''}
        />
      </Pressable>
    );
  };

  return (
    <ActionSheet visible={visible} onClose={closeModal.bind(this)}>
      <View padding="0 0 16 0" style={[styles.modal]}>
        <View margin="0 16 0 16" layoutStrategy="flexRowBetweenCenter">
          <Text
            padding="19 0 19 0"
            isCenter={true}
            category="h3"
            style={{
              color: Colors.TEXT_COLOR_800,
            }}
            i18nKey={Strings.repaymentString.chooseCardToPay}
          />
          <Pressable onPress={closeModal}>
            <Image name="_clearIcon" />
          </Pressable>
        </View>
        <FlatList
          contentContainerStyle={{ paddingHorizontal: 16 }}
          ItemSeparatorComponent={() => <Divider />}
          fadingEdgeLength={10}
          data={data}
          initialNumToRender={10}
          renderItem={renderItem}
          keyExtractor={item => item.cardNo}
        />
      </View>
    </ActionSheet>
  );
});

const useGetStyle = () => {
  return useMemo(() => {
    return {
      modal: {
        backgroundColor: 'background-color-0',
        borderRadius: 16,
        // maxHeight: Dimensions.get('window').height * 0.8,
      },
      input: {
        width: '100%',
        height: 48,
        borderBottomColor: 'line-color-200',
        borderBottomWidth: 1,
        backgroundColor: 'background-color-0',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        alignSelf: 'center',
      },
      dangerInput: {
        width: '100%',
        height: 48,
        borderRadius: 0,
        borderBottomColor: 'danger-color-500',
        borderWidth: 0,
        borderBottomWidth: 1,
        backgroundColor: 'background-color-0',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        alignSelf: 'center',
      },
      title: {
        color: 'text-color-500',
      },
      dangerTitle: {
        color: 'danger-color-500',
      },
    };
  }, []);
};
