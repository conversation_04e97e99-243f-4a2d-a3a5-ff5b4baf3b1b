import { BaseEnumsSpace, HitPointEnumsSpace } from '@/enums';
import { useOnInit } from '@/hooks';
import { BaseInfoManager } from '@/managers';
import { useCallback, useRef, useState } from 'react';
import { useNameSpace } from '@/i18n';
import { createDirectDebit, fetchDirectDebitBankList, fetchWithholdContract } from '@/server';
import CryptoJS from 'crypto-js';
import { RouterConfig } from '@/routes';
import { nav, TrackEvent } from '@/utils';
import { OrderVOSpace } from '@/types';
import { InputRefType } from '@/components';
import { PaymentType, RouteParams } from '.';
import { ParamListBase, RouteProp, useRoute } from '@react-navigation/native';

export default function useData() {
  const [bankcardList, setBankcardList] = useState<any>([]);
  // 选中的银行卡
  const [selectedBankCard, setSelectedBankCard] = useState<OrderVOSpace.DirectDebitBank>();
  const [tips, setTips] = useState<string>();
  const [minAmount, setMinAmount] = useState<number>(10);
  const partAmountInputRef = useRef<InputRefType>(null);
  const partAmountRef = useRef<number>(0);
  const [partAmount, setPartAmount] = useState<number>();
  // 支付方式
  const [paymentType, setPaymentType] = useState(PaymentType.ALL);
  const withholdContractRef = useRef(null);
  const route =
    useRoute<
      RouteProp<{ [RouterConfig.DIRECT_DEBIT_PAGE]: RouteParams }, RouterConfig.DIRECT_DEBIT_PAGE>
    >();

  /** 初始化方法 */
  const { loading, refreshing, setRefreshing, onRefresh } = useOnInit({
    callback: async () => {
      await fetchBankcardList();
    },
    refreshCallback: async () => {
      await fetchBankcardList();
    },
    isBackAutoRefresh: false,
    pageKey: HitPointEnumsSpace.EPageKey.P_CREDIT_REFUSE_QUESTION,
  });

  /** 跳转到代扣协议 */
  const openAutoWithholdContract = async () => {
    if (!selectedBankCard) {
      return;
    }
    if (!withholdContractRef.current) {
      const result = await fetchWithholdContract({
        cardNo: selectedBankCard.cardNo,
        bankName: selectedBankCard.bankName,
      });
      if (result.code === BaseEnumsSpace.ENetworkStatus.SUCCESS) {
        withholdContractRef.current = CryptoJS.enc.Base64.parse(String(result.data)).toString(
          CryptoJS.enc.Utf8,
        );
      }
    }
    nav.navigate(RouterConfig.AUTOMATIC_WITHHOLD_PROTOCOL as any, {
      html: withholdContractRef.current,
    });
    return !!withholdContractRef.current;
  };

  const fetchBankcardList = async () => {
    BaseInfoManager.changeLoadingModalVisible(true);
    const { data, code } = await fetchDirectDebitBankList();
    if (code === 0) {
      setBankcardList(data?.cardList || []);
      setTips(data?.tips || '');
      setMinAmount(data?.minAmount || 10);
      // 默认选中一张，首选开通的，没有选中第一张
      const defaultSelectCard =
        data?.cardList?.find(it => it.isDirectBebitOpened === 'YES') ?? data?.cardList?.[0];
      setSelectedBankCard(defaultSelectCard);
    }
    BaseInfoManager.changeLoadingModalVisible(false);
  };

  /**
   * 一键代扣
   **/
  const requestDirectDebit = async (params: {
    // 是否全额还款,YES(全额) NO(非全额)
    fullRepay: 'YES' | 'NO';
    // 还款金额
    amount: number;
    // 还款（代扣）卡号
    repayCardNo: string;
    // 放款订单id
    orderId: string;
  }) => {
    BaseInfoManager.changeLoadingModalVisible(true);
    const result = await createDirectDebit(params);
    if (result?.code === BaseEnumsSpace.ENetworkStatus.SUCCESS) {
      BaseInfoManager.changeLoadingModalVisible(false);
      nav.navigationGoBack();
    }
    BaseInfoManager.changeLoadingModalVisible(false);
  };

  const handleNext = async () => {
    TrackEvent.trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_ONE_CLICK_REPAY_APPLY_PAGE,
        e: HitPointEnumsSpace.EEventKey.BTN_NEXT,
      },
      '1',
    );
    await requestDirectDebit({
      fullRepay: paymentType === PaymentType.ALL ? 'YES' : 'NO',
      amount:
        paymentType === PaymentType.ALL
          ? Number(route.params?.realRemindRepayAmount)
          : partAmountRef.current,
      repayCardNo: selectedBankCard?.cardNo as string,
      orderId: route.params?.orderId,
    });
  };

  return {
    loading,
    refreshing,
    onRefresh,
    handleNext,
    fetchBankcardList,
    bankcardList,
    tips,
    selectedBankCard,
    setSelectedBankCard,
    openAutoWithholdContract,
    minAmount,
    partAmount,
    partAmountRef,
    partAmountInputRef,
    setPartAmount,
    paymentType,
    setPaymentType,
  };
}
