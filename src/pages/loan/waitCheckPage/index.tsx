/* eslint-disable react-native/no-inline-styles */

import { Button, Image, Layout, Swiper, Text, TopNavigation, View } from '@/components';
import { BaseEnumsSpace } from '@/enums';
import { GpReviewModal } from '@/modals';
import React, { ReactElement, useMemo } from 'react';
import { Dimensions, RefreshControl, ScrollView } from 'react-native';
import ActivitySwiper from '../../components/activitySwiper';
import useData from './useData';
export default (): ReactElement => {
  const {
    onRefresh,
    refreshing,
    activitySwiperRef,
    handleNext,
    waitCheckShowTipTextList,
    // requestNotificationPermission,
    // isShowOpenNotificationTip,
  } = useData();

  /** 通知权限弹窗 */
  // const $openNotificationPermissionTip = useMemo(() => {
  //   return (
  //     isShowOpenNotificationTip && (
  //       <View
  //         padding="8 16 8 16"
  //         style={{backgroundColor: 'secondary-color-100'}}
  //         layoutStrategy="flexRowBetweenCenter">
  //         <Text
  //           category="p2"
  //           style={{flex: 1}}
  //           i18nKey="permissionAgreeString.openNotificationPermissionTip"
  //         />
  //         <Button
  //           status="primary"
  //           margin="0 0 0 12"
  //           textI18nKey="btnString.open"
  //           onPress={requestNotificationPermission}
  //         />
  //       </View>
  //     )
  //   );
  // }, [requestNotificationPermission, isShowOpenNotificationTip]);

  return (
    <>
      <Layout level="0" pLevel="0">
        <TopNavigation showLogoAction={true} isShowVip type="basic" showMessage />
        <ScrollView
          fadingEdgeLength={10}
          keyboardShouldPersistTaps="always"
          refreshControl={
            <RefreshControl colors={['#4C6EFF']} refreshing={refreshing} onRefresh={onRefresh} />
          }>
          <View
            margin="40 0 0 0"
            style={{
              flexDirection: 'column',
              alignItems: 'center',
            }}>
            <Image margin="12 0 0 0" name="_evidenceWaitCheck" />
            <View
              layoutStrategy="flexColumnBetweenCenter"
              padding="0 16 0 16"
              style={{
                maxWidth: 288,
                minWidth: 188,
              }}>
              <Text
                margin="16 0 0 0"
                i18nKey={'waitCheckString.waitCheckTitle'}
                category="h3"
                bold="500"
              />
              <Swiper
                style={{
                  width: Dimensions.get('window').width - 32,
                  borderRadius: 8,
                  // borderWidth: 1,
                  // borderColor: 'line-color-200',
                }}
                type={2}
                canableShowBottom={true}
                bottomType={'dot'}
                backgroundColor="background-color-0"
                bottomMarkColor="fill-color-300"
                bottomMarkActiveColor="primary-color-500"
                textColor="text-color-800"
                margin="16 0 0 0"
                list={waitCheckShowTipTextList}
              />
            </View>
          </View>
          <ActivitySwiper
            ref={activitySwiperRef}
            margin="12 16 0 16"
            location={BaseEnumsSpace.EBannerLocationType.WAIT_CHECK}
          />
        </ScrollView>
        {/* {$openNotificationPermissionTip} */}
        <Button
          margin="16 16 20 16"
          status="primary"
          onPress={handleNext}
          textI18nKey="btnString.updateNow"
        />
      </Layout>
      <GpReviewModal />
    </>
  );
};
