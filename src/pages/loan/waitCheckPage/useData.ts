import { HitPointEnumsSpace } from '@/enums';
import {
  useCheckPushNotificationPermissionAndRequestPermission,
  useGetUserStateAndNextRouterOrOtherCallBack,
  useOnInit,
  useShowGPReviewStatus,
} from '@/hooks';
import {
  BaseInfoManager,
  KVManager,
  modalDataStoreInstance,
  ModalList,
  UserInfoManager,
} from '@/managers';
import { RouterConfig } from '@/routes';
import { fetchDisbursingPageShowTipList } from '@/server';
import { trackCommonEvent } from '@/trackEvent';
import { TrackEvent } from '@/utils';
import { useCallback, useRef, useState } from 'react';
import { isHasPushNotification } from '../../../utils/permission';
import { RefType as ActivitySwiperRefType } from '../../components/activitySwiper';

export default function useData() {
  let activitySwiperRef = useRef<ActivitySwiperRefType>(null);
  const [getUserStateAndNextRouterOrOtherCallBack] = useGetUserStateAndNextRouterOrOtherCallBack(
    RouterConfig.WAIT_CHECK,
  );

  const { showGooglePlayReviewOnRepay } = useShowGPReviewStatus({
    screen: 'APPLY',
  });

  // 等待还款页的文案信息
  const [waitCheckShowTipTextList, setWaitCheckShowTipTextList] = useState<string[]>([]);

  const checkPermission = useCheckPushNotificationPermissionAndRequestPermission()[0];

  /** 获取等待审核页提示文案列表 */
  const getDisbursingShowTipTextList = useCallback(async () => {
    const { code, data } = await fetchDisbursingPageShowTipList();
    if (code === 0) {
      if (Array.isArray(data.waitApprovePage)) {
        setWaitCheckShowTipTextList(data.waitApprovePage);
      }
    }
  }, []);

  const { loading, refreshing, onRefresh } = useOnInit({
    callback: async () => {
      BaseInfoManager.changeLoadingModalVisible(true);
      loading.current = true;
      await getUserStateAndNextRouterOrOtherCallBack();
      showGooglePlayReviewOnRepay();

      await getDisbursingShowTipTextList();
      if (!(await isHasPushNotification())) {
        // 没有提供通知权限。打开提示通知权限的开关
        const requestNotificationPermissionModalKey =
          'POP_UP_NOTIFY_MODAL_BOOLEAN' + '_' + UserInfoManager.context.userModel.applyOrderId;
        // 缓存和开关
        if (
          !KVManager.action.getBoolean(requestNotificationPermissionModalKey) &&
          BaseInfoManager.context.baseModel.isWaitingPushPop
        ) {
          modalDataStoreInstance.openModal({
            key: ModalList.INFO_PROMPT_CONFIRM,
            i18nKey: 'waitRiskString.notifyTip',
            imageKey: '_notify',
            confirmBtnName: 'btnString.activate',
            cancelBtnName: 'btnString.No',
            isBackdropClose: false,
            confirmBtnCallback: async () => {
              checkPermission();
              TrackEvent.trackCommonEvent(
                {
                  p: HitPointEnumsSpace.EPageKey.P_REVIEWING,
                  e: HitPointEnumsSpace.EEventKey.BTN_PUSH_POPUP,
                },
                '1',
              );
              await TrackEvent.uploadEventLog();
            },
            cancelBtnCallback: async () => {
              TrackEvent.trackCommonEvent(
                {
                  p: HitPointEnumsSpace.EPageKey.P_REVIEWING,
                  e: HitPointEnumsSpace.EEventKey.BTN_PUSH_POPUP,
                },
                '0',
              );
              TrackEvent.uploadEventLog();
            },
          });
          KVManager.action.setBoolean(requestNotificationPermissionModalKey, true);
        }
      }

      loading.current = false;

      BaseInfoManager.changeLoadingModalVisible(false);
    },
    buryCallback: () => {
      if (BaseInfoManager.context.baseModel.isAccUser) {
        trackCommonEvent(
          {
            p: HitPointEnumsSpace.EPageKey.P_GP_REVIEWING,
            e: HitPointEnumsSpace.EEventKey.IN_PAGE,
          },
          '1',
        );
      } else {
        trackCommonEvent(
          {
            p: HitPointEnumsSpace.EPageKey.P_REVIEWING,
            e: HitPointEnumsSpace.EEventKey.IN_PAGE,
          },
          '1',
        );
      }
    },
    buryBlurCallback: () => {
      if (BaseInfoManager.context.baseModel.isAccUser) {
        trackCommonEvent(
          {
            p: HitPointEnumsSpace.EPageKey.P_GP_REVIEWING,
            e: HitPointEnumsSpace.EEventKey.OUT_PAGE,
          },
          '1',
        );
      } else {
        trackCommonEvent(
          {
            p: HitPointEnumsSpace.EPageKey.P_REVIEWING,
            e: HitPointEnumsSpace.EEventKey.OUT_PAGE,
          },
          '1',
        );
      }
    },
    refreshCallback: async () => {
      BaseInfoManager.changeLoadingModalVisible(true);

      loading.current = true;

      await getUserStateAndNextRouterOrOtherCallBack();

      showGooglePlayReviewOnRepay();

      activitySwiperRef.current?.resetValue();

      if (await isHasPushNotification()) {
      } else {
        // 没有提供通知权限。打开提示通知权限的开关
      }

      loading.current = false;

      BaseInfoManager.changeLoadingModalVisible(false);
    },
    isBackAutoRefresh: true,
  });

  const handleNext = onRefresh;

  return {
    onRefresh,
    activitySwiperRef,
    refreshing,
    handleNext,
    waitCheckShowTipTextList,
  };
}
