import { HitPointEnumsSpace, UserEnumsSpace } from '@/enums';
import { useOnInit } from '@/hooks';
import { BaseInfoManager } from '@/managers';
import { Toast } from '@/nativeComponents';
import {
  fetchOfflineRepayMethodDetailAllChannel,
  fetchOfflineRepaymentCostCal,
  responseHandler,
} from '@/server';
import { trackCommonEvent } from '@/trackEvent';
import { OrderVOSpace, ScreenProps } from '@/types';
import Clipboard from '@react-native-clipboard/clipboard';
import dayjs from 'dayjs';
import { useCallback, useMemo, useState, useRef, useEffect } from 'react';
import { Linking, Keyboard } from 'react-native';
import { BaseConfig } from '@/baseConfig';
import { BaseInputRefType } from '@/components';
import { Strings, useNameSpace } from '@/i18n';

interface IProps extends ScreenProps<{}> {}
export default function useData(props: IProps) {
  const { realRemindRepayAmount } = props;
  const { refreshing, setRefreshing, loading, onRefresh } = useOnInit({
    callback: async () => {
      BaseInfoManager.changeLoadingModalVisible(true);
      loading.current = true;
      await getOfflieRepayDetail();
      loading.current = false;
      BaseInfoManager.changeLoadingModalVisible(false);
    },
    refreshCallback: async () => {
      setRefreshing(true);
      loading.current = true;
      await getOfflieRepayDetail();
      loading.current = false;
      setRefreshing(false);
    },
    buryCallback: () => {
      /** 线下还款页埋点 */
      trackCommonEvent(
        {
          p: HitPointEnumsSpace.EPageKey.P_OFFLINE,
          e: HitPointEnumsSpace.EEventKey.IN_PAGE,
        },
        '1',
      );
    },
    buryBlurCallback: () => {
      /** 线下还款页埋点 */
      trackCommonEvent(
        {
          p: HitPointEnumsSpace.EPageKey.P_OFFLINE,
          e: HitPointEnumsSpace.EEventKey.OUT_PAGE,
        },
        '1',
      );
    },
  });

  const [offlineRepayDetail, setOfflieRepayDetail] = useState<OrderVOSpace.RepayOfflineDataType>({
    /**付款码*/
    referenceNumber: '0',
    /** 付款码条形码图片 */
    referenceImage: '',
    /**待还金额*/
    totalAmount: '0',
    /**服务费*/
    repayFee: '0',
    /**服务费vat*/
    repayFeeVat: '0',
    /**到期日期*/
    expirDate: '0',
    /** 线下还款后的到期时间 */
    expiryDateNewFormat: '0',
    /**渠道名称*/
    channelName: undefined,
    /**银行列表图片页*/
    bankImage: '',
  });
  const [money, setMoney] = useState<string>(realRemindRepayAmount);
  const offlineRpeayChannelMinAmount = useRef<string>(
    BaseInfoManager.context.baseModel.appConfig.offlineRpeayChannelMinAmount || '10',
  );

  const [showQrCodeModal, setQrCodeModal] = useState<boolean>(true);
  const [isStart, setStart] = useState<boolean>(false);
  const moneyRef = useRef<string>(realRemindRepayAmount);
  const moneyInput = useRef<BaseInputRefType>(null);
  const t = useNameSpace().t;
  useEffect(() => {
    const keyboardDidHideListener = Keyboard.addListener('keyboardDidHide', () => {
      onRepaymentCostCal();
    });
    return () => {
      keyboardDidHideListener.remove();
    };
  }, []);
  useEffect(() => {
    if (!money && !moneyInput.current?.isFocused()) {
      moneyInput.current?.emitErrorStatus(
        t(Strings.repaymentString.offlineRepaymentMoneyInputErrorTip),
      );
    }
  }, [money]);
  const getOnlineDetail = async (money: string) => {
    const { code, data } = await fetchOfflineRepayMethodDetailAllChannel({
      expectedRepayAmount: money,
    });
    if (code === 0) {
      setOfflieRepayDetail((preState: OrderVOSpace.RepayOfflineDataType) => {
        return {
          ...preState,
          ...data,
          referenceImage: `data:image/png;base64,${data.referenceImage}`,
        };
      });
    }
  };

  const getOfflieRepayDetail = useCallback(async () => {
    await getOnlineDetail(moneyRef.current);
  }, []);

  const onCopyEmail = useCallback(() => {
    Clipboard.setString(BaseConfig.hotMail);
    Toast('Copiado con éxito', 'SHORT');
  }, []);

  const onCopyReferenceNumber = useCallback(() => {
    Clipboard.setString(String(offlineRepayDetail.referenceNumber));
    Toast('Copiado con éxito', 'SHORT');
  }, [offlineRepayDetail.referenceNumber]);

  const hotMail = useMemo(() => {
    return BaseConfig.hotMail;
  }, []);

  const openPaycash = useCallback(() => {
    Linking.openURL('https://www.paycashglobal.com/buscador.php');
  }, []);
  const isNumberInput = (str: string) => {
    return /^\d*\.?\d{0,2}$/.test(str);
  };
  const onSetCorrectValue = () => {
    setMoney((value: string) => {
      if (Number(value) > Number(realRemindRepayAmount)) {
        moneyRef.current = realRemindRepayAmount;
        return realRemindRepayAmount;
      }
      if (Number(value) < Number(offlineRpeayChannelMinAmount.current)) {
        moneyRef.current = offlineRpeayChannelMinAmount.current;
        return offlineRpeayChannelMinAmount.current;
      }
      return value;
    });
  };
  const onChangeMoney = useCallback((value: string) => {
    if (!isNumberInput(value)) return;
    if (value != money) {
      setOfflieRepayDetail(prevState => {
        return {
          ...prevState,
          repayFee: '0',
          repayFeeVat: '0',
          totalAmount: '0',
        };
      });
    }
    setMoney(value);
    moneyRef.current = value;
  }, []);
  const disabled = useMemo(() => {
    return !(
      Number(money) <= Number(realRemindRepayAmount) &&
      Number(money) >= Number(offlineRpeayChannelMinAmount.current)
    );
  }, [money]);
  const onBlur = () => {
    moneyInput.current?.blur();
  };
  const onRepaymentCostCal = useCallback(async () => {
    onBlur();
    onSetCorrectValue();
    moneyInput.current?.clearErrorStatus();
    const money = moneyRef.current;
    const disable = !(
      Number(money) <= Number(realRemindRepayAmount) &&
      Number(money) >= Number(offlineRpeayChannelMinAmount.current)
    );
    if (disable) return;
    const { error, data } = await responseHandler(
      fetchOfflineRepaymentCostCal({ expectedRepayAmount: money }),
    );
    if (error) {
      return;
    }
    setOfflieRepayDetail((prevState: OrderVOSpace.RepayOfflineDataType) => {
      return {
        ...prevState,
        repayFee: data.repayFee,
        repayFeeVat: data.repayFeeVat,
        totalAmount: data.totalAmount,
        bankImage: data.bankImage,
      };
    });
  }, []);
  const generateQrCode = useCallback(async () => {
    trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_OFFLINE,
        e: HitPointEnumsSpace.EEventKey[isStart ? 'BTN_REGENERATE_CODE' : 'BTN_GENERATE_CODE'],
      },
      '1',
    );
    onBlur();
    setStart(true);
    BaseInfoManager.changeLoadingModalVisible(true);
    await onRepaymentCostCal();
    await getOfflieRepayDetail();
    setQrCodeModal(false);
    BaseInfoManager.changeLoadingModalVisible(false);
  }, [isStart]);
  const onFocus = useCallback(() => {
    setQrCodeModal(true);
  }, []);
  return {
    offlineRepayDetail,
    onRefresh,
    refreshing,
    hotMail,
    onCopyEmail,
    onCopyReferenceNumber,
    openPaycash,
    money,
    onChangeMoney,
    offlineRpeayChannelMinAmount: offlineRpeayChannelMinAmount.current,
    onRepaymentCostCal,
    showQrCodeModal,
    disabled,
    generateQrCode,
    isStart,
    onFocus,
    moneyInput,
  };
}
