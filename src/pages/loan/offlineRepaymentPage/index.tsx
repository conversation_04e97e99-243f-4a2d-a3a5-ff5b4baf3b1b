/* eslint-disable react-native/no-inline-styles */

import { Card, Image, Layout, Text, TopNavigation, View, SeaInput, Button } from '@/components';
import { HitPointEnumsSpace, UserEnumsSpace } from '@/enums';
import { ScreenProps } from '@/types';
import React, { ReactElement, useMemo } from 'react';
import {
  ActivityIndicator,
  Dimensions,
  Image as RNImage,
  RefreshControl,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import useData from './useData';
import { Strings, useNameSpace } from '@/i18n';
import { Colors } from '@/themes';

export default ({
  navigation,
  route,
}: ScreenProps<{ realRemindRepayAmount: string }>): ReactElement => {
  const { realRemindRepayAmount = '' } = route?.params || {};
  const {
    offlineRepayDetail,
    refreshing,
    onRefresh,
    hotMail,
    onCopyEmail,
    money,
    onChangeMoney,
    onRepaymentCostCal,
    offlineRpeayChannelMinAmount,
    showQrCodeModal,
    disabled,
    generateQrCode,
    isStart,
    onFocus,
    moneyInput,
  } = useData({ navigation, realRemindRepayAmount });
  const t = useNameSpace().t;
  /** 线下付款二维码 */
  const $renderBarCode = useMemo(() => {
    if (offlineRepayDetail.referenceImage) {
      return (
        <View layoutStrategy="flexColumnStartCenter">
          <RNImage
            style={{
              marginTop: 16,
              marginBottom: 12,
              // resizeMode: 'contain',
            }}
            //@ts-ignore
            name="code"
            //@ts-ignore
            source={{ uri: offlineRepayDetail.referenceImage }}
            width={Dimensions.get('window').width * 0.8}
            height={60}
          />
        </View>
      );
    }
    return <Image name="_offlineQrCode" />;
  }, [offlineRepayDetail.referenceImage]);

  /** 顶部根据不同渠道展示的卡片 */
  const $renderTopCard = useMemo(() => {
    if (offlineRepayDetail.channelName === UserEnumsSpace.ERepayChannel.OPENPAY_STORE) {
      return (
        <View cardType="baseType" padding="16 12 16 12">
          <Image name="_paynetImage" />
          <Image margin="0 6 0 0" name="" style={{ tintColor: 'text-color-800' }} />
          <Text
            margin="8 12 0 12"
            category="p2"
            i18nKey={'repaymentString.peso_repay_offline_channel_openpay_store_tips1'}
          />
        </View>
      );
    }

    return (
      <View cardType="baseType" padding="16 12 16 12" layoutStrategy="flexRowStart">
        <Image name="_whiteNotice" style={{ tintColor: 'text-color-800', width: 20, height: 20 }} />
        <Text margin="0 0 0 8" category="p2" i18nKey={'repaymentString.offlineCard1_1'} />
      </View>
    );
  }, [offlineRepayDetail.channelName]);
  const renderRepaymentQrCode = useMemo(() => {
    const placeholder = t(Strings.repaymentString.offlineRepaymentInput, {
      minAmount: offlineRpeayChannelMinAmount,
    });
    return (
      <View cardType="baseType" padding="16 12 16 12" margin="16 0 0 0">
        <View margin="0 8 18 8">
          <Text i18nKey={Strings.repaymentString.offlineRepaymentPlaceholder} />
          <Image
            name="_offlineRepaymentBorderBg"
            style={{ position: 'absolute', left: 0, bottom: 0, zIndex: -1, borderRadius: 9 }}
          />
        </View>
        <View layoutStrategy="flexRowStart" padding="0 0 0 10">
          <Text textContent="$" category="h1" />
          <View margin="0 40 0 12">
            <SeaInput
              ref={moneyInput}
              onFocus={onFocus}
              type="line"
              placeholder={placeholder}
              value={money}
              setValue={onChangeMoney}
              keyboardType="number-pad"
              accessoryRight={() => {
                return <Image name="_offlineIconEdit" />;
              }}
              onSubmitEditing={onRepaymentCostCal}
              isShowPlaceholderKey
              pageKey={HitPointEnumsSpace.EPageKey.P_OFFLINE}
              eventKey={HitPointEnumsSpace.EEventKey.E_REPAYMENT_AMOUN}
              textStyle={money ? { fontSize: 32, fontWeight: '600' } : {}}
            />
          </View>
        </View>
        <View margin="32 0 0 0" style={{ position: 'relative' }}>
          <View layoutStrategy="flexColumnStartCenter">
            {$renderBarCode}
            <Text
              category="h3"
              bold="bold"
              textContent={String(offlineRepayDetail.referenceNumber).toFormatClabe()}
            />
          </View>
          <View margin="12 0 0 0" padding="6 12 12 12">
            <Text category="p2" isCenter>
              <Text
                category="p2"
                style={{ color: 'danger-color-500' }}
                i18nKey={'repaymentString.offlineCard1_2'}
              />
              <Text
                category="p2"
                style={{ color: 'danger-color-500' }}
                textContent={String(offlineRepayDetail.expirDate).toFormatDate() + '-24:00'}
              />
            </Text>
          </View>
          {showQrCodeModal ? (
            <View
              style={{
                position: 'absolute',
                left: 0,
                top: 0,
                width: '100%',
                height: '100%',
                backgroundColor: 'rgba(255,255,255,0.9)',
              }}
              layoutStrategy="flexRowCenterCenter">
              <Button onPress={generateQrCode} disabled={disabled}>
                {isStart ? <Image name="_offlineIconRefresh" /> : null}
                <Text
                  padding="0 0 0 4"
                  i18nKey={
                    Strings.repaymentString[
                      isStart ? 'offlineRepaymentBtnRefresh' : 'offlineRepaymentBtn'
                    ]
                  }
                  style={{
                    color: Colors.TEXT_COLOR_0,
                  }}
                />
              </Button>
            </View>
          ) : null}
        </View>
        <View padding="0 12 0 12">
          <View
            padding="12 0 12 0"
            layoutStrategy="flexRowBetweenCenter"
            style={{
              borderBottomWidth: 1,
              borderBottomColor: Colors.LINE_COLOR_100,
              borderStyle: 'dashed',
            }}>
            <Text
              category="h3"
              i18nKey={'repaymentString.offlineCard2_3'}
              style={{ color: 'text-color-800' }}
            />
            <Text
              category="h1"
              textContent={String(
                Number(offlineRepayDetail.totalAmount).toFixed(2),
              ).toFormatFinance()}
            />
          </View>
          <View padding="16 0 16 0" layoutStrategy="flexRowBetweenCenter">
            <Text
              category="p2"
              i18nKey={'repaymentString.offlineCard2_1'}
              style={{
                color: 'text-color-600',
              }}
            />
            <Text
              textContent={String(
                (
                  Number(offlineRepayDetail.totalAmount) -
                  Number(offlineRepayDetail.repayFee) -
                  Number(offlineRepayDetail.repayFeeVat)
                ).toFixed(2),
              ).toFormatFinance()}
              style={{
                color: Colors.TEXT_COLOR_700,
              }}
            />
          </View>
          <View padding="0 0 16 0" layoutStrategy="flexRowBetweenCenter">
            <Text
              category="p2"
              i18nKey={'repaymentString.offlineCard2_2'}
              style={{
                color: 'text-color-600',
              }}
            />
            <Text
              textContent={String(
                (
                  Number(offlineRepayDetail.repayFee) + Number(offlineRepayDetail.repayFeeVat)
                ).toFixed(2),
              ).toFormatFinance()}
              style={{
                color: Colors.TEXT_COLOR_700,
              }}
            />
          </View>
        </View>
      </View>
    );
  }, [money, showQrCodeModal, offlineRepayDetail]);
  /** 线下还款渠道 */
  const $rendeContent = useMemo(() => {
    return (
      <>
        {$renderTopCard}
        {renderRepaymentQrCode}
        <View cardType="baseType" padding="16 12 16 12" margin="16 0 0 0">
          <Text
            category="p2"
            i18nKey={'repaymentString.offlineCard2_5'}
            style={{
              color: 'text-color-600',
            }}
          />
          <View margin="10 0 10 0" layoutStrategy="flexRowBetweenCenter">
            <Text category="p1" textContent={hotMail} />
            <TouchableOpacity onPress={onCopyEmail}>
              <Image name="_rePaymentcopyIcon" />
            </TouchableOpacity>
          </View>
        </View>
        <View cardType="baseType" padding="16 12 16 12" margin="16 0 32 0">
          <Text category="p2" i18nKey={'repaymentString.offlineCard3_1'} />
          <View margin="16 0 0 0" layoutStrategy="flexRowStartCenterWarp">
            {offlineRepayDetail.bankImage && (
              <Image
                style={{
                  marginTop: 16,
                  marginBottom: 12,
                  resizeMode: 'contain',
                }}
                //@ts-ignore
                name="bankImage"
                //@ts-ignore
                uri={offlineRepayDetail.bankImage}
                width={Dimensions.get('window').width * 0.8}
              />
            )}
          </View>
        </View>
      </>
    );
  }, [offlineRepayDetail, money, showQrCodeModal]);

  return (
    <>
      <Layout level="1" pLevel="0">
        <TopNavigation titleKey={'repaymentString.onlinePay'} bottomLine />
        <ScrollView
          fadingEdgeLength={10}
          keyboardShouldPersistTaps="always"
          style={{
            paddingVertical: 16,
            paddingHorizontal: 16,
          }}
          refreshControl={
            <RefreshControl refreshing={refreshing} colors={['#4C6EFF']} onRefresh={onRefresh} />
          }>
          {$rendeContent}
        </ScrollView>
      </Layout>
    </>
  );
};
