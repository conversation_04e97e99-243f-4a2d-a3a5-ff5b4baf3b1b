/* eslint-disable react-native/no-inline-styles */

import { Button, Image, Layout, Text, TopNavigation, View } from '@/components';
import React, { ReactElement, useCallback } from 'react';
import { RefreshControl, ScrollView } from 'react-native';
import useData from './useData';
export default (): ReactElement => {
  const { refreshing, onRefresh, lockMessage, unLockTime, canUnLock, handleNext } = useData();

  const rendErrorTip = useCallback(() => {
    return lockMessage?.map((message: string, index: number) => {
      const DATE_PLACE = '{unlock_time}';
      if (!message.includes(DATE_PLACE)) {
        return (
          <Text
            key={index}
            margin="12 0 0 0"
            category="p2"
            status="basic"
            isCenter
            textContent={message}
          />
        );
      } else {
        const messageInfoList = message.split(DATE_PLACE);
        return (
          <Text key={index} margin="12 0 0 0" category="p2" isCenter>
            {messageInfoList[0] && (
              <Text category="p2" status="basic" textContent={messageInfoList[0]} />
            )}
            {unLockTime && <Text category="p2" status="danger" textContent={unLockTime} />}
            {messageInfoList[1] && (
              <Text category="p2" status="basic" textContent={messageInfoList[1]} />
            )}
          </Text>
        );
      }
    });
  }, [lockMessage, unLockTime]);
  return (
    <>
      <Layout level="0" pLevel="1">
        <TopNavigation showLogoAction={true} isShowVip type="basic" showMessage bottomLine />
        <ScrollView
          fadingEdgeLength={10}
          keyboardShouldPersistTaps="always"
          refreshControl={
            <RefreshControl colors={['#4C6EFF']} refreshing={refreshing} onRefresh={onRefresh} />
          }>
          <View
            style={{
              flexDirection: 'column',
              alignItems: 'center',
            }}>
            <Image margin="40 0 0 0" name="_evidenceReject" />
            <View
              layoutStrategy="flexColumnBetweenCenter"
              margin="40 0 0 0"
              padding="8 8 8 8"
              style={{
                width: '80%',
                backgroundColor: 'background-color-0',
                borderRadius: 8,
                // borderWidth: 1,
                // borderColor: 'line-color-200',
              }}>
              {rendErrorTip()}
            </View>
          </View>
        </ScrollView>
        <Button
          margin="16 0 32 0"
          disabled={!canUnLock}
          onPress={handleNext}
          status="primary"
          textI18nKey="btnString.againApply"
        />
      </Layout>
    </>
  );
};
