import { HitPointEnumsSpace } from '@/enums';
import {
  useGetUserStateAndNextRouterOrOtherCallBack,
  useOnInit,
  useSubscribeFilter,
} from '@/hooks';
import { BaseInfoManager, UserInfoContextType, UserInfoManager } from '@/managers';
import { RouterConfig } from '@/routes';
import { confirmUnlock } from '@/server';
import { useCallback } from 'react';

interface IFiletrData {
  lockMessage: string[];
  unLockTime: string;
  canUnLock: boolean;
}

export default function useData() {
  const [getUserStateAndNextRouterOrOtherCallBack] = useGetUserStateAndNextRouterOrOtherCallBack(
    RouterConfig.REJECT,
  );

  /** 初始化方法 */
  const { loading, refreshing, setRefreshing, onRefresh } = useOnInit({
    callback: async () => {
      BaseInfoManager.changeLoadingModalVisible(true);
      loading.current = true;
      await getUserStateAndNextRouterOrOtherCallBack();
      loading.current = false;
      BaseInfoManager.changeLoadingModalVisible(false);
    },
    refreshCallback: async () => {
      setRefreshing(true);
      loading.current = true;
      await getUserStateAndNextRouterOrOtherCallBack();
      loading.current = false;
      setRefreshing(false);
    },
    isBackAutoRefresh: true,
    pageKey: HitPointEnumsSpace.EPageKey.P_REJECT,
  });

  const handleNext = useCallback(async () => {
    if (!loading.current) {
      BaseInfoManager.changeLoadingModalVisible(true);
      loading.current = true;
      // 确认重新贷款
      const { code } = await confirmUnlock();
      if (code === 0) {
        // 更新状态列表数据
        await getUserStateAndNextRouterOrOtherCallBack();
      }
      loading.current = false;
      BaseInfoManager.changeLoadingModalVisible(false);
    }
  }, []);

  /** 人脸超时的提示信息 */
  const { lockMessage, unLockTime, canUnLock } = useSubscribeFilter({
    subject: UserInfoManager.messageCenter,
    filter: (subject: UserInfoContextType) => {
      return {
        unLockTime: subject.userModel?.userState?.unlockTime,
        lockMessage: subject.userModel?.userState?.lockMessage,
        canUnLock: subject.userModel?.isCanUnlocked,
      };
    },
  }) as IFiletrData;

  return {
    onRefresh,
    refreshing,
    handleNext,
    lockMessage,
    unLockTime,
    canUnLock,
  };
}
