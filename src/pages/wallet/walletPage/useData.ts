import { useOnInit } from '@/hooks';
import { useRef, useState } from 'react';
import { RefType as ActivitySwiperRefType } from '../../components/activitySwiper';
import { BaseInfoManager, WalletInfoManager } from '@/managers';
import { RouterConfig } from '@/routes';
import { nav, TrackEvent } from '@/utils';
import { BaseEnumsSpace, HitPointEnumsSpace } from '@/enums';
import { fetchIsInviteDuring } from '@/server';
import { useNameSpace } from '@/i18n';
import { Toast } from '@/nativeComponents';

export default function useData() {
  let activitySwiperRef = useRef<ActivitySwiperRefType>(null);
  const [isDuring, setIsDuring] = useState<boolean>(false);
  const t = useNameSpace().t;
  const { loading, refreshing, onRefresh } = useOnInit({
    callback: async () => {
      BaseInfoManager.changeLoadingModalVisible(true);

      loading.current = true;
      await WalletInfoManager.updateWalletInfo();
      await getInivteDuring();

      loading.current = false;

      BaseInfoManager.changeLoadingModalVisible(false);
    },
    refreshCallback: async () => {
      BaseInfoManager.changeLoadingModalVisible(true);

      loading.current = true;
      activitySwiperRef.current?.resetValue();
      await WalletInfoManager.updateWalletInfo();
      await getInivteDuring();

      loading.current = false;

      BaseInfoManager.changeLoadingModalVisible(false);
    },
    pageKey: HitPointEnumsSpace.EPageKey.P_WALLET,
  });

  /** 打开提现页面 */
  const handleOpenWithdrawPage = async () => {
    TrackEvent.trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_WALLET,
        e: HitPointEnumsSpace.EEventKey.BTN_WALLET_CASHOUT,
      },
      '1',
    );

    BaseInfoManager.changeLoadingModalVisible(true);
    await WalletInfoManager.updateWalletInfo().then(async walletInfoResult => {
      walletInfoResult &&
        (await WalletInfoManager.updateBankCardInfo().then(async bankCardResult => {
          WalletInfoManager.updateWithdrawAmount(Number(0));
          BaseInfoManager.changeLoadingModalVisible(false);
          if (WalletInfoManager.context.walletModel.withdrawCanable) {
            bankCardResult && nav.navigate(RouterConfig.WITHDRAW as any);
          } else {
            Toast(
              t('walletString.withdraw_unable_tips', {
                amount: WalletInfoManager.context.walletModel.walletInfo.canWithDrawMinAmount || 0,
              }),
            );
          }
        }));
    });
  };
  const enterRedeemCouponPage = () => {
    TrackEvent.trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_WALLET,
        e: HitPointEnumsSpace.EEventKey.BTN_REDEEM_COUPON,
      },
      '1',
    );
    nav.navigate(RouterConfig.REDEEM_COUPONS as any);
  };

  /** 打开明细页面 */
  const handleOpenDetailPage = async () => {
    TrackEvent.trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_WALLET,
        e: HitPointEnumsSpace.EEventKey.BTN_WALLET_DETAIL,
      },
      '1',
    );

    nav.navigate(RouterConfig.WALLET_FLOW as any);
  };

  const getInivteDuring = async () => {
    let result = await fetchIsInviteDuring();
    if (result.code === BaseEnumsSpace.ENetworkStatus.SUCCESS && result.data.isDuring) {
      setIsDuring(true);
    } else {
      setIsDuring(false);
    }
  };

  return {
    loading,
    refreshing,
    onRefresh,
    isDuring,
    activitySwiperRef,
    handleOpenDetailPage,
    handleOpenWithdrawPage,
    enterRedeemCouponPage,
  };
}
