import { ReactElement, memo, useMemo } from 'react';
import { Layout, TopNavigation, BusinessUI, View } from '@/components';
import { RefreshControl, ScrollView, StatusBar } from 'react-native';
import WalletCard from './components/walletCard';
import DataPannel from './components/dataPannel';
import useData from './useData';
import ActivitySwiper from '../../components/activitySwiper';
import { BaseEnumsSpace, HitPointEnumsSpace } from '@/enums';
const { VipUpLevelTipCard } = BusinessUI;

const WalletPage = (): ReactElement => {
  const {
    refreshing,
    onRefresh,
    activitySwiperRef,
    handleOpenDetailPage,
    handleOpenWithdrawPage,
    enterRedeemCouponPage,
  } = useData();

  return (
    <>
      <Layout pLevel="0" level="1" topCompensateColor="primary-color-500">
        <TopNavigation
          pageKey={HitPointEnumsSpace.EPageKey.P_WALLET}
          isShowVip
          bottomLine={false}
          showLogoAction={true}
          showMessage
          type="primary"
        />
        <ScrollView
          keyboardShouldPersistTaps="always"
          overScrollMode={'never'}
          refreshControl={
            <RefreshControl colors={['#4C6EFF']} refreshing={refreshing} onRefresh={onRefresh} />
          }>
          <View
            style={{
              height: 180,
              backgroundColor: 'primary-color-500',
            }}></View>
          <View
            style={{
              transform: [{ translateY: -150 }],
            }}>
            <WalletCard
              handleOpenWithdrawPage={handleOpenWithdrawPage}
              handleOpenDetailPage={handleOpenDetailPage}
              enterRedeemCouponPage={enterRedeemCouponPage}
            />
            <VipUpLevelTipCard pageKey={HitPointEnumsSpace.EPageKey.P_WALLET} />
            <ActivitySwiper
              ref={activitySwiperRef}
              margin="0 16 16 16"
              location={BaseEnumsSpace.EBannerLocationType.WALLET}
            />
            <DataPannel />
          </View>
        </ScrollView>
      </Layout>
    </>
  );
};

export default memo(WalletPage);
