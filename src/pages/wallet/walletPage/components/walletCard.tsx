import { View, Text, Image, Button } from '@/components';
import { memo } from 'react';
import { TouchableWithoutFeedback } from 'react-native';
import { WalletInfoContextType, WalletInfoManager } from '@/managers';
import { useSubscribeFilter } from '@/hooks';
import { UserVOSpace } from '@/types';
import Svg, { Circle, G, Path, Defs, ClipPath, Rect } from 'react-native-svg';
import { useTheme } from '@/hooks';
import { Strings } from '@/i18n';
import { Colors } from '@/themes';

interface IProps {
  handleOpenWithdrawPage: () => void;
  handleOpenDetailPage: () => void;
  enterRedeemCouponPage: () => void;
}
const WalletCard = (props: IProps) => {
  const { handleOpenWithdrawPage, handleOpenDetailPage, enterRedeemCouponPage } = props;
  const WalletInfo = useSubscribeFilter({
    subject: WalletInfoManager.messageCenter,
    filter: (subject: WalletInfoContextType) => {
      return subject.walletModel.walletInfo;
    },
  }) as UserVOSpace.WalletInfo;
  const theme = useTheme();
  const redeemCouponsTitle = WalletInfoManager.context.walletModel.walletInfo.redeemCouponsTitle;
  return (
    <View margin="8 16 0 16" padding="16 16 16 16" cardType="baseType">
      <Text
        i18nKey="walletString.title"
        category="p1"
        bold="bold"
        style={{
          color: 'text-color-800',
          fontWeight: '600',
        }}
      />
      <View margin="4 0 0 0" layoutStrategy="flexRowBetweenCenterWrap">
        <Text
          category="h1"
          textContent={String(WalletInfo.totalBalance || 0).toFormatFinance()}
          style={{
            color: 'text-color-800',
            fontWeight: 'bold',
          }}
        />
        <Button
          margin="32 0 0 0"
          padding="12 24 12 24"
          style={{ borderRadius: 99 }}
          onPress={handleOpenDetailPage}
          status="primary"
          textI18nKey="walletString.detail"
        />
      </View>
      <View
        padding="0 12 12 12"
        layoutStrategy="flexRowBetweenCenter"
        style={{
          borderColor: '#c4c4c4',
          borderBottomWidth: 1,
          borderStyle: 'dashed',
        }}>
        <View>
          <View layoutStrategy="flexRowStartCenter">
            <View
              style={{
                width: 6,
                height: 6,
                backgroundColor: Colors.SUCCESS_COLOR_500,
                borderRadius: 6,
              }}
              margin="0 6 0 0"
            />
            <Text
              isCenter
              category="c1"
              i18nKey="walletString.award_balance"
              style={{ color: 'text-color-600' }}
            />
          </View>
          <Text
            margin="4 0 0 8"
            category="p1"
            textContent={String(WalletInfo.cashbackBalance || 0).toFormatFinance()}
          />
        </View>
        <View>
          <View layoutStrategy="flexRowStartCenter">
            <View
              style={{
                width: 6,
                height: 6,
                backgroundColor: Colors.SUCCESS_COLOR_500,
                borderRadius: 6,
              }}
              margin="0 6 0 0"
            />
            <Text
              isCenter
              category="c1"
              i18nKey="walletString.invite_balance"
              style={{ color: 'text-color-600' }}
            />
          </View>

          <Text
            margin="4 0 0 8"
            category="p1"
            textContent={String(WalletInfo.balance || 0).toFormatFinance()}
          />
        </View>
      </View>
      {/* <TouchableWithoutFeedback onPress={handleOpenWithdrawPage}>
        <View margin="16 0 0 0" layoutStrategy="flexRowBetweenCenterWrap">
          <View layoutStrategy="flexRowBetweenCenter">
            {WalletInfoManager.context.walletModel.withdrawCanable ? (
              <DoneProgress />
            ) : (
              <CircularProgress progress={WalletInfoManager.context.walletModel.withdrawProcess} />
            )}
            <Text
              margin="0 0 0 8"
              style={{
                color: WalletInfoManager.context.walletModel.withdrawCanable
                  ? theme['primary-color-500']
                  : theme['text-color-600'],
                textDecorationColor: 'primary-color-500',
                textDecorationLine: 'underline',
              }}
              i18nKey="walletString.withdraw"
            />
          </View>
          <Image
            tintColor={
              WalletInfoManager.context.walletModel.withdrawCanable
                ? theme['primary-color-500']
                : theme['text-color-600']
            }
            name={'_withdrawLeftIcon'}
          />
        </View>
      </TouchableWithoutFeedback> */}
      <TouchableWithoutFeedback onPress={enterRedeemCouponPage}>
        <View padding="12 0 0 0" layoutStrategy="flexRowBetweenCenter">
          <View>
            <View layoutStrategy="flexRowStartCenter">
              <Image name="_walletCouponIcon" />
              <Text i18nKey={Strings.walletString.couponTitle} padding="0 8 0 10" category="p1" />
              <Image name="_walletHandleRightIcon" />
            </View>
            {redeemCouponsTitle && (
              <Text padding="0 0 0 30" category="c2" textContent={redeemCouponsTitle} />
            )}
          </View>
          <Image tintColor={theme['text-color-700']} name={'_withdrawLeftIcon'} />
        </View>
      </TouchableWithoutFeedback>
    </View>
  );
};

const CircularProgress = ({
  size = 32,
  strokeWidth = 3,
  progress = '75', // 进度百分比
  backgroundColor = 'fill-color-400',
  progressColor = 'primary-color-500',
}) => {
  const theme = useTheme();
  const radius = (size - strokeWidth) / 2;
  const circumference = 2 * Math.PI * radius;
  const strokeDashoffset = circumference - (Number(progress) / 100) * circumference;

  return (
    <View style={{ justifyContent: 'center', alignItems: 'center' }}>
      <Svg width={size} height={size}>
        {/* 背景圆环 */}
        <Circle
          stroke={theme[`${backgroundColor}`]}
          fill="none"
          cx={size / 2}
          cy={size / 2}
          r={radius}
          strokeWidth={strokeWidth}
        />
        {/* 进度圆环 */}
        <Circle
          stroke={theme[`${progressColor}`]}
          fill="none"
          cx={size / 2}
          cy={size / 2}
          r={radius}
          strokeWidth={strokeWidth}
          strokeDasharray={circumference}
          strokeDashoffset={strokeDashoffset}
          strokeLinecap="round"
          transform={`rotate(-90 ${size / 2} ${size / 2})`}
        />
      </Svg>
      <Text category="c2" status="primary" style={{ position: 'absolute' }}>{`${progress}%`}</Text>
    </View>
  );
};

const DoneProgress = ({ size = 32, color = 'success-color-500' }) => {
  const theme = useTheme();
  return (
    <Svg width={size} height={size} viewBox="0 0 32 32" fill="none">
      <Defs>
        <ClipPath id="clip0_9732_6167">
          <Rect width="32" height="32" fill="white" />
        </ClipPath>
      </Defs>
      <G clipPath="url(#clip0_9732_6167)">
        <Path
          d="M11.6364 14.5806L14.3636 16.9032L20.3636 12L22 13.5484L14.3636 20L10 16.3366L11.6364 14.5806Z"
          fill={theme[`${color}`]}
        />
        <Circle cx="16" cy="16" r="14.5" stroke={theme[`${color}`]} strokeWidth="3" />
      </G>
    </Svg>
  );
};

export default memo(WalletCard);
