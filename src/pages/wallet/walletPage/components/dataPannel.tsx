import { memo } from 'react';
import { View, Text } from '@/components';
import { WalletInfoContextType, WalletInfoManager } from '@/managers';
import { useSubscribeFilter } from '@/hooks';
import { UserVOSpace } from '@/types';

interface IProps {}
const DataPannel = (props: IProps) => {
  const WalletInfo = useSubscribeFilter({
    subject: WalletInfoManager.messageCenter,
    filter: (subject: WalletInfoContextType) => {
      return subject.walletModel.walletInfo;
    },
  }) as UserVOSpace.WalletInfo;

  return (
    <View margin="16 16 16 16" padding="16 16 16 16" cardType="baseType">
      <Text
        style={{ color: 'text-color-800' }}
        bold="bold"
        category="p1"
        isCenter
        i18nKey="walletString.cumulative_summary"
      />
      <View
        margin="16 0 0 0"
        layoutStrategy="flexRowBetweenCenter"
        style={{
          borderRadius: 8,
        }}>
        <View cardType="baseType" padding="12 4 12 4" style={{ width: '45%', height: 80 }}>
          <Text
            isCenter
            style={{ color: 'text-color-700' }}
            category="c1"
            i18nKey="walletString.card_invite_award"
          />
          <Text
            margin="4 0 0 0"
            isCenter
            category="h3"
            textContent={String(WalletInfo.rewardAmount || 0).toFormatFinance()}
          />
        </View>
        <View cardType="baseType" padding="12 4 12 4" style={{ width: '45%', height: 80 }}>
          <Text
            isCenter
            style={{ color: 'text-color-700' }}
            category="c1"
            i18nKey="walletString.card_invite_number"
          />
          <Text
            margin="4 0 0 0"
            isCenter
            category="h3"
            textContent={String(WalletInfo.invitedCount || 0)}
          />
        </View>
      </View>
      <View
        margin="16 0 0 0"
        layoutStrategy="flexRowBetweenCenter"
        style={{
          borderRadius: 8,
        }}>
        <View cardType="baseType" padding="12 4 12 4" style={{ width: '45%', height: 80 }}>
          <Text
            isCenter
            style={{ color: 'text-color-700' }}
            category="c1"
            i18nKey="walletString.card_cashback_award"
          />
          <Text
            margin="4 0 0 0"
            isCenter
            category="h3"
            textContent={String(WalletInfo.totalCashbackAmount || 0).toFormatFinance()}
          />
        </View>
        <View cardType="baseType" padding="12 4 12 4" style={{ width: '45%', height: 80 }}>
          <Text
            isCenter
            style={{ color: 'text-color-700' }}
            category="c1"
            i18nKey="walletString.card_cashback_number"
          />
          <Text
            margin="4 0 0 0"
            isCenter
            category="h3"
            textContent={String(WalletInfo.totalCashBackCount || 0)}
          />
        </View>
      </View>
    </View>
  );
};

export default memo(DataPannel);
