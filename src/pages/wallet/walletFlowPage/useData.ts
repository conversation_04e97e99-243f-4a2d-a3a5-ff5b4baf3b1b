import { useOnInit, useSubscribeFilter } from '@/hooks';
import { useRef, useState } from 'react';
import { BaseInfoManager, WalletInfoContextType, WalletInfoManager } from '@/managers';
import { fetchUserWaletFlow } from '@/server';
import { BaseEnumsSpace, HitPointEnumsSpace } from '@/enums';
import { UserVOSpace } from '@/types';

export default function useData() {
  const [balanceFlow, setBalanceFlow] = useState<UserVOSpace.WalletFlowDataItem[]>([]);

  const indexRef = useRef<number | null>(null);

  const isMoreDataRef = useRef<boolean>(true);

  const { loading, refreshing, onRefresh } = useOnInit({
    callback: async () => {
      BaseInfoManager.changeLoadingModalVisible(true);
      loading.current = true;
      getUserWalletFlow();
      loading.current = false;
      BaseInfoManager.changeLoadingModalVisible(false);
    },
    refreshCallback: async () => {
      BaseInfoManager.changeLoadingModalVisible(true);
      loading.current = true;
      getUserWalletFlow();
      loading.current = false;
      BaseInfoManager.changeLoadingModalVisible(false);
    },
    pageKey: HitPointEnumsSpace.EPageKey.P_WALLET_DETAIL,
  });

  const getUserWalletFlow = async (isAddMore: boolean = false) => {
    if (!isAddMore) {
      indexRef.current = null;
      isMoreDataRef.current = true;
    }
    if (!(isAddMore && !isMoreDataRef.current)) {
      let result = await fetchUserWaletFlow({ index: indexRef.current || 0 });
      if (result.code === BaseEnumsSpace.ENetworkStatus.SUCCESS) {
        if (indexRef.current === null) {
          setBalanceFlow(result.data?.rows);
        } else {
          if (result.data?.rows.length === 0) {
            isMoreDataRef.current = false;
          }
          setBalanceFlow([...balanceFlow, ...result.data?.rows]);
        }
      }
      indexRef.current = result.data?.index;
    }
  };

  const { balance, rewardAmount, invitedCount } = useSubscribeFilter({
    subject: WalletInfoManager.messageCenter,
    filter: (subject: WalletInfoContextType) => {
      return subject.walletModel.walletInfo;
    },
  }) as UserVOSpace.WalletInfo;

  return {
    loading,
    refreshing,
    onRefresh,
    balance,
    balanceFlow,
    getUserWalletFlow,
  };
}
