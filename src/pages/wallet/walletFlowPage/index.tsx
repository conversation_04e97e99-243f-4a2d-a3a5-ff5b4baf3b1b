import { ReactElement, memo, useCallback, useMemo } from 'react';
import { Layout, View, TopNavigation, Text, ImageBackground } from '@/components';
import { FlatList, RefreshControl, ScrollView } from 'react-native';
import useData from './useData';
import { UserVOSpace } from '@/types';
import { useSubscribeFilter } from '@/hooks';
import { WalletInfoContextType, WalletInfoManager } from '@/managers';
import { Colors } from '@/themes';

interface IProps {
  data: UserVOSpace.WalletFlowDataItem;
}

const FlowItem = memo((props: IProps) => {
  const {
    data: {
      /** 流水ID **/
      flowId,
      /** 金额 */
      totalAmount,
      /** 类型 "1" 代表收入 "2" 代表支出 **/
      side,
      /** 流水生成时间 **/
      createTime,
      /** 流水类型 */
      flowTitle,
      /** 交易时间 */
      transactionTime,
    },
  } = props;
  return (
    <View
      margin="0 0 20 0"
      padding="0 0 12 0"
      style={{
        borderBottomColor: 'background-color-100',
        borderBottomWidth: 1,
      }}
      layoutStrategy="flexRowBetweenCenter">
      <View layoutStrategy="flexColumnStart">
        <Text>
          <Text textContent={flowTitle} style={{ color: 'text-color-800' }} />
        </Text>
        <Text category="p2" style={{ color: 'text-color-600' }} textContent={transactionTime} />
      </View>
      <Text
        style={{ color: side === '1' ? 'success-color-500' : 'danger-color-500' }}
        textContent={totalAmount}
      />
    </View>
  );
});

const WalletFLowPage = (): ReactElement => {
  const { refreshing, onRefresh, balanceFlow, getUserWalletFlow } = useData();
  const WalletInfo = useSubscribeFilter({
    subject: WalletInfoManager.messageCenter,
    filter: (subject: WalletInfoContextType) => {
      return subject.walletModel.walletInfo;
    },
  }) as UserVOSpace.WalletInfo;

  /** 卡包卡片 */
  const $walletCard = useMemo(() => {
    return (
      <View
        margin="20 16 16 16"
        padding="16 16 16 16"
        style={{ backgroundColor: Colors.PRIMARY_COLOR_500, borderRadius: 8 }}>
        <Text
          i18nKey="walletString.title"
          category="c1"
          status="control"
          style={{ color: Colors.TEXT_COLOR_0 }}
        />

        <Text
          margin="4 0 0 4"
          category="h1"
          textContent={String(WalletInfo.totalBalance || 0).toFormatFinance()}
          bold="bold"
          status="control"
          style={{ color: Colors.TEXT_COLOR_0 }}
        />

        <View margin="16 0 0 0" layoutStrategy="flexRowBetweenCenter">
          <View>
            <Text
              isCenter
              category="c1"
              i18nKey="walletString.award_balance"
              style={{ color: Colors.TEXT_COLOR_0 }}
            />
            <Text
              style={{ color: Colors.TEXT_COLOR_0 }}
              margin="4 0 0 8"
              category="p1"
              textContent={String(WalletInfo.cashbackBalance || 0).toFormatFinance()}
            />
          </View>
          <View>
            <Text
              isCenter
              category="c1"
              i18nKey="walletString.invite_balance"
              style={{ color: Colors.TEXT_COLOR_0 }}
            />
            <Text
              style={{
                color: Colors.TEXT_COLOR_0,
              }}
              margin="4 0 0 8"
              category="p1"
              textContent={String(WalletInfo.balance || 0).toFormatFinance()}
            />
          </View>
        </View>
      </View>
    );
  }, [WalletInfo]);

  const $renderFooter = useMemo(() => {
    if (balanceFlow.length === 0) {
      return (
        <View layoutStrategy="flexColumnCenterCenter">
          <Text
            width={'80%'}
            isCenter
            category="p2"
            i18nKey="walletString.noData"
            style={{
              color: 'text-color-600',
            }}
          />
        </View>
      );
    } else {
      return null;
    }
  }, [balanceFlow]);

  const renderItem = useCallback(({ item }: { item: UserVOSpace.WalletFlowDataItem }) => {
    return <FlowItem data={item} key={item.flowId} />;
  }, []);

  return (
    <>
      <Layout pLevel="0" level="1">
        <TopNavigation titleKey={'walletString.flowTitle'} bottomLine />
        {$walletCard}
        <View cardType="baseType" padding="16 16 16 16" margin="0 16 16 16" style={{ flex: 1 }}>
          {/* <Text margin="0 0 12 0" i18nKey="walletString.flowDetail" /> */}
          <FlatList
            style={{ width: '100%', height: '100%', marginBottom: 20 }}
            ListFooterComponent={$renderFooter}
            refreshControl={
              <RefreshControl colors={['#4C6EFF']} refreshing={refreshing} onRefresh={onRefresh} />
            }
            data={balanceFlow}
            onEndReached={() => getUserWalletFlow(true)}
            renderItem={renderItem}
            onEndReachedThreshold={0.3}
          />
        </View>
      </Layout>
    </>
  );
};

export default memo(WalletFLowPage);
