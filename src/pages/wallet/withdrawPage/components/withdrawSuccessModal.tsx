/* eslint-disable react-native/no-inline-styles */
import { CommonModal, Text, View } from '@/components';
import { useSubscribeFilter } from '@/hooks';
import { ModalContextType, ModalList, modalDataStoreInstance } from '@/managers';
import _ from 'lodash';
import { memo, useEffect, useMemo, useRef, useState } from 'react';

/** 提现成功提示 */
export const WithdrawSuccessModal = (): React.ReactElement => {
  let timer = useRef<any>(null).current;
  let [count, setCount] = useState<number>(3);

  const modalInfo = useSubscribeFilter({
    subject: modalDataStoreInstance.messageCenter,
    filter: (subject: ModalContextType) => {
      const maxLength = subject.modalList.length;
      return subject.modalList[maxLength - 1];
    },
  });

  useEffect(() => {
    if (count === 0) {
      modalDataStoreInstance.closeModal(modalInfo?.id);
      countDownCallback();
    }
  }, [count]);

  const visible = useMemo(() => {
    if (modalInfo?.key === ModalList.WITHDRAW_SUCCESS) {
      setCount(3);
      timer = setInterval(() => {
        setCount(prevCount => {
          if (prevCount < 1) {
            clearInterval(timer);
            return 1;
          } else {
            return prevCount - 1;
          }
        });
      }, 1000);
    }
    return modalInfo?.key === ModalList.WITHDRAW_SUCCESS;
  }, [modalInfo]);

  const i18nKey = useMemo(() => {
    return modalInfo?.i18nKey;
  }, [modalInfo]);

  const imageKey = useMemo(() => {
    return modalInfo?.imageKey || '_withdrawModalSuccess';
  }, [modalInfo]);

  const handleBackdrop = _.debounce(() => {
    modalInfo?.isBackdropClose && modalDataStoreInstance.closeModal(modalInfo?.id);
  }, 100);

  const countDownCallback = _.debounce(() => {
    modalInfo?.extra?.countDownCallback && modalInfo?.extra?.countDownCallback();
  }, 100);

  return (
    <CommonModal
      visible={visible}
      i18nKey={i18nKey}
      imageKey={imageKey}
      onBackdropPress={handleBackdrop}>
      <View margin="12 12 12 12" padding="8 12 8 12">
        <Text isCenter={true}>
          <Text
            category="h3"
            bold="700"
            style={{
              color: 'danger-color-500',
            }}
            textContent={`${count}`}
          />
          <Text
            category="h3"
            style={{
              color: 'text-color-800',
            }}
            textContent={'s'}
          />
        </Text>
      </View>
    </CommonModal>
  );
};

/** 通用modal */
export default memo(WithdrawSuccessModal);
