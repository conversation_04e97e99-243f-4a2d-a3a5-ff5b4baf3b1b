/* eslint-disable react-native/no-inline-styles */
import { CommonModal } from '@/components';
import { useSubscribeFilter } from '@/hooks';
import { ModalContextType, ModalList, modalDataStoreInstance } from '@/managers';
import _ from 'lodash';
import { memo, useMemo } from 'react';

/** 提现警告 */
export const WithdrawWarningModal = (): React.ReactElement => {
  const modalInfo = useSubscribeFilter({
    subject: modalDataStoreInstance.messageCenter,
    filter: (subject: ModalContextType) => {
      const maxLength = subject.modalList.length;
      return subject.modalList[maxLength - 1];
    },
  });

  const visible = useMemo(() => {
    return modalInfo?.key === ModalList.WITHDRAW_WARNING;
  }, [modalInfo]);

  const i18nKey = useMemo(() => {
    return modalInfo?.i18nKey;
  }, [modalInfo]);
  const titleKey = useMemo(() => {
    return modalInfo?.titleKey;
  }, [modalInfo]);

  const content = useMemo(() => {
    return modalInfo?.content;
  }, [modalInfo]);

  const imageKey = useMemo(() => {
    return modalInfo?.imageKey || '_withdrawModalWarn';
  }, [modalInfo]);

  const confirmBtnName = useMemo(() => {
    return modalInfo?.confirmBtnName;
  }, [modalInfo]);

  const cancelBtnName = useMemo(() => {
    return modalInfo?.cancelBtnName;
  }, [modalInfo]);

  const handleBackdrop = _.debounce(() => {
    modalInfo?.isBackdropClose && modalDataStoreInstance.closeModal(modalInfo?.id);
  }, 100);

  const handleConfirmBtn = _.debounce(() => {
    modalInfo?.confirmBtnCallback && modalInfo?.confirmBtnCallback();
    modalDataStoreInstance.closeModal(modalInfo?.id);
  }, 100);

  const handleCloseBtn = _.debounce(() => {
    modalInfo?.cancelBtnCallback && modalInfo?.cancelBtnCallback();
    modalDataStoreInstance.closeModal(modalInfo?.id);
  }, 100);

  const buttonType = useMemo(() => {
    return modalInfo?.buttonType || 'nomal';
  }, [modalInfo]);

  const isBottomIconColse = useMemo(() => {
    return modalInfo?.isBottomIconColse;
  }, [modalInfo]);

  const children = useMemo(() => {
    return modalInfo?.children;
  }, [modalInfo]);

  return (
    <CommonModal
      visible={visible}
      titleKey={titleKey}
      i18nKey={i18nKey}
      content={content}
      imageKey={imageKey}
      isBottomIconColse={isBottomIconColse}
      buttonType={buttonType}
      hasLinearGradient={false}
      children={children}
      confirmBtnName={confirmBtnName}
      confirmCallback={handleConfirmBtn}
      cancelBtnName={cancelBtnName}
      cancelCallback={handleCloseBtn}
      onBackdropPress={handleBackdrop}
    />
  );
};

/** 通用modal */
export default memo(WithdrawWarningModal);
