import { memo, useEffect, useMemo } from 'react';
import { Layout, TopNavigation, Text, View, Button, Image, Input } from '@/components';
import WithdrawConfirmModal from './components/withdrawConfirmModal';
import WithdrawSuccessModal from './components/withdrawSuccessModal';
import WithdrawWarningModal from './components/withdrawWarningModal';
import useData from './useData';
import { ScrollView, StatusBar, TouchableWithoutFeedback } from 'react-native';
import { useNameSpace } from '@/i18n';
import { WalletInfoManager } from '@/managers';
import { HitPointEnumsSpace } from '@/enums';

const WithdrawPage = () => {
  const t = useNameSpace().t;
  const {
    handleWithdraw,
    handleWithdrawAll,
    closeIconVisible,
    reciveTotal,
    walletInfo,
    defaultBankCardInfo,
    balanceTipsDisplay,
    handleCloseIcon,
    withdrawInputRef,
    value,
    onChangeText,
    uxCallback,
    validateInputNumber,
  } = useData();

  useEffect(() => {
    setTimeout(() => {
      withdrawInputRef.current?.focus();
    }, 600);
  }, []);

  /** 提现卡片 */
  const $withdrawCard = useMemo(() => {
    const renderLeft = (): React.ReactElement => (
      <Text
        category="h1"
        style={{
          color: 'primary-color-500',
        }}
        textContent="$ "
      />
    );

    const renderRight = (): React.ReactElement => {
      if (closeIconVisible) {
        return (
          <>
            <TouchableWithoutFeedback onPress={handleCloseIcon}>
              <Image name="_withdrawInputCloseIcon" />
            </TouchableWithoutFeedback>
          </>
        );
      } else {
        return (
          <TouchableWithoutFeedback
            onPress={() => {
              handleWithdrawAll();
            }}>
            <Text
              textContent="Retirar todo"
              style={{
                color: 'primary-color-500',
                textDecorationLine: 'underline',
              }}
            />
          </TouchableWithoutFeedback>
        );
      }
    };

    const BalanceTips = (): React.ReactElement => {
      if (!balanceTipsDisplay) {
        return <></>;
      }

      return (
        <>
          <Text
            margin="8 2 0 2"
            category="c2"
            style={{
              color: 'text-color-600',
              fontWeight: '600',
            }}
            textContent={t('walletString.balance_display', {
              amount: `${String(walletInfo?.totalBalance || 0).toFormatFinance()}`,
            })}
          />
        </>
      );
    };

    return (
      <>
        <View margin="16 16 0 16" padding="12 12 12 12" cardType="baseType">
          <Text
            margin="2 8 0 8"
            style={{
              color: 'text-color-800',
              fontWeight: '600',
            }}
            i18nKey="walletString.withdraw"
          />
          <Input
            ref={withdrawInputRef}
            keyboardType="numeric"
            focusIsUpdate={false}
            pageKey={HitPointEnumsSpace.EPageKey.P_CASHOUT}
            eventKey={HitPointEnumsSpace.EEventKey.BTN_CASHOUT_AMOUNT}
            style={{
              height: 48,
              width: '100%',
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'space-between',
              borderRadius: 0,
              borderWidth: 0,
              borderColor: 'fill-color-500',
              backgroundColor: 'background-color-0',
              color: 'text-color-800',
              borderBottomWidth: 1,
            }}
            dangerStyle={{
              height: 48,
              width: '100%',
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'space-between',
              borderRadius: 0,
              borderWidth: 0,
              borderColor: 'danger-color-500',
              backgroundColor: 'background-color-0',
              color: 'text-color-800',
              borderBottomWidth: 1,
            }}
            onBlur={() => {
              validateInputNumber();
            }}
            dangerTextStyle={{
              marginTop: 12,
            }}
            textStyle={{
              fontSize: 24,
              lineHeight: 36,
              fontWeight: 'bold',
            }}
            accessoryLeft={renderLeft}
            accessoryRight={renderRight}
            value={value}
            onChangeText={onChangeText}
            uxCallback={uxCallback}
            validateConfig={[
              {
                condition: WalletInfoManager.context.walletModel.validateIsEnough,
                info: t('walletString.not_enough'),
                status: 'danger',
              },
              {
                condition: WalletInfoManager.context.walletModel.validateIsMutipleAndHigherLow,
                info: t('walletString.not_multiple', {
                  amount: WalletInfoManager.context.walletModel.walletInfo.withDrawMinMultiple,
                  doubleAmount:
                    WalletInfoManager.context.walletModel.walletInfo.withDrawMinMultiple * 2,
                  thirdAmount:
                    WalletInfoManager.context.walletModel.walletInfo.withDrawMinMultiple * 3,
                }),
                status: 'danger',
              },
              {
                condition: Boolean(WalletInfoManager.context.walletModel.withdrawAmount),
                info: '',
                status: 'danger',
              },
            ]}
          />
          <BalanceTips />
          <View margin="8 0 0 0" layoutStrategy="flexRowBetweenCenterWrap">
            <Text
              category="p1"
              style={{
                color: 'text-color-600',
                fontWeight: '500',
              }}
              i18nKey="walletString.channel_fee"
            />
            <Text
              category="p1"
              style={{
                color: 'text-color-800',
                fontWeight: '600',
              }}
              textContent={`-${String(walletInfo?.withDrawFee).toFormatFinance(false)}`}
            />
          </View>
          <View margin="8 0 0 0" layoutStrategy="flexRowBetweenCenterWrap">
            <Text
              category="p1"
              style={{
                color: 'text-color-600',
                fontWeight: '500',
              }}
              i18nKey="walletString.recive_total"
            />
            <Text
              category="p1"
              style={{
                color: 'text-color-800',
                fontWeight: '600',
              }}
              textContent={String(reciveTotal).toFormatFinance(false)}
            />
          </View>
        </View>
      </>
    );
  }, [
    value,
    walletInfo?.withDrawFee,
    walletInfo?.totalBalance,
    reciveTotal,
    balanceTipsDisplay,
    closeIconVisible,
  ]);

  /** 默认银行卡 */
  const $defaultBankCard = useMemo(() => {
    const TipsComp =
      defaultBankCardInfo?.bankCode?.length === 0 ? (
        <Text
          category="c1"
          style={{
            color: 'danger-color-500',
          }}
          margin="12 0 0 0"
          i18nKey="walletString.default_bank_error"
        />
      ) : (
        <Text
          category="c1"
          style={{
            color: 'text-color-600',
          }}
          margin="12 0 0 0"
          i18nKey="walletString.default_bank_desc"
        />
      );
    return (
      <>
        <View margin="16 16 0 16" padding="12 12 12 12" cardType="baseType">
          <Text margin="8 0 8 0" i18nKey="walletString.default_bank_card_title" />
          <View
            padding="12 12 12 12"
            style={{
              backgroundColor: 'fill-color-200',
              borderRadius: 8,
            }}>
            <Text
              style={{
                color: 'text-color-600',
              }}
              i18nKey="walletString.default_bank_name"
            />
            <Text
              style={{
                color: 'text-color-800',
                fontWeight: '600',
              }}
              margin="8 0 0 0"
              textContent={String(defaultBankCardInfo?.bankName) || '******'}
            />
            <Text
              style={{
                color: 'text-color-600',
              }}
              margin="16 0 8 0"
              i18nKey="walletString.default_bank_clabe"
            />
            <Text
              style={{
                color: 'text-color-800',
                fontWeight: '600',
              }}
              textContent={String(defaultBankCardInfo?.cardNo).toFormatClabe() || '******'}
            />
          </View>
          {TipsComp}
        </View>
      </>
    );
  }, []);

  /** 按钮区域 */
  const $button = useMemo(() => {
    return (
      <>
        <Button
          margin="24 32 24 32"
          padding="12 24 12 24"
          onPress={handleWithdraw}
          status="primary"
          textI18nKey="walletString.withdraw_confirm"
        />
      </>
    );
  }, []);

  return (
    <>
      <Layout level="0" pLevel="1">
        <TopNavigation bottomLine={true} titleKey="walletString.withdraw_title" isBack={true} />
        <ScrollView fadingEdgeLength={10} keyboardShouldPersistTaps="always">
          {$withdrawCard}
          {$defaultBankCard}
          {$button}
        </ScrollView>
      </Layout>
      <WithdrawConfirmModal />
      <WithdrawSuccessModal />
      <WithdrawWarningModal />
    </>
  );
};

export default memo(WithdrawPage);
