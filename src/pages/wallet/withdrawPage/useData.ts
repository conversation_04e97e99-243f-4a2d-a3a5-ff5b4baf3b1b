import { InputRefType } from '@/components';
import { BaseEnumsSpace, HitPointEnumsSpace } from '@/enums';
import { useOnInit, useSubscribeFilter } from '@/hooks';
import { useNameSpace } from '@/i18n';
import {
  BaseInfoManager,
  modalDataStoreInstance,
  ModalList,
  UserInfoManager,
  WalletInfoContextType,
  WalletInfoManager,
} from '@/managers';
import { fetchWalletBalanceWithdraw } from '@/server';
import { OrderVOSpace, UserVOSpace } from '@/types';
import { isNumberInput, Log, TrackEvent } from '@/utils';
import _ from 'lodash';
import { useCallback, useEffect, useRef, useState } from 'react';

const useData = () => {
  const t = useNameSpace().t;
  const withdrawInputRef = useRef<InputRefType>(null);
  const [value, setValue] = useState<string>('');
  const [closeIconVisible, setCloseIconVisible] = useState<boolean>(false);

  /** 初始化方法 */
  useOnInit({
    pageKey: HitPointEnumsSpace.EPageKey.P_CASHOUT,
  });

  const { walletInfo, reciveTotal, defaultBankCardInfo, balanceTipsDisplay } = useSubscribeFilter({
    subject: WalletInfoManager.messageCenter,
    filter: (subject: WalletInfoContextType) => {
      return {
        walletInfo: subject.walletModel.walletInfo,
        reciveTotal: subject.walletModel.reciveTotal,
        defaultBankCardInfo: subject.walletModel.defaultBankCardInfo,
        balanceTipsDisplay: subject.walletModel.balanceTipsDisplay,
      };
    },
  }) as {
    walletInfo: UserVOSpace.WalletInfo;
    reciveTotal: number;
    defaultBankCardInfo: OrderVOSpace.DefaultBankCardInfo;
    balanceTipsDisplay: boolean;
  };

  /** 输入框更新 */
  const onChangeText = useCallback(
    (value: string) => {
      /** 0 或者空字符串的处理 */
      if (value == '0' || value.length === 0) {
        setValue(String(value));
        WalletInfoManager.updateWithdrawAmount(0);
        withdrawInputRef.current?.clearErrorStatus();
        return;
      }

      /** 自动转换处理 */
      if (value === '.') {
        setValue('0.');
        WalletInfoManager.updateWithdrawAmount(0);
        validateInputNumber();
        return;
      }

      // 过滤不规则的字符
      if (isNumberInput(value)) {
        setValue(String(value).trim());
        WalletInfoManager.updateWithdrawAmount(Number(value));
        validateInputNumber();
      }
    },
    [value, setValue, walletInfo.withDrawMinAmount],
  );

  /** 交互行为回调 */
  const uxCallback = (status: string) => {
    switch (status) {
      case 'danger':
        setCloseIconVisible(true);
        WalletInfoManager.updateBalanceTipsDisplay(false);
        break;
      default:
        setCloseIconVisible(false);
        WalletInfoManager.updateBalanceTipsDisplay(true);
    }
  };

  /** 校验输入框内容 */
  const validateInputNumber = () => {
    if (WalletInfoManager.context.walletModel.withdrawStatus === 'not_enough') {
      withdrawInputRef.current?.emitErrorStatus(t('walletString.not_enough'));
      return false;
    }

    if (
      WalletInfoManager.context.walletModel.withdrawStatus === 'not_multiple' ||
      WalletInfoManager.context.walletModel.withdrawStatus === 'low_min'
    ) {
      withdrawInputRef.current?.emitErrorStatus(
        t('walletString.not_multiple', {
          amount: WalletInfoManager.context.walletModel.walletInfo.withDrawMinMultiple,
          doubleAmount: WalletInfoManager.context.walletModel.walletInfo.withDrawMinMultiple * 2,
          thirdAmount: WalletInfoManager.context.walletModel.walletInfo.withDrawMinMultiple * 3,
        }),
      );
      return false;
    }

    if (
      WalletInfoManager.context.walletModel.withdrawStatus === 'ok' ||
      WalletInfoManager.context.walletModel.withdrawStatus === 'pending'
    ) {
      withdrawInputRef.current?.clearErrorStatus();
      return true;
    }
  };

  const handleCloseIcon = () => {
    setValue('');
    WalletInfoManager.updateWithdrawAmount(Number(0));
    withdrawInputRef.current?.clearErrorStatus();
    setCloseIconVisible(false);
    setTimeout(() => {
      withdrawInputRef.current?.focus();
    }, 300);
  };

  /** 全部提现 */
  const handleWithdrawAll = () => {
    WalletInfoManager.context.walletModel.withdrawStatus = 'ok';

    let _banlance =
      WalletInfoManager.context.walletModel.walletInfo.totalBalance -
      (WalletInfoManager.context.walletModel.walletInfo.totalBalance %
        WalletInfoManager.context.walletModel.walletInfo.withDrawMinMultiple);

    WalletInfoManager.updateWithdrawAmount(Number(_banlance));
    setValue(String(_banlance));
  };

  /** 点击提现 */
  const handleWithdraw = () => {
    withdrawInputRef.current?.blur();
    TrackEvent.trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_CASHOUT,
        e: HitPointEnumsSpace.EEventKey.BTN_CASHOUT_COMFIRM,
      },
      '1',
    );
    TrackEvent.uploadEventLog();

    if (Number(WalletInfoManager.context.walletModel.withdrawAmount) <= 0) {
      return;
    }

    if (UserInfoManager.context.userModel.isHasActiveOrder) {
      modalDataStoreInstance.openModal({
        key: ModalList.WITHDRAW_WARNING,
        i18nKey: 'walletString.warn_modal_tips',
        confirmBtnName: 'walletString.warn_modal_ok',
        confirmBtnCallback: () => {},
      });
      return;
    }

    if (WalletInfoManager.context.walletModel.withdrawStatus === 'ok') {
      modalDataStoreInstance.openModal({
        key: ModalList.WITHDRAW_CONFIRM,
        content: t('walletString.confirm_modal_tips', {
          amount: `${String(
            WalletInfoManager.context.walletModel.walletInfo.withDrawFee,
          ).toFormatFinance(false)}`,
        }),
        confirmBtnName: 'walletString.confirm_modal_ok',
        cancelBtnName: 'walletString.confirm_modal_cancel',
        confirmBtnCallback: async () => {
          TrackEvent.trackCommonEvent(
            {
              p: HitPointEnumsSpace.EPageKey.P_CASHOUT,
              e: HitPointEnumsSpace.EEventKey.BTN_CASHOUT_POPUPCOMFIRM,
            },
            '1',
          );

          await withdrawAsync();
        },
        cancelBtnCallback: () => {
          TrackEvent.trackCommonEvent(
            {
              p: HitPointEnumsSpace.EPageKey.P_CASHOUT,
              e: HitPointEnumsSpace.EEventKey.BTN_CASHOUT_POPUPCANCEL,
            },
            '0',
          );
        },
      });
    }
  };

  /** 提现逻辑 */
  const withdrawAsync = async () => {
    BaseInfoManager.changeLoadingModalVisible(true);
    let result = await fetchWalletBalanceWithdraw({
      amount: Number(WalletInfoManager.context.walletModel.withdrawAmount),
    });

    if (result.code === BaseEnumsSpace.ENetworkStatus.SUCCESS) {
      await WalletInfoManager.updateWalletInfo().then(async walletInfoResult => {
        modalDataStoreInstance.openModal({
          key: ModalList.WITHDRAW_SUCCESS,
          isBackdropClose: false,
          i18nKey: 'walletString.success_modal_tips',
          extra: {
            countDownCallback: async () => {
              setValue('');
              WalletInfoManager.updateWithdrawAmount(0);
              withdrawInputRef.current?.emitErrorStatus('');
              BaseInfoManager.changeLoadingModalVisible(false);
            },
          },
        });
      });
    } else {
      BaseInfoManager.changeLoadingModalVisible(false);
    }
  };

  return {
    reciveTotal,
    defaultBankCardInfo,
    balanceTipsDisplay,
    walletInfo,
    handleWithdraw,
    handleWithdrawAll,
    withdrawInputRef,
    value,
    closeIconVisible,
    onChangeText,
    uxCallback,
    handleCloseIcon,
    validateInputNumber,
  };
};

export default useData;
