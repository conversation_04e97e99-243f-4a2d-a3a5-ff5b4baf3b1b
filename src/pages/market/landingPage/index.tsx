import React, { memo, ReactElement, useCallback, useMemo, useRef } from 'react';
import Layout from '../../../components/layout';
import TopNavigation from '../../../components/topNavigation';
import { HitPointEnumsSpace } from '@/enums';
import { BaseInfoManager, UserInfoManager } from '@/managers';

import { trackCommonEvent, uploadEventLog } from '@/trackEvent';
import Log from '../../../utils/log';
import _ from 'lodash';
import { ScreenProps } from '@/types';
import { useOnInit, useTheme } from '@/hooks';
import { BaseConfig } from '@/baseConfig';
import { Linking, View } from 'react-native';

import WebView from 'react-native-webview';

interface IScreenProps {
  /** url携带query变量 */
  query?: string;
  /** 活动规则 */
  rule?: string;
  /** 活动链接 */
  link: string;
  /** 落地页标题 */
  title?: string;
  /** 页面key */
  pageKey: HitPointEnumsSpace.EPageKey;
  /** 加载时背景色 */
  loadingColor?: string;
}

/** 落地页模版 */
export default memo(({ navigation, route }: ScreenProps<IScreenProps>): ReactElement => {
  const webViewRef = useRef<any>(null);
  const theme = useTheme();
  const {
    pageKey = '',
    link = '',
    query = '',
    rule = '',
    title = '',
    loadingColor = '',
  } = route?.params;

  useOnInit({
    pageKey,
  });

  const source = useMemo(() => {
    return {
      uri: `${link}?${encodeURIComponent(`${query}`)}`,
    };
  }, [link, query]);

  const onLoadEnd = useCallback(() => {
    const payload = {
      rule,
    };
    /** 向app传递对象数据, 例如规则 */
    webViewRef.current.injectJavaScript(`receiveMessage(${JSON.stringify(payload)})`);
    BaseInfoManager.changeLoadingModalVisible(false);
  }, [rule]);

  const handleShouldStartLoadWithRequest = (request: { url: any }) => {
    return true;
  };

  const onMessage = useCallback((e: any) => {
    try {
      // 事件名称, 参数字符串
      let [event, params] = ['', ''];
      [event, params] = e.nativeEvent.data.split('?');

      switch (event) {
        /** h5上报埋点事件 */
        case 'BURY_POINT':
          let [ePage, eEvent, eContent] = decodeURIComponent(params).split('&');
          trackCommonEvent(
            {
              p: ePage as HitPointEnumsSpace.EPageKey,
              e: eEvent as HitPointEnumsSpace.EEventKey,
            },
            eContent || '',
          );
          uploadEventLog();
          break;
        /** 跳转vip规则页 */
        case 'GO_VIP_RULE':
          UserInfoManager.getVipConfigAndNavigate({});
          break;
        /** 跳转vip规则细则页 */
        case 'GO_VIP_RULE_DETAIL':
          UserInfoManager.navigateVipDetailRule({});
          break;
        /** 跳转vip规则细则页 */
        case 'GO_WEBSITE':
          Linking.openURL(`${BaseConfig.websiteUrl}${BaseConfig.vipWebsiteRulesUrl}`);
          break;
        /** 其他扩展事件, 例如h5上报活动积分、app内部弹窗、跳转app其他页面 */
        case 'UPLOAD_RECORD':
          break;
        default:
      }
    } catch (error) {
      Log.error('#Land page >  webview onMessage error:', error);
    }
  }, []);

  return (
    <View>
      <Layout level="0" pLevel="0">
        <TopNavigation titleContent={title} isBack={true} bottomLine={true} />
        <WebView
          ref={webViewRef}
          style={{
            flex: 1,
            backgroundColor: theme[loadingColor || 'background-color-0'],
          }}
          cacheMode="LOAD_CACHE_ELSE_NETWORK"
          onLoadEnd={onLoadEnd}
          startInLoadingState={false}
          onMessage={onMessage}
          originWhitelist={['*']}
          javaScriptEnabledAndroid={true}
          source={source}
          onShouldStartLoadWithRequest={handleShouldStartLoadWithRequest}
        />
      </Layout>
    </View>
  );
});
