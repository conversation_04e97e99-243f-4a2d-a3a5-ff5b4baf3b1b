import { SwiperImage, View } from '@/components';
import { useOnInit } from '@/hooks';
import { fetchGetAppBanner } from '@/server';
import React, {
  useMemo,
  useState,
  forwardRef,
  Ref,
  useImperativeHandle,
  useRef,
  useCallback,
} from 'react';
import { Linking } from 'react-native';
import { BaseEnumsSpace, HitPointEnumsSpace } from '@/enums';
import { OrderVOSpace } from '@/types';
import { Log, TrackEvent, nav } from '@/utils';
import { RouterConfig } from '@/routes';
import _ from 'lodash';
import { UserInfoManager } from '@/managers';

interface IProps {
  /** 导航查询的位置 */
  location: BaseEnumsSpace.EBannerLocationType;
  /** 自定义样式 */
  customStyle?: object;
  /** 边距 */
  margin?: string;
}

export type RefType = {
  resetValue: () => void;
};

/** 活动轮播 */
export default forwardRef((props: IProps, ref: Ref<RefType>) => {
  const { location, customStyle = {}, margin = '16 16 0 16' } = props;
  const [state, setState] = useState<OrderVOSpace.BannerDataType[]>([]);
  let trackFlag = useRef<boolean>(false).current;

  useImperativeHandle(ref, () => ({
    resetValue() {
      getBannerList();
    },
  }));

  /** banner item点击回调 */
  const bannerItemClickCallback = useCallback(
    async (item: string) => {
      try {
        state.forEach(async (bannerItem, bannerIndex) => {
          if (bannerItem.logo === item) {
            switch (bannerItem.activityType) {
              /** 跳转app内部页面 */
              case BaseEnumsSpace.EBannerActivityType.BING_GOOGLE:
                trackBannerClickItem(location, bannerItem.activityType);
                nav.navigate(RouterConfig.ACCOUNT_BINDING as any);
                break;
              /** 跳转app外部第三方H5页面 */
              case BaseEnumsSpace.EBannerActivityType.OPEN_FACEBOOK:
                if (await Linking.canOpenURL(bannerItem.link)) {
                  trackBannerClickItem(location, bannerItem.activityType);
                  Linking.openURL(bannerItem.link);
                }
                break;
              /** 邀请新人落地页, 活动页模版; 支持链接、标题配置; 传入app内部数据 */
              case BaseEnumsSpace.EBannerActivityType.INVITE_NEW_USER:
                trackBannerClickItem(location, bannerItem.activityType);
                UserInfoManager.getInviteDataAndNavigateLand({
                  link: bannerItem.link,
                  title: bannerItem.title || 'Información general',
                });
                break;
              case BaseEnumsSpace.EBannerActivityType.INTERNAL_H5:
                if (await Linking.canOpenURL(bannerItem.link)) {
                  nav.navigate(RouterConfig.INVITE_USER as any, {
                    link: bannerItem.link,
                    title: bannerItem.title,
                    pageKey: HitPointEnumsSpace.EPageKey.P_INTERNAL_H5,
                  });
                }
                break;
              case BaseEnumsSpace.EBannerActivityType.EXTERNAL_H5:
                if (await Linking.canOpenURL(bannerItem.link)) {
                  Linking.openURL(bannerItem.link);
                }
                break;
            }
          }
        });
      } catch (error) {
        Log.error('#Banner click >', error);
      }
    },
    [state],
  );

  /** 点击banner埋点上报 */
  const trackBannerClickItem = async (
    location: BaseEnumsSpace.EBannerLocationType,
    activityType: BaseEnumsSpace.EBannerActivityType,
  ) => {
    Log.info(`#trackBannerClickItem ${location} ${activityType}`);
    let c: BaseEnumsSpace.EBannerActivityType = '' as BaseEnumsSpace.EBannerActivityType;
    let p: HitPointEnumsSpace.EPageKey = '' as HitPointEnumsSpace.EPageKey;
    switch (activityType) {
      case BaseEnumsSpace.EBannerActivityType.BING_GOOGLE:
        c = BaseEnumsSpace.EBannerActivityType.BING_GOOGLE_TRACK;
        break;
      case BaseEnumsSpace.EBannerActivityType.OPEN_FACEBOOK:
        c = BaseEnumsSpace.EBannerActivityType.OPEN_FACEBOOK;
        break;
      case BaseEnumsSpace.EBannerActivityType.INVITE_NEW_USER:
        c = BaseEnumsSpace.EBannerActivityType.INVITE_NEW_USER;
    }
    switch (location) {
      case BaseEnumsSpace.EBannerLocationType.MINE:
        p = HitPointEnumsSpace.EPageKey.P_PERSONAL_CENTER;
        break;
      case BaseEnumsSpace.EBannerLocationType.RELOAN_HOME:
        p = HitPointEnumsSpace.EPageKey.P_HOMEPAGE;
        break;
      case BaseEnumsSpace.EBannerLocationType.WAIT_CHECK:
        p = HitPointEnumsSpace.EPageKey.P_REVIEWING;
        break;
      case BaseEnumsSpace.EBannerLocationType.WALLET:
        p = HitPointEnumsSpace.EPageKey.P_WALLET;
    }

    if (!trackFlag) {
      trackFlag = true;
      _.delay(function (text) {
        trackFlag = false;
      }, 3000);

      await TrackEvent.trackCommonEvent(
        {
          p,
          e: HitPointEnumsSpace.EEventKey.BTN_BANNER_CLICK,
        },
        c,
      );
      await TrackEvent.uploadEventLog();
    }
  };

  /** swiper展示内容 */
  const list = useMemo(() => {
    return state.map(bannerItem => bannerItem.logo);
  }, [state]);

  /** 是否展示底部指示器 */
  const canableShowBottom = useMemo(() => {
    if (state.length === 1) {
      return false;
    } else if (state.length > 1) {
      return true;
    }
  }, [state]);

  useOnInit({
    callback: async () => {
      getBannerList();
    },
  });

  const getBannerList = async () => {
    let bannerListResult = await fetchGetAppBanner({
      location,
    });

    if (bannerListResult.code === BaseEnumsSpace.ENetworkStatus.SUCCESS) {
      setState(bannerListResult.data);
    }
  };

  if (list.length === 0) {
    return <></>;
  }

  return (
    <View margin={margin}>
      <SwiperImage
        style={{
          ...customStyle,
        }}
        bannerItemClickCallback={bannerItemClickCallback}
        canableShowBottom={canableShowBottom}
        bottomType={'square'}
        backgroundColor="#FFFFFF"
        bottomMarkColor="fill-color-500"
        bottomMarkActiveColor="fill-color-300"
        textColor="warn-color-500"
        list={list}
      />
    </View>
  );
});
