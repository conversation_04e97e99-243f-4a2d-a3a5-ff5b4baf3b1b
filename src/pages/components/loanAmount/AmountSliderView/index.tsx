/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react-native/no-inline-styles */
/* eslint-disable react/react-in-jsx-scope */
import {
  AmountSlider,
  BusinessUI,
  Button,
  Image,
  Layouts,
  Text,
  View,
  LoanDatePickerInput,
  DashedLine,
} from '@/components';
import { EProductType, HitPointEnumsSpace } from '@/enums';
import { useNameSpace } from '@/i18n';
import { OrderResponseSpace } from '@/server';
import { trackCommonEvent } from '@/trackEvent';
import _ from 'lodash';
import { Ref, memo, useCallback, useMemo, useState } from 'react';
import { GestureResponderEvent, ScrollView } from 'react-native';
import ActivitySwiper, {
  RefType as ActivitySwiperRefType,
} from '../../../components/activitySwiper';

import React from 'react';
import { OrderVOSpace, UserVOSpace } from '@/types';
import { modalDataStoreInstance, ModalList, BaseInfoManager } from '@/managers';
const { AutoWithholdCardAutoBind } = BusinessUI;
import { Colors } from '@/themes';
import {
  LoanDetailCard,
  LoanDetailModal,
  CouponSelectCard,
  CouponHelpModal,
  CouponSelectModal,
  IncreaseCouponAmountBadge
} from "../../coupon"


interface IState
  extends OrderResponseSpace.RooutAmountData,
  OrderResponseSpace.ReloanConfigData {
  loanAmount: number;
  calculateCost: OrderVOSpace.CalculateCostOnReLoanType;
  repayDate?: string;
}
interface IProps extends OrderResponseSpace.ReloanConfigData {
  isReadContract: boolean;
  // onChangeIsReadContract: (isReadContract: boolean) => void;
  onChangeAmount: (defaultAmount: number) => void;
  onUpdateAmount: (defaultAmount: number) => void;
  amount: string;
  term: string;
  // handleClick: (event: GestureResponderEvent) => void;
  onGetHomePageAmout: (event: GestureResponderEvent) => void;
  isPending: boolean;
  loanAmount: number;
  reLoanAmountConfigList: IState[];
  selectedAmountConfigNumber: number;
  onChangeAmountConfigNumber: (num: number) => void;
  activitySwipeRef: Ref<ActivitySwiperRefType>;
  autoWithhold: boolean;
  onOpenAutoWithhold: () => void;
  onCloseAutoWithhold: () => void;
  openAutoWithholdContract: () => void;
  reloanHomeWithholdBankCardInfo: OrderVOSpace.ReloanHomeWithholdBankCardInfo;
  couponList: UserVOSpace.CouponsItem[];
  couponSelected: UserVOSpace.CouponsItem[];
  showCouponHelpModal: boolean;
  showCouponSelectModal: boolean;
  onCloseCouponHelpModal: () => void;
  onCloseCouponSelectModal: () => void;
  onShowCouponHelpModal: () => void;
  onShowCouponSelectModal: () => void;
  onConfirmSelectCoupon: (coupons?: UserVOSpace.CouponsItem[]) => void;
  /** 还款日期 */
  repayDate: string;
  /** 变更还款日期 */
  onChangeRepayDate: (repayDate: string) => void;
  reloanRepayDays: OrderVOSpace.LoanRepayDays;
  /**
   * 打开贷款合同
   */
  onOpenContract: () => void;
  serviceFeePerDayRate?: string;
  onToggleRepayDateModal: () => void;
  calendarExtraData?: any;
}
const AmountSliderView = function AmountSliderView(props: IProps) {
  const {
    // isReadContract,
    // onChangeIsReadContract,
    days = 0,
    minAmount = 0,
    maxAmount = 0,
    defaultAmount = 0,
    loanAmount = 0,
    increment = 0,
    onChangeAmount,
    onUpdateAmount,
    // handleClick,
    onGetHomePageAmout,
    isPending,
    reLoanAmountConfigList,
    selectedAmountConfigNumber,
    onChangeAmountConfigNumber,
    autoWithhold,
    onOpenAutoWithhold,
    onCloseAutoWithhold,
    openAutoWithholdContract,
    reloanHomeWithholdBankCardInfo,
    couponList,
    couponSelected,
    showCouponHelpModal,
    showCouponSelectModal,
    onCloseCouponHelpModal,
    onCloseCouponSelectModal,
    onShowCouponHelpModal,
    onShowCouponSelectModal,
    onConfirmSelectCoupon,
    repayDate,
    onChangeRepayDate,
    reloanRepayDays,
    onOpenContract,
    serviceFeePerDayRate,
    onToggleRepayDateModal,
    calendarExtraData,
  } = props;
  /** 复贷详情 */
  const [reloanDetailDialogVisible, setReloanDetailDialogVisible] = useState<boolean>(false);

  /** 改变复贷详情弹框 */
  const changeReloanDetailDialogVisible = useCallback(
    (visible: boolean) => {
      if (visible) {
        trackCommonEvent(
          {
            p: HitPointEnumsSpace.EPageKey.P_REPEAT_CHOOSE_AMOUNT,
            e: HitPointEnumsSpace.EEventKey.BTN_CREDIT_DETAILS,
          },
          '1',
        );
      }

      setReloanDetailDialogVisible(visible);
    },
    [reloanDetailDialogVisible],
  );
  const onShowLoanDetailModal = useCallback(() => {
    trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_REPEAT_CHOOSE_AMOUNT,
        e: HitPointEnumsSpace.EEventKey.BTN_CREDIT_DETAILS,
      },
      '1',
    );
    setReloanDetailDialogVisible(true);
  }, []);
  const onCloseLoanDetailModal = useCallback(() => {
    setReloanDetailDialogVisible(false);
  }, []);

  const onSlidingComplete = (value: number) => {
    trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_REPEAT_CHOOSE_AMOUNT,
        e: HitPointEnumsSpace.EEventKey.E_BTN_CHOOSE_AMOUNT,
      },
      String(value),
    );
    onChangeAmount(value);
  };

  const onValueChange = (value: number) => {
    onUpdateAmount(value);
  };

  const currentSelAmount = useMemo(() => {
    if (loanAmount != 0) {
      return String(loanAmount).toFormatFinance();
    } else {
      return '--'.toFormatFinance();
    }
  }, [loanAmount]);

  const t = useNameSpace().t;

  const renderTermButtonView = useCallback(
    (loanAmountConfig: IState, index: number) => {
      const { days, enable, productType, repayDate } = loanAmountConfig;

      const enabled = enable === 'YES';
      const seleted = selectedAmountConfigNumber === index;
      // 初始化正常状态按钮样式
      let textColor: string = Colors.PRIMARY_COLOR_500;
      let borderColor: string = Colors.PRIMARY_COLOR_500;
      if (!enabled) {
        textColor = Colors.PRIMARY_COLOR_300;
        borderColor = Colors.PRIMARY_COLOR_300;
      }
      if (seleted) {
        textColor = Colors.TEXT_COLOR_0;
        borderColor = Colors.BACKGROUND_COLOR_0;
      }

      const textContent =
        productType === EProductType.FIXED_DATE ? repayDate : String(days).toFormatDay();
      const onSelect = _.throttle(() => {
        trackCommonEvent(
          {
            p: HitPointEnumsSpace.EPageKey.P_REPEAT_CHOOSE_AMOUNT,
            e: HitPointEnumsSpace.EEventKey.BTN_DATE_CHANGE,
          },
          String(days),
          enable ? '1' : '0',
        );
        if (!enabled) {
          modalDataStoreInstance.openModal({
            key: ModalList.INFO_PROMPT_CONFIRM,
            i18nKey: 'homeString.lockLoanTremClickTip',
            imageKey: '_epModalNormalIcon',
            confirmBtnName: 'btnString.agree',
            isBackdropClose: false,
          });
          // Toast(t('homeString.lockLoanTremClickTip'));
        }
        if (enabled && !seleted) {
          // 记录用户选择额度的事件

          onChangeAmountConfigNumber(index);
        }
      }, 300);

      return (
        <Button
          margin="0 3 0 3"
          padding="5 15 5 15"
          onPress={onSelect}
          size="custom"
          key={index.toString()}
          style={{
            position: 'relative',
            borderColor: borderColor,
            borderRadius: 8,
            borderWidth: 1,
          }}
          appearance={seleted ? 'filled' : 'outline'}>
          {!enabled && (
            <Image
              name="_lockTipIcon"
              style={{
                position: 'absolute',
                top: 6,
                right: 6,
                tintColor: 'primary-color-300',
              }}
            />
          )}
          <Text
            category="p1"
            bold="bold"
            style={{ color: textColor }}
            status="primary"
            textContent={textContent}
          />
        </Button>
      );
    },
    [selectedAmountConfigNumber, onChangeAmountConfigNumber, t],
  );

  const $selectTermView = useMemo(() => {
    const amountConfigListLen = reLoanAmountConfigList.length;
    if (amountConfigListLen > 3) {
      return (
        <ScrollView horizontal fadingEdgeLength={10} keyboardShouldPersistTaps="always">
          {(reLoanAmountConfigList || []).map((reLoanAmountConfig, index) => {
            return renderTermButtonView(reLoanAmountConfig, index);
          })}
        </ScrollView>
      );
    } else {
      return (
        <View padding="0 0 0 0" layoutStrategy="flexRowBetweenCenter">
          {(reLoanAmountConfigList || []).map((reLoanAmountConfig, index) => {
            return renderTermButtonView(reLoanAmountConfig, index);
          })}
        </View>
      );
    }
  }, [reLoanAmountConfigList, selectedAmountConfigNumber, onChangeAmountConfigNumber]);

  /** 刷新选择额度 view */
  const $selectAmountView = useMemo(() => {
    /** @todo 数据加载时默认展示, 后续可优化加载过程 */
    if (!isPending) {
      return (
        <>
          <AmountSlider
            animationType="spring"
            value={defaultAmount}
            minimumValue={minAmount}
            maximumValue={maxAmount}
            onSlidingComplete={value => onSlidingComplete(Number(value))}
            onValueChange={value => onValueChange(Number(value))}
            step={increment}
          />
        </>
      );
    }

    if (!days || !defaultAmount) {
      return (
        <>
          <View
            margin="4 0 0 0"
            style={{
              width: '100%',
              display: 'flex',
              flexDirection: 'row',
              justifyContent: 'center',
              alignItems: 'center',
              flexWrap: 'wrap',
            }}>
            <Button
              onPress={onGetHomePageAmout}
              style={{
                width: 144,
              }}>
              <Image name="_reCycle" />
              <Text
                i18nKey="homeString.update"
                style={{
                  color: 'text-color-0',
                  zIndex: 1,
                }}
              />
            </Button>
          </View>
          <View
            margin="12 0 0 0"
            padding="8 12 8 12"
            style={{
              backgroundColor: 'secondary-color-100',
              borderRadius: 10,
            }}>
            <Text
              isCenter={true}
              i18nKey="homeString.update_select_amount"
              style={{
                color: 'text-color-800',
                zIndex: 1,
              }}
            />
          </View>
        </>
      );
    }
    return (
      <>
        <AmountSlider
          animationType="spring"
          value={defaultAmount}
          minimumValue={minAmount}
          maximumValue={maxAmount}
          onSlidingComplete={value => onSlidingComplete(Number(value))}
          onValueChange={value => onValueChange(Number(value))}
          step={increment}
        />
      </>
    );
  }, [days, defaultAmount, isPending]);
  return (
    <>
      <View margin={'16 16 0 16'} cardType="baseType">
        <View
          padding="4 8 4 8"
          style={{
            backgroundColor: 'background-color-100',
            borderRadius: 8,
            overflow: 'hidden',
          }}
          layoutStrategy="flexRowBetweenCenter">
          <Image margin="0 10 0 0" name="_soundBlackIcon" />
          <Text
            style={{ flex: 1, color: 'text-color-700' }}
            category="c1"
            i18nKey={'homeString.reloanTip'}
          />
        </View>
        <View padding={'12 16 16 16'}>
          <View layoutStrategy="flexRowCenterCenter">
            <View>
              <Text
                category="p1"
                i18nKey={'homeString.loanAmountUpToNew'}
                style={{
                  color: 'text-color-800',
                  zIndex: 1,
                }}
              />
              <View layoutStrategy={Layouts.flexRowCenterCenter}>
                <Text
                  category="h1"
                  isCenter={true}
                  textContent={currentSelAmount}
                  bold={'bold'}
                  margin={'8 12 0 0'}
                  status="basic"
                />
                <IncreaseCouponAmountBadge
                  couponUseType={
                    reLoanAmountConfigList[selectedAmountConfigNumber]?.calculateCost?.couponUseType
                  }
                  amount={
                    reLoanAmountConfigList[selectedAmountConfigNumber]?.calculateCost
                      ?.couponUseType === 'amount'
                      ? reLoanAmountConfigList[selectedAmountConfigNumber]?.calculateCost
                        ?.creditCouponAmount || '0'
                      : (
                        Number(
                          reLoanAmountConfigList[selectedAmountConfigNumber]?.calculateCost
                            ?.creditCouponPercent || '0',
                        ) * 100
                      ).toString()
                  }
                />
              </View>
            </View>
          </View>
          {$selectAmountView}
          <DashedLine dashColor={Colors.TEXT_COLOR_400} style={{ paddingTop: 12 + 12 }} />
          <Text
            category="p1"
            isCenter
            i18nKey={
              BaseInfoManager.context.baseModel.repayDateSwitch
                ? 'homeString.repayDateTip'
                : 'homeString.loanTermUpToNew'
            }
            style={{
              color: 'text-color-800',
              zIndex: 1,
              fontSize: 14,
            }}
            margin={'12 0 12 0'}
          />
          {BaseInfoManager.context.baseModel.repayDateSwitch ? (
            <>
              <LoanDatePickerInput
                repayDate={repayDate}
                onChangeRepayDate={onChangeRepayDate}
                loanRepayDays={reloanRepayDays}
                isFirstLoan={false}
                onFocus={onToggleRepayDateModal}
                extraData={calendarExtraData}
              />
              <View
                margin="8 0 0 0"
                layoutStrategy="flexRowStartCenter"
                style={{ alignItems: 'flex-start' }}>
                <Image name="_grayInfo" resizeMode="contain" margin="2 0 0 0" />
                <Text
                  margin="0 0 0 4"
                  category="c1"
                  i18nKey={'homeString.repayDateSelectTips'}
                  style={{
                    color: 'text-color-500',
                    flex: 1,
                  }}
                />
              </View>
            </>
          ) : (
            $selectTermView
          )}
        </View>
      </View>
      {reloanHomeWithholdBankCardInfo.isShowCard === 'YES' && (
        <AutoWithholdCardAutoBind
          openAutoWithholdContract={openAutoWithholdContract}
          cardNo={reloanHomeWithholdBankCardInfo.cardNo}
          withholdState={autoWithhold}
          openWithholdAuthorization={onOpenAutoWithhold}
          closeWithholdAuthorization={onCloseAutoWithhold}
        />
      )}
      {couponList?.length > 0 ? (
        <CouponSelectCard
          couponList={couponList}
          selectCouponList={couponSelected}
          onOpenCouponHelp={onShowCouponHelpModal}
          onOpenCouponSelect={onShowCouponSelectModal}
        />
      ) : null}
      <LoanDetailCard
        data={reLoanAmountConfigList[selectedAmountConfigNumber]?.calculateCost as any}
        onOpenLoanDetailModal={onShowLoanDetailModal}
        onOpenContract={onOpenContract}
        extraData={{ serviceFeePerDayRate }}
        pageKey={HitPointEnumsSpace.EPageKey.P_REPEAT_CHOOSE_AMOUNT}
      />

      <LoanDetailModal
        visible={reloanDetailDialogVisible}
        onClose={onCloseLoanDetailModal}
        data={reLoanAmountConfigList[selectedAmountConfigNumber]?.calculateCost as any}
      />
      <CouponHelpModal visible={showCouponHelpModal} onClose={onCloseCouponHelpModal} />
      <CouponSelectModal
        visible={showCouponSelectModal}
        onCancel={onCloseCouponSelectModal}
        couponList={couponList}
        availableCouponList={couponList}
        couponSelected={couponSelected}
        onConfirm={onConfirmSelectCoupon}
        pageKey={HitPointEnumsSpace.EPageKey.P_APPLY_PAGE}
      />
    </>
  );
};

export default memo(AmountSliderView);
