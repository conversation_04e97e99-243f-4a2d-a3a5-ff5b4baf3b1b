import type { IRadioDataListItem } from '@/components';
import { Image, CommonModal, RadioGroup, Text, View } from '@/components';
import { HitPointEnumsSpace } from '@/enums';
import { useNameSpace } from '@/i18n';
import { Toast } from '@/nativeComponents';
import { trackCommonEvent } from '@/trackEvent';
import React, { useCallback, useMemo, useState } from 'react';
import { ScrollView, TouchableOpacity } from 'react-native';

interface IProps {
  onConfrim: (data: any) => void;
  onCancel: () => void;
  visible: boolean;
}

interface IquestionItem {
  title?: string;
  titleI18nKey?: string;
  dataList: IRadioDataListItem[];
}

const questionList: IquestionItem[] = [
  {
    titleI18nKey: 'homeString.reloanQuestion0Title',
    dataList: [
      {
        labelI18nKey: 'btnString.OK',
        value: '1',
      },
      {
        labelI18nKey: 'btnString.No',
        value: '0',
      },
    ],
  },
  {
    titleI18nKey: 'homeString.reloanQuestion01Title',
    dataList: [
      {
        labelI18nKey: 'homeString.reloanQuestion011answer0',
        value: 'a',
      },
      {
        labelI18nKey: 'homeString.reloanQuestion011answer1',
        value: 'b',
      },
      {
        labelI18nKey: 'homeString.reloanQuestion011answer2',
        value: 'c',
      },
      {
        labelI18nKey: 'homeString.reloanQuestion011answer3',
        value: 'd',
      },
      {
        labelI18nKey: 'homeString.reloanQuestion011answer4',
        value: 'e',
      },
    ],
  },
  {
    titleI18nKey: 'homeString.reloanQuestion1Title',
    dataList: [
      {
        labelI18nKey: 'btnString.OK',
        value: '1',
      },
      {
        labelI18nKey: 'btnString.No',
        value: '0',
      },
    ],
  },
];

/** 复贷二单增信页面 */
export default React.memo((props: IProps) => {
  const { visible, onCancel, onConfrim } = props;

  const [questionAnser0, setQusetionAnser0] = useState<string>('');
  const [questionAnser0_1, setQusetionAnser0_1] = useState<string>('');
  const [questionAnser1, setQusetionAnser1] = useState<string>('');

  const t = useNameSpace().t;

  const onSubmit = useMemo(
    () => () => {
      // 点击提交增信按钮
      trackCommonEvent(
        {
          p: HitPointEnumsSpace.EPageKey.P_HOMEPAGE_RELOAN,
          e: HitPointEnumsSpace.EEventKey.E_BTN_SUBMIT,
        },
        '1',
      );

      const unCompleted =
        (questionAnser0 === '1' && questionAnser0_1 === '') ||
        questionAnser1 == '' ||
        questionAnser0 == '';

      if (unCompleted) {
        Toast(t('basicInfoString.submit_need_complete'));
        return;
      }
      onConfrim([
        {
          questionId: 1,
          optionKey: questionAnser0, // 选项
          fillAnswer: null, // 填写
        },
        {
          questionId: 2,
          optionKey: questionAnser0_1,
          fillAnswer: null,
        },
        {
          questionId: 3,
          optionKey: questionAnser1,
          fillAnswer: null,
        },
      ]);
    },
    [questionAnser0, questionAnser0_1, questionAnser1, t],
  );

  const $close = useMemo(() => {
    return (
      <View layoutStrategy="flexRowBetweenCenter" padding="8 0 8 0">
        <View />
        <TouchableOpacity onPress={onCancel}>
          <Image name="_modalClose" />
        </TouchableOpacity>
      </View>
    );
  }, []);

  const $title = useMemo(() => {
    return (
      <Text
        margin="4 0 0 0"
        padding="0 16 0 16"
        status="basic"
        i18nKey="homeString.reloanQuestionTitle"
      />
    );
  }, []);

  const onChangeQuestion0 = useCallback((value: any) => {
    if (value === '0') {
      setQusetionAnser0_1('');
    }
    setQusetionAnser0(value);
  }, []);

  const onChangeQuestion0_1 = useCallback((value: any) => {
    setQusetionAnser0_1(value);
  }, []);

  const onChangeQuestion1 = useCallback((value: any) => {
    setQusetionAnser1(value);
  }, []);

  const quest0Selected = useMemo(() => {
    return questionAnser0 === '1';
  }, [questionAnser0]);

  const $qusesionContainer = useMemo(() => {
    return (
      <ScrollView
        fadingEdgeLength={10}
        keyboardShouldPersistTaps="always"
        style={{
          marginTop: 12,
          width: '100%',
          maxHeight: 260,
        }}>
        <View
          padding="12 12 12 12"
          style={{
            borderRadius: 8,
            backgroundColor: 'background-color-100',
          }}>
          <RadioGroup
            key={'RadioGroup_0'}
            onChange={onChangeQuestion0}
            horizontal={true}
            titleI18nKey={questionList[0].titleI18nKey}
            radioDataList={questionList[0].dataList}
            pageKey={HitPointEnumsSpace.EPageKey.P_HOMEPAGE_RELOAN}
            eventKey={HitPointEnumsSpace.EEventKey.E_PADING_LOAN}
            defalutSelectedValue={questionAnser0}
          />
          {quest0Selected && (
            <RadioGroup
              key={'RadioGroup_01'}
              onChange={onChangeQuestion0_1}
              horizontal={false}
              titleI18nKey={questionList[1].titleI18nKey}
              radioDataList={questionList[1].dataList}
              pageKey={HitPointEnumsSpace.EPageKey.P_HOMEPAGE_RELOAN}
              eventKey={HitPointEnumsSpace.EEventKey.E_MAX_CREDIT}
              defalutSelectedValue={questionAnser0_1}
            />
          )}
          <RadioGroup
            key={'RadioGroup_1'}
            onChange={onChangeQuestion1}
            horizontal={true}
            titleI18nKey={questionList[2].titleI18nKey}
            radioDataList={questionList[2].dataList}
            pageKey={HitPointEnumsSpace.EPageKey.P_HOMEPAGE_RELOAN}
            eventKey={HitPointEnumsSpace.EEventKey.E_CREDIT_CARD_HOLDER}
            defalutSelectedValue={questionAnser1}
          />
        </View>
      </ScrollView>
    );
  }, [questionAnser0]);

  return (
    <CommonModal
      visible={visible}
      // onBackdropPress={onCancel}
      confirmBtnName="homeString.submit"
      confirmCallback={onSubmit}>
      {/* 外部容器 */}
      <View padding="0 12 0 12" width="100%">
        {/* 关闭按钮 */}
        {$close}
        {/* 标题 */}
        {$title}
        {/* 问题内容 */}
        {$qusesionContainer}
      </View>
    </CommonModal>
  );
});
