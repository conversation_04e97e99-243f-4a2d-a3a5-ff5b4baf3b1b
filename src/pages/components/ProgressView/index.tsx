import React from 'react';
import { StyleSheet, StyleProp, ViewStyle } from 'react-native';
import { Colors } from '@/themes';
import { Image, Text, View } from '@/components';

interface ProcessViewProps {
  // 总节点数
  totalSteps?: number;
  // 当前进度（1～100）
  current: number;
  // 未完成部分颜色, default=fill-color-400
  backgroundColor?: string;
  // 是否显示进度数
  showProgressNumber?: boolean;
  // 进度条高度
  style?: StyleProp<ViewStyle>;
  // 主题色: default=primary-color-500
  primaryColor?: string;
}

const ProcessView: React.FC<ProcessViewProps> = ({
  totalSteps = 4,
  showProgressNumber = true,
  style,
  primaryColor = Colors.PRIMARY_COLOR_500,
  backgroundColor = Colors.FILL_COLOR_400,
  current = 1,
}) => {
  // 每一个节点的值
  const PerNodeValue = 100 / (totalSteps - 1);
  /**
   * 渲染节点
   * @param isNormal 是否为普通节点，非首尾节点
   * @param isCompleted 是否为已完成节点
   */
  const renderDot = (isNormal: boolean = true, isCompleted: boolean = false) => {
    let imageName = '_progressNodeNotArrive';
    if (isNormal) {
      imageName = isCompleted ? '_progressNodeArrived' : '_progressNodeNotArrive';
    } else {
      imageName = isCompleted ? '_progressNodeCompleted' : '_progressNodeNotCompleted';
    }
    return <Image name={imageName} style={{ backgroundColor: '#fff' }} />;
  };

  const renderProgressNumber = () => {
    if (!showProgressNumber) {
      return null;
    }
    return (
      <Text
        textContent={`${current}%`}
        category="p1"
        style={[styles.stepNumber, { color: primaryColor }]}
      />
    );
  };

  const renderProgressLine = () => {
    return (
      <View
        style={[
          styles.progressBar,
          {
            backgroundColor: backgroundColor,
            top: 7,
            width: '100%',
            position: 'absolute',
          },
        ]}>
        <View
          style={[
            styles.progress,
            {
              width: `${current}%`,
              backgroundColor: primaryColor,
            },
          ]}
        />
      </View>
    );
  };

  return (
    <View
      style={{
        marginTop: -10,
        height: 48,
        backgroundColor: Colors.BACKGROUND_COLOR_0,
        paddingLeft: 32,
        paddingRight: 32,
        zIndex: 3,
      }}>
      <View style={{ flex: 1 }} layoutStrategy="flexRowBetweenCenter">
        <View style={{ flex: 1 }} layoutStrategy="flexRowBetweenTop">
          {renderProgressLine()}
          <View layoutStrategy="flexRowBetweenTop" style={{ width: '100%' }}>
            {Array.from({ length: totalSteps }).map((_, index) => {
              const isCompleted = current >= index * PerNodeValue;
              const isNormal = index !== 0 && index !== totalSteps - 1;
              return renderDot(isNormal, isCompleted);
            })}
          </View>
        </View>
        {renderProgressNumber()}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  progressBar: {
    flex: 1,
    borderRadius: 2,
    height: 2,
  },
  progress: {
    borderRadius: 2,
    height: 2,
  },
  stepNumber: {
    marginLeft: 12,
  },
});

export default ProcessView;
