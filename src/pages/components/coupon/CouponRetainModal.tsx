/**
 * @description 优惠券挽留弹窗：包含提额和减免两种方式
 * @param extra.data={
        couponType: 'discount' | 'increase';
        originalQuota?: string;
        currentQuota?: string;
        couponAmount?: string;
    }
 */
import {
  CommonModal,
  Image,
  ImageBackground,
  Layouts,
  LinearGradient,
  Text,
  View,
} from '@/components';
import { ImageNames } from '@/config';
import { useSubscribeFilter } from '@/hooks';
import { Strings } from '@/i18n';
import { ModalContextType, modalDataStoreInstance, ModalList } from '@/managers';
import { Colors } from '@/themes';
import { t } from 'i18next';
import _ from 'lodash';
import React, { useMemo } from 'react';
import { ImageStyle, Pressable, StyleProp, ViewStyle } from 'react-native';

/**
   * 
   * extra.data={
        couponType: 'discount' | 'increase';
        originalQuota?: string;
        currentQuota?: string;
        couponAmount?: string;
    }
   */
const CouponRetainModal = () => {
  // 顶部图片导致的弹窗偏移量
  const OFFSET_Y = 130 / 4;

  const modalInfo = useSubscribeFilter({
    subject: modalDataStoreInstance.messageCenter,
    filter: (subject: ModalContextType) => {
      const maxLength = subject.modalList.length;
      return subject.modalList[maxLength - 1];
    },
  });

  const visible = useMemo(() => {
    return modalInfo?.key === ModalList.COUPON_RETAIN;
  }, [modalInfo]);

  // 优惠券展示的提额金额或百分比
  const data: {
    couponType: 'increase' | 'discount';
    originalQuota?: string;
    currentQuota?: string;
    couponAmount?: string;
  } = useMemo(() => {
    return modalInfo?.extra?.data;
  }, [modalInfo?.extra?.data]);

  const onPressConfirm = _.debounce(() => {
    modalInfo?.confirmBtnCallback && modalInfo?.confirmBtnCallback();
    modalDataStoreInstance.closeModal(modalInfo?.id);
  }, 100);

  const IncreaseTextView = () => {
    const couponAmount = data?.couponAmount ?? '--';
    return (
      <Text isCenter margin="24 12 0 12">
        <Text
          style={{ color: Colors.TEXT_COLOR_800 }}
          i18nKey={Strings.couponString.retainIncreaseContent1}
        />
        <Text
          category="h3"
          style={{ color: Colors.TERTIARY_COLOR_500 }}
          textContent={String(Number(couponAmount)).toFormatFinance(false)}
        />
        <Text
          style={{ color: Colors.TEXT_COLOR_800 }}
          i18nKey={Strings.couponString.retainIncreaseContent2}
        />
      </Text>
    );
  };

  const DiscountTextView = () => {
    const couponAmount = modalInfo?.extra?.data?.couponAmount ?? '--';
    return (
      <Text isCenter margin="24 12 0 12">
        <Text
          style={{ color: Colors.TEXT_COLOR_800 }}
          i18nKey={Strings.couponString.retainDiscountContent1}
        />

        <Text
          category="h3"
          style={{ color: '#BB653F' }}
          textContent={String(Number(couponAmount)).toFormatFinance(false)}
        />
        <Text
          style={{ color: Colors.TEXT_COLOR_800 }}
          i18nKey={Strings.couponString.retainDiscountContent2}
        />
      </Text>
    );
  };

  const ModalContentContainer = (params: { children: React.ReactNode }) => {
    const couponType = data?.couponType;
    let gradientContainer = null;

    switch (couponType) {
      case 'increase':
        // 满减
        gradientContainer = (
          <LinearGradient
            useAngle
            angle={19}
            angleCenter={{ x: 0.5, y: 0.5 }}
            colors={['rgba(193, 206, 255, 0.15)', 'rgba(216, 184, 254, 0.5)']}>
            <LinearGradient
              useAngle
              angle={161}
              angleCenter={{ x: 0.7, y: 0.3 }}
              colors={['#CCD7FF', '#FFE1E2']}>
              {params.children}
            </LinearGradient>
          </LinearGradient>
        );
        break;
      case 'discount':
        // 提额
        gradientContainer = (
          <LinearGradient
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
            locations={[0, 0.5, 1]}
            colors={['#FEF3D9', '#FFFCF4', '#FFF7EB']}>
            {params.children}
          </LinearGradient>
        );
        break;
    }

    return (
      <View
        style={{
          borderTopLeftRadius: 24,
          borderTopRightRadius: 24,
          overflow: 'hidden',
        }}>
        {gradientContainer}
      </View>
    );
  };

  const ButtonView = useMemo(() => {
    const buttonImageName =
      data?.couponType === 'discount'
        ? ImageNames._retainDiscountCouponBtn
        : ImageNames._retainIncreaseCouponBtn;
    return (
      <Pressable onPress={onPressConfirm}>
        <ImageBackground
          style={{ justifyContent: 'center', alignItems: 'center' }}
          margin="24 0 0 0"
          name={buttonImageName}>
          <Text
            i18nKey={Strings.btnString.get}
            style={{
              color: Colors.TEXT_COLOR_0,
              fontSize: 16,
              lineHeight: 24,
              fontWeight: '500',
            }}
          />
        </ImageBackground>
      </Pressable>
    );
  }, [data?.couponType]);

  const ModalView = useMemo(() => {
    const topImageName =
      data?.couponType === 'discount'
        ? ImageNames._retainDiscountCouponHeader
        : ImageNames._retainIncreaseCouponHeader;
    const topImageStyle: StyleProp<ImageStyle> =
      data?.couponType === 'discount'
        ? { alignSelf: 'center', top: 2 }
        : { top: 50, zIndex: 2, alignSelf: 'center' };
    const titleImageName =
      data?.couponType === 'discount'
        ? ImageNames._retainDiscountSurprise
        : ImageNames._retainIncreaseSurprise;

    const TextView = data?.couponType === 'discount' ? <DiscountTextView /> : <IncreaseTextView />;
    return (
      <>
        <Image name={topImageName} style={topImageStyle} />
        <ModalContentContainer>
          <View padding="32 40 32 40" layoutStrategy={Layouts.flexColumnStartCenter}>
            <Image
              name={titleImageName}
              style={{ marginTop: data?.couponType === 'discount' ? 0 : 16 }}
            />
            {TextView}
            {ButtonView}
          </View>
        </ModalContentContainer>
      </>
    );
  }, [data?.couponType]);

  return (
    <CommonModal
      visible={visible}
      onBackdropPress={onPressConfirm}
      hasLinearGradient={false}
      style={{ backgroundColor: 'transparent', borderRadius: 24, top: -OFFSET_Y }}>
      {ModalView}
    </CommonModal>
  );
};

export default CouponRetainModal;
