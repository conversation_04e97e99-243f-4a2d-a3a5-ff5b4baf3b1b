import { memo, useCallback, useMemo, useRef } from 'react';
import {
  TouchableOpacity,
  LayoutAnimation,
  StyleSheet,
  TouchableWithoutFeedback,
  Dimensions,
} from 'react-native';
import { Image, View, Text, LinearGradient, ImageBackground, Button } from '@/components';
import { Colors } from '@/themes';
import { Strings, useNameSpace } from '@/i18n';
import { useSetState } from 'ahooks';
import { UserEnumsSpace } from '@/enums';
import { UserVOSpace } from '@/types';
import { RouterConfig } from '@/routes';

const screenWidth = Dimensions.get('window').width;

interface State {
  isShowDesc: boolean;
  isDown: boolean;
}
type Dimension = number | `${number}%`;
type Data = UserVOSpace.BaseCouponItem & {
  serialNumber?: string;
  configId?: number;
  expirationDate?: string;
  redeemQuantity?: number;
};
interface Props {
  data: Data;
  balance?: string;
  onExchangeCoupon?: (configId?: number) => void;
  pageKey?: string;
  height?: Dimension;
  leftWidth?: Dimension;
  leftTextSize?: number;
  selected?: boolean;
  onSelect?: () => void;
  isShowDescription?: boolean;
}
type Unavailable = Exclude<UserEnumsSpace.ECouponsStatus, 'AVAILABLE' | 'UNAVAILABLE' | 'BINDING'>;
const useData = (props: Props) => {
  const { data, isShowDescription } = props;
  const [state, setState] = useSetState<State>({
    isShowDesc: false,
    isDown: true,
  });
  const { isShowDesc, isDown } = state;
  const onToggleShowDesc = useCallback(() => {
    if (!isShowDescription) return;
    setState({ isShowDesc: !isShowDesc, isDown: !isDown });
    configureAnimation();
  }, [isShowDesc, isDown]);
  const configureAnimation = () => {
    LayoutAnimation.configureNext(
      LayoutAnimation.create(
        200, // 动画时长
        LayoutAnimation.Types.easeInEaseOut,
        LayoutAnimation.Properties.opacity,
      ),
    );
  };
  const type = data.type;
  const isVip =
    data.distributeScene === UserEnumsSpace.ECouponsDistributeSceneStatus.VIP_REDEMPTION;
  const isRetention =
    data.distributeScene === UserEnumsSpace.ECouponsDistributeSceneStatus.VOLUNTARY_REJECT;
  const isExpire = [
    UserEnumsSpace.ECouponsStatus.EXPIRED,
    UserEnumsSpace.ECouponsStatus.HX,
    UserEnumsSpace.ECouponsStatus.VOIDED,
  ].includes(data.couponStatus);
  const isDeduction = type === UserEnumsSpace.ECouponsType.DEDUCTION;
  const isShow$ = !isDeduction && data.useType === 'amount';
  const DEDUCTION_MAP = {
    vip: {
      colors: ['#FFE8D5', '#DBAC93'],
      leftBg: '_couponDeductionLeftVipBg',
      rightBg: '_couponDeductionRightVipBg',
      leftTextColor: '#F5C7A3',
      rightTextColor: '#262626',
      hotIcon: '_couponHotVipIcon',
      rightBtn: '_couponDeductionRightVipBtn',
      descBg: '#FDFAF3',
      descTextColor: '#666666',
    },
    noVip: {
      colors: ['#ECDAF4', '#A981F3'],
      leftBg: '_couponDeductionLeftBg',
      rightBg: '_couponDeductionRightBg',
      leftTextColor: '#ffffff',
      rightTextColor: '#9C3FFF',
      hotIcon: '_couponHotIcon',
      rightBtn: '_couponDeductionRightBtn',
      descBg: '#FFF6FA',
      descTextColor: '#7000FF',
    },
  };
  const DISABLE_ICON_MAP = {
    [UserEnumsSpace.ECouponsStatus.EXPIRED]: '_couponExpireIcon',
    [UserEnumsSpace.ECouponsStatus.HX]: '_couponHXIcon',
    [UserEnumsSpace.ECouponsStatus.VOIDED]: '_couponVoidedIcon',
  };
  const deduction = DEDUCTION_MAP[isVip ? 'vip' : 'noVip'];
  const getColors = () => {
    const colors = {
      linearGradientColors: ['#8098FF', '#BF99FF'],
      descBg: '#FFF6FA',
      descTextColor: '#7000FF',
    };
    if (isDeduction) {
      let linearGradientColors = ['#ECDAF4', '#A981F3'];
      if (isVip) {
        linearGradientColors = ['#FFE8D5', '#DBAC93'];
      }
      colors.linearGradientColors = linearGradientColors;
      colors.descBg = deduction.descBg;
      colors.descTextColor = deduction.descTextColor;
    }
    if (isExpire) {
      let linearGradientColors = ['#A3A3A3', '#C4C4C4'];
      if (isDeduction) {
        linearGradientColors = ['#E3E3E3', '#818181'];
      }
      colors.linearGradientColors = linearGradientColors;
      colors.descBg = '#E3E3E3';
      colors.descTextColor = '#666666';
    }
    return colors;
  };
  const amount = Number(data.amount);
  const creditAmount = (amount < 1 ? amount * 100 : amount) + '';
  return {
    isShowDesc,
    onToggleShowDesc,
    colors: getColors(),
    deduction,
    isVip,
    isExpire,
    isDeduction,
    isShow$,
    creditAmount,
    DISABLE_ICON_MAP,
    isDown,
    isRetention,
  };
};
const CouponItem = (props: Props) => {
  const {
    data,
    balance,
    onExchangeCoupon,
    pageKey,
    height = 86,
    leftTextSize = 32,
    leftWidth = 94,
    selected,
    onSelect,
    isShowDescription = true,
  } = props;
  const {
    isShowDesc,
    onToggleShowDesc,
    colors,
    deduction,
    isVip,
    isExpire,
    isDeduction,
    isShow$,
    creditAmount,
    DISABLE_ICON_MAP,
    isDown,
    isRetention,
  } = useData({ data, isShowDescription });
  const t = useNameSpace().t;
  const isSmallScreen = screenWidth <= 365;
  const renderExpire = () => {
    if (isDeduction) {
      return (
        <View layoutStrategy="flexRowStartCenter" style={styes.screen_container}>
          <ImageBackground
            resizeMode="stretch"
            name="_couponDeductionLeftVipBg"
            style={{ ...styes.screen_height, width: leftWidth }}>
            <View
              layoutStrategy="flexRowCenterCenter"
              style={{ ...styes.screen_height, zIndex: 2 }}>
              <Text
                category="h1"
                textContent={data.amount + ''}
                style={{ color: Colors.TEXT_COLOR_400, fontSize: leftTextSize }}
              />
            </View>
          </ImageBackground>
          <ImageBackground
            resizeMode="stretch"
            name="_couponExpireRightBg"
            style={{ flex: 1, ...styes.screen_container, borderRadius: 8 }}>
            <View
              padding="8 8 8 16"
              layoutStrategy="flexRowStartCenter"
              style={styes.screen_height}>
              <TouchableOpacity
                activeOpacity={0.8}
                onPress={onToggleShowDesc}
                style={{
                  flex: 1,
                  ...styes.screen_height,
                  flexDirection: 'column',
                  justifyContent: 'space-around',
                }}>
                <Text category={isVip ? 'p1' : 'p2'} textContent={data.name} />
                {!isVip ? (
                  isRetention ? (
                    <Text
                      category="c2"
                      i18nKey={Strings.redeemCouponsString.couponRetention}
                      style={{ fontSize: 8 }}
                    />
                  ) : data.expirationDate ? (
                    <Text category="c2">
                      <Text
                        i18nKey={Strings.redeemCouponsString.validityDate}
                        style={{ fontSize: 8 }}
                      />
                      <Text
                        padding="0 0 0 4"
                        style={{ fontSize: 8 }}
                        textContent={data.expirationDate + ''}
                      />
                    </Text>
                  ) : null
                ) : null}
                {isShowDescription && data.description ? (
                  <View layoutStrategy="flexRowStartCenter">
                    <Text i18nKey={Strings.redeemCouponsString.couponItemDescTitle} category="c2" />
                    <Image
                      name={isDown ? '_arrowBottom' : '_arrowTop'}
                      style={{
                        width: 20,
                        height: 20,
                        marginLeft: 4,
                        tintColor: Colors.TEXT_COLOR_800,
                      }}
                    />
                  </View>
                ) : null}
              </TouchableOpacity>
              {renderContentRight}
              {isVip && (
                <Image
                  name="_coinVipExpireIcon"
                  style={{
                    position: 'absolute',
                    right: 5,
                    bottom: 4,
                  }}
                />
              )}
            </View>
          </ImageBackground>
        </View>
      );
    }
    return (
      <View layoutStrategy="flexRowStartCenter" style={styes.screen_container}>
        <ImageBackground
          resizeMode="stretch"
          style={{ ...styes.screen_container, borderRadius: 8 }}
          name="_couponCreditInnerExpireBg">
          <View layoutStrategy="flexRowStartCenter" style={{ ...styes.screen_height }}>
            <View
              layoutStrategy="flexRowCenterCenter"
              style={{
                width: leftWidth,
                flexDirection: 'row',
                justifyContent: 'center',
                alignItems: 'flex-end',
                paddingLeft: 10,
              }}>
              {isShow$ && (
                <Text
                  textContent="$"
                  style={{ color: '#595959', paddingBottom: 8 }}
                  category="h3"
                />
              )}
              <Text
                textContent={creditAmount}
                category="h1"
                style={{ color: '#595959', fontSize: leftTextSize }}
              />
              {!isShow$ && (
                <Text
                  textContent="%"
                  style={{ color: '#595959', paddingBottom: 8 }}
                  category="h3"
                />
              )}
            </View>
            <LinearGradient
              start={{ x: 0, y: 0 }}
              end={{ x: 0, y: 1 }}
              colors={['#F5F5F5', '#D5D5D5', '#ADADAD', '#D5D5D5', '#F5F5F5']}
              style={{
                width: 1,
                height: 57,
              }}
            />
            <View
              padding="8 24 8 16"
              layoutStrategy="flexRowStart"
              style={{ ...styes.screen_height, flex: 1 }}>
              <TouchableOpacity
                activeOpacity={0.8}
                onPress={onToggleShowDesc}
                style={{ flex: 1, flexDirection: 'column', justifyContent: 'space-around' }}>
                <Text category="p2" textContent={data.name} />
                {isRetention ? (
                  <Text
                    category="c2"
                    i18nKey={Strings.redeemCouponsString.couponRetention}
                    style={{ fontSize: 8 }}
                  />
                ) : data.expirationDate ? (
                  <Text category="c2">
                    <Text
                      i18nKey={Strings.redeemCouponsString.validityDate}
                      style={{ fontSize: 8 }}
                    />
                    <Text
                      padding="0 0 0 4"
                      style={{ fontSize: 8 }}
                      textContent={data.expirationDate + ''}
                    />
                  </Text>
                ) : null}
                {isShowDescription && data.description ? (
                  <View layoutStrategy="flexRowStartCenter">
                    <Text i18nKey={Strings.redeemCouponsString.couponItemDescTitle} category="c2" />
                    <Image
                      name={isDown ? '_arrowBottom' : '_arrowTop'}
                      style={{
                        width: 20,
                        height: 20,
                        marginLeft: 4,
                        tintColor: Colors.TEXT_COLOR_800,
                      }}
                    />
                  </View>
                ) : null}
              </TouchableOpacity>
              <View layoutStrategy="flexRowCenterCenter">{renderContentRight}</View>
            </View>
          </View>
        </ImageBackground>
      </View>
    );
  };
  const renderCouponDesc = useMemo(() => {
    if (!isShowDesc || !data.description) {
      return null;
    }
    const descText = data.description
      .split('|')
      ?.map?.((item, index) => (
        <Text
          key={index}
          category="c2"
          textContent={index + 1 + '. ' + item}
          style={{ color: colors.descTextColor }}
        />
      ));
    return (
      <View
        margin="6 0 0 0"
        padding="8 12 8 12"
        style={{ backgroundColor: colors.descBg, borderRadius: 4 }}>
        {descText}
      </View>
    );
  }, [isShowDesc, data]);
  const renderContentRight = useMemo(() => {
    if (isExpire) {
      return <Image name={DISABLE_ICON_MAP[data?.couponStatus as Unavailable]} />;
    }
    if (isDeduction && onExchangeCoupon) {
      const disabled = Number(balance) < Number(data.amount);
      return (
        <View padding="0 0 0 20" style={{ flex: 1 }} layoutStrategy="flexColumnCenterCenter">
          <TouchableOpacity
            activeOpacity={0.8}
            onPress={() => {
              !disabled && onExchangeCoupon?.(data.configId);
            }}
            style={{ paddingRight: isSmallScreen && disabled ? 20 : 0 }}>
            <ImageBackground
              resizeMode="stretch"
              name={disabled ? '_couponDeductionRightBtnDisable' : deduction.rightBtn}>
              <View
                padding="0 0 3 0"
                style={{ ...styes.screen_container }}
                layoutStrategy="flexRowCenterCenter">
                <Text
                  category="c2"
                  style={{ color: disabled ? Colors.TEXT_COLOR_0 : '#FFC08E' }}
                  i18nKey={
                    Strings.redeemCouponsString[disabled ? 'couponItemBtnDisable' : 'couponItemBtn']
                  }
                />
              </View>
            </ImageBackground>
          </TouchableOpacity>
          <View layoutStrategy="flexRowStartCenter">
            <Image name={deduction.hotIcon} />
            <Text
              padding="0 0 0 4"
              style={{ fontSize: 8, color: deduction.rightTextColor }}
              textContent={t(Strings.redeemCouponsString.exchange) + data.redeemQuantity}
            />
          </View>
        </View>
      );
    }
    return null;
  }, [data, balance]);
  const renderContent = () => {
    if (isDeduction) {
      return (
        <TouchableWithoutFeedback
          style={styes.screen_container}
          onPress={() => {
            onSelect?.();
          }}>
          <View layoutStrategy="flexRowStartCenter" style={styes.screen_container}>
            <ImageBackground
              resizeMode="stretch"
              name={deduction.leftBg}
              style={{ ...styes.screen_height, width: leftWidth }}>
              <View
                layoutStrategy="flexRowCenterCenter"
                style={{ ...styes.screen_height, zIndex: 2 }}>
                <Text
                  category="h1"
                  textContent={data.amount}
                  style={{
                    color: deduction.leftTextColor,
                    fontSize: leftTextSize,
                  }}
                />
              </View>
            </ImageBackground>
            <ImageBackground
              resizeMode="stretch"
              name={deduction.rightBg}
              style={{ flex: 1, ...styes.screen_container, borderRadius: 8 }}>
              <View padding="8 8 8 16" layoutStrategy="flexRowStart" style={styes.screen_height}>
                <View
                  style={{
                    flexDirection: 'column',
                    justifyContent: 'space-around',
                    ...styes.screen_height,
                  }}>
                  <Text
                    textContent={data.name}
                    style={{ color: deduction.rightTextColor }}
                    category={isVip ? 'p1' : 'p2'}
                  />
                  {!isVip ? (
                    isRetention ? (
                      <Text
                        category="c2"
                        i18nKey={Strings.redeemCouponsString.couponRetention}
                        style={{ color: '#7000FF50', fontSize: 8 }}
                      />
                    ) : data.expirationDate ? (
                      <Text category="c2">
                        <Text
                          i18nKey={Strings.redeemCouponsString.validityDate}
                          style={{ color: '#7000FF50', fontSize: 8 }}
                        />
                        <Text
                          padding="0 0 0 4"
                          style={{ color: '#7000FF50', fontSize: 8 }}
                          textContent={data.expirationDate}
                        />
                      </Text>
                    ) : null
                  ) : null}
                  {isShowDescription && data.description ? (
                    <TouchableOpacity
                      activeOpacity={0.8}
                      onPress={onToggleShowDesc}
                      hitSlop={{ top: 20, bottom: 10, left: 10, right: 10 }}>
                      <View layoutStrategy="flexRowStartCenter">
                        <Text
                          i18nKey={Strings.redeemCouponsString.couponItemDescTitle}
                          category="c2"
                          style={{ color: deduction.rightTextColor }}
                        />
                        <Image
                          name={isDown ? '_arrowBottom' : '_arrowTop'}
                          style={{
                            width: 20,
                            height: 20,
                            marginLeft: 4,
                            tintColor: deduction.rightTextColor,
                          }}
                        />
                      </View>
                    </TouchableOpacity>
                  ) : null}
                </View>
                {renderContentRight}
                {data.distributeScene ===
                  UserEnumsSpace.ECouponsDistributeSceneStatus.VIP_REDEMPTION && (
                  <Image
                    name="_couponVipIcon"
                    style={{
                      position: 'absolute',
                      right: 4,
                      bottom: 4,
                    }}
                  />
                )}
              </View>
            </ImageBackground>
            {selected ? (
              <Image
                name="_couponItemSelectedIcon"
                style={{
                  position: 'absolute',
                  right: -6,
                  top: -6,
                }}
              />
            ) : null}
          </View>
        </TouchableWithoutFeedback>
      );
    }
    return (
      <TouchableWithoutFeedback
        style={styes.screen_container}
        onPress={() => {
          onSelect?.();
        }}>
        <View layoutStrategy="flexRowStartCenter" style={styes.screen_container}>
          <ImageBackground
            resizeMode="stretch"
            style={{ ...styes.screen_container, borderRadius: 8 }}
            name="_couponCreditInnerBg">
            <View layoutStrategy="flexRowStartCenter" style={{ ...styes.screen_height }}>
              <View
                style={{
                  width: leftWidth,
                  flexDirection: 'row',
                  justifyContent: 'center',
                  alignItems: 'flex-end',
                  paddingLeft: 10,
                }}>
                {isShow$ && (
                  <Text
                    textContent="$"
                    category="h3"
                    style={{ color: Colors.TERTIARY_COLOR_500, paddingBottom: 8 }}
                  />
                )}
                <Text
                  textContent={creditAmount}
                  category="h1"
                  style={{ color: Colors.TERTIARY_COLOR_500, fontSize: leftTextSize }}
                />
                {!isShow$ && (
                  <Text
                    textContent="%"
                    style={{ color: Colors.TERTIARY_COLOR_500, paddingBottom: 8 }}
                    category="h3"
                  />
                )}
              </View>
              <LinearGradient
                start={{ x: 0, y: 0 }}
                end={{ x: 0, y: 1 }}
                colors={['#FAF3F2', '#DBC2FF', '#9F69FF', '#DBC2FF', '#FBF4F3']}
                style={{
                  width: 1,
                  height: 57,
                }}
              />
              <View padding="8 8 8 16" layoutStrategy="flexRowStart" style={styes.screen_height}>
                <View style={{ flexDirection: 'column', justifyContent: 'space-around' }}>
                  <Text category="p2" style={{ color: '#9C3FFF' }} textContent={data.name} />
                  {isRetention ? (
                    <Text
                      category="c2"
                      i18nKey={Strings.redeemCouponsString.couponRetention}
                      style={{ color: '#7000FF50', fontSize: 8 }}
                    />
                  ) : data.expirationDate ? (
                    <Text category="c2">
                      <Text
                        i18nKey={Strings.redeemCouponsString.validityDate}
                        style={{ color: '#7000FF50', fontSize: 8 }}
                      />
                      <Text
                        padding="0 0 0 4"
                        style={{ color: '#7000FF50', fontSize: 8 }}
                        textContent={data.expirationDate}
                      />
                    </Text>
                  ) : null}
                  {isShowDescription && data.description ? (
                    <TouchableOpacity
                      onPress={onToggleShowDesc}
                      activeOpacity={0.8}
                      hitSlop={{ top: 20, bottom: 10, left: 10, right: 10 }}>
                      <View layoutStrategy="flexRowStartCenter">
                        <Text
                          i18nKey={Strings.redeemCouponsString.couponItemDescTitle}
                          category="c2"
                          style={{ color: '#9C3FFF' }}
                        />
                        <Image
                          name={isDown ? '_arrowBottom' : '_arrowTop'}
                          style={{ width: 20, height: 20, marginLeft: 4, tintColor: '#9C3FFF' }}
                        />
                      </View>
                    </TouchableOpacity>
                  ) : null}
                </View>
              </View>
            </View>
          </ImageBackground>
          {selected ? (
            <Image
              name="_couponItemSelectedIcon"
              style={{
                position: 'absolute',
                right: -6,
                top: -6,
              }}
            />
          ) : null}
        </View>
      </TouchableWithoutFeedback>
    );
  };
  return (
    <LinearGradient
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 0 }}
      colors={colors.linearGradientColors}
      style={{
        borderRadius: 8,
        padding: 6,
        marginBottom: 16,
        width: '100%',
      }}
      key={data?.configId || data?.serialNumber}>
      <View style={{ height }}>{isExpire ? renderExpire() : renderContent()}</View>
      {renderCouponDesc}
    </LinearGradient>
  );
};
export default memo(CouponItem);
const styes = StyleSheet.create({
  screen_width: {
    width: '100%',
  },
  screen_height: {
    height: '100%',
  },
  screen_container: {
    height: '100%',
    width: '100%',
  },
});
