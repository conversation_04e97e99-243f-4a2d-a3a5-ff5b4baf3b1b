/** 贷款详情弹窗 */

import { ActionSheet, Button, Image, Text, View } from '@/components';
import { HitPointEnumsSpace } from '@/enums';
import { OrderVOSpace, UserVOSpace } from '@/types';
import { TrackEvent } from '@/utils';
import React, { useCallback, useEffect, useMemo, useState, useRef } from 'react';
import { Dimensions, FlatList, TouchableNativeFeedback } from 'react-native';
import CouponItem from './CouponItem';
import { Colors } from '@/themes';
import { Strings } from '@/i18n';
import { UserEnumsSpace } from '@/enums';

interface IProps {
  onCancel: () => void;
  onConfirm: (coupons?: UserVOSpace.CouponsItem[], index?: number) => void;
  couponList: UserVOSpace.CouponsItem[];
  availableCouponList: UserVOSpace.CouponsItem[];
  visible: boolean;
  couponSelected: UserVOSpace.CouponsItem[];
  pageKey?: HitPointEnumsSpace.EPageKey;
}

export default (props: IProps) => {
  const { visible, onCancel, onConfirm, couponList, availableCouponList, couponSelected, pageKey } =
    props;

  const [couponSelectedList, setCouponSelectedList] =
    useState<UserVOSpace.CouponsItem[]>(couponSelected);
  const couponSelectedListRef = useRef<UserVOSpace.CouponsItem[]>(couponSelectedList);

  useEffect(() => {
    couponSelectedListRef.current = couponSelectedList;
  }, [couponSelectedList]);
  useEffect(() => {
    setCouponSelectedList(couponSelected);
    couponSelectedListRef.current = couponSelectedList;
  }, [couponSelected]);
  const onSelectCouponTrackEvent = (coupon: UserVOSpace.CouponsItem) => {
    if (!pageKey) {
      return;
    }
    const isDeduction = coupon.type === UserEnumsSpace.ECouponsType.DEDUCTION;
    TrackEvent.trackCommonEvent(
      {
        p: pageKey,
        e: HitPointEnumsSpace.EEventKey[
          isDeduction ? 'BTN_CHOOSE_DISCOUNT_COUPON' : 'BTN_CHOOSE_EXTRA_COUPON'
        ],
      },
      coupon.serialNumber,
    );
  };
  const onConfirmCouponTrackEvent = (coupon: UserVOSpace.CouponsItem) => {
    if (!pageKey) {
      return;
    }
    const isDeduction = coupon.type === UserEnumsSpace.ECouponsType.DEDUCTION;
    TrackEvent.trackCommonEvent(
      {
        p: pageKey,
        e: HitPointEnumsSpace.EEventKey[
          isDeduction ? 'BTN_USE_DISCOUNT_COUPON' : 'BTN_CUPON_USE_EXTRA'
        ],
      },
      coupon.serialNumber,
    );
  };

  const onCheckSelectCoupon = useCallback(async (item: UserVOSpace.CouponsItem) => {
    if (!item) return;
    const { serialNumber = '', type, canStack } = item;
    const couponSelectedList = couponSelectedListRef.current;
    const isSelected = couponSelectedList.find(item => item.serialNumber === serialNumber);
    if (couponSelectedList.length === 0) {
      onSelectCouponTrackEvent(item);
      return setCouponSelectedList([item]);
    }
    if (isSelected) {
      return setCouponSelectedList(prev => {
        const couponList = prev.filter(i => i.serialNumber !== serialNumber);
        couponList.map(coupon => onSelectCouponTrackEvent(coupon));
        return couponList;
      });
    }
    const sameTypeCoupons = couponSelectedList.filter(i => i.type === type);
    const isAddWithOthers = (list: UserVOSpace.CouponsItem[]) => {
      const differentTypeCoupons = list.filter(i => i.type !== type);
      const canAddWithOthers =
        differentTypeCoupons.every(i => i.canStack === 'YES') && canStack === 'YES';
      return canAddWithOthers;
    };
    if (sameTypeCoupons.length > 0) {
      const canAdd = isAddWithOthers(sameTypeCoupons);
      if (canAdd) {
        return setCouponSelectedList(prev => {
          const couponList = [...prev.filter(i => i.type !== type), item];
          couponList.map(coupon => onSelectCouponTrackEvent(coupon));
          return couponList;
        });
      }
    }
    const canAdd = isAddWithOthers(couponSelectedList);
    if (canAdd) {
      return setCouponSelectedList(prev => {
        const couponList = [...prev, item];
        couponList.map(coupon => onSelectCouponTrackEvent(coupon));
        return couponList;
      });
    }
    onSelectCouponTrackEvent(item);
    setCouponSelectedList([item]);
  }, []);

  const onConfirmHandle = useCallback(() => {
    const couponSelectedList = couponSelectedListRef.current;
    if (couponSelectedList.length) {
      couponSelectedList.map(coupon => onConfirmCouponTrackEvent(coupon));
    }
    onConfirm && onConfirm(couponSelectedList);
    onCancel && onCancel();
  }, [availableCouponList, onConfirm, onCancel]);
  const onSelectCoupon = useCallback((coupon: UserVOSpace.CouponsItem) => {
    onCheckSelectCoupon(coupon);
  }, []);

  const renderItem = ({ item }: { item: UserVOSpace.CouponsItem; index: number }) => {
    const { serialNumber } = item;
    const isSelected = !!couponSelectedList.find(item => item.serialNumber === serialNumber);
    return (
      <CouponItem data={item} selected={isSelected} onSelect={onSelectCoupon.bind(null, item)} />
    );
  };

  const $renderFooter = useMemo(() => {
    let textI18nKey = '';
    if (couponList.length === 0) {
      textI18nKey = 'couponString.noHasCoupones';
    } else if (availableCouponList.length === 0) {
      textI18nKey = 'couponString.noHasAvailableCoupones';
    }
    return textI18nKey === '' ? null : (
      <View
        margin="0 0 16 0"
        padding="8 16 8 18"
        layoutStrategy="flexRowStart"
        style={{
          borderRadius: 8,
          backgroundColor: 'background-color-0',
        }}>
        <Image
          margin="3 12 0 0"
          name={'_infoIconYellow'}
          style={{ tintColor: Colors.PRIMARY_COLOR_500, width: 16, height: 16 }}
        />
        <Text
          style={{ flex: 1, color: Colors.TEXT_COLOR_700 }}
          category="p2"
          i18nKey={textI18nKey}
        />
      </View>
    );
  }, [availableCouponList, couponList, couponSelected]);

  const $renderHeader = useMemo(() => {
    if (availableCouponList.length !== 0) {
      return (
        <View
          padding="8 16 8 16"
          layoutStrategy="flexRowStartCenter"
          style={{
            borderRadius: 8,
            backgroundColor: 'background-color-0',
            marginBottom: 16,
          }}>
          <Image margin="0 12 0 0" name="_couponModalTipIcon" />
          <Text
            style={{ flex: 1, color: Colors.TEXT_COLOR_700 }}
            category="p2"
            i18nKey={Strings.couponString.couponModalTip}
          />
        </View>
      );
    }
    return null;
  }, [availableCouponList]);

  const [height, setHeight] = useState(
    (Dimensions.get('window').height * 3) / 4 > 700
      ? 700
      : (Dimensions.get('window').height * 3) / 4,
  );

  return (
    <ActionSheet visible={visible} onClose={onCancel}>
      <View
        height={height}
        style={{
          borderTopLeftRadius: 12,
          borderTopRightRadius: 12,
          width: Dimensions.get('window').width,
          backgroundColor: 'background-color-100',
        }}>
        <View
          padding="12 18 12 12"
          layoutStrategy="flexRowBetweenCenter"
          style={{ backgroundColor: Colors.BACKGROUND_COLOR_0 }}>
          <Text i18nKey="couponString.coupon" />
          <TouchableNativeFeedback onPress={onCancel}>
            <Image name="_couponModalCloseIcon" />
          </TouchableNativeFeedback>
        </View>
        <View
          style={{
            flex: 1,
            backgroundColor: 'background-color-100',
            paddingHorizontal: 16,
            paddingTop: 16,
          }}>
          <FlatList
            style={{ height: '100%', marginHorizontal: 16 }}
            renderItem={renderItem}
            ListHeaderComponent={$renderHeader}
            ListFooterComponent={$renderFooter}
            data={availableCouponList}
            showsVerticalScrollIndicator={false}
          />
        </View>
        <View
          width={'100%'}
          padding="16 32 24 32"
          style={{ backgroundColor: Colors.BACKGROUND_COLOR_0 }}
          layoutStrategy="flexColumnCenterCenter">
          <Button
            width={'100%'}
            status="primary"
            textI18nKey={Strings.btnString.confirm}
            onPress={onConfirmHandle}
          />
        </View>
      </View>
    </ActionSheet>
  );
};
