import { memo, useMemo } from 'react';
import { View, Text, Image } from '@/components';
import { TouchableOpacity } from 'react-native';
import { Colors } from '@/themes';
import { useNameSpace } from '@/i18n';
import { UserVOSpace, OrderVOSpace } from '@/types';
import { UserEnumsSpace } from '@/enums';

interface Props {
  onOpenCouponHelp?: () => void;
  onOpenCouponSelect?: () => void;
  couponList: UserVOSpace.CouponsItem[];
  selectCouponList: UserVOSpace.CouponsItem[];
  repayDetail?: OrderVOSpace.BillDetailDataType;
}
const CouponSelectCard = (props: Props) => {
  const { onOpenCouponHelp, onOpenCouponSelect, couponList, selectCouponList, repayDetail } = props;
  const t = useNameSpace().t;
  const { overdueDay, deductionFlag, couponStatus, deductionAmount } = repayDetail || {};
  const isOverdue = Number(overdueDay) > 0;
  const disabled = isOverdue || deductionFlag === 'YES';
  const text = useMemo(() => {
    // 优惠券文本颜色
    let color = Colors.TEXT_COLOR_600;
    // 优惠券文本文案
    let text = t('couponString.couponNum', {
      num: couponList.length,
    });
    let textDecorationLine: any;
    if (selectCouponList.length || (deductionFlag === 'YES' && Number(deductionAmount) > 0)) {
      color = Colors.PRIMARY_COLOR_500;
    }
    if (deductionFlag === 'YES' && deductionAmount) {
      text = '- ' + deductionAmount.toFormatFinance();
    } else if (selectCouponList.length) {
      text = t('couponString.couponSelectNum', {
        num: selectCouponList.length,
      });
    }
    if (deductionFlag === 'YES' && couponStatus !== UserEnumsSpace.ECouponsStatus.BINDING) {
      textDecorationLine = 'line-through';
    }

    return {
      content: text,
      color,
      textDecorationLine,
    };
  }, [couponList, selectCouponList]);
  return (
    <View
      layoutStrategy="flexRowBetweenCenter"
      cardType="baseType"
      padding="8 12 8 12"
      margin="16 16 0 16">
      <Text category="p1" i18nKey={'couponString.coupon'} />
      <TouchableOpacity
        disabled={disabled}
        onPress={onOpenCouponSelect}
        style={{
          flexDirection: 'row',
          justifyContent: 'center',
          alignItems: 'center',
        }}>
        <Text
          category="p2"
          textContent={text.content}
          style={{
            color: text.color,
            textDecorationLine: text.textDecorationLine,
          }}
        />
        <Image tintColor={Colors.PRIMARY_COLOR_500} margin="0 0 0 6" name="_evidenceSelectIcon" />
      </TouchableOpacity>
    </View>
  );
};
export default memo(CouponSelectCard);
