import { ActionSheet, Image, View } from '@/components';
import React, { useEffect, useState, useCallback } from 'react';
import { Dimensions, ScrollView, TouchableNativeFeedback } from 'react-native';
import { fetchRichTextConfig } from '@/server';
import RenderHtml from 'react-native-render-html';

interface IProps {
  onClose: () => void;
  visible: boolean;
}
export default React.memo((props: IProps) => {
  const { visible, onClose } = props;
  const [couponHtmlString, setCouponHtmlString] = useState<string>(
    '<div style="margin-left:12px;margin-right:12px"><h4 style="color: #000D;text-align: center">Instrucciones</h4><p style="color: #0009;padding-left:20px">Cupón de reembolso: Puede ser utilizado para deducir el monto de reembolso al momento del pago.</p><h4 style="color: #000D;text-align: center">Normas de uso</h4><ol style="color: #0009; line-height:24px"><li>Cada cupón de reembolso equivale a una cantidad en efectivo.</li><li>Cada cupón de reembolso tiene que ser utilizado antes de que expire y solo puede ser utilizado una vez.</li><li>Solo se puede utilizar un cupón de reembolso por cada préstamo y no se puede cambiar una vez seleccionado.</li><li>Al realizar el pago, se puede utilizar cupón de reembolso para reducir el monto a pagar.</li><li>Asegúrate de liquidar el préstamo antes de la fecha de vencimiento del pago para que el cupón de reembolso sea válido.</li></ol></div>',
  );
  useEffect(() => {
    getCouponHtmlString();
  }, []);
  const getCouponHtmlString = useCallback(async () => {
    const { code, data } = await fetchRichTextConfig({ scene: 'COUPON_USAGE' });
    if (code === 0) {
      setCouponHtmlString(data);
    }
  }, []);

  const [height, setHeight] = useState(
    (Dimensions.get('window').height * 3) / 4 > 700
      ? 700
      : (Dimensions.get('window').height * 3) / 4,
  );

  return (
    <ActionSheet visible={visible} height={height} onClose={onClose}>
      <View
        height={height}
        style={{
          display: 'flex',
          borderTopLeftRadius: 12,
          borderTopRightRadius: 12,
          width: Dimensions.get('window').width,
          backgroundColor: 'background-color-0',
        }}>
        <View padding="16 16 16 16" layoutStrategy="flexRowBetweenCenter">
          <View />
          <TouchableNativeFeedback onPress={onClose}>
            <Image name="_modalCloseIcon" />
          </TouchableNativeFeedback>
        </View>
        <View
          style={{
            display: 'flex',
            alignItems: 'center',
          }}>
          <Image name="_modalCouponHelpsTitleBgIcon" />
        </View>
        <ScrollView style={{ marginTop: 16, flex: 1 }}>
          <RenderHtml
            contentWidth={Dimensions.get('window').width}
            source={{ html: couponHtmlString || '' }}
          />
        </ScrollView>
      </View>
    </ActionSheet>
  );
});
