/**
 * @description 首贷、复贷额度选择页提额劵角标，点击后唤起弹窗
 */

import { Image, ImageBackground, Layouts, Text, View } from '@/components';
import { ImageNames } from '@/config';
import { modalDataStoreInstance, ModalList } from '@/managers';
import _ from 'lodash';
import { useCallback } from 'react';
import { Pressable } from 'react-native';
import { Colors } from '@/themes';

export type IncreaseCouponAmountBadgeProps = {
  amount: string;
  couponUseType: 'amount' | 'percent';
};

const IncreaseCouponAmountBadge = (props: IncreaseCouponAmountBadgeProps) => {
  const { amount, couponUseType } = props;

  const showIncreaseCouponModal = useCallback(() => {
    // 唤起弹窗
    modalDataStoreInstance.openModal({
      key: ModalList.INCREASE_COUPON,
      extra: {
        data: {
          value: amount,
          type: couponUseType,
        },
      },
    });
  }, [amount, couponUseType]);

  if (!amount || amount === '--' || !couponUseType) {
    return null;
  }
  return (
    <Pressable onPress={showIncreaseCouponModal}>
      <View layoutStrategy={Layouts.flexRowStartCenter}>
        <ImageBackground
          name={ImageNames._increaseAmountLoanBg}
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'center',
            width: undefined,
            paddingLeft: 6,
            paddingRight: 20,
          }}
          margin="0 0 0 0"
          resizeMode="stretch">
          <Text
            style={{ fontSize: 14, lineHeight: 20, color: Colors.TEXT_COLOR_0 }}
            textContent={couponUseType === 'amount' ? `+${amount}` : `+${amount}%`}
          />
          <Image name={ImageNames._increaseAmountLoanInfo} margin="0 0 0 4" />
        </ImageBackground>
      </View>
    </Pressable>
  );
};
export default IncreaseCouponAmountBadge;
