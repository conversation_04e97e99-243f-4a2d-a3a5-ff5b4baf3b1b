import { Card, Divider, Image, Layouts, Text, View } from '@/components';
import { HitPointEnumsSpace } from '@/enums';
import { Strings, useNameSpace } from '@/i18n';
import { AppDefaultConfigManager, UserInfoManager, WalletInfoManager } from '@/managers';
import { Toast } from '@/nativeComponents';
import { OrderVOSpace } from '@/types';
import React, { ReactElement, memo, useCallback, useMemo, useState } from 'react';
import { Pressable, TouchableWithoutFeedback, LayoutAnimation } from 'react-native';
import { Colors } from '@/themes';
import { ImageNames } from '@/config';
import _ from 'lodash';
import { trackCommonEvent } from '@/trackEvent';

interface Props {
  data: OrderVOSpace.CalculateCostOnReLoanType | OrderVOSpace.CalculateCostOnFirstLoanType;
  onOpenLoanDetailModal?: () => void;
  onOpenContract?: () => void;
  // 额外需要展示的数据 {serviceFeePerDayRate:string}
  extraData?: any;
  pageKey?: HitPointEnumsSpace.EPageKey;
}
const LoanDetailCard = (props: Props): ReactElement => {
  const { data, onOpenLoanDetailModal, onOpenContract, extraData, pageKey } = props;
  const {
    loanAmount = '-',
    processFee = '-',
    loanDateNewFormat = '-',
    repayDateNewFormat = '-',
    repaymentAmount = '-',
    realRepaymentAmount,
    processFeeVat,
    realAmount,
  } = data || {};
  const t = useNameSpace().t;

  const [isExpand, setIsExpand] = useState(false);

  const handleOpenTips = useCallback(() => {
    Toast(t('directPaymentString.loan_amount_calc_tips'));
  }, []);

  const openConfirmLoanDialog = useCallback(() => {
    onOpenLoanDetailModal?.();
  }, []);

  /** 展示/隐藏扩展信息卡片 */
  const toggleExpandView = _.throttle(() => {
    pageKey &&
      trackCommonEvent(
        {
          p: pageKey,
          e: HitPointEnumsSpace.EEventKey.BTN_REPAYMENT_INFO_EXPAND,
        },
        '1',
      );
    setIsExpand(pre => !pre);
    LayoutAnimation.configureNext(
      LayoutAnimation.create(
        300, // 动画时长
        LayoutAnimation.Types.easeInEaseOut,
        LayoutAnimation.Properties.opacity,
      ),
    );
  }, 300);

  /**
   * 贷款信息详情卡片第一版
   */
  function DetailCardV1() {
    return (
      <View
        margin="16 16 0 16"
        padding="16 12 16 12"
        style={{
          backgroundColor: 'background-color-0',
          borderRadius: 8,
        }}>
        <TouchableWithoutFeedback onPress={handleOpenTips}>
          <View margin="0 0 0 0" layoutStrategy="flexRowStartCenter">
            <Text
              margin="0 8 0 0"
              style={{ color: 'text-color-600' }}
              i18nKey={'directPaymentString.loan_amount_calc'}
              category="p2"
            />
            <Image name="_question" />
          </View>
        </TouchableWithoutFeedback>
        <View margin="12 0 0 0" layoutStrategy="flexRowBetweenCenter">
          <Text
            style={{ color: 'text-color-600' }}
            i18nKey={'directPaymentString.home_loan_loan_date'}
            category="p2"
          />
          <Text style={{ color: 'text-color-800' }} textContent={loanDateNewFormat} />
        </View>
        <View margin="12 0 0 0" layoutStrategy="flexRowBetweenCenter">
          <Text
            style={{ color: 'text-color-600' }}
            i18nKey={'directPaymentString.home_loan_repayment_date'}
            category="p2"
          />
          <Text style={{ color: 'text-color-800' }} textContent={repayDateNewFormat} />
        </View>
        <View margin="12 0 0 0" layoutStrategy="flexRowBetweenCenter">
          <Text
            style={{ color: 'text-color-600' }}
            i18nKey={'directPaymentString.home_loan_total'}
            category="p2"
          />
          <TouchableWithoutFeedback onPress={openConfirmLoanDialog}>
            <View layoutStrategy="flexRowBetweenCenter">
              {realRepaymentAmount !== repaymentAmount && (
                <>
                  <Text
                    margin="0 8 0 0"
                    category="h3"
                    textContent={String(repaymentAmount).toFormatFinance()}
                    style={{
                      color: 'text-color-600',
                      opacity: 0.6,
                      textDecorationLine: 'line-through',
                    }}
                  />
                  <Image name="_mountDownIcon" style={{ marginRight: 6 }} />
                </>
              )}
              <Text
                margin="0 12 0 0"
                style={{ color: 'text-color-800' }}
                textContent={
                  realRepaymentAmount ? String(realRepaymentAmount).toFormatFinance() : '-'
                }
              />
              <Image name="_detailIcon" />
            </View>
          </TouchableWithoutFeedback>
        </View>
      </View>
    );
  }

  const ITEM_DATA = useMemo(
    () => [
      {
        labelKey: Strings.loanConfirmString.loan_detail_card_account, // 收款帐户
        value:
          WalletInfoManager.context.walletModel.defaultBankCardInfo?.cardNo?.toFormatClabe(true) ||
          '-',
      },
      {
        labelKey: Strings.loanConfirmString.loan_detail_card_repay_date, //还款日期
        value: repayDateNewFormat,
      },
      {
        labelKey: Strings.loanConfirmString.loan_detail_card_more_detail, // 还款信息及协议
        value: isExpand
          ? ''
          : t(Strings.loanConfirmString.loan_detail_card_fee_day, {
            serviceFeePerDayRate: extraData?.serviceFeePerDayRate || '-',
          }),
        action: toggleExpandView,
        valueStyle: { fontSize: 10 },
      },
    ],
    [
      isExpand,
      extraData?.serviceFeePerDayRate,
      repayDateNewFormat,
      WalletInfoManager.context.walletModel.defaultBankCardInfo?.cardNo,
    ],
  );

  const EXPAND_ITEM_DATA = useMemo(
    () => [
      {
        labelKey: Strings.loanConfirmString.confirm_loan_dialog_amount,
        children: (
          <View layoutStrategy="flexRowStartCenter">
            {loanAmount != '-' && realAmount !== loanAmount && (
              <>
                <Text
                  margin="0 0 0 0"
                  category="p1"
                  textContent={String(loanAmount).toFormatFinance()}
                  style={{
                    color: 'text-color-600',
                    opacity: 0.6,
                    textDecorationLine: 'line-through',
                    fontSize: 14,
                  }}
                />
                <Image name="_upSuccessIcon" style={{ marginRight: 4 }} />
              </>
            )}
            <Text
              style={{ color: 'text-color-800', fontSize: 14 }}
              category="p1"
              textContent={String(realAmount).toFormatFinance()}
            />
          </View>
        ),
      },
      {
        labelKey: Strings.loanConfirmString.confirm_loan_dialog_fee,
        value: String(processFee)?.toFormatFinance(),
      },
      {
        labelKey: Strings.loanConfirmString.confirm_loan_dialog_iva,
        value: String(processFeeVat)?.toFormatFinance(),
      },
      {
        labelKey: Strings.loanConfirmString.confirm_loan_dialog_total,
        children: (
          <View layoutStrategy="flexRowStartCenter" margin="0 0 0 0">
            {repaymentAmount != '-' && realRepaymentAmount !== repaymentAmount && (
              <>
                <Text
                  margin="0 8 0 0"
                  category="p1"
                  textContent={String(repaymentAmount).toFormatFinance()}
                  style={{
                    color: 'text-color-600',
                    opacity: 0.6,
                    textDecorationLine: 'line-through',
                    fontSize: 14,
                  }}
                />
                <Image name="_mountDownIcon" style={{ marginRight: 6 }} />
              </>
            )}
            <Text
              style={{ color: 'text-color-800', fontSize: 14 }}
              category="p1"
              textContent={String(realRepaymentAmount).toFormatFinance()}
            />
          </View>
        ),
      },
      {
        labelKey: Strings.loanConfirmString.loan_detail_card_contract, // 借款协议
        valueKey: Strings.loanConfirmString.loan_detail_card_check, // 查看
        action: onOpenContract,
      },
    ],
    [realRepaymentAmount, repaymentAmount, processFee, processFeeVat, realAmount, loanAmount],
  );
  const DetailCardV2 = function DetailCardV2() {
    return (
      <Card margin="16 16 0 16" padding="16 12 16 12">
        <View layoutStrategy={Layouts.flexColumnStart}>
          {ITEM_DATA.map((item, index) => {
            return (
              <View key={index} layoutStrategy={Layouts.flexRowBetweenCenter} padding="8 0 8 0">
                <Text
                  category="p1"
                  margin="0 8 0 0"
                  style={{ flex: 1, color: Colors.TEXT_COLOR_600, fontSize: 14 }}
                  i18nKey={item.labelKey}
                />
                <Pressable
                  style={{
                    flexDirection: 'row',
                    flex: 1,
                    justifyContent: 'flex-end',
                    alignItems: 'center',
                  }}
                  onPress={item.action}>
                  <Text
                    category="p1"
                    style={{ color: Colors.TEXT_COLOR_800, fontSize: 14, ...item.valueStyle }}
                    textContent={item.value}
                  />
                  {!!item.action ? (
                    <Image
                      padding="0 0 0 0"
                      margin="0 -8 0 0"
                      name={ImageNames._arrowRight}
                      style={{
                        transform: [{ rotate: isExpand ? '-90deg' : '0deg' }],
                        tintColor: Colors.TEXT_COLOR_700,
                      }}
                    />
                  ) : null}
                </Pressable>
              </View>
            );
          })}
          {isExpand ? <Divider /> : null}
          {isExpand
            ? EXPAND_ITEM_DATA.map((item, index) => {
              return (
                <View key={index} layoutStrategy={Layouts.flexRowBetweenCenter} padding="8 0 8 0">
                  <Text
                    category="p1"
                    margin="0 8 0 0"
                    style={{ flex: 1, color: Colors.TEXT_COLOR_600, fontSize: 14 }}
                    i18nKey={item.labelKey}
                  />
                  {item.children ? (
                    item.children
                  ) : (
                    <Pressable
                      style={{ flexDirection: 'row', alignItems: 'flex-start' }}
                      onPress={item.action}
                      hitSlop={10}>
                      <Text
                        category="p1"
                        style={{
                          color: item.action ? Colors.PRIMARY_COLOR_500 : Colors.TEXT_COLOR_800,
                          fontSize: 14,
                          textDecorationLine: item.action ? 'underline' : 'none',
                        }}
                        textContent={item.value}
                        i18nKey={item.valueKey}
                      />
                      {/* {!!item.action ? ( */}
                      {/*   <Image */}
                      {/*     name={ImageNames._arrowRight} */}
                      {/*     style={{ tintColor: Colors.PRIMARY_COLOR_500 }} */}
                      {/*   /> */}
                      {/* ) : null} */}
                    </Pressable>
                  )}
                </View>
              );
            })
            : null}
          {isExpand ? (
            <View layoutStrategy={Layouts.flexRowStart}>
              <Image margin="4 0 0 0" name={ImageNames._grayInfo} resizeMode="contain" />
              <Text
                margin="0 0 6 6"
                category="c1"
                style={{ color: Colors.TEXT_COLOR_500 }}
                i18nKey={Strings.loanConfirmString.loan_detail_card_tips}
              />
            </View>
          ) : null}
          {/* <Pressable onPress={toggleExpandView} hitSlop={10} style={{ alignItems: 'center' }}> */}
          {/*   <Image */}
          {/*     name={isExpand ? ImageNames._arrowTop : ImageNames._arrowBottom} */}
          {/*     width={20} */}
          {/*     height={20} */}
          {/*     style={{ tintColor: Colors.TEXT_COLOR_500 }} */}
          {/*   /> */}
          {/* </Pressable> */}
        </View>
      </Card>
    );
  };

  /**
   * 复贷用户且后端开关为 true 显示
   */
  const showV2DetailCard = useMemo(() => {
    return (
      AppDefaultConfigManager.context.appDefaultConfigModel.defaultConfigState?.calculateCostConfig
        ?.newStyleSwitch === 'YES' && UserInfoManager.context.userModel.isUserTypeOld
    );
  }, [
    AppDefaultConfigManager.context.appDefaultConfigModel.defaultConfigState?.calculateCostConfig,
    UserInfoManager.context.userModel.isUserTypeOld,
  ]);

  return showV2DetailCard ? <DetailCardV2 /> : <DetailCardV1 />;
};
export default memo(LoanDetailCard);
