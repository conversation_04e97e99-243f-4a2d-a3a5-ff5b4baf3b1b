import { Layout, Text, View } from '@/components';
import { ThemeManager, UserInfoManager } from '@/managers';
import React, { ReactElement, useEffect } from 'react';
import { ScrollView, TouchableOpacity } from 'react-native';
import nav, { navigate } from '../../../utils/nav';
import { RouterConfig } from '@/routes';
import { HitPointEnumsSpace } from '@/enums';
import { BaseConfig } from '../../../baseConfig';

export default (): ReactElement => {
  return (
    <Layout>
      <TouchableOpacity
        onPress={() => {
          navigate(RouterConfig.SELECT_AMOUNT as any);
        }}>
        <Text>选择额度页</Text>
      </TouchableOpacity>
      <TouchableOpacity
        onPress={() => {
          navigate(RouterConfig.REPAYMENT as any);
        }}>
        <Text>还款页</Text>
      </TouchableOpacity>
      <TouchableOpacity
        onPress={() => {
          navigate(RouterConfig.COMFIRM_LOAN as any);
        }}>
        <Text>用信页</Text>
      </TouchableOpacity>
      <TouchableOpacity
        onPress={() => {
          navigate(RouterConfig.WAIT_CHECK as any);
        }}>
        <Text>等待用信页</Text>
      </TouchableOpacity>
      <TouchableOpacity
        onPress={() => {
          navigate(RouterConfig.WAIT_PAYMENT as any);
        }}>
        <Text>等待放款页</Text>
      </TouchableOpacity>
      <TouchableOpacity
        onPress={() => {
          ThemeManager.ThemeManagerInstance.action.setLightMode();
        }}>
        <Text>浅色模式</Text>
      </TouchableOpacity>
      <TouchableOpacity
        onPress={() => {
          ThemeManager.ThemeManagerInstance.action.setDarkMode();
        }}>
        <Text>深色模式</Text>
      </TouchableOpacity>
    </Layout>
  );
};
