/**
 * @description: 品牌页
 * 前往登录页
 */
import { Image, View } from '@/components';
import { Colors } from '@/themes';
import { useProcessColorStyles } from '@/utils';
import { useMemo, useRef } from 'react';
import { Animated, TouchableOpacity } from 'react-native';

export default function (props: { callback: () => void }) {
  const AnimTime = 500;
  const styles = useProcessColorStyles({
    PRIMARY_COLOR_500: Colors.PRIMARY_COLOR_500,
    PRIMARY_COLOR_600: Colors.PRIMARY_COLOR_600,
  });
  const topValue = useRef(new Animated.Value(0)).current;
  const bottomValue = useRef(new Animated.Value(0)).current;
  const opacityValue = useRef(new Animated.Value(1)).current;
  // 动画执行完后跳转
  const AnimEndCallback = () => {
    props?.callback?.();
  };
  const onNext = () => {
    Animated.parallel([
      Animated.timing(topValue, {
        toValue: -350,
        duration: AnimTime,
        useNativeDriver: true,
      }),
      Animated.timing(bottomValue, {
        toValue: 450,
        duration: AnimTime,
        useNativeDriver: true,
      }),
      Animated.timing(opacityValue, {
        toValue: 0,
        duration: AnimTime,
        useNativeDriver: true,
      }),
    ]).start(() => AnimEndCallback());
  };

  const TopView = useMemo(
    () => (
      <Animated.View
        style={{
          paddingTop: 60,
          opacity: opacityValue,
          transform: [{ translateY: topValue }],
          alignItems: 'center',
          width: '100%',
          backgroundColor: styles.PRIMARY_COLOR_500,
        }}>
        <Image style={{ position: 'absolute', bottom: 100 }} name="_brandLine" />
        <Image margin="40 0 0 0" name={'_brandLogo'} />
        <Image margin="16 0 0 0" name={'_brandInfo'} />
        <TouchableOpacity style={{ marginTop: 76 }} onPress={onNext}>
          <Image name={'_brandPhoneNumber'} />
        </TouchableOpacity>
      </Animated.View>
    ),
    [onNext],
  );

  const BottomView = (
    <Animated.View
      style={{
        position: 'absolute',
        bottom: 0,
        opacity: opacityValue,
        transform: [{ translateY: bottomValue }],
        paddingBottom: 40,
        alignItems: 'center',
        backgroundColor: styles.PRIMARY_COLOR_500,
      }}>
      <Image name={'_brandBottomBg'} margin="0 20 0 0" />
    </Animated.View>
  );

  return (
    <View
      style={{
        flex: 1,
        alignItems: 'center',
        backgroundColor: 'transparent',
      }}>
      {BottomView}
      {TopView}
    </View>
  );
}
