import { BaseInputRefType } from '@/components';
import { BaseEnumsSpace, HitPointEnumsSpace, UserEnumsSpace } from '@/enums';
import { useLoginInfo, useOnInit, useSubscribeFilter } from '@/hooks';
import { BaseInfoContextType, BaseInfoManager, UserInfoManager } from '@/managers';
import { RouterConfig } from '@/routes';
import { fetchCheckMobile } from '@/server';
import { TrackEvent, nav, verifyPhoneNumber } from '@/utils';
import AppCenter from 'appcenter';
import _ from 'lodash';
import { useMemo, useRef } from 'react';

export default function useData() {
  const {
    phoneNumber,
    // readPrivacyNoticeBool,
    onSetPhoneNumber,
    // onChangeReadPrivacyNoticeBool,
  } = useLoginInfo();
  const phoneNumberRef = useRef<BaseInputRefType>(null);

  const computedButtonDisable = useMemo(() => {
    return !verifyPhoneNumber(phoneNumber);
  }, [phoneNumber]);

  /** 初始化 */
  useOnInit({
    pageKey: HitPointEnumsSpace.EPageKey.P_LOGIN_NUMBER,
  });

  const { agreePermissions } = useSubscribeFilter({
    subject: BaseInfoManager.messageCenter,
    filter: (subject: BaseInfoContextType) => {
      return {
        agreePermissions: subject.baseModel.agreePermissions,
      };
    },
  }) as {
    agreePermissions: boolean;
  };

  const onChangeAgreePermissions = (agreePermissions: boolean) =>
    BaseInfoManager.updateAgreePermissions(agreePermissions);
  // useEffect(() => {
  //   onChangeReadPrivacyNoticeBool(agreePermissions);
  // }, [agreePermissions]);

  const onCheckMobile = async () => {
    let params = {
      mobile: phoneNumber,
    };

    BaseInfoManager.changeLoadingModalVisible(true);
    let result = await fetchCheckMobile(params);
    BaseInfoManager.changeLoadingModalVisible(false);

    if (result?.code === BaseEnumsSpace.ENetworkStatus.SUCCESS) {
      let { registerFlag } = result?.data;
      UserInfoManager.updateMobile(phoneNumber);
      AppCenter.setUserId(phoneNumber);

      /** 埋点 && 表单失去焦点 */
      phoneNumberRef.current?.blur();
      TrackEvent.trackCommonEvent(
        {
          p: HitPointEnumsSpace.EPageKey.P_PHONE_NUMBER,
          e: HitPointEnumsSpace.EEventKey.BTN_NEXT,
        },
        '',
      );

      BaseInfoManager.updateSkipSetPasswordStatus(true);
      switch (registerFlag) {
        case UserEnumsSpace.ELoginStatus.LOGIN:
          BaseInfoManager.updateSetPasswordStatus(true);
          nav.resetRouteNavigateCanGoback(RouterConfig.ENTER_PWD as any, {
            phoneNumber,
            password: '',
            smsCode: '',
          });
          break;
        case UserEnumsSpace.ELoginStatus.REGISTER:
          BaseInfoManager.updateSetPasswordStatus(false);
          nav.resetRouteNavigateCanGoback(RouterConfig.GET_SMS_CODE as any, {
            phoneNumber,
            smsCode: '',
          });
          break;
        case UserEnumsSpace.ELoginStatus.SETPWD:
          BaseInfoManager.updateSetPasswordStatus(false);
          if (UserInfoManager.context.userModel.isLogined) {
            nav.nextToTopRouter();
            return;
          } else {
            nav.resetRouteNavigateCanGoback(RouterConfig.ENTER_PWD as any, {
              phoneNumber,
              smsCode: '',
              isRegister: true,
            });
          }
          break;
        default:
      }
    }

    return result?.code === BaseEnumsSpace.ENetworkStatus.SUCCESS;
  };

  const handleOpenPrivacyNotice = _.debounce(() => {
    TrackEvent.trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_PHONE_NUMBER,
        e: HitPointEnumsSpace.EEventKey.BTN_PRIVACY_POLICY,
      },
      '',
    );
    nav.navigate(RouterConfig.PERMISSION_PRIVACY as any, {
      // onNext: onCheckMobile,
    });
  }, 100);

  /** 点击下一步行为事件 */
  const handleGoNextStep = _.debounce(async () => {
    phoneNumberRef.current?.blur();
    // if (!agreePermissions) {
    //   // 直接跳转到隐私协议页
    //   nav.navigate(RouterConfig.PERMISSION_PRIVACY as any, {
    //     onNext: onCheckMobile,
    //   });
    // } else {
    onCheckMobile();
    // }
  }, 100);
  const enterVerifyOriginalMobile = () => {
    nav.navigate(RouterConfig.VERIFY_ORIGINAL_MOBILE as any);
  };

  return {
    phoneNumberRef,
    phoneNumber,
    agreePermissions,
    onChangeAgreePermissions,
    onSetPhoneNumber,
    computedButtonDisable,
    onCheckMobile,
    handleOpenPrivacyNotice,
    handleGoNextStep,
    enterVerifyOriginalMobile,
  };
}
