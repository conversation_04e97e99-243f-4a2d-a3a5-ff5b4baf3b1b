/* eslint-disable react-native/no-inline-styles */

import {
  Button,
  Check,
  Image,
  Layout,
  PhoneNumberInput,
  Text,
  TopNavigation,
  View,
} from '@/components';
import { HitPointEnumsSpace } from '@/enums';
import { UserInfoManager } from '@/managers';
import { ScreenProps } from '@/types';
import React, { ReactElement, useEffect, useMemo } from 'react';
import {
  ScrollView,
  TouchableOpacity,
  TouchableWithoutFeedback,
  KeyboardAvoidingView,
} from 'react-native';
import useData from './useData';
import { useTheme } from '@/hooks';
import { Strings } from '@/i18n';
import BrandView from './components/BrandView';
import { TrackEvent } from '@/utils';

export default ({ route }: ScreenProps<{}>): ReactElement => {
  const {
    phoneNumberRef,
    phoneNumber,
    // agreePermissions,
    onSetPhoneNumber,
    // onChangeAgreePermissions,
    computedButtonDisable,
    handleOpenPrivacyNotice,
    handleGoNextStep,
    enterVerifyOriginalMobile,
  } = useData();

  const theme = useTheme();
  const [isBrandShown, setIsBrandShown] = React.useState(false);

  useEffect(() => {
    onSetPhoneNumber(UserInfoManager.context.userModel.mobile || '');
  }, [route]);
  useEffect(() => {
    isBrandShown && phoneNumberRef.current?.focus();
  }, [isBrandShown]);

  const onPressBrandPhoneNumber = () => {
    TrackEvent.trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_LOGIN_NUMBER,
        e: HitPointEnumsSpace.EEventKey.BTN_NEXT,
      },
      '1',
    );
    setIsBrandShown(true);
  };

  const BrandMaskView = useMemo(() => {
    return isBrandShown ? null : (
      <View style={{ position: 'absolute', width: '100%', height: '100%', zIndex: 2 }}>
        <BrandView callback={onPressBrandPhoneNumber} />
      </View>
    );
  }, [isBrandShown]);

  return (
    <>
      <Layout pLevel="0" level="0">
        <TopNavigation isBack={false} bottomLine={false} showRightAction={false} />
        <ScrollView keyboardShouldPersistTaps="always">
          <View
            margin="0 16 0 16"
            padding="0 16 0 16"
            style={{
              borderRadius: 24,
            }}>
            <Text
              margin="70 0 0 0"
              category="h3"
              style={{
                color: 'text-color-800',
              }}
              i18nKey={'loginString.check_phone_number_title'}
            />

            <PhoneNumberInput
              ref={phoneNumberRef}
              // prefixKey={'loginString.phoneNumber'}
              type="line"
              prefixMargin="32 0 0 0"
              placeholderKey={'loginString.inputPlaceholder'}
              phoneNumber={phoneNumber}
              setPhoneNumber={onSetPhoneNumber}
              pageKey={HitPointEnumsSpace.EPageKey.P_PHONE_NUMBER}
              eventKey={HitPointEnumsSpace.EEventKey.E_PHONE_NUMBER}
            />
            <View padding="8 8 8 8" layoutStrategy="flexRowStartCenter">
              <Image margin="0 8 0 0" name="_tipGrayIcon" />
              <Text
                category="c2"
                style={{
                  color: 'text-color-600',
                }}
                i18nKey={'loginString.check_phone_number_content'}
              />
            </View>
            <View
              style={{
                width: '100%',
                flexDirection: 'column',
                alignItems: 'center',
                position: 'relative',
                backgroundColor: 'background-color-0',
              }}>
              <Button
                margin="32 0 0 0"
                padding="12 24 12 24"
                onPress={handleGoNextStep}
                disabled={computedButtonDisable}
                style={{ width: '100%' }}
                status="primary"
                textI18nKey="btnString.next"
              />
              <View
                margin="16 0 32 0"
                style={{
                  flexDirection: 'row',
                }}>
                {/* <Check
                  margin={'0 8 0 0'}
                  checked={agreePermissions}
                  onChange={onChangeAgreePermissions}
                  pageKey={HitPointEnumsSpace.EPageKey.P_PHONE_NUMBER}
                  eventKey={HitPointEnumsSpace.EEventKey.BTN_SELECT_PRIVACY}
                /> */}
                <Text
                  category="c2"
                  i18nKey={'loginString.readPrivacyNoticeTabOne'}
                  style={{
                    color: 'text-color-700',
                  }}
                />
                <Text
                  onPress={handleOpenPrivacyNotice}
                  category="c2"
                  i18nKey={'loginString.readPrivacyNoticeTabTwo'}
                  style={{
                    color: 'primary-color-500',
                    textDecorationLine: 'underline',
                  }}
                />
                <Text
                  category="c2"
                  i18nKey={'loginString.readPrivacyNoticeTabThree'}
                  style={{
                    color: 'text-color-700',
                  }}
                />
              </View>
            </View>
            <TouchableOpacity
              activeOpacity={0.8}
              style={{ paddingHorizontal: 16, paddingBottom: 44 }}
              onPress={enterVerifyOriginalMobile}>
              <Text
                i18nKey={Strings.loginString.update_phone_number}
                style={{ color: 'primary-color-500' }}
                isCenter
              />
            </TouchableOpacity>
          </View>
        </ScrollView>

        {BrandMaskView}
      </Layout>
    </>
  );
};
