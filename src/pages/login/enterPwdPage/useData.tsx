import { SmsCodeInputRefType } from '@/components';
import { BaseEnumsSpace, HitPointEnumsSpace, UserEnumsSpace } from '@/enums';
import { useLoginInfo, useOnInit, useSubscribeFilter } from '@/hooks';
import { useNameSpace } from '@/i18n';
import { BaseInfoManager, UserInfoContextType, UserInfoManager } from '@/managers';
import { Toast } from '@/nativeComponents';
import { RouterConfig } from '@/routes';
import { fetchLogin, fetchSendSmsCode } from '@/server';
import { trackCommonEvent, uploadEventLog } from '@/trackEvent';
import { nav, verifyPhoneNumber } from '@/utils';
import { useCallback, useMemo, useRef, useState } from 'react';

interface IProps {
  isRegister: boolean;
}
export default function useData(props: IProps) {
  const { isRegister } = props;
  const phoneNumber = useSubscribeFilter({
    subject: UserInfoManager.messageCenter,
    filter: (subject: UserInfoContextType) => {
      return subject.userModel.mobile;
    },
  }) as string;

  const t = useNameSpace('homeString').t;

  const smsCodeInputRef = useRef<SmsCodeInputRefType>(null);

  const {
    password,
    secureTextEntry,
    smsCode,
    onSetSmsCode,
    onToggleSecureEntry,
    onPasswordChange,
  } = useLoginInfo();

  const [loginType, setLoginType] = useState<UserEnumsSpace.ELoginType>(
    UserEnumsSpace.ELoginType.PWD,
  );

  const computedButtonDisable = useMemo(() => {
    if (loginType === UserEnumsSpace.ELoginType.PWD) {
      return !(verifyPhoneNumber(phoneNumber) && password.length >= 6);
    }
    if (loginType === UserEnumsSpace.ELoginType.SMS) {
      return !(verifyPhoneNumber(phoneNumber) && smsCode.length >= 4);
    }
    return true;
  }, [loginType, phoneNumber, smsCode, password]);

  const onGetLoginData = () => {
    let _params = {
      loginType: loginType,
      mobile: phoneNumber,
      password: password,
      smsCode: smsCode,
    };

    return new Promise(async resolve => {
      let result = await fetchLogin(_params);
      if (result?.code === BaseEnumsSpace.ENetworkStatus.SUCCESS) {
        // 用户登录
        let { token, userId } = result?.data;
        UserInfoManager.updateToken(token);
        UserInfoManager.updateUserId(userId);
      }
      resolve(result?.code === BaseEnumsSpace.ENetworkStatus.SUCCESS);
    });
  };

  /** 跳转首页 */
  const onNavigateHome = async () => {
    BaseInfoManager.changeLoadingModalVisible(true);
    if (!(await onGetLoginData())) {
      BaseInfoManager.changeLoadingModalVisible(false);
      return;
    }

    if (!(await UserInfoManager.updateUserState())) {
      BaseInfoManager.changeLoadingModalVisible(false);
      return;
    }

    BaseInfoManager.changeLoadingModalVisible(false);

    nav.nextToTopRouter(RouterConfig.ENTER_PWD);
  };

  const onSendSmsCode = (): Promise<boolean> => {
    let _params = {
      mobile: phoneNumber,
    };
    return new Promise(async resolve => {
      let result = await fetchSendSmsCode(_params);
      if (result?.code === BaseEnumsSpace.ENetworkStatus.SUCCESS) {
        Toast(`${t('peso_otp_success_tip')}${phoneNumber}`);
      }

      resolve(result?.code === BaseEnumsSpace.ENetworkStatus.SUCCESS);
    });
  };

  const onClickSendPreMethod = useCallback(async (): Promise<boolean> => {
    let result = false;
    BaseInfoManager.changeLoadingModalVisible(true);
    (await onSendSmsCode()) && smsCodeInputRef.current?.reStartCountDown();
    BaseInfoManager.changeLoadingModalVisible(false);
    return result;
  }, []);

  /** 初始化方法 */
  useOnInit({
    buryCallback: async () => {
      if (UserEnumsSpace.ELoginType.SMS === loginType) {
        trackCommonEvent(
          {
            p: HitPointEnumsSpace.EPageKey.P_LOGIN_OTP,
            e: HitPointEnumsSpace.EEventKey.IN_PAGE,
          },
          '',
        );
      } else if (UserEnumsSpace.ELoginType.PWD === loginType) {
        trackCommonEvent(
          {
            p: HitPointEnumsSpace.EPageKey.P_LOGIN_PASSWORD,
            e: HitPointEnumsSpace.EEventKey.IN_PAGE,
          },
          '',
        );
      }
    },
    buryBlurCallback: async () => {
      if (UserEnumsSpace.ELoginType.SMS === loginType) {
        trackCommonEvent(
          {
            p: HitPointEnumsSpace.EPageKey.P_LOGIN_OTP,
            e: HitPointEnumsSpace.EEventKey.OUT_PAGE,
          },
          '',
        );
      } else if (UserEnumsSpace.ELoginType.PWD === loginType) {
        trackCommonEvent(
          {
            p: HitPointEnumsSpace.EPageKey.P_LOGIN_PASSWORD,
            e: HitPointEnumsSpace.EEventKey.OUT_PAGE,
          },
          '',
        );
      }
    },
  });

  const onSwitchLoginType = useCallback(async (loginType: UserEnumsSpace.ELoginType) => {
    if (UserEnumsSpace.ELoginType.SMS === loginType) {
      trackCommonEvent(
        {
          p: HitPointEnumsSpace.EPageKey.P_LOGIN_OTP,
          e: HitPointEnumsSpace.EEventKey.IN_PAGE,
        },
        '',
      );
    } else if (UserEnumsSpace.ELoginType.PWD === loginType) {
      trackCommonEvent(
        {
          p: HitPointEnumsSpace.EPageKey.P_LOGIN_PASSWORD,
          e: HitPointEnumsSpace.EEventKey.IN_PAGE,
        },
        '',
      );
    }
    uploadEventLog();

    setLoginType(loginType);
  }, []);

  return {
    smsCodeInputRef,
    onNavigateHome,
    setLoginType,
    loginType,
    smsCode,
    onSwitchLoginType,
    onSendSmsCode,
    onSetSmsCode,
    onClickSendPreMethod,
    phoneNumber,
    password,
    secureTextEntry,
    onPasswordChange,
    onToggleSecureEntry,
    computedButtonDisable,
  };
}
