/* eslint-disable react-native/no-inline-styles */
import {
  Button,
  Layout,
  PasswordInput,
  SmsCodeInput,
  Text,
  TopNavigation,
  View,
} from '@/components';
import { UserEnumsSpace } from '@/enums';
import { ScreenProps } from '@/types';
import { isMoreThanLength } from '@/utils';
import React, { useEffect } from 'react';
import { ScrollView, TouchableWithoutFeedback } from 'react-native';
import { Strings, useNameSpace } from '@/i18n';
import useData from './useData';
import { useSmsCodeHandler } from '../../../hooks/useSmsCodeHandler';

export default ({
  navigation,
  route,
}: ScreenProps<{
  phoneNumber: string;
  password: string;
  smsCode: string;
  loginType: UserEnumsSpace.ELoginType.PWD;
  isRegister: boolean;
}>): React.ReactElement => {
  const t = useNameSpace().t;

  const {
    smsCodeInputRef,
    onNavigateHome,
    setLoginType,
    loginType,
    smsCode,
    onSwitchLoginType,
    onSetSmsCode,
    onClickSendPreMethod,
    phoneNumber,
    password,
    secureTextEntry,
    onToggleSecureEntry,
    onPasswordChange,
    computedButtonDisable,
  } = useData({
    isRegister: route?.params?.isRegister,
  });
  useSmsCodeHandler(onSetSmsCode);

  useEffect(() => {
    initRouterParams();
  }, [route]);

  const initRouterParams = () => {
    onPasswordChange(route?.params?.password || '');
    onSetSmsCode(route?.params?.smsCode || '');
    onSwitchLoginType(
      route?.params?.isRegister ? UserEnumsSpace.ELoginType.SMS : UserEnumsSpace.ELoginType.PWD,
    );
  };

  const EnterPwdView = (
    <>
      <Text category="h2" i18nKey={'pwdString.inputPassword'} />
      <View
        style={{
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}>
        <Text padding="32 0 0 0" style={{ alignSelf: 'flex-start' }}>
          <Text
            category="p1"
            margin="32 0 0 8"
            i18nKey={Strings.myString.smscode_send_to}
            style={{ color: 'text-color-600' }}
          />
          <Text category="p1" textContent={`+52 ${phoneNumber}`} />
        </Text>
        <PasswordInput
          type="line"
          // prefixKey={'pwdString.inputPassword'}
          prefixMargin={'32 0 0 0'}
          validateConfig={[
            {
              condition: isMoreThanLength(password, 6),
              info: t('formString.require_min_six_size'),
              status: 'danger',
            },
          ]}
          value={password}
          placeholderKey={'pwdString.inputPassword'}
          secureTextEntry={secureTextEntry}
          toggleSecureEntry={onToggleSecureEntry}
          onChangeText={onPasswordChange}
        />
        <Button
          margin="32 0 0 0"
          status="primary"
          disabled={computedButtonDisable}
          padding={'16 0 16 0'}
          style={{ width: '100%' }}
          onPress={onNavigateHome}
          textI18nKey="btnString.next"
        />
        {!route?.params?.isRegister && (
          <TouchableWithoutFeedback
            onPress={() => {
              onSwitchLoginType(UserEnumsSpace.ELoginType.SMS);
            }}>
            <Text
              margin="24 0 0 0"
              category="p1"
              status="primary"
              i18nKey={'pwdString.changeSms'}
            />
          </TouchableWithoutFeedback>
        )}
      </View>
    </>
  );

  const EnterSmsView = (
    <>
      <Text category="h2" i18nKey={'pwdString.inputSmscode'} />
      <View
        style={{
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}>
        <Text padding="32 0 0 0" style={{ alignSelf: 'flex-start' }}>
          <Text
            category="p1"
            margin="32 0 0 8"
            i18nKey={Strings.myString.smscode_send_to}
            style={{ color: 'text-color-600' }}
          />
          <Text category="p1" textContent={`+52 ${phoneNumber}`} />
        </Text>
        <SmsCodeInput
          type="line"
          ref={smsCodeInputRef}
          placeholderKey={'loginString.smscode_input_placeholder'}
          prefixMargin={'32 0 0 0'}
          smsCode={smsCode}
          setSmsCode={onSetSmsCode}
          clickSendPreMethod={onClickSendPreMethod}
          scenesId="login"
        />
        <Button
          margin="32 0 0 0"
          status="primary"
          disabled={computedButtonDisable}
          padding={'16 0 16 0'}
          style={{ width: '100%' }}
          onPress={onNavigateHome}
          textI18nKey="btnString.next"
        />
        {!route?.params?.isRegister && (
          <TouchableWithoutFeedback
            onPress={() => {
              onSwitchLoginType(UserEnumsSpace.ELoginType.PWD);
            }}>
            <Text
              margin="24 0 0 0"
              category="p1"
              isCenter={true}
              status="primary"
              i18nKey={'pwdString.changePwd'}
            />
          </TouchableWithoutFeedback>
        )}
      </View>
    </>
  );
  return (
    <>
      <Layout pLevel="0" level="0">
        <TopNavigation marginTop="32" showRightAction={false} bottomLine={false} />
        <ScrollView fadingEdgeLength={10} keyboardShouldPersistTaps="always">
          <View margin="32 32 0 32">
            {loginType === UserEnumsSpace.ELoginType.PWD && EnterPwdView}
            {loginType === UserEnumsSpace.ELoginType.SMS && EnterSmsView}
          </View>
        </ScrollView>
      </Layout>
    </>
  );
};
