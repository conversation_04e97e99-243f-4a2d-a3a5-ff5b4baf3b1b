import { BaseInputRefType, SmsCodeInputRefType, AnimatedSmsCodeInputRefType } from '@/components';
import { BaseEnumsSpace, HitPointEnumsSpace, UserEnumsSpace } from '@/enums';
import { useLoginInfo, useOnInit, useSubscribeFilter } from '@/hooks';
import {
  BaseInfoManager,
  ModalList,
  UserInfoContextType,
  UserInfoManager,
  modalDataStoreInstance,
} from '@/managers';
import { Toast } from '@/nativeComponents';
import { RouterConfig } from '@/routes';
import { fetchRegister, fetchIsInviteDuring, fetchOtp, responseHandler } from '@/server';
import {
  TrackEvent,
  checkTextLength,
  isInviteCodeExist,
  nav,
  verifyPhoneNumber,
  isInviteCodeInput,
} from '@/utils';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { AdjustEventPointTools } from '../../../business/adjust';
import { useNameSpace } from '../../../i18n';

interface IProps {
  isRegister: boolean;
}
export default function useData(props: IProps) {
  const { isRegister } = props;
  const t = useNameSpace('homeString').t;
  const { smsCode, onSetPhoneNumber, onSetSmsCode, inviteCode, onSetInviteCode } = useLoginInfo();
  const [inviteCodeInputVisible, setInviteCodeInputVisible] = useState<boolean>(false);
  const smsCodeInputRef = useRef<AnimatedSmsCodeInputRefType>(null);
  const inviteCodeInputRef = useRef<BaseInputRefType>(null);
  const [otpVisible, setOtpVisible] = useState<boolean>(false);
  const [isStart, setStart] = useState<boolean>(false);

  const phoneNumber =
    useSubscribeFilter({
      subject: UserInfoManager.messageCenter,
      filter: (subject: UserInfoContextType) => {
        return subject.userModel.mobile;
      },
    }) || '';

  /** 初始化 */
  useOnInit({
    pageKey: HitPointEnumsSpace.EPageKey.P_SIGNUP,
    callback: async () => {
      await onFetchIsInviteDuring();
    },
  });

  // 计算按钮是否可用
  const computedButtonDisable = useMemo(() => {
    return !(
      verifyPhoneNumber(phoneNumber) &&
      checkTextLength(smsCode, 4) &&
      (inviteCode ? isInviteCodeExist(inviteCode) : true)
    );
  }, [phoneNumber, smsCode, inviteCode]);
  const onSendOtp = async (
    otpChannel: UserEnumsSpace.OtpChannel,
    callback?: Function,
  ): Promise<boolean> => {
    const params = {
      mobile: phoneNumber,
      otpChannel,
      bizType: UserEnumsSpace.BizType.LOGIN,
    };
    BaseInfoManager.changeLoadingModalVisible(true);
    const { error, data } = await responseHandler(fetchOtp(params));
    BaseInfoManager.changeLoadingModalVisible(false);
    if (error) {
      return false;
    }
    callback?.(data);
    return true;
  };
  const onSendSmsCode = async (): Promise<boolean> => {
    return await onSendOtp(UserEnumsSpace.OtpChannel.SMS, () => {
      Toast(`${t('peso_otp_success_tip')}${phoneNumber}`);
    });
  };
  const onSendOtpCode = async (): Promise<boolean> => {
    return await onSendOtp(UserEnumsSpace.OtpChannel.VOICE_PHONE, () => {
      modalDataStoreInstance.openModal({
        key: ModalList.RECIVE_OTP_BY_CALL,
        i18nKey: 'modalString.callYouSendOTP',
        confirmBtnName: 'btnString.OK',
        confirmBtnCallback: () => {
          /** 埋点 */
          TrackEvent.trackCommonEvent(
            {
              p: HitPointEnumsSpace.EPageKey.P_SIGNUP,
              e: HitPointEnumsSpace.EEventKey.BTN_OBTAIN_OTP_CALL_SI,
            },
            '',
          );
        },
      });
    });
  };

  const onFetchIsInviteDuring = async (): Promise<boolean> => {
    let result = await fetchIsInviteDuring();

    setInviteCodeInputVisible(
      result.code === BaseEnumsSpace.ENetworkStatus.SUCCESS && result.data.isDuring,
    );
    return result.code === BaseEnumsSpace.ENetworkStatus.SUCCESS && result.data.isDuring;
  };

  const onCheckSmsCode = async (_smsCode: string): Promise<boolean> => {
    let params = {
      loginType: UserEnumsSpace.ELoginType.SMS,
      mobile: phoneNumber,
      smsCode: _smsCode ? _smsCode : smsCode,
      inviteCode,
    };

    let result = await fetchRegister(params);
    if (result.code !== BaseEnumsSpace.ENetworkStatus.SUCCESS) {
      return false;
    }

    let { token, userId } = result?.data;
    UserInfoManager.updateToken(token);
    UserInfoManager.updateUserId(userId);
    BaseInfoManager.updateSkipSetPasswordStatus(false);
    AdjustEventPointTools.trackEventOfRegister();
    return result?.code === BaseEnumsSpace.ENetworkStatus.SUCCESS;
  };

  const onNavigateRegisterPage = async (str: string = '') => {
    if (isRegister) {
      nav.navigate(RouterConfig.SET_PWD as any, {
        fromPage: RouterConfig.GET_SMS_CODE,
        password: '',
        repassword: '',
      });
    } else {
      BaseInfoManager.changeLoadingModalVisible(true);

      if (!(await onCheckSmsCode(str))) {
        BaseInfoManager.changeLoadingModalVisible(false);
        return false;
      }

      if (!(await UserInfoManager.updateUserState())) {
        BaseInfoManager.changeLoadingModalVisible(false);
        return;
      }

      BaseInfoManager.changeLoadingModalVisible(false);
      nav.nextToTopRouter();
    }
  };
  /** smscode进入页面逻辑 */
  const onSmsCodeInit = useCallback(async (): Promise<boolean> => {
    if (smsCodeInputRef.current?.isStart()) {
      return false;
    }
    BaseInfoManager.changeLoadingModalVisible(true);
    let result = await onSendSmsCode();
    result && smsCodeInputRef.current?.reStartCountDown();
    setStart(true);
    return result;
  }, []);

  useEffect(() => {
    initRouterParams();
  });

  // 进入当前页面,从路由获取电话号码
  const initRouterParams = () => {
    onSetPhoneNumber(UserInfoManager.context.userModel.mobile);
  };

  /** 点击下一步行为事件 */
  const handlePress = async () => {
    /** 埋点 */
    TrackEvent.trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_SIGNUP,
        e: HitPointEnumsSpace.EEventKey.BTN_NEXT,
      },
      '1',
    );
    smsCodeInputRef.current?.blur();

    inviteCodeInputRef.current?.blur();

    onNavigateRegisterPage();
  };

  const onClickSendPreMethod = useCallback(async (): Promise<boolean> => {
    TrackEvent.trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_SIGNUP,
        e: HitPointEnumsSpace.EEventKey.BTN_SELECT_OTP_METHOD_SHOW,
      },
      '',
    );
    setOtpVisible(true);
    return true;
  }, []);
  const onCloseOtpModal = () => {
    setOtpVisible(false);
  };
  const handleSendSmsCode = useCallback(async () => {
    onCloseOtpModal();
    /** 埋点 */
    TrackEvent.trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_SIGNUP,
        e: HitPointEnumsSpace.EEventKey.BTN_OBTAIN_OTP_SMS,
      },
      '1',
    );
    return await onSendSmsCode();
  }, []);
  const onSendWhatsappCode = async (): Promise<boolean> => {
    return await onSendOtp(UserEnumsSpace.OtpChannel.WHATS_APP, async () => {
      Toast(`${t('peso_otp_success_tip')}${phoneNumber}`);
    });
  };
  const onSendWhatsapp = useCallback(async () => {
    onCloseOtpModal();
    TrackEvent.trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_SIGNUP,
        e: HitPointEnumsSpace.EEventKey.BTN_OBTAIN_OTP_WHATSAPP,
      },
      '1',
    );
    return await onSendWhatsappCode();
  }, []);
  const onSendVoice = useCallback(async () => {
    onCloseOtpModal();
    /** 埋点 */
    TrackEvent.trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_SIGNUP,
        e: HitPointEnumsSpace.EEventKey.BTN_OBTAIN_OTP_CALL,
      },
      '1',
    );
    return await onSendOtpCode();
  }, []);
  const onChangeSmsCode = (smsCode: string) => {
    if (isInviteCodeInput(smsCode)) {
      onSetSmsCode(smsCode);
    }
  };
  const handleOtpChannel = useCallback(async (otpChannel: UserEnumsSpace.OtpChannel) => {
    const sendMap = {
      [UserEnumsSpace.OtpChannel.VOICE_PHONE]: onSendVoice,
      [UserEnumsSpace.OtpChannel.SMS]: handleSendSmsCode,
      [UserEnumsSpace.OtpChannel.WHATS_APP]: onSendWhatsapp,
    };
    (await sendMap[otpChannel]()) && smsCodeInputRef.current?.reStartCountDown();
    return true;
  }, []);
  return {
    handleOtpChannel,
    smsCodeInputRef,
    inviteCodeInputRef,
    inviteCodeInputVisible,
    inviteCode,
    onSetInviteCode,
    onSmsCodeInit,
    onSendOtpCode,
    handlePress,
    phoneNumber,
    smsCode,
    onSetPhoneNumber,
    onSetSmsCode: onChangeSmsCode,
    computedButtonDisable,
    onClickSendPreMethod,
    otpVisible,
    onCloseOtpModal,
    isStart,
  };
}
