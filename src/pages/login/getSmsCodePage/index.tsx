import {
  Button,
  Layout,
  PhoneNumberInput,
  SmsCodeInput,
  PrefixInput,
  Text,
  TopNavigation,
  View,
  AnimatedSmsCodeInput,
  SeaInput,
} from '@/components';
import { HitPointEnumsSpace } from '@/enums';
import { CallYouSendSmsModal, ReciveOtpByCallModal } from '@/modals';
import { ScreenProps } from '@/types';
import React, { useMemo } from 'react';
import { ScrollView } from 'react-native';
import useData from './useData';
import { checkTextLength, isInviteCodeExist, isStringExist } from '@/utils';
import OtpModal from './components/OtpModal';
import { t } from 'i18next';
import { useSmsCodeHandler } from '../../../hooks/useSmsCodeHandler';

export default ({
  navigation,
  route,
}: ScreenProps<{
  phoneNumber: string;
  smsCode: string;
  isRegister: boolean;
}>): React.ReactElement => {
  const {
    smsCodeInputRef,
    inviteCodeInputRef,
    inviteCode,
    onSetInviteCode,
    inviteCodeInputVisible,
    onSmsCodeInit,
    onClickSendPreMethod,
    handlePress,
    phoneNumber,
    smsCode,
    onSetPhoneNumber,
    onSetSmsCode,
    computedButtonDisable,
    otpVisible,
    onCloseOtpModal,
    handleOtpChannel,
    isStart,
  } = useData({
    isRegister: route?.params?.isRegister,
  });

  useSmsCodeHandler(onSetSmsCode);

  React.useEffect(() => {
    onSetPhoneNumber('');
    onSetSmsCode('');
    initRouterParams();
  }, [route]);

  const initRouterParams = () => {
    onSetPhoneNumber(route?.params?.phoneNumber || '');
    onSetSmsCode(route?.params?.smsCode || '');
  };

  const $inviteCodeInput = useMemo(() => {
    return (
      <>
        {inviteCodeInputVisible && (
          <SeaInput
            type="line"
            ref={inviteCodeInputRef}
            prefixKey={'loginString.invite_code'}
            prefixMargin={'32 0 0 0'}
            placeholderKey={'loginString.invite_code_placeholder'}
            value={inviteCode}
            maxLength={6}
            setValue={onSetInviteCode}
            validateConfig={[
              {
                condition: isInviteCodeExist(inviteCode),
                info: t('verificationString.failed', {
                  name: t('loginString.invite_code_placeholder'),
                }),
                status: 'danger',
              },
            ]}
            isShowPlaceholderKey
          />
        )}
      </>
    );
  }, [inviteCode, inviteCodeInputVisible]);

  return (
    <>
      <Layout pLevel="0" level="0">
        <TopNavigation marginTop="32" showRightAction={false} bottomLine={false} />
        <ScrollView fadingEdgeLength={10} keyboardShouldPersistTaps="always">
          <View margin="32 32 0 32">
            <Text category="h2" i18nKey={'loginString.SMSLogin'} />
            <View
              style={{
                flexDirection: 'column',
                justifyContent: 'space-between',
              }}>
              <Text padding="32 0 0 0">
                <Text
                  category="p1"
                  margin="32 0 0 8"
                  textContent={`Tu celular: `}
                  style={{ color: 'text-color-600' }}
                />
                <Text category="p1" textContent={`+52 ${phoneNumber}`} />
              </Text>
              <AnimatedSmsCodeInput
                type="line"
                ref={smsCodeInputRef}
                placeholderKey={'loginString.smscode_input_placeholder'}
                prefixMargin={'32 0 0 0'}
                smsCode={smsCode}
                pageKey={HitPointEnumsSpace.EPageKey.P_SIGNUP}
                eventKey={HitPointEnumsSpace.EEventKey.E_PHONE_OTP}
                setSmsCode={onSetSmsCode}
                initMethod={onSmsCodeInit}
                clickSendPreMethod={onClickSendPreMethod}
                scenesId="register"
                maxLength={4}
                validateConfig={[
                  {
                    condition: isStringExist(smsCode),
                    info: t('verificationString.required'),
                    status: 'danger',
                  },
                  {
                    condition: checkTextLength(smsCode, 4),
                    info: t('verificationString.failed', {
                      name: t('loginString.smscode_input_prefix'),
                    }),
                    status: 'danger',
                  },
                ]}
                accessoryRightTextKey={isStart ? 'reSend' : 'send'}
              />
              {$inviteCodeInput}
              <Button
                status="primary"
                disabled={computedButtonDisable}
                onPress={handlePress}
                margin="32 0 0 0"
                style={{ width: '100%' }}
                padding={'12 24 12 24'}
                textI18nKey={'btnString.next'}
              />
            </View>
          </View>
        </ScrollView>
      </Layout>
      <ReciveOtpByCallModal />
      <CallYouSendSmsModal />
      <OtpModal
        visible={otpVisible}
        onClose={onCloseOtpModal}
        handleOtpChannel={handleOtpChannel}
      />
    </>
  );
};
