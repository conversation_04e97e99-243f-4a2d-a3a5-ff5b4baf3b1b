import { memo, useMemo } from 'react';
import { TouchableOpacity } from 'react-native';
import { Text, View, ActionSheet, Image, Divider, Button } from '@/components';
import { Strings } from '@/i18n';
import { BaseInfoManager } from '@/managers';
import { HitPointEnumsSpace, UserEnumsSpace } from '@/enums';

interface IProps {
  visible: boolean;
  onClose: Function;
  handleOtpChannel: Function;
}
interface Item {
  title: string;
  icon: string;
  otpChannel: UserEnumsSpace.OtpChannel;
}
const OtpModal = (props: IProps) => {
  const { visible, onClose, handleOtpChannel } = props;
  const list = useMemo(() => {
    const isVoiceSmsSwitchStatus = BaseInfoManager.context.baseModel.isVoiceSmsSwitchStatus;
    const data: Item[] = [
      {
        title: Strings.loginString.otp_modal_sms,
        icon: '_modalIconSms',
        otpChannel: UserEnumsSpace.OtpChannel.SMS,
      },
      {
        title: Strings.loginString.otp_modal_whatsapp,
        icon: '_modalIconWhatsapp',
        otpChannel: UserEnumsSpace.OtpChannel.WHATS_APP,
      },
      ...(isVoiceSmsSwitchStatus
        ? [
            {
              title: Strings.loginString.otp_modal_voice,
              icon: '_modalIconVoice',
              otpChannel: UserEnumsSpace.OtpChannel.VOICE_PHONE,
            },
          ]
        : []),
    ];
    return data;
  }, []);
  const renderItem = (item: Item, index: number) => {
    return (
      <View key={item.title}>
        <View padding="12 0 12 0" layoutStrategy="flexRowBetweenCenter">
          <View layoutStrategy="flexRowStartCenter">
            <Image name={item.icon} />
            <Text category="p2" padding="0 0 0 8" i18nKey={item.title} />
          </View>
          <Button
            textI18nKey={Strings.loginString.otp_btn}
            onPress={() => {
              handleOtpChannel(item.otpChannel);
            }}
          />
        </View>
        {index === list.length - 1 ? null : <Divider margin="0 0 8 0" />}
      </View>
    );
  };
  return (
    <ActionSheet visible={visible} onClose={onClose}>
      <View
        padding="12 12 24 12"
        style={{
          backgroundColor: 'primary-color-500',
          borderTopLeftRadius: 8,
          borderTopRightRadius: 8,
        }}>
        <View padding="16 0 20 0">
          <Text
            i18nKey={Strings.loginString.otp_modal_title}
            category="h3"
            isCenter
            style={{ color: 'text-color-0' }}
          />
          <TouchableOpacity
            onPress={() => {
              onClose();
            }}
            activeOpacity={0.8}
            style={{ position: 'absolute', right: 0, top: 0 }}>
            <Image name="_modalIconClose" />
          </TouchableOpacity>
        </View>
        <View padding="0 16 0 16" style={{ borderRadius: 8, backgroundColor: 'text-color-0' }}>
          {list.map(renderItem)}
        </View>
      </View>
    </ActionSheet>
  );
};
export default memo(OtpModal);
