import { BaseInputRefType } from '@/components';
import { BaseEnumsSpace, UserEnumsSpace, HitPointEnumsSpace } from '@/enums';
import { useSubscribeFilter, useOnInit } from '@/hooks';
import { BaseInfoManager, modalDataStoreInstance, ModalList, UserInfoManager } from '@/managers';
import { RouterConfig } from '@/routes';
import { fetchLogin, fetchSetPassword, doResetPassword } from '@/server';
import { UserVOSpace } from '@/types';
import { TrackEvent, nav } from '@/utils';
import _ from 'lodash';
import { useRef } from 'react';
import { UserInfoContextType } from 'src/managers/userInfo';

interface IProps {
  password: string;
  smsCode: string;
  fromPage:
    | RouterConfig.HOME_SCREEN
    | RouterConfig.SPLASH
    | RouterConfig.ENTER_PHONE_NUMBER
    | RouterConfig.VERIFY_PWD
    | RouterConfig.MY
    | '';
}

export default function useData(props: IProps) {
  const { password, smsCode, fromPage } = props;
  const phoneNumber = useSubscribeFilter({
    subject: UserInfoManager.messageCenter,
    filter: (subject: UserInfoContextType) => {
      return subject.userModel.mobile;
    },
  }) as string;

  /** 初始化 */
  useOnInit({
    pageKey: HitPointEnumsSpace.EPageKey.P_PASSWORD_SET,
  });

  const pwdInputRef = useRef<BaseInputRefType>(null);
  const rePwdInputRef = useRef<BaseInputRefType>(null);

  const onSetPasswordFromHome = async () => {
    let params = {
      loginType: UserEnumsSpace.ELoginType.PWD,
      mobile: phoneNumber,
      password: password,
    };

    return new Promise(async resolve => {
      let result = await fetchSetPassword(params);
      BaseInfoManager.changeLoadingModalVisible(false);
      const { token, userId } = result?.data;

      if (result?.code === BaseEnumsSpace.ENetworkStatus.SUCCESS) {
        if (!UserInfoManager.context.userModel.isLogined) {
          UserInfoManager.updateToken(token);
          UserInfoManager.updateUserId(userId);
        }
        BaseInfoManager.updateSetPasswordStatus(true);
        nav.navigate(fromPage as any);
      }

      resolve(result?.code === BaseEnumsSpace.ENetworkStatus.SUCCESS);
    });
  };
  const onSetPassword = async () => {
    let params = {
      loginType: UserEnumsSpace.ELoginType.SMS,
      smsCode: smsCode,
      mobile: phoneNumber,
      password: password,
    };

    return new Promise(async resolve => {
      let result = await doResetPassword(params);
      BaseInfoManager.changeLoadingModalVisible(false);
      const { token, userId } = result?.data || {};

      if (result?.code === BaseEnumsSpace.ENetworkStatus.SUCCESS) {
        if (!UserInfoManager.context.userModel.isLogined && token) {
          UserInfoManager.updateToken(token);
          UserInfoManager.updateUserId(userId);
        }
        BaseInfoManager.updateSetPasswordStatus(true);
        modalDataStoreInstance.openModal({
          key: ModalList.INFO_PROMPT_CONFIRM,
          confirmBtnName: 'btnString.OK',
          imageKey: '_loginRightIcon',
          i18nKey: 'myString.modify_success',
          isBackdropClose: false,
          confirmBtnCallback: () => {
            if (fromPage === RouterConfig.VERIFY_PWD) {
              nav.navigate(fromPage as any);
            } else {
              nav.navigate(RouterConfig.MY as any);
            }
          },
        });
      }

      resolve(result?.code === BaseEnumsSpace.ENetworkStatus.SUCCESS);
    });
  };

  const onLogin = async () => {
    BaseInfoManager.changeLoadingModalVisible(true);
    if (fromPage === RouterConfig.HOME_SCREEN) {
      if (!(await onSetPasswordFromHome())) {
      }
    } else {
      if (!(await onSetPassword())) {
      }
    }
  };

  const handleGoHome = async () => {
    /** 埋点 */
    pwdInputRef.current?.blur();
    rePwdInputRef.current?.blur();
    TrackEvent.trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_PASSWORD,
        e: HitPointEnumsSpace.EEventKey.BTN_NEXT,
      },
      '1',
    );

    await onLogin();
  };

  return {
    pwdInputRef,
    rePwdInputRef,
    phoneNumber,
    handleGoHome,
  };
}
