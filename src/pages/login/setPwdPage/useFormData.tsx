import { useMemo } from 'react';
import { useLoginInfo } from '@/hooks';
import { isEqual, isMoreThanLength } from '@/utils';

export default function useFormData() {
  const {
    phoneNumber,
    password,
    rePassword,
    secureTextEntry,
    reSecureTextEntry,
    onToggleSecureEntry,
    onToggleReSecureEntry,
    onPasswordChange,
    onRePasswordChange,
  } = useLoginInfo();

  const computedButtonDisable = useMemo(() => {
    return !(
      isEqual(password, rePassword) &&
      isMoreThanLength(password, 6) &&
      isMoreThanLength(rePassword, 6)
    );
  }, [rePassword, password]);

  return {
    phoneNumber,
    password,
    rePassword,
    secureTextEntry,
    reSecureTextEntry,
    onToggleSecureEntry,
    onToggleReSecureEntry,
    onPasswordChange,
    onRePasswordChange,
    computedButtonDisable,
  };
}
