/* eslint-disable react-native/no-inline-styles */
import {
  Button,
  Layout,
  PasswordInput,
  PhoneNumberInput,
  Text,
  TopNavigation,
  View,
  Image,
} from '@/components';
import { HitPointEnumsSpace } from '@/enums';
import { RouterConfig } from '@/routes';
import { ScreenProps } from '@/types';
import { isEqual, isMoreThanLength } from '@/utils';
import React from 'react';
import { ScrollView } from 'react-native';
import useData from './useData';
import useFormData from './useFormData';
import { Strings, useNameSpace } from '@/i18n';

export default ({
  route,
}: ScreenProps<{
  smsCode: string;
  fromPage:
    | RouterConfig.HOME_SCREEN
    | RouterConfig.SPLASH
    | RouterConfig.ENTER_PHONE_NUMBER
    | RouterConfig.VERIFY_PWD
    | RouterConfig.MY
    | '';
}>): React.ReactElement => {
  const {
    password,
    rePassword,
    secureTextEntry,
    reSecureTextEntry,
    onToggleSecureEntry,
    onToggleReSecureEntry,
    onPasswordChange,
    onRePasswordChange,
    computedButtonDisable,
  } = useFormData();
  const t = useNameSpace().t;
  const { handleGoHome, phoneNumber, pwdInputRef, rePwdInputRef } = useData({
    password,
    smsCode: route?.params?.smsCode,
    fromPage: route?.params?.fromPage,
  });

  React.useEffect(() => {
    onPasswordChange('');
    onRePasswordChange('');
  }, [route]);
  const renderTop = () => {
    return (
      <View
        margin="24 0 24 0"
        padding="12 12 12 12"
        style={{ backgroundColor: 'primary-color-100', borderRadius: 8 }}
        layoutStrategy="flexRowStartCenter">
        <Image name="_safeTipIcon" />
        <Text padding="0 12 0 8" i18nKey={Strings.validateBasicInfoString.tip} category="c1" />
      </View>
    );
  };
  return (
    <>
      <Layout pLevel="0" level="0">
        <TopNavigation titleKey="myString.modify_pwd" />
        <ScrollView
          style={{ paddingHorizontal: 16 }}
          fadingEdgeLength={10}
          keyboardShouldPersistTaps="always">
          {renderTop()}
          <View margin="0 16 0 16">
            <Text i18nKey={'myString.modify_pwd'} />
            <Text padding="32 8 0 8" textContent={'+52 ' + phoneNumber} />
            <PasswordInput
              type="line"
              ref={pwdInputRef}
              validateConfig={[
                {
                  condition: isMoreThanLength(password, 6),
                  info: t(Strings.myString.pwd),
                  status: 'danger',
                },
              ]}
              onBlur={() => {
                rePassword && rePwdInputRef.current?.blur();
              }}
              prefixMargin={'32 0 0 0'}
              value={password}
              pageKey={HitPointEnumsSpace.EPageKey.P_PASSWORD}
              eventKey={HitPointEnumsSpace.EEventKey.E_PASSWORD}
              placeholderKey={Strings.validateBasicInfoString.setPwd}
              secureTextEntry={secureTextEntry}
              toggleSecureEntry={onToggleSecureEntry}
              onChangeText={onPasswordChange}
            />
            <PasswordInput
              type="line"
              ref={rePwdInputRef}
              placeholderKey={Strings.validateBasicInfoString.resetPwd}
              validateConfig={[
                // {
                //   condition: isMoreThanLength(rePassword, 6),
                //   info: t(Strings.myString.pwd),
                //   status: 'danger',
                // },
                {
                  condition: isEqual(password, rePassword),
                  info: t(Strings.validateBasicInfoString.notEqual),
                  status: 'danger',
                },
              ]}
              prefixMargin={'32 0 0 0'}
              value={rePassword}
              pageKey={HitPointEnumsSpace.EPageKey.P_PASSWORD}
              eventKey={HitPointEnumsSpace.EEventKey.E_PASSWORD_CONFIRM}
              secureTextEntry={reSecureTextEntry}
              toggleSecureEntry={onToggleReSecureEntry}
              onChangeText={onRePasswordChange}
            />
            <Button
              status="primary"
              disabled={computedButtonDisable}
              onPress={handleGoHome}
              margin="32 0 0 0"
              style={{ width: '100%' }}
              padding={'16 0 16 0'}
              textI18nKey="btnString.next"
            />
          </View>
        </ScrollView>
      </Layout>
    </>
  );
};
