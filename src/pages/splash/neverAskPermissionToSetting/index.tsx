/* eslint-disable react-native/no-inline-styles */
import {
  Button,
  // Divider,
  Image,
  Layout,
  Text,
  // TopNavigation,
  View,
} from '@/components';
import { HitPointEnumsSpace } from '@/enums';
import { useOnInit } from '@/hooks';
import { RouterConfig } from '@/routes';
import { ScreenProps } from '@/types';
import {
  ApplicationState,
  TrackEvent,
  applicationState,
  isHasAllPermission,
  jumpAppSettingActivity,
  nav,
  navigation,
} from '@/utils';
import React, { ReactElement, useCallback, useEffect } from 'react';
import { Dimensions, ScrollView } from 'react-native';
import { useNavigation } from '@react-navigation/native';

/**
 * @name 无法自动申请权限，需要手动在设置页面打开权限
 *
 * @description
 * 1、点击按钮, 获取权限信息
 * 2、如果存在拒绝或者不再询问的权限，就引导至app设置页
 */
export default ({ route }: ScreenProps<{}>): ReactElement => {
  /** 初始化 */
  useOnInit({
    pageKey: HitPointEnumsSpace.EPageKey.P_PERMISSION_GUIDE_SETTINGS,
  });

  useEffect(() => {
    const subscribe = applicationState.instance.subject.subscribe(async state => {
      if (state === ApplicationState.active) {
        // 从非活跃状态变换成活跃状态
        // todo 这里增加权限判断
        if (await isHasAllPermission()) {
          const { toNext } = route?.params;
          nav.navigationGoBack();
          toNext && toNext();
        }
      } else {
      }
    });
    return () => {
      subscribe.unsubscribe();
    };
  }, []);

  /** 同意协议行为事件 */
  const handleNext = useCallback(async () => {
    /** 埋点 */
    TrackEvent.trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_PERMISSION_GUIDE_SETTINGS,
        e: HitPointEnumsSpace.EEventKey.BTN_GOTO_SETTINGS,
      },
      '1',
    );
    jumpAppSettingActivity();
  }, []);

  return (
    <Layout level="0" pLevel="0">
      {/* <TopNavigation
        titleKey={'permissionAgreeString.title'}
        showRightAction={false}
        isBack={false}
        bottomLine
      /> */}
      <ScrollView fadingEdgeLength={10}>
        <View
          margin="16 28 16 28"
          style={{
            // borderWidth: 1,
            borderRadius: 8,
            // borderColor: 'line-color-200',
            backgroundColor: 'background-color-0',
          }}>
          <Text category="h3" i18nKey="permissionRefuseString.title"></Text>
          <Text
            margin="12 0 0 0"
            style={{ color: 'text-color-700' }}
            i18nKey="permissionRefuseString.tip1"></Text>
          <Text margin="12 0 0 0" i18nKey="permissionRefuseString.tip2"></Text>
          <Image
            margin="16 0 0 0"
            width={Dimensions.get('window').width - 56}
            height={(Dimensions.get('window').width - 56) * 1.14}
            resizeMode="contain"
            name="_permissionToSettingTipsIcon"
          />
        </View>
      </ScrollView>
      <View
        padding="12 16 12 16"
        style={{
          backgroundColor: 'background-color-0',
          // borderTopColor: 'line-color-200',
          // borderTopWidth: 1,
        }}>
        <Button
          margin="0 16 0 16"
          style={{
            height: 48,
          }}
          onPress={handleNext}
          textI18nKey="btnString.toSetting"
        />
      </View>
    </Layout>
  );
};
