/* eslint-disable react/react-in-jsx-scope */
import { Layout, PrivacyAgreement, TopNavigation } from '@/components';
import { HitPointEnumsSpace } from '@/enums';
import { useOnInit } from '@/hooks';

export default (): React.ReactElement => {
  /** 初始化 */
  useOnInit({
    pageKey: HitPointEnumsSpace.EPageKey.P_PRAVICY,
  });

  return (
    <>
      <Layout level="0" pLevel="0">
        <TopNavigation titleKey={'privacyString.title'} />
        <PrivacyAgreement />
      </Layout>
    </>
  );
};
