/**
 * @description 欢迎页
 * 显示 5 秒，可跳过
 */
import { Image, ImageBackground, Layout, LinearGradient, Text, View, Button } from '@/components';
import { Strings } from '@/i18n';
import { RouterConfig } from '@/routes';
import { Colors } from '@/themes';
import { checkAndShowVersionNote, nav, useProcessColorStyles } from '@/utils';
import { t } from 'i18next';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { UserInfoContextType, UserInfoManager } from '@/managers';
import { trackPushNotifitionPermssion, useSubscribeFilter } from '@/hooks';
import { HitPointEnumsSpace } from '@/enums';
import { firebase } from '@react-native-firebase/analytics';
import AppCenter from 'appcenter';
import { InteractionManager } from 'react-native';

export default function () {
  const userId = useSubscribeFilter({
    subject: UserInfoManager.messageCenter,
    filter: (subject: UserInfoContextType) => {
      return subject.userModel.userId;
    },
  }) as string;
  const styles = useProcessColorStyles({
    bgStartColor: Colors.PRIMARY_COLOR_500,
  });

  const [countdown, setCountdown] = useState(3);

  const ItemData = [
    {
      iconKey: '_welcomeMessage1',
      titleKey: Strings.welcomeString.welcome_title1,
      subTitleKey: Strings.welcomeString.welcome_subtitle1,
    },
    {
      iconKey: '_welcomeMessage2',
      titleKey: Strings.welcomeString.welcome_title2,
      subTitleKey: Strings.welcomeString.welcome_subtitle2,
    },
    {
      iconKey: '_welcomeMessage3',
      titleKey: Strings.welcomeString.welcome_title3,
      subTitleKey: Strings.welcomeString.welcome_subtitle3,
    },
  ];

  const handleNext = useCallback(async () => {
    await UserInfoManager.checkMobileAndNavigateFlow({
      buryCallback: async () => {
        await trackPushNotifitionPermssion(HitPointEnumsSpace.EPageKey.P_LAUNCH);
      },
      appCenterInitCallback: () => {
        AppCenter.setUserId(userId);
        firebase.analytics().setUserId(userId);
      },
    });
    setTimeout(() => {
      checkAndShowVersionNote();
    }, 0);
  }, []);

  useEffect(() => {
    const timer = setInterval(async () => {
      if (countdown <= 0) {
        clearInterval(timer);
        handleNext();
      } else {
        setCountdown(prevCountdown => prevCountdown - 1);
      }
    }, 1000);
    return () => {
      clearInterval(timer);
    };
  }, [countdown]);
  const CountdownButton = useMemo(() => {
    return (
      <Button
        type="opacity"
        appearance="filled"
        status="primary"
        size="small"
        style={{
          backgroundColor: Colors.PRIMARY_COLOR_300,
          borderRadius: 99,
          minWidth: undefined,
          paddingLeft: 12,
          paddingRight: 12,
          paddingTop: 4,
          paddingBottom: 4,
          // top: 88,
          // right: 0,
        }}
        text={t(Strings.btnString.skipNow, { time: countdown })}
        onPress={handleNext}
      />
    );
  }, [countdown]);

  const renderItemView = ({
    iconKey,
    titleKey,
    subTitleKey,
  }: {
    iconKey: string;
    titleKey: string;
    subTitleKey: string;
  }) => {
    return (
      <View layoutStrategy="flexRowBetweenCenter" margin="32 0 0 0">
        <Image name={iconKey} margin="6 4 0 0" />
        <View margin="0 0 0 10" style={{ flex: 1 }}>
          <Text
            category="h3"
            style={{ color: Colors.TEXT_COLOR_0 }}
            bold={'bold'}
            i18nKey={titleKey}
          />
        </View>
      </View>
    );
  };

  return (
    <Layout pLevel="0" topCompensateColor="primary-color-500">
      <View style={{ flex: 1, backgroundColor: styles.bgStartColor }}>
        <ImageBackground
          style={{ alignItems: 'center', justifyContent: 'flex-end' }}
          name={'_welcomeBg'}>
          <Image name={'_welcomeMan'} margin="0 0 36 0" />
        </ImageBackground>
        <Image style={{ position: 'absolute', bottom: 0, marginTop: 16 }} name={'_welcomeBottom'} />
        <View style={{ flex: 1, justifyContent: 'flex-end' }} padding="8 24 90 24">
          {ItemData.map((item, index) => {
            return <View key={index}>{renderItemView(item)}</View>;
          })}
        </View>
        <View
          style={{ position: 'absolute', top: 18, right: 16 }}
          layoutStrategy="flexColumnCenterCenter">
          {CountdownButton}
        </View>
      </View>
    </Layout>
  );
}
