/* eslint-disable react-native/no-inline-styles */
import {
  But<PERSON>,
  Card,
  Check,
  Divider,
  Image,
  Layout,
  Text,
  TopNavigation,
  View,
} from '@/components';
import { HitPointEnumsSpace } from '@/enums';
import { useOnInit } from '@/hooks';
import { RouterConfig } from '@/routes';
import { ScreenProps } from '@/types';
import { TrackEvent, nav } from '@/utils';
import React, { ReactElement, useCallback } from 'react';
import { ScrollView, TouchableWithoutFeedback } from 'react-native';

/**
 * @name 权限允许页
 *
 * @description
 * 1、点击按钮, 获取权限信息
 * 2、如果存在拒绝或者不再询问的权限，就引导至app设置页
 */
export default ({ route }: ScreenProps<{}>): ReactElement => {
  const [readBool, setReadBool] = React.useState(true);
  /** 初始化 */
  useOnInit({
    pageKey: HitPointEnumsSpace.EPageKey.P_PERMISSION_CHECK,
  });

  React.useEffect(() => {
    setReadBool(route?.params?.agree || true);
  }, [route]);

  /** 同意协议行为事件 */
  const handleNext = useCallback(async () => {
    /** 埋点 */
    TrackEvent.trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_PERMISSION_CHECK,
        e: HitPointEnumsSpace.EEventKey.BTN_AUTHORIZE,
      },
      '1',
    );
    nav.navigate(RouterConfig.PERMISSION_PRIVACY as any);
  }, []);

  /** 点击阅读隐私协议行为事件 */
  const handleReadPrivacy = useCallback(() => {
    nav.navigate(RouterConfig.PRIVACY_SCREEN as any);
  }, []);

  const computedButtonDisabled = React.useMemo(() => {
    return !readBool;
  }, [readBool]);
  return (
    <Layout level="1" pLevel="0">
      <TopNavigation
        titleKey={'permissionAgreeString.title'}
        showRightAction={false}
        isBack={false}
        bottomLine
      />
      <ScrollView fadingEdgeLength={10} keyboardShouldPersistTaps="always">
        <View
          layoutStrategy="flexRowStartCenter"
          margin="16 16 0 16"
          padding="12 16 12 16"
          style={{
            backgroundColor: 'primary-color-500',
            borderRadius: 8,
          }}>
          <Image margin="0 12 0 0" resizeMode="contain" name="_auth" />
          <View margin="0 16 0 0">
            <Text
              style={{
                color: 'text-color-0',
              }}
              i18nKey={'permissionAgreeString.peso_per_header_detail'}
            />
          </View>
        </View>
        <View
          margin="16 16 16 16"
          padding="12 12 12 12"
          style={{
            // borderWidth: 1,
            borderRadius: 8,
            // borderColor: 'line-color-200',
            backgroundColor: 'background-color-0',
          }}>
          <Text
            margin="12 0 16 0"
            category="p2"
            style={{
              color: 'text-color-700',
            }}
            i18nKey={'permissionAgreeString.peso_per_internet_des'}
          />
          <Divider />
          <Text
            margin="12 0 16 0"
            category="p2"
            style={{
              color: 'text-color-700',
            }}
            i18nKey={'permissionAgreeString.peso_per_camera_des'}
          />
          <Divider />
          <Text
            margin="12 0 16 0"
            category="p2"
            style={{
              color: 'text-color-700',
            }}
            i18nKey={'permissionAgreeString.peso_per_account_des'}
          />
          <Divider />
          <Text
            margin="12 0 16 0"
            category="p2"
            style={{
              color: 'text-color-700',
            }}
            i18nKey={'permissionAgreeString.peso_per_sms_des'}
          />
          <Divider />
          <Text
            margin="12 0 16 0"
            category="p2"
            style={{
              color: 'text-color-700',
            }}
            i18nKey={'permissionAgreeString.peso_per_receive_sms_des'}
          />
          <Divider />
          <Text
            margin="12 0 16 0"
            category="p2"
            style={{
              color: 'text-color-700',
            }}
            i18nKey={'permissionAgreeString.peso_per_location_des'}
          />
          <Divider />
          <Text
            margin="12 0 16 0"
            category="p2"
            style={{
              color: 'text-color-700',
            }}
            i18nKey={'permissionAgreeString.peso_per_wifi_des'}
          />
          <Divider />
          <Text
            margin="12 0 16 0"
            category="p2"
            style={{
              color: 'text-color-700',
            }}
            i18nKey={'permissionAgreeString.peso_per_network_des'}
          />
          <Divider />
          <Text
            margin="12 0 16 0"
            category="p2"
            style={{
              color: 'text-color-700',
            }}
            i18nKey={'permissionAgreeString.peso_per_phone_des'}
          />
          <Divider />
          <Text
            margin="12 0 16 0"
            category="p2"
            style={{
              color: 'text-color-700',
            }}
            i18nKey={'permissionAgreeString.peso_per_install_app_des'}
          />
          <Divider />
          <Text
            margin="12 0 16 0"
            category="p2"
            style={{
              color: 'text-color-700',
            }}
            i18nKey={'permissionAgreeString.peso_per_phone_des'}
          />
          <Divider />
          <Text
            margin="12 0 16 0"
            category="p2"
            style={{
              color: 'text-color-700',
            }}
            i18nKey={'permissionAgreeString.peso_per_foreground_service_des'}
          />
          <Divider />
          <Text
            margin="12 0 16 0"
            category="p2"
            style={{
              color: 'text-color-700',
            }}
            i18nKey={'permissionAgreeString.peso_per_setup_des'}
          />
          {/* <Divider />
          <Text
            margin="12 0 16 0"
            category="p2"
            style={{
              color: 'text-color-700',
            }}
            i18nKey={'permissionAgreeString.mach_per_data_security_des'}
          /> */}
        </View>
      </ScrollView>
      <View
        padding="12 16 12 16"
        style={{
          backgroundColor: 'background-color-0',
          borderTopColor: 'line-color-200',
          borderTopWidth: 1,
        }}>
        <Button
          margin="0 16 0 16"
          style={{
            height: 48,
          }}
          disabled={computedButtonDisabled}
          onPress={handleNext}
          textI18nKey="btnString.agreePremission"
        />
        <View
          margin="12 0 0 0"
          style={{
            flexDirection: 'row',
          }}>
          <Check margin="0 8 8 0" checked={readBool} onChange={setReadBool} />
          <Text margin="0 16 0 0">
            <Text
              category="p2"
              i18nKey={'permissionAgreeString.readPrivacyNoticeTabOne'}
              style={{
                color: 'text-color-800',
              }}
            />
            <TouchableWithoutFeedback onPress={handleReadPrivacy}>
              <Text
                category="p2"
                i18nKey={'permissionAgreeString.readPrivacyNoticeTabTwo'}
                style={{
                  color: 'primary-color-500',
                  textDecorationLine: 'underline',
                }}
              />
            </TouchableWithoutFeedback>
            <Text
              category="p2"
              i18nKey={'permissionAgreeString.readPrivacyNoticeTabThree'}
              style={{
                color: 'text-color-800',
              }}
            />
          </Text>
        </View>
      </View>
    </Layout>
  );
};
