/* eslint-disable react-native/no-inline-styles */
import { Button, Layout, PrivacyAgreement, Text, TopNavigation, View, Image } from '@/components';
import { HitPointEnumsSpace } from '@/enums';
import { useOnInit, useTheme } from '@/hooks';
import { BaseInfoManager } from '@/managers';
import { ScreenProps } from '@/types';
import { TrackEvent, nav } from '@/utils';
import React, { ReactElement, useCallback } from 'react';

export default ({ route }: ScreenProps<{}>): ReactElement => {
  /** 初始化 */
  useOnInit({
    pageKey: HitPointEnumsSpace.EPageKey.P_PRAVICY,
  });

  const theme = useTheme();

  /** 接受隐私协议行为事件 */
  const onAccept = useCallback(async () => {
    /** 同意隐私协议 */
    TrackEvent.trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_PRIVACY,
        e: HitPointEnumsSpace.EEventKey.BTN_AGREE,
      },
      '1',
    );

    BaseInfoManager.updateAgreePermissions(true);

    // nav.nextToTopRouter(RouterConfig.SPLASH);
    // const {onNext} = route?.params;
    // onNext && onNext();
    nav.navigationGoBack();
  }, [route]);

  /** 取消接受隐私协议行为事件 */
  const onCancelAccept = async () => {
    /** 拒绝隐私协议 */
    TrackEvent.trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_PRIVACY,
        e: HitPointEnumsSpace.EEventKey.BTN_AGREE,
      },
      '0',
    );
    nav.navigationGoBack();
  };

  return (
    <Layout level="0" pLevel="0">
      <TopNavigation
        isBack={false}
        showRightAction={false}
        bottomLine
        titleKey={'permissionAgreeString.subTitle'}
      />
      <PrivacyAgreement />
      <View
        padding="12 16 12 16"
        style={{ backgroundColor: 'background-color-100' }}
        layoutStrategy="flexRowCenterCenter">
        <Image margin="2 8 0 0" name="_vector" tintColor={theme['text-color-700']} />
        <Text
          category="c1"
          style={{
            color: 'text-color-700',
          }}
          i18nKey="permissionAgreeString.agreePrivayTip"
        />
      </View>
      <View
        padding="16 0 16 0"
        layoutStrategy="flexRowBetweenCenter"
        style={{
          backgroundColor: 'background-color-0',
          // borderTopColor: 'line-color-200',
          // borderTopWidth: 1,
        }}>
        {/* <Button
          margin="0 16 0 16"
          style={{flex: 1, height: 44}}
          appearance="outline"
          onPress={() => {
            onCancelAccept();
          }}
          textI18nKey="btnString.noAccept"
        /> */}
        <Button
          margin="0 36 0 36"
          appearance="outline"
          style={{ flex: 1, height: 44 }}
          onPress={() => {
            onAccept();
          }}
          textI18nKey="btnString.know"
        />
      </View>
    </Layout>
  );
};
