import React from 'react';
import { View, Text, Layout, Button, ImageBackground, Image } from '@/components';
import { TouchableOpacity, StatusBar, Image as RNImage } from 'react-native';
import { ScreenProps } from '@/types';
import useData from './useData';
import Video from 'react-native-video';
import { Colors } from '@/themes';
import { Strings } from '@/i18n';
import { t } from 'i18next';
import { LaunchScreenSourceType } from '@/types';
import { ImageNames } from '@/config';

export default ({ navigation }: ScreenProps<{}>) => {
  const {
    filePath,
    handleNext,
    countdown,
    sourceType,
    showPoster,
    navigationToLink,
    link,
    onLoad,
  } = useData();
  const isVideo = sourceType === LaunchScreenSourceType.VIDEO;
  const renderSplashContent = () => {
    if (!filePath) {
      return null;
    }
    return (
      <TouchableOpacity
        activeOpacity={1}
        onPress={navigationToLink}
        disabled={!link}
        style={{ flex: 1 }}>
        {isVideo ? (
          <Video
            source={{ uri: filePath }}
            style={{ flex: 1 }}
            muted
            repeat
            resizeMode="cover"
            onLoad={onLoad}
          />
        ) : (
          <RNImage
            source={{ uri: filePath }}
            style={{
              flex: 1,
            }}
            resizeMode="cover"
          />
        )}
        <Image
          name={ImageNames._launchScreenBg}
          style={{
            display: isVideo && showPoster ? 'flex' : 'none',
            position: 'absolute',
            left: 0,
            right: 0,
            top: 0,
            bottom: 0,
            zIndex: 1,
          }}
          resizeMode="cover"
        />
      </TouchableOpacity>
    );
  };
  const renderCountdownButton = () => {
    return (
      <View
        style={{
          display: countdown < 0 ? 'none' : 'flex',
          position: 'absolute',
          top: (StatusBar.currentHeight || 0) + 18,
          right: 16,
        }}
        layoutStrategy="flexColumnCenterCenter">
        <Button
          type="opacity"
          appearance="filled"
          status="primary"
          size="small"
          style={{
            backgroundColor: Colors.PRIMARY_COLOR_500,
            borderRadius: 99,
            paddingLeft: 12,
            paddingRight: 12,
            paddingTop: 4,
            paddingBottom: 4,
          }}
          text={t(Strings.btnString.skipNow, { time: countdown })}
          onPress={handleNext}
        />
      </View>
    );
  };
  const renderDefaultView = () => {
    return (
      <Image
        name={ImageNames._launchScreenBg}
        resizeMode="cover"
        style={{
          display: filePath ? 'none' : 'flex',
          position: 'absolute',
          left: 0,
          right: 0,
          top: 0,
          bottom: 0,
          zIndex: -1,
        }}
      />
    );
  };
  return (
    <Layout pLevel="0" topCompensateOpen={false}>
      <View style={{ flex: 1, backgroundColor: Colors.BACKGROUND_COLOR_0 }}>
        {renderSplashContent()}
        {renderCountdownButton()}
        {renderDefaultView()}
      </View>
    </Layout>
  );
};
