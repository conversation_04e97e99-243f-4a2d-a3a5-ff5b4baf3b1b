import { useEffect, useRef, useState } from 'react';
import { useSetState } from 'ahooks';
import { SPLASH_ACTIVITY_LOCAL_FILE_CACHE_PATH } from '@/config';
import { FileUtils, nav } from '@/utils';
import { UserInfoManager, UserInfoContextType } from '@/managers';
import { trackPushNotifitionPermssion, useSubscribeFilter, useOnInit } from '@/hooks';
import { HitPointEnumsSpace } from '@/enums';
import { EvidenceVOSpace, LaunchScreenSourceType } from '@/types';
import AppCenter from 'appcenter';
import { firebase } from '@react-native-firebase/analytics';
import { Linking } from 'react-native';
import { RouterConfig } from '@/routes';
import BackgroundTimer from 'react-native-background-timer';
import { ActivityResponseSpace, getLaunchScreen, responseHandler } from '@/server';
import _ from 'lodash';

interface State {
  filePath: string;
  link: string;
  sourceType: LaunchScreenSourceType;
  showPoster: boolean;
}
const useData = () => {
  const [countdown, setCountdown] = useState<number>(5);
  const [state, setState] = useSetState<State>({
    filePath: '',
    link: '',
    sourceType: LaunchScreenSourceType.IMAGE,
    showPoster: true,
  });
  const timerRef = useRef<number>(0);
  const isLoading = useRef<boolean>(false);
  const { link, sourceType } = state;
  const userId = useSubscribeFilter({
    subject: UserInfoManager.messageCenter,
    filter: (subject: UserInfoContextType) => {
      return subject.userModel.userId;
    },
  }) as string;

  useEffect(() => {
    loadSplashConfig();
    beginCountdown();
  }, []);
  useEffect(() => {
    return () => {
      isLoading.current = false;
    };
  }, [countdown, sourceType]);
  const beginCountdown = () => {
    if (countdown > 0) {
      timerRef.current = BackgroundTimer.setInterval(async () => {
        setCountdown(pre => {
          if (pre === 0) {
            handleNext();
          }
          return pre > 0 ? pre - 1 : 0;
        });
      }, 1000);
    }
  };
  const loadSplashConfig = async () => {
    try {
      const { error, data } = await responseHandler(getLaunchScreen());
      if (error) {
        return handleNext();
      }
      cacheSplashConfig(data);
    } catch (error) {
      console.log('loadSplashConfig error', error);
    }
  };
  const cacheSplashConfig = (data: ActivityResponseSpace.LaunchScreenConfig) => {
    const { sourceId, sourceUrl, duration, active, link, sourceType } = data;
    if (active === 'NO') {
      return handleNext();
    }

    _.isNumber(duration) && setCountdown(duration);

    setState({
      link,
      sourceType,
    });
    FileUtils.downloadFile({
      fileUrl: sourceUrl,
      fileName: sourceId,
      catalogue: SPLASH_ACTIVITY_LOCAL_FILE_CACHE_PATH,
      callback: localFilePath => {
        if (localFilePath) {
          return setState({ filePath: localFilePath });
        }
        deleteSplashConfig();
      },
    });
  };
  const deleteSplashConfig = () => {
    FileUtils.deleteFile(SPLASH_ACTIVITY_LOCAL_FILE_CACHE_PATH);
  };
  const handleNext = async () => {
    if (isLoading.current) return;
    isLoading.current = true;
    BackgroundTimer.clearInterval(timerRef.current);
    await UserInfoManager.checkMobileAndNavigateFlow({
      buryCallback: async () => {
        await trackPushNotifitionPermssion(HitPointEnumsSpace.EPageKey.P_LAUNCH);
      },
      appCenterInitCallback: () => {
        isLoading.current = false;
        AppCenter.setUserId(userId);
        firebase.analytics().setUserId(userId);
      },
    });
  };
  const navigationToLink = async () => {
    BackgroundTimer.clearInterval(timerRef.current);
    if (await Linking.canOpenURL(link)) {
      nav.navigate(RouterConfig.INTERNAL_H5 as any, {
        link,
        pageKey: HitPointEnumsSpace.EPageKey.P_INTERNAL_H5,
        onBack: handleNext,
      });
    }
  };
  const onLoad = () => {
    setState({ showPoster: false });
  };
  return {
    ...state,
    countdown,
    handleNext,
    navigationToLink,
    onLoad,
  };
};
export default useData;
