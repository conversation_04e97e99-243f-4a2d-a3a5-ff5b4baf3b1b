import { HitPointEnumsSpace } from '@/enums';
import {
  trackPushNotifitionPermssion,
  // useCodePushUpdate,
  useDeviceDataReport,
  useOnInit,
  useSubscribeFilter,
} from '@/hooks';

import {
  BaseInfoManager,
  CodePushManager,
  UserInfoContextType,
  UserInfoManager,
  EUpdateAppConfigSource,
  AppDefaultConfigManager,
} from '@/managers';
import {
  hasDeepLinkAction,
  deepLinkAction,
  isHasLaunchPermission,
  Log,
  onGetAppVersionAndUpdate,
  TrackEvent,
  nav,
} from '@/utils';
import AppCenter from 'appcenter';
import { useCallback, useEffect } from 'react';
import SplashScreen from 'react-native-splash-screen';
import { NativeModules } from '@/native';
import { ELaunchMethod } from '../../../native/module/intentData';
import { RouterConfig } from '@/routes';
const { intentData } = NativeModules;

export default function useData() {
  const { onLaunchReportDeviceData } = useDeviceDataReport();

  // const {CodePushUpdateCheck} = useCodePushUpdate();
  const mobile = useSubscribeFilter({
    subject: UserInfoManager.messageCenter,
    filter: (subject: UserInfoContextType) => {
      return subject.userModel.mobile;
    },
  }) as string;

  useEffect(() => {
    setTimeout(() => {
      closeSplashScreen();
    }, 500);
    TrackEvent.trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_APP,
        e: HitPointEnumsSpace.EEventKey.E_APP_START,
      },
      '1',
    );
  }, []);

  /** 初始化方法 */
  const { loading } = useOnInit({
    callback: async () => {
      loading.current = true;

      if (await isHasLaunchPermission()) {
        onLaunchReportDeviceData();
      }

      if (UserInfoManager.context.userModel.isLogined) {
        AppDefaultConfigManager.updateAppDefaultConfig();
      }
      await Promise.all([
        BaseInfoManager.updateContactConfig(),
        UserInfoManager.updateUserState(EUpdateAppConfigSource.APP_CONFIG),
      ]);

      // 打开app的方式埋点
      const { launchMethod, data } = await intentData.getLaunchMethod();
      switch (launchMethod) {
        case ELaunchMethod.ICON_LAUNCH:
          /** 通过点击应用图标打开 */
          TrackEvent.trackCommonEvent(
            {
              p: HitPointEnumsSpace.EPageKey.P_LAUNCH,
              e: HitPointEnumsSpace.EEventKey.ICON_LAUNCH,
            },
            '1',
          );
          break;
        case ELaunchMethod.DEEOLINK_LANUNCH:
          TrackEvent.trackCommonEvent(
            {
              p: HitPointEnumsSpace.EPageKey.P_LAUNCH,
              e: HitPointEnumsSpace.EEventKey.DEEPLINK_LAUNCH,
            },
            data || '',
          );
          break;
        case ELaunchMethod.PUSH_LAUNCH:
          TrackEvent.trackCommonEvent(
            {
              p: HitPointEnumsSpace.EPageKey.P_LAUNCH,
              e: HitPointEnumsSpace.EEventKey.PUSH_LAUNCH,
            },
            data || '',
          );
          break;
      }

      let deeplinkCbResult = await deepLinkAction(data);
      hasDeepLinkAction(data) && (await intentData.clearIntentData());

      if (deeplinkCbResult) {
        return;
      }
      console.log('-----deeplinkCbResult------');
      SplashScreen.hide();
      if (!(await onGetAppVersionAndUpdate())) {
        return;
      }
      // 跳转欢迎页，在欢迎页执行跳转逻辑
      nav.resetRouteNavigate(RouterConfig.SPLASH_ACTIVITY as any);
    },
    async refreshCallback() {
      loading.current = true;

      if (await isHasLaunchPermission()) {
        await onLaunchReportDeviceData();
      }

      await UserInfoManager.updateUserState(EUpdateAppConfigSource.APP_CONFIG);
      nav.resetRouteNavigate(RouterConfig.SPLASH_ACTIVITY as any);

      // if (!(await onGetAppVersionAndUpdate(hotUpdateChcekHandle))) {
      //   return;
      // }
    },
    pageKey: HitPointEnumsSpace.EPageKey.P_LAUNCH,
    isBackAutoRefresh: true,
  });

  const onAppUpdateNextCallback = async () => {
    loading.current = false;

    CodePushManager.closeModal();

    const { isSplashH5Switch } = BaseInfoManager.context.baseModel;

    /** 没有开启下架机制开关走正常流程 */
    if (!isSplashH5Switch) {
      await UserInfoManager.checkMobileAndNavigateFlow({
        buryCallback: async () => {
          await trackPushNotifitionPermssion(HitPointEnumsSpace.EPageKey.P_LAUNCH);
        },
        appCenterInitCallback: () => {
          AppCenter.setUserId(mobile);
        },
      });
    }
  };

  const hotUpdateChcekHandle = async () => {
    // 是否为强制更新
    // const isForce = (() => {
    //   if (
    //     !UserInfoManager.context.userModel.isLogined &&
    //     BaseInfoManager.context.baseModel.isNewUserHotUpdateForceSwitch
    //   ) {
    //     return true;
    //   }
    //   if (
    //     UserInfoManager.context.userModel.isUserTypeNew &&
    //     BaseInfoManager.context.baseModel.isNewUserHotUpdateForceSwitch
    //   ) {
    //     return true;
    //   }
    //   if (
    //     UserInfoManager.context.userModel.isUserTypeOld &&
    //     BaseInfoManager.context.baseModel.isOldUserHotUpdateForceSwitch
    //   ) {
    //     return true;
    //   }
    //   return false;
    // })();
    onAppUpdateNextCallback();
    // await CodePushUpdateCheck(isForce, onAppUpdateNextCallback);
  };

  /** 关闭启动页 */
  const closeSplashScreen = useCallback(() => {
    SplashScreen.hide();
  }, []);
}
