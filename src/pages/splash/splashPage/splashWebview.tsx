import { BaseInfoManager, UserInfoContextType, UserInfoManager } from '@/managers';
import { ReactElement, useCallback, useEffect } from 'react';
import { Button, View } from '@/components';
import WebView from 'react-native-webview';
import { trackPushNotifitionPermssion, useSubscribeFilter } from '@/hooks';
import { HitPointEnumsSpace } from '@/enums';
import AppCenter from 'appcenter';
import { Log, nav } from '@/utils';
import { trackCommonEvent, uploadEventLog } from '@/trackEvent';
import { firebase } from '@react-native-firebase/analytics';
import _ from 'lodash';
import { RouterConfig } from '@/routes';

interface IProps {
  link: string;
}
export default (props: IProps): ReactElement => {
  const { link = '' } = props;
  const userId = useSubscribeFilter({
    subject: UserInfoManager.messageCenter,
    filter: (subject: UserInfoContextType) => {
      return subject.userModel.userId;
    },
  }) as string;

  useEffect(() => {
    onLoadStart();
  }, []);

  const onLoadStart = useCallback(() => {
    BaseInfoManager.changeLoadingModalVisible(true);
  }, []);

  const onLoadEnd = useCallback(() => {
    BaseInfoManager.changeLoadingModalVisible(false);
  }, []);

  const handleShouldStartLoadWithRequest = (request: { url: any }) => {
    return true;
  };

  const handleNext = useCallback(async () => {
    nav.resetRouteNavigate(RouterConfig.SPLASH_ACTIVITY as any);
    // await UserInfoManager.checkMobileAndNavigateFlow({
    //   buryCallback: async () => {
    //     await trackPushNotifitionPermssion(
    //       HitPointEnumsSpace.EPageKey.P_LAUNCH,
    //     );
    //   },
    //   appCenterInitCallback: () => {
    //     AppCenter.setUserId(userId);
    //     firebase.analytics().setUserId(userId);
    //   },
    // });
  }, []);

  const handleNavigationStateChange = ({ url }: { url: string }) => {
    onMessage(url);
  };

  const onMessage = _.debounce((url: string) => {
    try {
      // 事件名称, 参数字符串
      let [event, params] = ['', ''];
      if (url.includes('?')) {
        [event, params] = url.split('?')[1].split('&')[0].split('=');
      }

      switch (event) {
        case 'BURY_POINT':
          let [ePage, eEvent, eContent] = decodeURIComponent(params).split('&');
          trackCommonEvent(
            {
              p: ePage as HitPointEnumsSpace.EPageKey,
              e: eEvent as HitPointEnumsSpace.EEventKey,
            },
            eContent,
          );
          uploadEventLog();
          break;
        default:
      }
    } catch (error) {
      Log.error('splash webview onMessage >', error);
    }
  }, 300);

  return (
    <>
      <WebView
        style={{ flex: 1 }}
        cacheMode="LOAD_NO_CACHE"
        onLoadEnd={onLoadEnd}
        originWhitelist={['*']}
        javaScriptEnabledAndroid={true}
        source={{ uri: link }}
        onNavigationStateChange={handleNavigationStateChange}
        onShouldStartLoadWithRequest={handleShouldStartLoadWithRequest}
      />
      <View
        padding="32 32 32 32"
        style={{
          backgroundColor: 'background-color-0',
          borderRadius: 10,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 20 },
          shadowOpacity: 0.5,
          shadowRadius: 10,
          elevation: 10,
        }}>
        <Button
          margin="0 32 0 32"
          padding="16 0 16 0"
          appearance="outline"
          onPress={handleNext}
          textI18nKey="btnString.next"
        />
      </View>
    </>
  );
};
