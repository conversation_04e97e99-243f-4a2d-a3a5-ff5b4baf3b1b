import { Layout } from '@/components';
import { useSubscribeFilter } from '@/hooks';
import { BaseInfoContextType, BaseInfoManager } from '@/managers';
import { ScreenProps } from '@/types';
import React, { ReactElement } from 'react';
import { requireNativeComponent } from 'react-native';
import SplashWebview from './splashWebview';
import useData from './useData';

const SplashView = requireNativeComponent<any>('SplashView');

export enum ESplashPageType {
  CLICK_LOGO = 'CLICK_LOGO',
  CLICK_TEXT = 'CLICK_TEXT',
}

export default (): ReactElement => {
  const { isSplashH5Switch, splashH5Link } = useSubscribeFilter({
    subject: BaseInfoManager.messageCenter,
    filter: (subject: BaseInfoContextType) => {
      return {
        isSplashH5Switch: subject.baseModel.isSplashH5Switch,
        splashH5Link: subject.baseModel.splashH5Link,
      };
    },
  }) as {
    isSplashH5Switch: false;
    splashH5Link: '';
  };

  useData();

  return (
    <>
      {!isSplashH5Switch && (
        <Layout level="0" pLevel="0" topCompensateOpen={false}>
          <SplashView style={{ flex: 1 }} />
        </Layout>
      )}
      {isSplashH5Switch && (
        <Layout level="0" pLevel="0" topCompensateOpen={false}>
          <SplashWebview link={splashH5Link} />
        </Layout>
      )}
    </>
  );
};
