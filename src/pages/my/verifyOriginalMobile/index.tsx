import {
  Button,
  Image,
  Layout,
  SeaInput,
  Text,
  TopNavigation,
  View,
  BaseInputRefType,
} from '@/components';
import { HitPointEnumsSpace } from '@/enums';
import { ScreenProps } from '@/types';
import { isStringExist, verifyPhoneNumber, nav, TrackEvent } from '@/utils';
import React, { ReactElement, useMemo, useRef, useCallback } from 'react';
import { ScrollView } from 'react-native';
import { Strings } from '@/i18n';
import { t } from 'i18next';
import { useSetState } from 'ahooks';
import { BaseInfoManager, UserInfoManager } from '@/managers';
import { validateUserExist, responseHandler } from '@/server';
import { RouterConfig } from '@/routes';
import { useOnInit } from '@/hooks';

interface State {
  phoneNumber: string;
}
const useData = () => {
  const [state, setState] = useSetState<State>({
    phoneNumber: '',
  });
  const { phoneNumber } = state;
  const phoneNumberRef = useRef<BaseInputRefType>(null);
  useOnInit({
    pageKey: HitPointEnumsSpace.EPageKey.P_CHANGE_MOBILE_INPUT_OLD,
  });

  const onChangePhoneNumber = useCallback((phoneNumber: string) => {
    setState({ phoneNumber });
  }, []);
  const disabled = useMemo(() => {
    return !verifyPhoneNumber(phoneNumber);
  }, [phoneNumber]);
  const handleNext = useCallback(async () => {
    TrackEvent.trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_CHANGE_MOBILE_INPUT_OLD,
        e: HitPointEnumsSpace.EEventKey.BTN_NEXT,
      },
      '1',
    );
    BaseInfoManager.changeLoadingModalVisible(true);
    const { error, data } = await responseHandler(
      validateUserExist({ originalMobile: phoneNumber }),
    );
    BaseInfoManager.changeLoadingModalVisible(false);
    if (error) {
      return;
    }
    UserInfoManager.updateUserId(data.currentUserId);
    nav.navigate(RouterConfig.VERIFY_BASIC_INFO as any, data);
  }, [phoneNumber]);
  return {
    phoneNumber,
    phoneNumberRef,
    disabled,
    onChangePhoneNumber,
    handleNext,
  };
};
export default ({ navigation }: ScreenProps<{}>): ReactElement => {
  const { phoneNumber, phoneNumberRef, disabled, onChangePhoneNumber, handleNext } = useData();

  const renderTop = () => {
    return (
      <>
        <View
          padding="12 12 12 12"
          style={{ backgroundColor: 'primary-color-100', borderRadius: 8 }}
          layoutStrategy="flexRowStartCenter">
          <Image name="_safeTipIcon" />
          <Text padding="0 12 0 8" i18nKey={Strings.validateBasicInfoString.tip} category="c1" />
        </View>
        <Text
          i18nKey={Strings.validateBasicInfoString.formTitle}
          padding="12 12 12 12"
          style={{ color: 'text-color-600' }}
          category="p2"
        />
      </>
    );
  };
  const renderForm = useMemo(() => {
    return (
      <View
        padding="16 16 16 16"
        cardType="baseType"
        style={{
          flex: 1,
        }}>
        <SeaInput
          type="line"
          keyboardType="number-pad"
          ref={phoneNumberRef}
          prefixMargin="12 0 0 0"
          prefixKey={Strings.validateBasicInfoString.oldPhoneNumber}
          placeholderKey={Strings.validateBasicInfoString.oldPhoneNumber}
          value={phoneNumber}
          pageKey={HitPointEnumsSpace.EPageKey.P_CHANGE_MOBILE_INPUT_OLD}
          eventKey={HitPointEnumsSpace.EEventKey.E_PHONE_NUMBER}
          setValue={onChangePhoneNumber}
          validateConfig={[
            {
              condition: isStringExist(phoneNumber),
              info: t('verificationString.required'),
              status: 'danger',
            },
            {
              condition: verifyPhoneNumber(phoneNumber),
              info: t(Strings.verificationString.error),
              status: 'danger',
            },
          ]}
          isShowPlaceholderKey
        />
      </View>
    );
  }, [phoneNumber]);
  const renderButton = () => {
    return (
      <View padding="12 32 16 32" style={{ backgroundColor: 'fill-color-0' }}>
        <Button textI18nKey={Strings.btnString.continue} disabled={disabled} onPress={handleNext} />
      </View>
    );
  };
  return (
    <Layout pLevel="0" level="1">
      <TopNavigation titleKey={Strings.validateBasicInfoString.title} bottomLine />
      <ScrollView
        style={{ paddingHorizontal: 16, paddingTop: 22 }}
        keyboardShouldPersistTaps={'always'}>
        {renderTop()}
        {renderForm}
      </ScrollView>
      {renderButton()}
    </Layout>
  );
};
