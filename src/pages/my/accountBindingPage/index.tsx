import { Button, Image, Layout, Text, TopNavigation, View } from '@/components';
import { HitPointEnumsSpace, UserEnumsSpace } from '@/enums';
import { useOnInit } from '@/hooks';
import { BaseInfoManager } from '@/managers';
import { getAccountBindingState3rd, submitAccountBinding } from '@/server';
import { log, TrackEvent } from '@/utils';
import { GoogleSignin } from '@react-native-google-signin/google-signin';
import { memo, ReactElement, useMemo, useState, useTransition } from 'react';
import { ScrollView } from 'react-native';
import { BaseConfig } from '@/baseConfig';

GoogleSignin.configure({
  webClientId: BaseConfig.googleWebClientId,
});

interface IState {
  facebook: boolean;
  google: boolean;
}

const defaultState: IState = {
  facebook: true,
  google: true,
};
const useData = () => {
  const [isPending, startTransition] = useTransition();
  const [state, setState] = useState<IState>(defaultState);

  /** 初始化 */
  useOnInit({
    callback: async () => {
      // 获取第三方账户的绑定状态
      await onGetAccountBindingState();
    },
    isActivityAutoRefresh: true,
    pageKey: HitPointEnumsSpace.EPageKey.P_BING,
  });

  /** 获取第三方账户的绑定状态 */
  const onGetAccountBindingState = async () => {
    BaseInfoManager.changeLoadingModalVisible(true);
    let result = await getAccountBindingState3rd();

    if (result.code === 0) {
      const { FACEBOOK, GOOGLE } = result.data;
      startTransition(() => {
        setState({
          facebook: FACEBOOK === UserEnumsSpace.EStatusType.YES,
          google: GOOGLE === UserEnumsSpace.EStatusType.YES,
        });
      });
    }
    BaseInfoManager.changeLoadingModalVisible(false);
  };

  const handGoogleBindng = async () => {
    const { google } = state;
    TrackEvent.trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_BING,
        e: HitPointEnumsSpace.EEventKey.BTN_BINDING_GOOGLE,
      },
      '1',
    );
    if (!google) {
      try {
        BaseInfoManager.changeLoadingModalVisible(true);
        // Check if your device supports Google Play
        const isHasPlayServices = await GoogleSignin.hasPlayServices({
          showPlayServicesUpdateDialog: true,
        });
        if (isHasPlayServices) {
          if (await GoogleSignin.isSignedIn()) {
            await GoogleSignin.signOut();
          }
          const idToken = (await GoogleSignin.signIn()).idToken || '';
          if (idToken) {
            const result = await submitAccountBinding({
              oauthType: 'GOOGLE',
              accessToken: idToken,
            });
            if (result.code === 0) {
              startTransition(() => {
                setState(preState => ({
                  ...preState,
                  google: true,
                }));
              });
            }
          }
        }
      } catch (error) {
        log.error('google sign error', {
          error,
        });
      }
      BaseInfoManager.changeLoadingModalVisible(false);
    }
  };

  // const handleFacebookBinding = async () => {
  // TrackEvent.trackCommonEvent(
  //   {
  //     p: HitPointEnumsSpace.EPageKey.P_BING,
  //     e: HitPointEnumsSpace.EEventKey.BTN_BING_FACEBOOK,
  //   },
  //   '1',
  // );

  //   const {facebook} = state;
  //   if (!facebook) {
  //     BaseInfoManager.changeLoadingModalVisible(true);

  //     // Attempt login with permissions
  //     const result = await LoginManager.logInWithPermissions([
  //       'public_profile',
  //       // 'email',
  //     ]);

  //     if (result.isCancelled) {
  //       throw 'User cancelled the login process';
  //     }

  //     // Once signed in, get the users AccessToken
  //     const data = await AccessToken.getCurrentAccessToken();

  //     if (!data) {
  //       throw 'Something went wrong obtaining access token';
  //     }

  //     console.log('#AcceccToken =', data.accessToken);

  //     BaseInfoManager.changeLoadingModalVisible(false);
  //   }
  // };

  return {
    isPending,
    ...state,
    handGoogleBindng,
    // handleFacebookBinding,
  };
};

const AccountBindingPage = (): ReactElement => {
  const {
    facebook,
    google,
    handGoogleBindng,
    // handleFacebookBinding
  } = useData();

  // facebook 绑定卡片
  // const $facebokBindingCard = useMemo(() => {
  //   return (
  //     <View
  //       padding="12 16 12 16"
  //       margin="16 0 0 0"
  //       style={{backgroundColor: 'background-color-0', borderRadius: 8}}
  //       layoutStrategy="flexRowBetweenCenter">
  //       <View layoutStrategy="flexRowBetweenCenter">
  //         <Image margin="0 6 0 0" name="_facebook" />
  //         <View>
  //           <Text
  //             i18nKey="accountBindingString.facebook"
  //             category="p1"
  //             style={{
  //               flex: 1,
  //             }}
  //           />
  //           <Text
  //             i18nKey="accountBindingString.facebookDes"
  //             category="c1"
  //             style={{
  //               flex: 1,
  //               color: 'text-color-600',
  //             }}
  //           />
  //         </View>
  //       </View>
  //       <Button
  //         disabled={facebook}
  //         onPress={handleFacebookBinding}
  //         textI18nKey={
  //           facebook ? 'btnString.linked' : 'btnString.link'
  //         }></Button>
  //     </View>
  //   );
  // }, [facebook]);

  // gmail 绑定卡片
  const $gmailBindingCard = useMemo(() => {
    return (
      <View
        padding="12 16 12 16"
        margin="16 0 0 0"
        cardType="baseType"
        layoutStrategy="flexRowBetweenCenter">
        <View>
          <Image margin="0 0 8 0" name="_google" />
          <Text
            i18nKey="accountBindingString.gmailDes"
            category="c1"
            style={{
              flex: 1,
              color: 'text-color-600',
            }}
          />
        </View>
        <Button
          onPress={handGoogleBindng}
          style={{ borderRadius: 99 }}
          disabled={google}
          disabledTipTextI18nKey="accountBindingString.accountBinded"
          textI18nKey={google ? 'btnString.linked' : 'btnString.link'}></Button>
      </View>
    );
  }, [google]);

  /** 绑定提额的引导文案 */
  const $topCard = useMemo(() => {
    return (
      <View
        padding="8 16 8 16"
        style={{ backgroundColor: 'primary-color-200', borderRadius: 8 }}
        layoutStrategy="flexRowBetweenCenter">
        <Text
          i18nKey="accountBindingString.topTip"
          category="p1"
          style={{
            flex: 1,
          }}
        />
      </View>
    );
  }, []);

  return (
    <>
      <Layout pLevel="0" level="1">
        <TopNavigation titleKey="accountBindingString.title" />
        <ScrollView fadingEdgeLength={10} style={{ marginHorizontal: 16, marginVertical: 16 }}>
          {$topCard}
          {$gmailBindingCard}
        </ScrollView>
      </Layout>
    </>
  );
};

export default memo(AccountBindingPage);
