import { Layout, TopNavigation } from '@/components';
import { HitPointEnumsSpace } from '@/enums';
import { useOnInit } from '@/hooks';
import { BaseInfoManager } from '@/managers';
import { getLoanRecoedList } from '@/server';
import { OrderVOSpace } from '@/types';
import { memo, ReactElement, useState } from 'react';
import LoanRecordListPage from './components/recordPage';
import React from 'react';

const useData = () => {
  // 活动消息
  const [recordList, setRecordList] = useState<OrderVOSpace.LoanRecordItem[]>([]);

  /** 初始化 */
  useOnInit({
    callback: async () => {
      BaseInfoManager.changeLoadingModalVisible(true);
      // 获取用户消息列表
      await getLoanRecordList();
      BaseInfoManager.changeLoadingModalVisible(false);
    },
    pageKey: HitPointEnumsSpace.EPageKey.P_LOAN_RECORD,
  });

  /** 获取用户的通知消息列表 */
  const getLoanRecordList = async () => {
    let result = await getLoanRecoedList();
    if (result.code === 0) {
      if (Array.isArray(result.data)) {
        setRecordList(result.data);
      }
    }
  };

  return {
    recordList,
  };
};
const MessagePage = (): ReactElement => {
  const { recordList } = useData();

  return (
    <>
      <Layout pLevel="0" level="1">
        <TopNavigation titleKey="loanRecordString.title" />
        <LoanRecordListPage dataList={recordList} />
      </Layout>
    </>
  );
};

export default memo(MessagePage);
