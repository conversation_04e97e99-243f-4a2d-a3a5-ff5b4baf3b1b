import { Divider, Text, View } from '@/components';
import { RouterConfig } from '@/routes';
import { OrderVOSpace } from '@/types';
import { nav } from '@/utils';
import React, { useMemo } from 'react';
import { FlatList, TouchableOpacity } from 'react-native';
type MessagePagePropsType = {
  dataList: OrderVOSpace.LoanRecordItem[];
};

type MessageItemPropsType = {
  data: OrderVOSpace.LoanRecordItem;
};

const MessageItem = React.memo((props: MessageItemPropsType) => {
  let $loanState = useMemo(() => {
    switch (props.data.status) {
      case '0':
        return (
          <Text
            padding="4 8 4 8"
            status="control"
            category="p2"
            style={{
              borderRadius: 99,
              backgroundColor: 'fill-color-500',
            }}
            i18nKey="multiPeriodString.waiting"
          />
        );
      case '1':
        return (
          <Text
            padding="4 8 4 8"
            status="control"
            category="p2"
            style={{
              borderRadius: 99,
              backgroundColor: 'primary-color-500',
            }}
            i18nKey="multiPeriodString.process"
          />
        );
      case '2':
        return (
          <Text
            padding="4 8 4 8"
            status="control"
            category="p2"
            style={{
              borderRadius: 99,
              backgroundColor: 'danger-color-500',
            }}
            i18nKey="multiPeriodString.failure"
          />
        );
      case '3':
        return (
          <Text
            padding="4 8 4 8"
            status="control"
            category="p2"
            style={{
              borderRadius: 99,
              backgroundColor: 'fill-color-500',
            }}
            i18nKey="multiPeriodString.success"
          />
        );
    }
  }, [props.data.status]);

  const junmpRepayPage = () => {
    if (props.data.productPeriod !== 1) {
      nav.resetRouteNavigate(RouterConfig.MULTI_PERIOD_REPAYMENT as any);
    } else {
      nav.resetRouteNavigate(RouterConfig.REPAYMENT as any);
    }
  };

  const $repayNowLabel = useMemo(() => {
    if (props.data.status !== '3' && props.data.status !== '0') {
      return (
        <>
          <View padding="0 16 0 16">
            <Divider />
          </View>
          <View padding="12 16 12 16" layoutStrategy="flexRowBetweenCenter">
            <View />
            <TouchableOpacity onPress={junmpRepayPage}>
              <Text status="primary" i18nKey="loanRecordString.repayNow" />
            </TouchableOpacity>
          </View>
        </>
      );
    }
    return null;
  }, [props]);

  const $loanDate = useMemo(() => {
    return (
      <View padding="6 16 6 16" layoutStrategy="flexRowBetweenCenter">
        <Text
          category="p2"
          style={{ color: 'text-color-600' }}
          i18nKey="loanRecordString.loanDate"
        />
        <Text textContent={props.data.loanTime} />
      </View>
    );
  }, [props]);

  const $loanPeriod = useMemo(() => {
    return (
      <View padding="6 16 6 16" layoutStrategy="flexRowBetweenCenter">
        <Text
          category="p2"
          style={{ color: 'text-color-600' }}
          i18nKey="loanRecordString.loanPeriod"
        />
        <Text textContent={String(props.data.productPeriod)} />
      </View>
    );
  }, [props]);

  const $repayDate = useMemo(() => {
    if (props.data.status !== '3') {
      return (
        <View padding="6 16 12 16" layoutStrategy="flexRowBetweenCenter">
          <Text
            category="p2"
            style={{ color: 'text-color-600' }}
            i18nKey="loanRecordString.repayDate"
          />
          <Text textContent={props.data.repayDate} />
        </View>
      );
    } else {
      return null;
    }
  }, [props]);

  return (
    <View padding="12 0 0 0" margin="0 16 24 16" cardType="baseType">
      <View padding="0 16 12 16" layoutStrategy="flexRowBetweenCenter">
        <Text bold="700" category="p2" i18nKey="loanRecordString.loanStatus" />
        {$loanState}
      </View>
      <Divider />
      <View padding="12 16 6 16" layoutStrategy="flexRowBetweenCenter">
        <Text
          category="p2"
          style={{ color: 'text-color-600' }}
          i18nKey="loanRecordString.loanAmount"
        />
        <Text textContent={String(props.data.realAmount).toFormatFinance()} />
      </View>
      {$loanDate}
      {$loanPeriod}
      {$repayDate}
      {$repayNowLabel}
    </View>
  );
});

const LoanRecordListPage = (props: MessagePagePropsType) => {
  const $renderFooter = useMemo(() => {
    if (props.dataList.length === 0) {
      return (
        <View layoutStrategy="flexColumnCenterCenter">
          <Text
            width={'80%'}
            isCenter
            category="p2"
            style={{ color: 'text-color-600' }}
            i18nKey="loanRecordString.noData"
          />
        </View>
      );
    } else {
      return (
        <View layoutStrategy="flexColumnCenterCenter">
          <Text
            width={'80%'}
            isCenter
            category="p2"
            style={{ color: 'text-color-600' }}
            i18nKey="loanRecordString.footerTip"
          />
        </View>
      );
    }
  }, [props.dataList]);

  return (
    <FlatList
      contentContainerStyle={{ paddingTop: 20 }}
      style={{ flex: 1 }}
      data={props.dataList}
      renderItem={({ item }) => <MessageItem data={item} key={item.orderId} />}
      ListFooterComponent={$renderFooter}
    />
  );
};

export default React.memo(LoanRecordListPage);
