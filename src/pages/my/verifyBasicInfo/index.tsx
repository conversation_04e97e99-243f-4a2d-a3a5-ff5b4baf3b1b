import { ScrollView, TouchableOpacity } from 'react-native';
import {
  Button,
  EProcessStatus,
  Image,
  Layout,
  PrefixInput,
  SeaInput,
  Text,
  TopNavigation,
  View,
  AnimatedSmsCodeInput,
  PasswordInput,
} from '@/components';
import { ScreenProps } from '@/types';
import { isStringExist, isInviteCodeExist, isMoreThanLength, validateCurp } from '@/utils';
import React, { ReactElement, useMemo } from 'react';
import useData from './useData';
import { Strings } from '@/i18n';
import { t } from 'i18next';
import { TypeItem, Type, Props } from './type';
import { Colors } from '@/themes';
import { useLoginInfo } from '@/hooks';
import { HitPointEnumsSpace } from '@/enums';
import CurpDesModal from '../../evidence/ocrPage/components/curpDesModal';

export default ({ route }: ScreenProps<Props>): ReactElement => {
  const { email, validateChannels } = route.params;
  const {
    typeList,
    emailCode,
    setEmailCode,
    setType,
    disabled,
    handleNext,
    clickSendPreMethodForEmail,
    password,
    setPassword,
    curp,
    setCurp,
    type,
    configureAnimation,
    maskEmail,
    emailCodeRef,
    onShowCurpModal,
    onCloseCurpModal,
    curpDesModalVisible,
  } = useData({ email, validateChannels });
  const { secureTextEntry, onToggleSecureEntry } = useLoginInfo();

  const renderTop = () => {
    return (
      <View
        padding="12 12 12 12"
        style={{ backgroundColor: 'primary-color-100', borderRadius: 8 }}
        layoutStrategy="flexRowStartCenter">
        <Image name="_safeTipIcon" />
        <Text padding="0 12 0 8" i18nKey={Strings.validateBasicInfoString.tip} category="c1" />
      </View>
    );
  };
  const renderContent = (type: Type) => {
    switch (type) {
      case Type.EMAIL:
        return (
          <View
            margin="20 0 0 0"
            padding="12 12 12 12"
            style={{ borderRadius: 8, backgroundColor: Colors.FILL_COLOR_100 }}>
            <Text>
              <Text
                i18nKey={Strings.validateBasicInfoString.emailTip}
                style={{ color: 'text-color-700' }}
              />
              <Text textContent={` (${maskEmail(email)})`} style={{ color: 'text-color-700' }} />
            </Text>
            <AnimatedSmsCodeInput
              ref={emailCodeRef}
              type="line"
              placeholderKey={Strings.validateBasicInfoString.code}
              prefixMargin={'16 0 0 0'}
              smsCode={emailCode}
              setSmsCode={setEmailCode}
              clickSendPreMethod={clickSendPreMethodForEmail}
              scenesId="register"
              maxLength={6}
              validateConfig={[
                {
                  condition: isStringExist(emailCode),
                  info: t('verificationString.required'),
                  status: 'danger',
                },
                {
                  condition: isInviteCodeExist(emailCode),
                  info: t(Strings.verificationString.error),
                  status: 'danger',
                },
              ]}
              style={{ backgroundColor: Colors.FILL_COLOR_100 }}
              dangerStyle={{ backgroundColor: Colors.FILL_COLOR_100 }}
              eventKey={HitPointEnumsSpace.EEventKey.E_EMAIL_OTP}
              pageKey={HitPointEnumsSpace.EPageKey.P_CHANGE_MOBILE_ID_VERIFICATION}
            />
          </View>
        );
      case Type.PASSWORD:
        return (
          <View
            margin="20 0 0 0"
            padding="12 12 12 12"
            style={{ borderRadius: 8, backgroundColor: Colors.FILL_COLOR_100 }}>
            <PasswordInput
              type="line"
              validateConfig={[
                {
                  condition: isStringExist(password),
                  info: t(Strings.verificationString.required),
                  status: 'danger',
                },
                {
                  condition: isMoreThanLength(password, 6),
                  info: t(Strings.verificationString.error),
                  status: 'danger',
                },
              ]}
              value={password}
              placeholderKey={Strings.validateBasicInfoString.pwdPlaceholder}
              style={{ backgroundColor: Colors.FILL_COLOR_100 }}
              secureTextEntry={secureTextEntry}
              toggleSecureEntry={onToggleSecureEntry}
              onChangeText={setPassword}
              pageKey={HitPointEnumsSpace.EPageKey.P_CHANGE_MOBILE_ID_VERIFICATION}
              eventKey={HitPointEnumsSpace.EEventKey.E_PASSWORD}
            />
          </View>
        );
      default:
        return (
          <View
            margin="20 0 0 0"
            padding="12 12 12 12"
            style={{ borderRadius: 8, backgroundColor: Colors.FILL_COLOR_100 }}>
            <SeaInput
              type="line"
              prefixMargin="12 0 0 0"
              placeholderKey={Strings.validateBasicInfoString.curpPlaceholder}
              value={curp}
              setValue={setCurp}
              validateConfig={[
                {
                  condition: isStringExist(curp),
                  info: t('verificationString.required'),
                  status: 'danger',
                },
                {
                  condition: validateCurp(curp),
                  info: t(Strings.verificationString.error),
                  status: 'danger',
                },
              ]}
              style={{ backgroundColor: Colors.FILL_COLOR_100 }}
              dangerStyle={{ backgroundColor: Colors.FILL_COLOR_100 }}
              maxLength={18}
              isShowPlaceholderKey
              pageKey={HitPointEnumsSpace.EPageKey.P_CHANGE_MOBILE_ID_VERIFICATION}
              eventKey={HitPointEnumsSpace.EEventKey.E_CURP}
            />
            <TouchableOpacity activeOpacity={0.8} onPress={onShowCurpModal}>
              <View layoutStrategy="flexRowStartCenter">
                <Text
                  i18nKey={Strings.validateBasicInfoString.selectCurp}
                  category="c1"
                  style={{ color: Colors.TEXT_COLOR_500 }}
                />
                <Image name="_arrowRight" style={{ width: 14, height: 14 }} margin="0 0 0 4" />
              </View>
            </TouchableOpacity>
          </View>
        );
    }
  };
  const renderTypeItem = (item: TypeItem) => {
    const selected = type === item.type;
    return (
      <View key={item.type} margin="20 0 0 0">
        <TouchableOpacity
          activeOpacity={0.8}
          onPress={() => {
            configureAnimation();
            setType(item.type);
          }}>
          <View layoutStrategy="flexRowStartCenter">
            <Image
              style={{ width: 26, height: 26 }}
              name={selected ? '_fromRadioChecked' : '_fromRadioUnchecked'}
            />
            <Text padding="0 0 0 8" i18nKey={item.title} style={{ color: 'text-color-700' }} />
          </View>
        </TouchableOpacity>
        {selected && renderContent(item.type)}
      </View>
    );
  };
  const renderForm = useMemo(() => {
    return (
      <View cardType="baseType" padding="12 12 12 12" margin="0 0 40 0">
        <Text i18nKey={Strings.validateBasicInfoString.cardTitle} margin="0 0 6 0" />
        {typeList?.map(renderTypeItem)}
      </View>
    );
  }, [type, password, curp, emailCode, secureTextEntry, typeList]);
  const renderButton = useMemo(() => {
    return (
      <View padding="12 32 16 32" style={{ backgroundColor: 'fill-color-0' }}>
        <Button textI18nKey={Strings.btnString.continue} disabled={disabled} onPress={handleNext} />
      </View>
    );
  }, [disabled, type]);
  return (
    <Layout pLevel="0" level="1">
      <TopNavigation titleKey={Strings.validateBasicInfoString.title} bottomLine />
      <ScrollView
        style={{ paddingHorizontal: 16, paddingTop: 22 }}
        keyboardShouldPersistTaps={'always'}>
        {renderTop()}
        {renderForm}
      </ScrollView>
      {renderButton}
      <CurpDesModal visible={curpDesModalVisible} onCancel={onCloseCurpModal} />
    </Layout>
  );
};
