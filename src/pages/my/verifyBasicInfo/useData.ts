import { LayoutAnimation } from 'react-native';
import { HitPointEnumsSpace, UserEnumsSpace } from '@/enums';
import {
  useOnInit,
  useFaceLive,
  useGetUserStateAndNextRouterOrOtherCallBack,
  useCheckCameraPermissionAndRequestPermission,
} from '@/hooks';
import { Toast } from '@/nativeComponents';
import {
  responseHandler,
  fetchEmailOtp,
  fetchValidateIsSelf,
  fetchGetBizTokenV7,
  faceVerifyV7,
  fetchUploadImageV7,
} from '@/server';
import { BaseInfoManager, modalDataStoreInstance, ModalList } from '@/managers';
import { trackCommonEvent } from '@/trackEvent';
import {
  TrackEvent,
  nav,
  validateCurp,
  isInviteCodeExist,
  isStringExist,
  isMoreThanLength,
  log,
  FileUtils,
} from '@/utils';
import { useCallback, useEffect, useMemo, useRef } from 'react';
import useAction from './useAction';
import { RouterConfig } from '@/routes';
import { useSetState } from 'ahooks';
import { State, Type, TypeItem, Props } from './type';
import { Strings } from '@/i18n';
import { EFaceLiveEventType, IFaceEventData } from '../../../native/module/faceLive';
import { t } from 'i18next';

export default function useData(props: Props) {
  const { email, validateChannels } = props;
  const { actionState, emailCodeRef, ...restActions } = useAction();
  const [state, setState] = useSetState<State>({
    typeList: [],
    type: '',
    curpDesModalVisible: false,
  });
  const { emailCode, curp, password, type } = { ...actionState, ...state };
  const bizToken = useRef<string>('');
  const passwordRef = useRef<string>('');

  useEffect(() => {
    passwordRef.current = password;
  }, [password]);
  useOnInit({
    pageKey: HitPointEnumsSpace.EPageKey.P_CHANGE_MOBILE_ID_VERIFICATION,
    callback() {
      onInit();
    },
  });
  const onInit = () => {
    const typeList = [
      {
        title: Strings.validateBasicInfoString.emailTitle,
        type: Type.EMAIL,
      },
      {
        title: Strings.validateBasicInfoString.pwdTitle,
        type: Type.PASSWORD,
      },
      {
        title: Strings.validateBasicInfoString.curpTitle,
        type: Type.FACE,
      },
    ].filter(item => validateChannels.includes(item.type));
    setState({ typeList });
  };
  const checkCameraPermission = useCheckCameraPermissionAndRequestPermission()[0];
  const pointParam = useRef<TrackEvent.IEventPoint>({
    p: HitPointEnumsSpace.EPageKey.P_LIVE,
    e: HitPointEnumsSpace.EEventKey.E_LIVE_ACTION,
  });
  const onStartLiveCompare = async (
    faceLivenessFilePath: string = '',
    megliveData: string = '',
  ) => {
    if (megliveData === '') return;
    const faceVerifyParam = {
      bizToken: bizToken.current,
      megliveData: megliveData,
      bizType: UserEnumsSpace.BizType.CHANGE_MOBILE,
    };
    BaseInfoManager.changeLoadingModalVisible(true);
    const { error, data } = await responseHandler(faceVerifyV7(faceVerifyParam));
    // BaseInfoManager.changeLoadingModalVisible(false);
    if (error) {
      BaseInfoManager.changeLoadingModalVisible(false);
      return;
    }
    const absoluteFilePath = FileUtils.getPlatformPathDir(faceLivenessFilePath);
    if (faceLivenessFilePath !== '') {
      // 上传原始的活体加密文件
      log.info(`Upload live encrypted files ${absoluteFilePath}`);
      const { error, data } = await responseHandler(
        fetchUploadImageV7(absoluteFilePath, {
          cardType: 'original_face',
          fileType: 'megvii',
          bizType: UserEnumsSpace.BizType.CHANGE_MOBILE,
        }),
      );
      BaseInfoManager.changeLoadingModalVisible(false);
      if (!error) {
        log.info('Uploading live encrypted files successfully');
      }
    }
    await TrackEvent.uploadEventLog();
    // nav.navigationGoBack()
    enterChangeMobile();
  };
  const faceLiveExamineResultCallback = useCallback(async (event: IFaceEventData) => {
    log.debug('# faceLiveExamineResultCallback', event);
    const { eventType, faceLivenessFilePath, megliveData } = event;
    switch (eventType) {
      case EFaceLiveEventType.FACE_LIVE_PRE_START:
        break;
      case EFaceLiveEventType.FACE_LIVE_PRE_DETECT:
        break;
      case EFaceLiveEventType.FACE_LIVE_PRE_FINISH:
        break;
      case EFaceLiveEventType.FACE_LIVE_START_DETECT:
        BaseInfoManager.changeLoadingModalVisible(false);
        break;
      // 关闭loading
      case EFaceLiveEventType.FACE_LIVE_START_DETECT_STATE_SUCCESS:
        // 活体成功
        onStartLiveCompare(faceLivenessFilePath, megliveData);
        break;
      case EFaceLiveEventType.FACE_LIVE_PRE_DETECT_STATE_FAIL:
      case EFaceLiveEventType.FACE_LIVE_PRE_FINISH_STATE_FAIL:
      case EFaceLiveEventType.FACE_LIVE_START_DETECT_STATE_FAIL:
        // 关闭loading
        Toast(`${t(Strings.loanConfirmString.face_failed_pls_retry)}`);
        await TrackEvent.uploadEventLog();
        // nav.navigationGoBack();
        BaseInfoManager.changeLoadingModalVisible(false);
        break;
    }
  }, []);
  const [startFaceLiveVerification] = useFaceLive(
    pointParam.current,
    faceLiveExamineResultCallback,
  );
  const onTrackEvent = (event: keyof typeof HitPointEnumsSpace.EEventKey, status: string = '') => {
    TrackEvent.trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_CHANGE_MOBILE_ID_VERIFICATION,
        e: HitPointEnumsSpace.EEventKey[event],
      },
      status,
    );
  };
  const disabled = useMemo(() => {
    const MAP = {
      [Type.EMAIL]: !(isStringExist(emailCode) && isInviteCodeExist(emailCode)),
      [Type.PASSWORD]: !isMoreThanLength(password, 6),
      [Type.FACE]: !validateCurp(curp),
    };
    return type ? MAP[type] : true;
  }, [type, emailCode, password, curp]);
  const enterChangeMobile = () => {
    nav.navigate(RouterConfig.CHANGE_MOBILE as any);
  };
  const onFace = useCallback(async () => {
    const result = await checkCameraPermission();
    if (!result) return;
    BaseInfoManager.changeLoadingModalVisible(true);
    const { error, data } = await responseHandler(
      fetchGetBizTokenV7({ bizType: UserEnumsSpace.BizType.CHANGE_MOBILE }),
    );
    BaseInfoManager.changeLoadingModalVisible(false);
    if (error) {
      return;
    }
    const { biz_token } = data;
    if (biz_token) {
      bizToken.current = biz_token;
      startFaceLiveVerification(biz_token);
    }
  }, []);
  const handleNext = useCallback(async () => {
    if (!type) return;
    onTrackEvent('BTN_NEXT');
    BaseInfoManager.changeLoadingModalVisible(true);
    const params = {
      validateChannel: type,
      emailOTP: emailCode,
      password: passwordRef.current,
      curp,
    };
    const { error } = await responseHandler(fetchValidateIsSelf(params));
    BaseInfoManager.changeLoadingModalVisible(false);
    if (error) {
      return;
    }
    if (type === Type.FACE) {
      return onFace();
    }
    enterChangeMobile();
  }, [type, emailCode, curp]);
  const onSendEmailCode = useCallback(async () => {
    const params = {
      bizType: UserEnumsSpace.BizType.CHANGE_MOBILE,
      email,
    };
    BaseInfoManager.changeLoadingModalVisible(true);
    const { error } = await responseHandler(fetchEmailOtp(params));
    BaseInfoManager.changeLoadingModalVisible(false);
    if (error) {
      return false;
    }
    emailCodeRef.current?.reStartCountDown();
    return true;
  }, []);
  const clickSendPreMethodForEmail = useCallback(async () => {
    onTrackEvent('BTN_OBTAIN_EMAIL_OTP');
    return onSendEmailCode();
  }, []);
  /**
   * 邮箱脱敏处理
   * 规则：仅保留邮箱前缀的首和尾部后2位字符，中间用*号替换，并完整保留域名部分
   * @param email 原始邮箱
   * @returns 脱敏后的邮箱
   */
  const maskEmail = (email: string): string => {
    if (!email || !email.includes('@')) return email;
    const [prefix, domain] = email.split('@');
    if (prefix.length <= 2) return email;
    const firstChar = prefix.charAt(0);
    const lastTwoChars = prefix.slice(-2);
    const maskedPrefix = `${firstChar}${Array(prefix.length - 3)
      .fill('*')
      .join('')}${lastTwoChars}`;
    return `${maskedPrefix}@${domain}`;
  };
  const configureAnimation = () => {
    LayoutAnimation.configureNext(
      LayoutAnimation.create(
        200, // 动画时长
        LayoutAnimation.Types.easeInEaseOut,
        LayoutAnimation.Properties.opacity,
      ),
    );
  };
  const setType = (type: Type) => {
    setState({ type });
  };
  const onCloseCurpModal = useCallback(() => {
    setState({ curpDesModalVisible: false });
  }, []);
  const onShowCurpModal = () => {
    setState({ curpDesModalVisible: true });
  };
  return {
    ...state,
    ...restActions,
    handleNext,
    disabled,
    clickSendPreMethodForEmail,
    maskEmail,
    configureAnimation,
    setType,
    emailCodeRef,
    onCloseCurpModal,
    onShowCurpModal,
    passwordRef,
  };
}
