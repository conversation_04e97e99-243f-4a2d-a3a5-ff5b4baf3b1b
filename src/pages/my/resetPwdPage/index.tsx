import { BaseInputRefType, Button, Layout, PasswordInput, TopNavigation } from '@/components';
import { HitPointEnumsSpace } from '@/enums';
import { useLoginInfo, useOnInit } from '@/hooks';
import { BaseInfoManager, ModalList, modalDataStoreInstance } from '@/managers';
import { RouterConfig } from '@/routes';
import { doResetPassword } from '@/server';
import { ScreenProps } from '@/types';
import { isEqual, isMoreThanLength, nav } from '@/utils';
import { ReactElement, memo, useMemo, useRef } from 'react';
import { ScrollView } from 'react-native';
import { useNameSpace } from '../../../i18n';
import { Strings } from '@/i18n';

interface IProps {
  fromPage: RouterConfig.VERIFY_PWD | '';
}
const useData = (props: IProps) => {
  const { fromPage } = props;
  const loginInfo = useLoginInfo();
  const rePwdInputRef = useRef<BaseInputRefType>(null);

  /** 初始化 */
  useOnInit({
    pageKey: HitPointEnumsSpace.EPageKey.P_PASSWORD_RESET,
  });

  const computedButtonDisable = useMemo(() => {
    return !(
      isEqual(loginInfo.password, loginInfo.rePassword) &&
      isMoreThanLength(loginInfo.password, 6) &&
      isMoreThanLength(loginInfo.rePassword, 6)
    );
  }, [loginInfo.rePassword, loginInfo.password]);

  const handleGoLogout = async (smsCode: string, phoneNumber: string) => {
    let params = {
      loginType: 'sms',
      mobile: phoneNumber,
      smsCode: smsCode,
      password: loginInfo.password,
    };

    BaseInfoManager.changeLoadingModalVisible(true);
    let result = await doResetPassword(params);
    BaseInfoManager.changeLoadingModalVisible(false);
    if (result.code === 0) {
      BaseInfoManager.updateSetPasswordStatus(true);
      modalDataStoreInstance.openModal({
        key: ModalList.INFO_PROMPT_CONFIRM,
        confirmBtnName: 'btnString.OK',
        i18nKey: 'myString.modify_success',
        isBackdropClose: false,
        confirmBtnCallback: () => {
          if (fromPage === RouterConfig.VERIFY_PWD) {
            nav.navigate(fromPage as any);
          } else {
            nav.navigate(RouterConfig.MY as any);
          }
        },
      });
    }

    return result.code === 0;
  };

  return {
    ...loginInfo,
    handleGoLogout,
    rePwdInputRef,
    computedButtonDisable,
  };
};

const ResetPwdPage = ({
  route,
}: ScreenProps<{
  smsCode: string;
  phoneNumber: string;
  fromPage: RouterConfig.ENTER_PHONE_NUMBER | '';
}>): ReactElement => {
  const { smsCode = '', phoneNumber = '', fromPage } = route.params;
  const {
    rePwdInputRef,
    handleGoLogout,
    password,
    rePassword,
    secureTextEntry,
    reSecureTextEntry,
    onToggleSecureEntry,
    onToggleReSecureEntry,
    onPasswordChange,
    onRePasswordChange,
    computedButtonDisable,
  } = useData({ fromPage });

  const t = useNameSpace().t;

  return (
    <>
      <Layout pLevel="0" level="0">
        <TopNavigation titleKey="myString.modify_pwd" />
        <ScrollView
          fadingEdgeLength={10}
          style={{ marginHorizontal: 16 }}
          keyboardShouldPersistTaps="always">
          <PasswordInput
            type="line"
            validateConfig={[
              {
                condition: isMoreThanLength(password, 6),
                info: t(Strings.validateBasicInfoString.pwdErrorTip),
                status: 'danger',
              },
            ]}
            onBlur={() => {
              rePassword && rePwdInputRef.current?.blur();
            }}
            prefixMargin={'32 0 0 0'}
            value={password}
            placeholderKey={Strings.validateBasicInfoString.setPwd}
            secureTextEntry={secureTextEntry}
            toggleSecureEntry={onToggleSecureEntry}
            onChangeText={onPasswordChange}
          />
          <PasswordInput
            type="line"
            ref={rePwdInputRef}
            validateConfig={[
              // {
              //   condition: isMoreThanLength(rePassword, 6),
              //   info: t('formString.require_min_six_size'),
              //   status: 'danger',
              // },
              {
                condition: isEqual(password, rePassword),
                info: t(Strings.validateBasicInfoString.notEqual),
                status: 'danger',
              },
            ]}
            prefixMargin={'24 0 0 0'}
            value={rePassword}
            placeholderKey={Strings.validateBasicInfoString.resetPwd}
            secureTextEntry={reSecureTextEntry}
            toggleSecureEntry={onToggleReSecureEntry}
            onChangeText={onRePasswordChange}
          />
          <Button
            status="primary"
            disabled={computedButtonDisable}
            onPress={() => {
              handleGoLogout(smsCode, phoneNumber);
            }}
            margin="32 0 0 0"
            style={{ width: '100%' }}
            padding={'16 0 16 0'}
            textI18nKey="btnString.next"
          />
        </ScrollView>
      </Layout>
    </>
  );
};

export default memo(ResetPwdPage);
