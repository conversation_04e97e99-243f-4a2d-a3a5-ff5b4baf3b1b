import {
  Button,
  Layout,
  Text,
  TopNavigation,
  View,
  Image,
  AnimatedSmsCodeInput,
  AnimatedSmsCodeInputRefType,
} from '@/components';
import { HitPointEnumsSpace, UserEnumsSpace } from '@/enums';
import { useLoginInfo, useOnInit } from '@/hooks';
import { BaseInfoManager, ModalList, UserInfoManager, modalDataStoreInstance } from '@/managers';
import { Toast } from '@/nativeComponents';
import { RouterConfig } from '@/routes';
import { doModifyMobile, fetchOtpByModifyPwd, responseHandler, fetchOtp } from '@/server';
import { ScreenProps } from '@/types';
import { isStringExist, nav, checkTextLength, TrackEvent } from '@/utils';
import _ from 'lodash';
import { ReactElement, memo, useCallback, useMemo, useRef } from 'react';
import { ScrollView } from 'react-native';
import { Strings, useNameSpace } from '@/i18n';
import OtpModal from '../../login/getSmsCodePage/components/OtpModal';
import { useSetState } from 'ahooks';
import { useSmsCodeHandler } from '../../../hooks/useSmsCodeHandler';

interface IProps {
  password: string;
  phoneNumber: string;
}
const useData = (props: IProps) => {
  const { password, phoneNumber } = props;
  const { smsCode, onSetSmsCode } = useLoginInfo();
  useSmsCodeHandler(onSetSmsCode);
  const [state, setState] = useSetState({
    optVisible: false,
  });
  const smsCodeInputRef = useRef<AnimatedSmsCodeInputRefType>(null);
  const t = useNameSpace().t;
  /** 初始化 */
  useOnInit({
    pageKey: HitPointEnumsSpace.EPageKey.P_MODIFY_NUMBER_CONFIRM,
    callback() {
      smsCodeInputRef.current?.reStart();
    },
  });

  const onClickSendPreMethod = useCallback(async () => {
    setState({ optVisible: true });
    return true;
  }, []);
  const buttonDisable = useMemo(() => {
    return !checkTextLength(smsCode, 4);
  }, [smsCode]);

  const handleNext = async () => {
    BaseInfoManager.changeLoadingModalVisible(true);
    if (!(await onModifyMobile())) {
      BaseInfoManager.changeLoadingModalVisible(false);
      return;
    }
    BaseInfoManager.changeLoadingModalVisible(false);
  };

  const onModifyMobile = async () => {
    let result = await doModifyMobile({
      mobile: phoneNumber,
      smsCode,
      password,
    });

    if (result.code === 0) {
      UserInfoManager.updateMobile(phoneNumber);
      modalDataStoreInstance.openModal({
        imageKey: '_changeMobileSuccessIcon',
        key: ModalList.INFO_PROMPT_CONFIRM,
        i18nKey: 'myString.changeMobileTip',
        confirmBtnName: 'btnString.OK',
        isBackdropClose: false,
        confirmBtnCallback: () => {
          nav.resetRouteNavigate(RouterConfig.MY as any);
        },
      });
    }
    return result.code === 0;
  };
  const onCloseOtpModal = useCallback(() => {
    setState({ optVisible: false });
  }, []);
  const onSendOtp = async (options: {
    otpChannel: UserEnumsSpace.OtpChannel;
    callback?: Function;
    mobile?: string;
  }): Promise<boolean> => {
    const { otpChannel, callback, mobile = '' } = options;
    const params = {
      otpChannel,
      bizType: UserEnumsSpace.BizType.CHANGE_MOBILE,
      mobile,
    };
    BaseInfoManager.changeLoadingModalVisible(true);
    const { error, data } = await responseHandler(fetchOtp(params));
    BaseInfoManager.changeLoadingModalVisible(false);
    if (error) {
      return false;
    }
    callback?.(data);
    return true;
  };
  const onTrackEvent = (event: keyof typeof HitPointEnumsSpace.EEventKey, status: string = '1') => {
    TrackEvent.trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_MODIFY_NUMBER_CONFIRM,
        e: HitPointEnumsSpace.EEventKey[event],
      },
      status,
    );
  };
  const onSendVoiceCode = async (): Promise<boolean> => {
    onTrackEvent('BTN_OBTAIN_OTP_CALL');
    const params = {
      otpChannel: UserEnumsSpace.OtpChannel.VOICE_PHONE,
      callback: () => {
        modalDataStoreInstance.openModal({
          key: ModalList.RECIVE_OTP_BY_CALL,
          i18nKey: 'modalString.callYouSendOTP',
          confirmBtnName: 'btnString.OK',
          confirmBtnCallback: () => {
            onTrackEvent('BTN_OBTAIN_OTP_CALL_SI');
          },
        });
      },
      mobile: phoneNumber,
    };
    return onSendOtp(params);
  };
  const onSendSmsCode = async (): Promise<boolean> => {
    onTrackEvent('BTN_OBTAIN_OTP_SMS');
    const params = {
      otpChannel: UserEnumsSpace.OtpChannel.SMS,
      callback: () => {
        Toast(`${t('homeString.peso_otp_success_tip')}${phoneNumber}`);
      },
      mobile: phoneNumber,
    };
    return onSendOtp(params);
  };
  const onSendWhatsappCode = async (): Promise<boolean> => {
    const params = {
      otpChannel: UserEnumsSpace.OtpChannel.WHATS_APP,
      callback: () => {
        Toast(`${t('homeString.peso_otp_success_tip')}${phoneNumber}`);
      },
      mobile: phoneNumber,
    };
    return onSendOtp(params);
  };
  const handleOtpChannel = useCallback(async (otpChannel: UserEnumsSpace.OtpChannel) => {
    onCloseOtpModal();
    const sendMap = {
      [UserEnumsSpace.OtpChannel.VOICE_PHONE]: onSendVoiceCode,
      [UserEnumsSpace.OtpChannel.SMS]: onSendSmsCode,
      [UserEnumsSpace.OtpChannel.WHATS_APP]: onSendWhatsappCode,
    };
    (await sendMap[otpChannel]()) && smsCodeInputRef.current?.reStartCountDown();
    return true;
  }, []);
  const onSmsCodeInit = useCallback(async () => {
    await onSendSmsCode();
    smsCodeInputRef.current?.reStartCountDown();
    return true;
  }, []);
  return {
    smsCode,
    onSetSmsCode,
    smsCodeInputRef,
    onClickSendPreMethod,
    handleNext,
    buttonDisable,
    t,
    ...state,
    onCloseOtpModal,
    handleOtpChannel,
    onSmsCodeInit,
  };
};

const NewMobileSmsCode = ({ route }: ScreenProps<IProps>): ReactElement => {
  const { password = '', phoneNumber = '' } = route.params;
  const {
    smsCode,
    onSetSmsCode,
    smsCodeInputRef,
    onClickSendPreMethod,
    handleNext,
    buttonDisable,
    t,
    onCloseOtpModal,
    optVisible,
    handleOtpChannel,
    onSmsCodeInit,
  } = useData({ password, phoneNumber });

  const renderTop = () => {
    return (
      <View
        margin="24 0 24 0"
        padding="12 12 12 12"
        style={{ backgroundColor: 'primary-color-100', borderRadius: 8 }}
        layoutStrategy="flexRowStartCenter">
        <Image name="_safeTipIcon" />
        <Text padding="0 12 0 8" i18nKey={Strings.validateBasicInfoString.tip} category="c1" />
      </View>
    );
  };
  return (
    <Layout pLevel="0" level="0">
      <TopNavigation titleKey="myString.my_modify_mobile" />
      <ScrollView
        style={{ paddingHorizontal: 16 }}
        fadingEdgeLength={10}
        keyboardShouldPersistTaps="always">
        {renderTop()}
        <View margin="0 16 0 16">
          <Text i18nKey={'myString.changeMobileSmsCodeTitle'} />
          <Text padding="52 8 0 8" textContent={'+52 ' + phoneNumber} />
          <AnimatedSmsCodeInput
            type="line"
            ref={smsCodeInputRef}
            prefixMargin={'32 0 0 0'}
            placeholderKey={'loginString.smscode_input_prefix'}
            smsCode={smsCode}
            setSmsCode={onSetSmsCode}
            clickSendPreMethod={onClickSendPreMethod}
            initMethod={onSmsCodeInit}
            scenesId="new_mobile"
            validateConfig={[
              {
                condition: isStringExist(smsCode),
                info: t('verificationString.required'),
                status: 'danger',
              },
              {
                condition: checkTextLength(smsCode, 4),
                info: t(Strings.verificationString.error),
                status: 'danger',
              },
            ]}
          />
          <Button
            margin="32 0 0 0"
            status="primary"
            disabled={buttonDisable}
            onPress={handleNext}
            padding={'16 0 16 0'}
            textI18nKey="btnString.next"
          />
        </View>
      </ScrollView>
      <OtpModal
        visible={optVisible}
        onClose={onCloseOtpModal}
        handleOtpChannel={handleOtpChannel}
      />
    </Layout>
  );
};

export default memo(NewMobileSmsCode);
