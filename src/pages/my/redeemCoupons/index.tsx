import { memo } from 'react';
import { Button, Image, Layout, Text, TopNavigation, View } from '@/components';
import { Strings } from '@/i18n';
import { Colors } from '@/themes';
import CouponList from './components/CouponList';
import useData from './useData';
import CouponExchangeSuccess from './components/CouponExchangeSuccess';

const RedeemCoupons = () => {
  const {
    failed,
    data,
    balance,
    onExchangeCoupon,
    isShowExchangeSuccess,
    enterCouponList,
    onClose,
    onBack,
    loadData,
  } = useData();
  return (
    <Layout pLevel="0" level="1">
      <TopNavigation titleKey={Strings.redeemCouponsString.title} bottomLine={false} />
      <CouponList
        failed={failed}
        data={data}
        balance={balance}
        onExchangeCoupon={onExchangeCoupon}
        loadData={loadData}
      />
      <CouponExchangeSuccess
        visible={isShowExchangeSuccess}
        onConfirm={enterCouponList}
        onClose={onClose}
        onBackdropPress={onBack}
      />
    </Layout>
  );
};

export default memo(RedeemCoupons);
