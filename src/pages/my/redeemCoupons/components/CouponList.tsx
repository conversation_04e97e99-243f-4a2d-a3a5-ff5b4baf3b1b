import { memo, useMemo } from 'react';
import { Button, Image, Layout, Text, TopNavigation, View, LinearGradient } from '@/components';
import { FlatList } from 'react-native';
import FailPage from './FailPage';
import { Colors } from '@/themes';
import { Strings, useNameSpace } from '@/i18n';
import CouponItem from '../../../components/coupon/CouponItem';
import { CouponPageProps } from '../type';
import { RouterConfig } from '@/routes';

const CouponList = (props: CouponPageProps) => {
  const { data, failed, balance, onExchangeCoupon, loadData } = props;
  const renderTop = () => {
    return (
      <View padding="0 8 0 8" margin="24 0 16 0" layoutStrategy="flexRowStartCenter">
        <Text i18nKey={Strings.redeemCouponsString.subTitle} />
        <LinearGradient
          style={{
            height: 8,
            width: 100,
            borderRadius: 8,
            position: 'absolute',
            left: 8,
            bottom: 0,
            zIndex: -1,
          }}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
          colors={['#F5A541', '#FFFFFF', '#FFFFFF']}
          locations={[0, 0.5, 0.5]}
        />
        <Text textContent={` $ ${balance}`} />
      </View>
    );
  };
  const renderFooter = useMemo(() => {
    if (data?.length === 0) {
      return (
        <View
          padding="0 0 32 0"
          style={{
            backgroundColor: 'background-color-0',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-around',
          }}>
          <Text
            width={'100%'}
            isCenter
            category="p2"
            style={{ color: 'text-color-600' }}
            i18nKey="couponListString.noData"
          />
        </View>
      );
    }
    return null;
  }, [data]);
  if (failed) {
    return <FailPage loadData={loadData} />;
  }
  return (
    <View
      padding="16 16 0 16"
      margin="0 0 16 0"
      style={{ backgroundColor: Colors.BACKGROUND_COLOR_100, flex: 1 }}>
      <View
        padding="0 8 0 8"
        style={{ backgroundColor: Colors.BACKGROUND_COLOR_0, borderRadius: 8, flex: 1 }}>
        {renderTop()}
        <FlatList
          data={data}
          renderItem={({ item }) => {
            return (
              <CouponItem
                data={item}
                balance={balance}
                onExchangeCoupon={onExchangeCoupon}
                pageKey={RouterConfig.REDEEM_COUPONS}
              />
            );
          }}
          // ListFooterComponent={renderFooter}
        />
      </View>
    </View>
  );
};
export default memo(CouponList);
