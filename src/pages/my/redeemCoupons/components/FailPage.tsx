import { memo } from 'react';
import { Button, Image, Layout, Text, TopNavigation, View, LinearGradient } from '@/components';
import { Colors } from '@/themes';
import { Strings } from '@/i18n';
import { FailPageProps } from '../type';

const FailPage = (props: FailPageProps) => {
  const { loadData } = props;
  return (
    <View
      layoutStrategy="flexColumnCenterCenter"
      style={{ backgroundColor: Colors.BACKGROUND_COLOR_100, flex: 1 }}
      padding="0 0 180 0">
      <Image name="_redeemCouponLoadFail" />
      <Text i18nKey={Strings.redeemCouponsString.loadFailTip} padding="32 0 32 0" />
      <Button
        textI18nKey={Strings.redeemCouponsString.reLoad}
        style={{ width: '60%' }}
        onPress={loadData}
        appearance="outline"
      />
    </View>
  );
};
export default memo(FailPage);
