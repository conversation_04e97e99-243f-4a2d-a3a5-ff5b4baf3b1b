import { memo, useMemo } from 'react';
import {
  Button,
  Image,
  Layout,
  Text,
  TopNavigation,
  View,
  CommonModal,
  ImageBackground,
} from '@/components';
import { TouchableOpacity, Dimensions, StyleSheet } from 'react-native';
import { Strings, useNameSpace } from '@/i18n';
import { ExchangeModel } from '../type';
import { useCustomStyleSheet } from '@/utils';

const CouponExchangeSuccess = (props: ExchangeModel) => {
  const { onClose, onConfirm, visible, onBackdropPress } = props;
  const styles = useCustomStyleSheet(useGetStyle());
  const isSmallScreen = screenWidth <= 365;
  const renderContent = () => {
    return (
      <ImageBackground resizeMode="stretch" name="_couponExchangeSuccessBg" style={styles.modal_bg}>
        <View
          layoutStrategy="flexColumnCenterCenter"
          padding={`${isSmallScreen ? '60' : '80'} 0 0 0`}>
          <Image name="_couponExchangeSuccessTopIcon" />
          <Text
            padding={isSmallScreen ? '20 0 20 0' : '24 0 24 0'}
            i18nKey={Strings.redeemCouponsString.exchangeModalTitle}
            category="h2"
          />

          <View layoutStrategy="flexRowStartCenter">
            <ImageBackground name="_couponExchangeSuccessCancelBtn">
              <TouchableOpacity onPress={onClose} activeOpacity={0.8}>
                <View layoutStrategy="flexRowCenterCenter" style={{ height: '100%' }}>
                  <Text
                    category="p2"
                    i18nKey={Strings.redeemCouponsString.exchangeModalCancelBtn}
                    style={{ color: '#FFEBD9' }}
                  />
                </View>
              </TouchableOpacity>
            </ImageBackground>
            <ImageBackground
              style={{ marginLeft: isSmallScreen ? 10 : 20 }}
              name="_couponExchangeSuccessSureBtn">
              <TouchableOpacity onPress={onConfirm} activeOpacity={0.8}>
                <View layoutStrategy="flexRowCenterCenter" style={{ height: '100%' }}>
                  <Text
                    i18nKey={Strings.redeemCouponsString.exchangeModalSureBtn}
                    style={{ color: '#FFC08E' }}
                    category="p2"
                  />
                </View>
              </TouchableOpacity>
            </ImageBackground>
          </View>
        </View>
        <TouchableOpacity
          activeOpacity={0.8}
          onPress={onClose}
          style={{ position: 'absolute', top: 1, right: 28, width: 20, height: 20 }}
        />
      </ImageBackground>
    );
  };
  return (
    <CommonModal
      hasLinearGradient={false}
      visible={visible}
      style={{ backgroundColor: 'transparent' }}
      onBackdropPress={onBackdropPress}>
      {renderContent()}
    </CommonModal>
  );
};
export default memo(CouponExchangeSuccess);
const screenWidth = Dimensions.get('window').width;

const useGetStyle = () => {
  return useMemo(() => {
    return {
      modal_bg: {
        width: screenWidth - 40,
        height: Math.floor(((screenWidth - 40) * 362) / 373),
      },
    };
  }, []);
};
