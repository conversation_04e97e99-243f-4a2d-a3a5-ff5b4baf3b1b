import { useSetState } from 'ahooks';
import { State } from './type';
import { HitPointEnumsSpace } from '@/enums';
import { useOnInit } from '@/hooks';
import { useCallback, useEffect, useRef } from 'react';
import { BackHandler } from 'react-native';
import { responseHandler, fetchRedeemCouponList, submitRedeemCoupon } from '@/server';
import { BaseInfoManager, WalletInfoManager } from '@/managers';
import { trackCommonEvent } from '@/trackEvent';
import { nav } from '@/utils';
import { RouterConfig } from '@/routes';

const useData = () => {
  const [state, setState] = useSetState<State>({
    data: [],
    balance: '',
    failed: false,
    isShowExchangeSuccess: false,
  });
  useOnInit({
    pageKey: HitPointEnumsSpace.EPageKey.P_COUPON_REDEEM_CENTER,
    callback() {
      loadData();
    },
  });
  useEffect(() => {
    BackHandler.addEventListener('hardwareBackPress', onBack);
    return () => {
      BackHandler.removeEventListener('hardwareBackPress', onBack);
      WalletInfoManager.updateWalletInfo();
    };
  }, []);
  const onBack = () => {
    if (state.isShowExchangeSuccess) {
      setState({ isShowExchangeSuccess: false });
      return true;
    }
    return false;
  };
  const loadData = useCallback(async () => {
    BaseInfoManager.changeLoadingModalVisible(true);
    const { error, data } = await responseHandler(fetchRedeemCouponList());
    BaseInfoManager.changeLoadingModalVisible(false);
    if (error) {
      return setState({ failed: true });
    }
    const { redeemableCoupons, balance } = data;
    setState({
      data: redeemableCoupons,
      balance,
      failed: false,
    });
  }, []);
  const onTrackEvent = (event: keyof typeof HitPointEnumsSpace.EEventKey, status: string = '') => {
    trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_COUPON_REDEEM_CENTER,
        e: HitPointEnumsSpace.EEventKey[event],
      },
      status,
    );
  };

  const onClose = useCallback(() => {
    onTrackEvent('BTN_COUPON_POPUP_CONTINUE');
    setState({ isShowExchangeSuccess: false });
  }, []);
  const enterCouponList = useCallback(() => {
    onTrackEvent('BTN_COUPON_POPUP_VIEW');
    nav.navigate(RouterConfig.COUPON_LIST as any);
  }, []);
  const onExchangeCoupon = useCallback(async (redeemCouponId?: number) => {
    if (!redeemCouponId) return;
    onTrackEvent('BTN_COUPON_REDEEM');
    BaseInfoManager.changeLoadingModalVisible(true);
    const { error } = await responseHandler(submitRedeemCoupon({ redeemCouponId }));
    BaseInfoManager.changeLoadingModalVisible(false);
    await loadData();
    if (error) {
      return;
    }
    setState({
      isShowExchangeSuccess: true,
    });
  }, []);

  return {
    ...state,
    onExchangeCoupon,
    enterCouponList,
    onClose,
    onBack,
    loadData,
  };
};
export default useData;
