import { UserVOSpace } from '@/types';
export interface State {
  data: UserVOSpace.RedeemCouponItem[];
  failed: boolean;
  balance: string;
  isShowExchangeSuccess: boolean;
}
export interface CouponPageProps {
  failed: boolean;
  data: UserVOSpace.RedeemCouponItem[];
  balance: string;
  onExchangeCoupon: (id?: number) => void;
  loadData: () => void;
}
export interface ExchangeModel {
  visible: boolean;
  onConfirm: () => void;
  onClose: () => void;
  onBackdropPress: () => void;
}
export interface FailPageProps {
  loadData: () => void;
}
