import {
  Button,
  Layout,
  Text,
  TopNavigation,
  View,
  Image,
  AnimatedSmsCodeInput,
  AnimatedSmsCodeInputRefType,
} from '@/components';
import { HitPointEnumsSpace } from '@/enums';
import { useLoginInfo, useOnInit, useSubscribeFilter } from '@/hooks';
import { BaseInfoManager, UserInfoContextType, UserInfoManager } from '@/managers';
import { Toast } from '@/nativeComponents';
import { RouterConfig } from '@/routes';
import { fetchForgetPwdOtp } from '@/server';
import { ScreenProps } from '@/types';
import { nav, checkTextLength, isStringExist } from '@/utils';
import { Ref, memo, useCallback, useMemo, useRef } from 'react';
import { ScrollView } from 'react-native';
import { useNameSpace, Strings } from '@/i18n';
import { useSmsCodeHandler } from '../../../hooks/useSmsCodeHandler';

interface IProps {
  fromPage?: RouterConfig.VERIFY_PWD | '';
}
const useDate = (props: IProps) => {
  const { fromPage } = props;
  const loginInfoState = useLoginInfo();
  const t = useNameSpace().t;
  const smsCodeInputRef = useRef<AnimatedSmsCodeInputRefType>(null);

  /** 发送次数记录 */
  const phoneNumber = useSubscribeFilter({
    subject: UserInfoManager.messageCenter,
    filter: (subject: UserInfoContextType) => {
      return subject.userModel.mobile;
    },
  }) as string;

  /** 初始化 */
  useOnInit({
    pageKey: HitPointEnumsSpace.EPageKey.P_PASSWORD_RESET_OTP,
    callback() {
      smsCodeInputRef.current?.reStart();
    },
  });

  const buttonDisable = useMemo(() => {
    return !checkTextLength(loginInfoState.smsCode, 4);
  }, [loginInfoState.smsCode]);

  const onSendCode = async (): Promise<boolean> => {
    let params = {
      mobile: phoneNumber,
    };
    BaseInfoManager.changeLoadingModalVisible(true);
    let result = await fetchForgetPwdOtp(params);
    BaseInfoManager.changeLoadingModalVisible(false);

    if (result.code === 0) {
      Toast(`${t('loanConfirmString.peso_otp_success_tip')}${phoneNumber}`);
    }
    return result.code === 0;
  };

  const handleNext = useCallback(async (smsCode: string) => {
    nav.navigate(RouterConfig.SET_PWD as any, {
      smsCode,
      fromPage,
    });
    return false;
  }, []);

  const onClickSendPreMethod = useCallback(async (): Promise<boolean> => {
    return onSmsCodeInit();
  }, []);

  const onSmsCodeInit = useCallback(async () => {
    await onSendCode();
    smsCodeInputRef.current?.reStartCountDown();
    return true;
  }, []);
  return {
    ...loginInfoState,
    buttonDisable,
    smsCodeInputRef,
    phoneNumber,
    handleNext,
    onClickSendPreMethod,
    t,
    onSmsCodeInit,
  };
};

const ModifyPwdPage = ({
  route,
}: ScreenProps<{
  fromPage?: RouterConfig.VERIFY_PWD | '';
}>) => {
  const { fromPage } = route?.params || {};
  const {
    smsCode,
    onSetSmsCode,
    phoneNumber,
    smsCodeInputRef,
    handleNext,
    buttonDisable,
    onClickSendPreMethod,
    t,
    onSmsCodeInit,
  } = useDate({ fromPage });
  useSmsCodeHandler(onSetSmsCode);

  const renderTop = () => {
    return (
      <View
        margin="24 0 24 0"
        padding="12 12 12 12"
        style={{ backgroundColor: 'primary-color-100', borderRadius: 8 }}
        layoutStrategy="flexRowStartCenter">
        <Image name="_safeTipIcon" />
        <Text padding="0 12 0 8" i18nKey={Strings.validateBasicInfoString.tip} category="c1" />
      </View>
    );
  };
  return (
    <Layout pLevel="0" level="0">
      <TopNavigation titleKey="myString.modify_pwd" />
      <ScrollView
        fadingEdgeLength={10}
        style={{ marginHorizontal: 16 }}
        keyboardShouldPersistTaps="always">
        {/* {renderTop()} */}
        <View margin="24 16 24 16">
          <Text i18nKey={'myString.changeMobileSmsCodeTitle'} />
          <Text padding="32 8 0 8" textContent={'+52 ' + phoneNumber} />
          <AnimatedSmsCodeInput
            type="line"
            ref={smsCodeInputRef}
            prefixMargin={'32 0 0 0'}
            placeholderKey={'loginString.smscode_input_prefix'}
            smsCode={smsCode}
            setSmsCode={onSetSmsCode}
            clickSendPreMethod={onClickSendPreMethod}
            initMethod={onSmsCodeInit}
            scenesId="new_mobile"
            validateConfig={[
              {
                condition: isStringExist(smsCode),
                info: t('verificationString.required'),
                status: 'danger',
              },
              {
                condition: checkTextLength(smsCode, 4),
                info: t(Strings.verificationString.error),
                status: 'danger',
              },
            ]}
          />
          <ButtonView buttonDisable={buttonDisable} smsCode={smsCode} handleNext={handleNext} />
        </View>
      </ScrollView>
    </Layout>
  );
};

/**
 * 按钮区域
 * @function handleNext 验证验证码,进行路由跳转等后续逻辑
 */
const ButtonView = memo(function ButtonView({
  handleNext,
  smsCode,
  buttonDisable,
}: {
  handleNext: (smsCode: string) => Promise<boolean>;
  smsCode: string;
  buttonDisable: boolean;
}) {
  return (
    <Button
      margin="32 0 0 0"
      status="primary"
      disabled={buttonDisable}
      onPress={() => {
        handleNext(smsCode);
      }}
      padding={'16 0 16 0'}
      textI18nKey="btnString.next"
    />
  );
});

export default memo(ModifyPwdPage);
