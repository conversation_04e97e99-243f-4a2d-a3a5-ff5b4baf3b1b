import { useCallback, useMemo, useState } from 'react';
import { View, Image, Text } from '@/components';
import { useNameSpace } from '@/i18n';
import { KVManager } from '@/managers';
import React from 'react';
import { TouchableWithoutFeedback } from 'react-native';
import { useVipFuncSwitch } from '@/hooks';

/** 用户头像图标展示，支持VIP提示显示 */
export default React.memo(
  ({
    theme = 'normal',
    onPress = () => {},
  }: {
    theme?: 'primary' | 'normal';
    onPress?: () => void;
  }) => {
    const isVipSwitch = useVipFuncSwitch();

    const [newFuncTip, setNewFuncTip] = useState<boolean>(
      !!KVManager.action.getBoolean('isMyPageIconNewVipFuncPress'),
    );

    const onPressHandle = useCallback(() => {
      KVManager.action.setBoolean('isMyPageIconNewVipFuncPress', true);
      onPress();
      setTimeout(() => {
        setNewFuncTip(true);
      }, 500);
    }, [onPress]);

    const $newFuncIcon = useMemo(() => {
      if (newFuncTip) {
        return null;
      } else {
        return (
          <View
            style={{
              position: 'absolute',
              top: -5,
              left: 33,
            }}>
            <Image name="_newFuncIcon" />
          </View>
        );
      }
    }, [newFuncTip]);

    const $vipEnterIcon = useMemo(() => {
      if (isVipSwitch)
        return (
          <TouchableWithoutFeedback onPress={onPressHandle}>
            <View layoutStrategy="flexColumnStartCenter">
              <View layoutStrategy="flexRowStartCenter">
                <Image name="_vipEnterIcon" />
                {$newFuncIcon}
              </View>
              <Text
                margin="8 0 0 0"
                category="c1"
                bold="bold"
                isCenter={true}
                style={{
                  width: 100,
                  color: 'text-color-800',
                }}
                i18nKey="vipString.vip"
              />
            </View>
          </TouchableWithoutFeedback>
        );
    }, [isVipSwitch, newFuncTip, $newFuncIcon]);

    return <>{$vipEnterIcon}</>;
  },
);
