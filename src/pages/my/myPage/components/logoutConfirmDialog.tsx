import { CommonModal } from '@/components';
import React from 'react';

interface IProps {
  onConfrim: () => void;
  onCancel: () => void;
  visible: boolean;
}

// 退出登录二次确认弹窗

export default React.memo((props: IProps) => {
  const { visible, onCancel, onConfrim } = props;
  return (
    <CommonModal
      visible={visible}
      onBackdropPress={onCancel}
      hasLinearGradient={true}
      i18nKey="myString.logout_tips"
      confirmBtnName="btnString.OK"
      cancelBtnName="btnString.No"
      cancelCallback={onCancel}
      confirmCallback={onConfrim}
    />
  );
});
