import { useMemo, useState } from 'react';
import { View, Text, Image } from '@/components';
import { useNameSpace } from '@/i18n';
import { KVManager, UserInfoContextType, UserInfoManager } from '@/managers';
import { useSubscribeFilter, useVipFuncSwitch } from '@/hooks';
import React from 'react';
import { TouchableWithoutFeedback } from 'react-native';

type TUserInfoVipSubject = {
  level: number;
  rate: string;
  status: 'NORMAL' | 'RECOVERY' | 'CLEAR';
};

/** 用户头像图标展示，支持VIP提示显示 */
export default React.memo(() => {
  const t = useNameSpace().t;
  const { level, rate, status } = useSubscribeFilter({
    subject: UserInfoManager.messageCenter,
    filter: (subject: UserInfoContextType) => {
      const {
        /** vip 等级 */
        level,
        /** vip 返息率 */
        rate,
        /** 会员等级状态 NORMAL:正常 RECOVERY:恢复期 CLEAR:清空 */
        status,
      } = subject.userModel.userState.vip || {
        level: 0,
        rate: '0%',
        status: 'NORMAL',
      };

      return {
        level,
        rate,
        status,
      };
    },
  }) as TUserInfoVipSubject;

  const $vipLevel = useMemo(() => {
    let textBgColor = status === 'RECOVERY' ? 'danger-color-500' : 'warn-color-500';
    let text = '';
    switch (status) {
      case 'RECOVERY':
        text = `VIP${level} (${t('vipString.resume')}) `;
        break;
      case 'CLEAR':
      case 'NORMAL':
      default:
        text = `VIP${level} (${rate}) `;
    }
    return (
      <Text
        category="c2"
        padding="0 0 0 6"
        textContent={text}
        status="control"
        bold="800"
        numberOfLines={1}
        style={{
          fontStyle: 'italic',
          // backgroundColor: textBgColor,
          borderRadius: 17,
        }}
      />
    );
  }, [status, rate, level, t]);

  const $Icon = useMemo(() => {
    return (
      <>
        <Image margin="0 0 0 6" name="_userIcon" />
        <Image margin="0 0 0 6" resizeMode="contain" name={'_vipIcon'} />
      </>
    );
  }, []);

  return (
    <View width={200} layoutStrategy="flexRowStartCenter">
      {$Icon}
      {$vipLevel}
    </View>
  );
});
