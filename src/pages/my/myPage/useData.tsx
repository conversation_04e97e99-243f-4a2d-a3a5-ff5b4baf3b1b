import { HitPointEnumsSpace, BaseEnumsSpace } from '@/enums';
import { useOnInit, useSubscribeFilter } from '@/hooks';
import {
  BaseInfoContextType,
  BaseInfoManager,
  modalDataStoreInstance,
  ModalList,
  UserInfoManager,
} from '@/managers';
import { Toast } from '@/nativeComponents';
import { RouterConfig } from '@/routes';
import { nav, onGetAppVersionAndUpdate, TrackEvent } from '@/utils';
import { useCallback, useMemo, useState } from 'react';
import { Linking } from 'react-native';
import { UserInfoContextType } from 'src/managers/userInfo';
import { BaseConfig } from '../../../baseConfig';
import { useNameSpace } from '../../../i18n';
import { trackCommonEvent } from '@/trackEvent';
import {
  fetchExcessPaymentBalanceWithdraw,
  fetchExcessPaymentCostData,
  fetchExcessPaymentBalance,
} from '@/server';
import { UserVOSpace } from '@/types';
import { Text, View } from '@/components';
import React from 'react';

type TUserInfoSubject = {
  name: string;
  phoneNumber: string;
};

type TBaseInfoSubject = {
  isSetPassword: boolean;
};

export default function useData() {
  const [showLogoutDialogVisibe, setShowLogoutTipsDialogVisibe] = useState<boolean>(false);

  const { phoneNumber, name } = useSubscribeFilter({
    subject: UserInfoManager.messageCenter,
    filter: (subject: UserInfoContextType) => {
      return {
        name: subject.userModel.userState.name,
        phoneNumber: subject.userModel.mobile,
      };
    },
  }) as TUserInfoSubject;

  const { isSetPassword } = useSubscribeFilter({
    subject: BaseInfoManager.messageCenter,
    filter: (subject: BaseInfoContextType) => {
      return {
        isSetPassword: subject.baseModel.isSetPassword,
      };
    },
  }) as TBaseInfoSubject;

  const [excessRepayBalance, setExcessRepayBalance] = useState<string>('0');

  const t = useNameSpace('myString').t;
  const t_excessPayment = useNameSpace('excessPaymentString').t;

  const getExcessPaymentCostData = async () => {
    let result = await fetchExcessPaymentBalance();
    if (result.code === BaseEnumsSpace.ENetworkStatus.SUCCESS) {
      setExcessRepayBalance(result.data.excessRepayBalance);
    }
  };

  /** 初始化方法 */
  const { loading, refreshing, onRefresh } = useOnInit({
    callback: getExcessPaymentCostData,
    refreshCallback: getExcessPaymentCostData,
    pageKey: HitPointEnumsSpace.EPageKey.P_CONFIGURARION,
  });

  const handleLogout = useCallback(() => {
    setShowLogoutTipsDialogVisibe(true);
  }, []);

  const onCancelLogout = useCallback(async () => {
    setShowLogoutTipsDialogVisibe(false);
  }, []);

  const onConfirmLogout = useCallback(async () => {
    setShowLogoutTipsDialogVisibe(false);
    await TrackEvent.uploadEventLog();
    // 退出登录。需要弹窗提示
    UserInfoManager.clearAll();
    nav.nextToTopRouter(RouterConfig.ENTER_PHONE_NUMBER as any);
  }, []);

  const handleGoPersonalInfo = useCallback(async () => {
    nav.navigate(RouterConfig.PERSONAL_INFO as any);
  }, []);

  const handleGoFaq = useCallback(async () => {
    nav.navigate(RouterConfig.FAQ as any);
  }, []);

  const handleGoBankCardList = useCallback(async () => {
    nav.navigate(RouterConfig.BANK_CARD_LIST as any);
  }, []);

  const handleCheckVersion = useCallback(async () => {
    if (await onGetAppVersionAndUpdate()) {
      Toast(t('peso_current_is_newest_version'));
    }
  }, []);

  const handleOpenHotLine = useCallback(async () => {
    modalDataStoreInstance.openModal({
      key: ModalList.HOT_LINE,
    });
  }, []);

  const handleOpenPrivacy = useCallback(async () => {
    nav.navigate(RouterConfig.PRIVACY_SCREEN as any);
  }, []);

  const handleGoModifyPwd = useCallback(() => {
    nav.navigate(RouterConfig.MODIFY_PWD as any);
  }, []);

  const handleGoSetPwd = useCallback(() => {
    nav.navigate(RouterConfig.MODIFY_PWD as any);
  }, []);

  const handleGoCouponList = useCallback(() => {
    nav.navigate(RouterConfig.COUPON_LIST as any);
  }, []);

  const handleGoVipRelus = useCallback(() => {
    UserInfoManager.getVipConfigAndNavigate({});
    trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_CONFIGURARION,
        e: HitPointEnumsSpace.EEventKey.BTN_VIP_CASHBACK,
      },
      '1',
    );
  }, []);

  const handleModifyMobile = useCallback(() => {
    nav.navigate(RouterConfig.VERIFY_PWD as any);
  }, []);

  const handleGoDeleteUser = useCallback(() => {
    Linking.openURL(`${BaseConfig.h5Url}${BaseConfig.deleteAccountUrl}`);
  }, []);
  // facebook gmail 绑定
  const handleAccountBinding = useCallback(() => {
    nav.navigate(RouterConfig.ACCOUNT_BINDING as any);
  }, []);

  // facebook gmail 绑定
  const handleLoanRecord = useCallback(() => {
    nav.navigate(RouterConfig.LOAN_RECORD as any);
  }, []);

  const version = useMemo(() => {
    return BaseConfig.appVersionName || '';
  }, []);

  // 提现试算
  const handleExcessPaymentCal = useCallback(async () => {
    trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_CONFIGURARION,
        e: HitPointEnumsSpace.EEventKey.BTN_OVERPAYMENT,
      },
      '1',
    );

    let result = await fetchExcessPaymentCostData();
    if (result.code === BaseEnumsSpace.ENetworkStatus.SUCCESS) {
      const { isWithdraw, modalType, withdrawFee } = result.data;
      if (isWithdraw === 'YES') {
        costCalModal(result.data);
        return;
      }
      cannotWithdrawModal(modalType, withdrawFee);
    }
  }, []);

  // 不能提现弹窗
  const cannotWithdrawModal = useCallback((modalType: string, withdrawFee: string) => {
    let imageIconKey = modalType === 'noMoney' ? '_epModalNormalIcon' : '_epModalWarningIcon';
    let i18nModalKey = modalType === 'noMoney' ? 'excessPaymentString.noMoneyNotice' : undefined;
    let contentKey =
      modalType === 'notEnough'
        ? t_excessPayment('moneyLessFeeNotice', {
            amount: `${withdrawFee.toFormatFinance(false)}`,
          })
        : undefined;

    const $children = (
      <View margin="16 12 8 12 " padding="4 24 0 24">
        <Text isCenter i18nKey={i18nModalKey} textContent={contentKey}></Text>
      </View>
    );

    modalDataStoreInstance.openModal({
      key: ModalList.INFO_PROMPT_CONFIRM,
      buttonType: 'vertical-special-1',
      imageKey: imageIconKey,
      children: $children,
      confirmBtnName: 'btnString.close',
      isBackdropClose: true,
    });
  }, []);

  // 试算弹窗
  const costCalModal = useCallback((param: UserVOSpace.ExcessPaymentCostData) => {
    // todo 溢缴款提现按钮埋点
    // TrackEvent.trackCommonEvent(
    //   {
    //     p: HitPointEnumsSpace.EPageKey.P_CREDIT_TBC,
    //     e: HitPointEnumsSpace.EEventKey.BTN_CREDIT_REFUSE,
    //   },
    //   '1',
    // );
    const { excessRepayBalance, withdrawFee, withdrawAmount, cardNo, bankName } = param;

    const $withdrawCost = (
      <>
        <View
          margin="16 12 0 12"
          padding="12 16 12 16"
          style={{ backgroundColor: 'background-color-100', borderRadius: 8 }}>
          <View layoutStrategy="flexRowBetweenCenter" margin="4 0 4 0" padding="2 0 2 0">
            <Text
              style={{ color: 'text-color-600' }}
              category="p1"
              i18nKey={'excessPaymentString.excessAmount'}
            />
            <Text category="p1" textContent={excessRepayBalance.toFormatFinance()} />
          </View>
          <View layoutStrategy="flexRowBetweenCenter" margin="4 0 4 0" padding="2 0 2 0">
            <Text
              style={{ color: 'text-color-600' }}
              category="p1"
              i18nKey={'excessPaymentString.withdrawFee'}
            />
            <Text category="p1" textContent={withdrawFee.toFormatFinance()} />
          </View>
          <View layoutStrategy="flexRowBetweenCenter" margin="4 0 4 0" padding="2 0 2 0">
            <Text
              style={{ color: 'text-color-600' }}
              category="p1"
              i18nKey={'excessPaymentString.withdrawAmount'}
            />
            <Text category="p1" textContent={withdrawAmount.toFormatFinance()} />
          </View>
          <View layoutStrategy="flexRowBetweenCenter" margin="4 0 4 0" padding="2 0 2 0">
            <Text
              style={{ color: 'text-color-600' }}
              category="p1"
              i18nKey={'excessPaymentString.bankName'}
            />
            <Text category="p1" textContent={bankName} />
          </View>
          <View layoutStrategy="flexColumnStart" margin="4 0 4 0" padding="2 0 2 0">
            <Text
              style={{ color: 'text-color-600' }}
              category="p1"
              i18nKey={'excessPaymentString.cardNo'}
            />
            <Text margin="8 0 0 0" category="h3" textContent={cardNo.toFormatClabe()} />
          </View>
        </View>
        <Text isCenter margin="16 32 8 32">
          <Text style={{ color: 'danger-color-500' }} category="p1" textContent="* " />
          <Text
            style={{ color: 'text-color-600' }}
            category="p1"
            i18nKey={'excessPaymentString.costExtraTips'}
          />
        </Text>
      </>
    );

    modalDataStoreInstance.openModal({
      key: ModalList.INFO_PROMPT_CONFIRM,
      buttonType: 'vertical-special-1',
      isBottomIconColse: true,
      titleKey: 'excessPaymentString.costTitle',
      confirmBtnName: 'btnString.confirm',
      isBackdropClose: true,
      children: $withdrawCost,
      confirmBtnCallback: async () => {
        let result = await fetchExcessPaymentBalanceWithdraw();
        if (result.code === BaseEnumsSpace.ENetworkStatus.SUCCESS) {
          withdrawSucModal();
          setExcessRepayBalance('0');
        }
      },
    });
  }, []);

  const $withdrawSuccessChildren = useMemo(() => {
    return (
      <View margin="16 12 8 12 " padding="4 24 0 24">
        <Text isCenter i18nKey="excessPaymentString.withdrawalSuccessful"></Text>
      </View>
    );
  }, []);

  // 提现成功弹窗
  const withdrawSucModal = useCallback(() => {
    // todo 确认按钮埋点
    // TrackEvent.trackCommonEvent(
    //   {
    //     p: HitPointEnumsSpace.EPageKey.P_CREDIT_TBC,
    //     e: HitPointEnumsSpace.EEventKey.BTN_CREDIT_REFUSE,
    //   },
    //   '1',
    // );

    modalDataStoreInstance.openModal({
      key: ModalList.INFO_PROMPT_CONFIRM,
      buttonType: 'vertical-special-1',
      imageKey: '_epModalSuccessIcon',
      children: $withdrawSuccessChildren,
      confirmBtnName: 'btnString.confirm',
      isBackdropClose: true,
      confirmBtnCallback: () => {
        // todo 触发埋点直接关闭弹窗
        // TrackEvent.trackCommonEvent(
        //   {
        //     p: HitPointEnumsSpace.EPageKey.P_CREDIT_TBC,
        //     e: HitPointEnumsSpace.EEventKey.BTN_REMIND_CHOOSE,
        //   },
        //   '1',
        // );
      },
    });
  }, []);

  return {
    showLogoutDialogVisibe,
    version,
    phoneNumber,
    name,
    handleLogout,
    onCancelLogout,
    onConfirmLogout,
    handleGoPersonalInfo,
    handleGoFaq,
    handleGoBankCardList,
    handleCheckVersion,
    handleOpenHotLine,
    handleOpenPrivacy,
    handleGoModifyPwd,
    handleModifyMobile,
    handleGoDeleteUser,
    handleAccountBinding,
    handleGoSetPwd,
    isSetPassword,
    handleGoVipRelus,
    handleLoanRecord,
    handleGoCouponList,
    t,
    handleExcessPaymentCal,
    excessRepayBalance,
    onRefresh,
    refreshing,
  };
}
