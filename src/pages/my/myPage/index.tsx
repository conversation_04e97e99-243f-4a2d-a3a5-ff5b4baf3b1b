/* eslint-disable react-native/no-inline-styles */

import { Button, Cell, Image, Layout, Text, TopNavigation, View } from '@/components';
import { BaseEnumsSpace } from '@/enums';
import React, { ReactElement, useMemo } from 'react';
import { RefreshControl, ScrollView, TouchableWithoutFeedback } from 'react-native';
import ActivitySwiper from '../../components/activitySwiper';
import LogoutConfirmDialog from './components/logoutConfirmDialog';
import useData from './useData';
import { useVipFuncSwitch } from '@/hooks';
import VipEnterIcon from './components/vipEnterIcon';
import VipUserIcon from './components/vipUserIcon';
import { Strings } from '@/i18n';
export default (): ReactElement => {
  const {
    version,
    phoneNumber,
    name,
    showLogoutDialogVisibe,
    handleLogout,
    onCancelLogout,
    onConfirmLogout,
    handleGoPersonalInfo,
    handleGoFaq,
    handleGoBankCardList,
    handleCheckVersion,
    handleOpenHotLine,
    handleOpenPrivacy,
    handleGoModifyPwd,
    handleModifyMobile,
    handleGoDeleteUser,
    handleAccountBinding,
    handleGoVipRelus,
    handleLoanRecord,
    handleGoCouponList,
    t,
    isSetPassword,
    handleGoSetPwd,
    handleExcessPaymentCal,
    excessRepayBalance,
    refreshing,
    onRefresh,
  } = useData();

  const $rendSetPwdOrAlterPwd = useMemo(() => {
    if (isSetPassword) {
      return (
        <Cell
          padding="16 12 16 12"
          leftIconKey="_lockIcon"
          titleKey="myString.reset_pwd"
          style={{
            backgroundColor: 'background-color-0',
            // borderBottomWidth: 1,
            // borderColor: 'line-color-200',
          }}
          onClick={handleGoModifyPwd}
        />
      );
    } else {
      return (
        <Cell
          padding="16 12 16 12"
          leftIconKey="_lockIcon"
          titleKey="myString.set_pwd"
          style={{
            backgroundColor: 'background-color-0',
            // borderBottomWidth: 1,
            // borderColor: 'line-color-200',
          }}
          onClick={handleGoSetPwd}
        />
      );
    }
  }, [isSetPassword]);

  const isVipFuncSwitch = useVipFuncSwitch();

  const $vipUserIcon = useMemo(() => {
    if (isVipFuncSwitch) {
      return <VipUserIcon />;
    }
  }, []);

  const $userIcon = useMemo(() => {
    if (!isVipFuncSwitch) {
      return <Image name="_userIcon" />;
    }
  }, [isVipFuncSwitch]);

  return (
    <>
      <Layout pLevel="0" level="1" topCompensateColor="primary-color-500">
        <TopNavigation showLogoAction={true} showMessage type="primary" bottomLine={false} />
        <ScrollView
          keyboardShouldPersistTaps="always"
          overScrollMode={'never'}
          refreshControl={
            <RefreshControl colors={['#4C6EFF']} refreshing={refreshing} onRefresh={onRefresh} />
          }>
          <View
            style={{
              height: 280,
              backgroundColor: 'primary-color-500',
            }}
          />
          <View margin="-280 0 0 0">
            <View margin="0 0 0 8" padding="28 24 0 24">
              {$vipUserIcon}
            </View>
            <View
              layoutStrategy="flexRowStartCenter"
              padding="14 24 16 24"
              style={{
                zIndex: 1,
                overflow: 'hidden',
              }}>
              {$userIcon}
              <View margin="0 0 0 12">
                <Text
                  category="p2"
                  textContent={name || t('name')}
                  style={{
                    color: 'text-color-0',
                  }}
                />
                <View
                  style={{
                    display: 'flex',
                    flexDirection: 'row',
                    alignItems: 'center',
                    flexWrap: 'wrap',
                  }}>
                  <Text
                    margin="8 0 0 0"
                    category="p2"
                    textContent={`+52 ${phoneNumber}`}
                    style={{
                      color: 'text-color-0',
                    }}
                  />
                </View>
              </View>
            </View>
            <View
              margin="12 16 0 16"
              padding="12 8 12 8"
              layoutStrategy="flexRowAroundCenter"
              cardType="baseType">
              <VipEnterIcon onPress={handleGoVipRelus} />
              <Cell
                leftIconKey="_myCouponIcon"
                titleKey="myString.coupon"
                type="grid"
                onClick={handleGoCouponList}
              />
              <Cell
                leftIconKey="_myRecordIcon"
                titleKey="myString.loanRecord"
                type="grid"
                onClick={handleLoanRecord}
              />
            </View>
            <ActivitySwiper
              margin="12 16 0 16"
              location={BaseEnumsSpace.EBannerLocationType.MINE}
            />
            <View margin="12 16 0 16" cardType="baseType">
              <Cell
                padding="16 12 16 12"
                leftIconKey="_accountBinding"
                rightIconKey="_amountUpIcon"
                titleKey="myString.accountBinding"
                style={{
                  backgroundColor: 'background-color-0',
                }}
                onClick={handleAccountBinding}
              />
              <Cell
                padding="16 12 16 12"
                leftIconKey="_personalInfoIcon"
                titleKey={Strings.myString.personalInfo}
                style={{
                  backgroundColor: 'background-color-0',
                }}
                onClick={handleGoPersonalInfo}
              />
              <Cell
                padding="16 12 16 12"
                leftIconKey="_faqIcon"
                titleKey="myString.faq"
                style={{
                  backgroundColor: 'background-color-0',
                }}
                onClick={handleGoFaq}
              />
              <Cell
                padding="16 12 16 12"
                leftIconKey="_privacyIcon"
                titleKey="myString.safety"
                style={{
                  backgroundColor: 'background-color-0',
                }}
                onClick={handleOpenPrivacy}
              />
              <Cell
                padding="16 12 16 12"
                leftIconKey="_hotlineIcon"
                titleKey="myString.service"
                style={{
                  backgroundColor: 'background-color-0',
                }}
                onClick={handleOpenHotLine}
              />
              <Cell
                padding="16 12 16 12"
                leftIconKey="_cardIcon"
                titleKey="myString.bankInfo"
                style={{
                  backgroundColor: 'background-color-0',
                }}
                onClick={handleGoBankCardList}
              />
              <Cell
                padding="16 12 16 12"
                leftIconKey="_excessPaymentIcon"
                titleKey="excessPaymentString.title"
                rigthAmount={excessRepayBalance}
                style={{
                  backgroundColor: 'background-color-0',
                }}
                onClick={handleExcessPaymentCal}
              />
            </View>
            <View margin="12 16 0 16" cardType="baseType">
              <Cell
                padding="16 12 16 12"
                leftIconKey="_mobileIcon"
                titleKey="myString.modify_mobile"
                style={{
                  backgroundColor: 'background-color-0',
                }}
                onClick={handleModifyMobile}
              />
              {/* 设置密码还是修改密码 */}
              {$rendSetPwdOrAlterPwd}
              <Cell
                padding="16 12 16 12"
                leftIconKey="_transhIcon"
                titleKey="myString.delete_user"
                // contentKey="myString.delete_user_desc"
                onClick={handleGoDeleteUser}
              />
            </View>
            <Button
              margin="16 16 0 16"
              status="primary"
              onPress={handleLogout}
              textI18nKey="btnString.logoutApp"
            />
            <TouchableWithoutFeedback onPress={handleCheckVersion}>
              <Text margin="12 0 16 0" isCenter={true}>
                <Text
                  style={{
                    color: 'text-color-600',
                  }}
                  i18nKey={'myString.peso_version_flag'}
                />
                <Text
                  style={{
                    color: 'text-color-600',
                  }}
                  textContent={`${version}   `}
                />
                <Text
                  style={{ color: 'primary-color-500' }}
                  i18nKey={'myString.peso_deteccion_flag'}
                />
              </Text>
            </TouchableWithoutFeedback>
          </View>
        </ScrollView>
        <LogoutConfirmDialog
          onConfrim={onConfirmLogout}
          onCancel={onCancelLogout}
          visible={showLogoutDialogVisibe}
        />
      </Layout>
    </>
  );
};
