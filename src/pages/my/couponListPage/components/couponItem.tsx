import { Text, View } from '@/components';
import { UserEnumsSpace } from '@/enums';
import { useNameSpace } from '@/i18n';
import { UserVOSpace } from '@/types';
import { needFixPixel } from '@/utils';
import React, { useMemo } from 'react';

type CouponItemPropsType = {
  data: UserVOSpace.CouponsItem;
};

/** 优惠券卡片 */
const CouponItem = (props: CouponItemPropsType) => {
  const t = useNameSpace().t;
  /** 金额展示样式 */
  const StyleAmountColor = useMemo(() => {
    switch (props.data.couponStatus) {
      case UserEnumsSpace.ECouponsStatus.AVAILABLE:
        return {
          color: 'secondary-color-500',
        };

      case UserEnumsSpace.ECouponsStatus.BINDING:
      case UserEnumsSpace.ECouponsStatus.EXPIRED:
      case UserEnumsSpace.ECouponsStatus.HX:
      case UserEnumsSpace.ECouponsStatus.VOIDED:
        return {
          color: 'text-color-600',
        };
    }
    return {};
  }, [props.data.couponStatus]);

  /** 文本展示样式 */
  const StyleTextColor = useMemo(() => {
    switch (props.data.couponStatus) {
      case UserEnumsSpace.ECouponsStatus.AVAILABLE:
        return {
          color: 'text-color-800',
        };

      case UserEnumsSpace.ECouponsStatus.BINDING:
      case UserEnumsSpace.ECouponsStatus.EXPIRED:
      case UserEnumsSpace.ECouponsStatus.HX:
      case UserEnumsSpace.ECouponsStatus.VOIDED:
        return {
          color: 'text-color-600',
        };
    }
    return {};
  }, [props.data.couponStatus]);

  /** 标签展示样式 */
  const $couponTips = useMemo(() => {
    const width = needFixPixel() ? 90 : 75;

    switch (props.data.couponStatus) {
      case UserEnumsSpace.ECouponsStatus.AVAILABLE:
        return <></>;
      case UserEnumsSpace.ECouponsStatus.BINDING:
        return (
          <View
            margin="0 4 0 0"
            padding="4 6 4 6"
            style={{ backgroundColor: '#EAEAEB', borderRadius: 4, width }}>
            <Text
              style={{ color: '#000000' }}
              bold="700"
              isCenter={true}
              category={needFixPixel() ? 'c1' : 'c2'}
              i18nKey="couponListString.binding"
            />
          </View>
        );
      case UserEnumsSpace.ECouponsStatus.EXPIRED:
        return (
          <View
            margin="0 4 0 0"
            padding="4 6 4 6"
            style={{ backgroundColor: '#EAEAEB', borderRadius: 4, width }}>
            <Text
              style={{ color: '#000000' }}
              bold="700"
              isCenter={true}
              category={needFixPixel() ? 'c1' : 'c2'}
              i18nKey="couponListString.expired"
            />
          </View>
        );
      case UserEnumsSpace.ECouponsStatus.HX:
        return (
          <View
            margin="0 4 0 0"
            padding="4 6 4 6"
            style={{ backgroundColor: '#EFFBDC', borderRadius: 4, width }}>
            <Text
              style={{ color: '#549C33' }}
              bold="700"
              isCenter={true}
              category={needFixPixel() ? 'c1' : 'c2'}
              i18nKey="couponListString.hx"
            />
          </View>
        );
      case UserEnumsSpace.ECouponsStatus.VOIDED:
        return (
          <View
            margin="0 4 0 0"
            padding="4 6 4 6"
            style={{ backgroundColor: '#EAEAEB', borderRadius: 4, width }}>
            <Text
              style={{ color: '#000000' }}
              bold="700"
              isCenter={true}
              category={needFixPixel() ? 'c1' : 'c2'}
              i18nKey="couponListString.voided"
            />
          </View>
        );
    }

    return <></>;
  }, [props.data.couponStatus]);

  /** 优惠券名称展示 */
  const I18nKeyCounponName = useMemo(() => {
    switch (props.data.type) {
      case UserEnumsSpace.ECouponsType.DEDUCTION:
        return 'couponListString.deductionCouponName';

      case UserEnumsSpace.ECouponsType.INCREASE:
        return 'couponListString.increaseCouponName';
    }
  }, [props.data.type]);

  return (
    <View padding="8 12 8 12" margin="0 16 16 16" cardType="baseType">
      <View
        padding="12 0 12 0"
        style={{
          position: 'relative',
          flexDirection: 'row',
          alignItems: 'center',
        }}>
        <View
          margin="0 0 0 0"
          style={{
            position: 'relative',
            flexDirection: 'row',
            alignItems: 'baseline',
            maxWidth: 80,
            alignContent: 'space-around',
          }}>
          <Text
            style={{ ...StyleAmountColor }}
            category={needFixPixel() ? 'p1' : 'p2'}
            textContent={`$ `}
          />
          <Text
            style={{ ...StyleAmountColor }}
            bold="bold"
            category={needFixPixel() ? 'h1' : 'h2'}
            textContent={`${Number(props.data.amount).toFixed(0)}`}
          />
        </View>
        <View margin="0 0 0 16" style={{ flex: 1 }}>
          <Text
            style={{ ...StyleTextColor }}
            bold="600"
            category={needFixPixel() ? 'p1' : 'p2'}
            i18nKey={I18nKeyCounponName}
          />
          <Text
            margin="4 0 0 0"
            style={{ ...StyleTextColor }}
            category={needFixPixel() ? 'c1' : 'c2'}
            textContent={t('couponListString.couponShowLastDate', {
              expirationDate: props.data.expirationDate,
            })}
          />
        </View>
        {$couponTips}
      </View>
      <View
        padding="8 0 8 0"
        style={{
          borderTopColor: 'line-color-200',
          borderTopWidth: 1,
          borderStyle: 'dashed',
        }}
        layoutStrategy="flexRowStartCenter">
        <Text
          category={needFixPixel() ? 'c1' : 'c2'}
          style={{
            color: 'text-color-600',
          }}
          i18nKey="couponListString.useDesction"
        />
      </View>
    </View>
  );
};

export default React.memo(CouponItem);
