import { Image, Text, View } from '@/components';
// import CouponItem from './couponItem';
import { UserVOSpace } from '@/types';
import React, { useMemo } from 'react';
import { FlatList } from 'react-native';
import CouponItem from '../../../components/coupon/CouponItem';
type CouponListPropsType = {
  dataList: UserVOSpace.CouponsItem[];
  page: number;
};

const CouponList = (props: CouponListPropsType) => {
  const { page } = props;
  const $renderFooter = useMemo(() => {
    if (props.dataList?.length === 0) {
      return (
        <View
          margin="0 16 0 16"
          padding="16 16 16 16"
          style={{
            backgroundColor: 'background-color-0',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-around',
          }}>
          <Text
            width={'100%'}
            isCenter
            category="p2"
            style={{ color: 'text-color-600' }}
            i18nKey="couponListString.noData"
          />
        </View>
      );
    } else {
      return <></>;
    }
  }, [props.dataList]);

  const $renderHeader = useMemo(() => {
    if (page === 1 || props.dataList?.length) {
      return <></>;
    }
    return (
      <View
        padding="16 16 16 16"
        style={{
          backgroundColor: 'background-color-0',
          borderRadius: 8,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'flex-start',
          flexDirection: 'row',
        }}>
        <Image margin="0 12 0 0" name="_myWarnIcon" />
        <Text
          style={{ color: 'text-color-700 ' }}
          i18nKey="couponListString.useSence"
          category="p1"
        />
      </View>
    );
  }, [props.page, props.dataList]);
  return (
    <View padding="16 16 0 16" style={{ flex: 1 }}>
      <FlatList
        data={props.dataList}
        renderItem={({ item }) => {
          return <CouponItem data={item} />;
        }}
        // ListHeaderComponent={$renderHeader}
        ListFooterComponent={$renderHeader}
      />
    </View>
  );
};

export default React.memo(CouponList);
