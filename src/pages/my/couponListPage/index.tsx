import { Layout, Text, TopNavigation, View } from '@/components';
import { BaseEnumsSpace, HitPointEnumsSpace, UserEnumsSpace } from '@/enums';
import { useOnInit } from '@/hooks';
import { BaseInfoManager } from '@/managers';
import { fetchCouponsByConditions, fetchAllCouponsByConditions } from '@/server';
import { UserVOSpace } from '@/types';
import ReactNativeViewPager, { PagerViewOnPageSelectedEvent } from 'react-native-pager-view';
import { memo, ReactElement, useCallback, useMemo, useRef, useState } from 'react';
import { TouchableOpacity } from 'react-native';
import CouponList from './components/couponListPage';

const useData = () => {
  /** 优惠券列表 */
  const [availableCouponList, setAvailableCouponList] = useState<UserVOSpace.CouponsItem[]>([]);
  const [unavailableCouponList, setUnavailableCouponList] = useState<UserVOSpace.CouponsItem[]>([]);

  const viewPagerRef = useRef<ReactNativeViewPager>(null);

  const [page, setPage] = useState<number>(0);

  const onChangePage = useCallback(
    async (event: PagerViewOnPageSelectedEvent) => {
      const position = event.nativeEvent.position;
      setPage(position);
      BaseInfoManager.changeLoadingModalVisible(true);
      if (position === 1) {
        await fetchCouponsByConditionsAsync(UserEnumsSpace.ECouponsStatus.UNAVAILABLE);
      } else {
        await fetchCouponsByConditionsAsync(UserEnumsSpace.ECouponsStatus.AVAILABLE);
      }
      BaseInfoManager.changeLoadingModalVisible(false);
    },
    [setAvailableCouponList, setUnavailableCouponList],
  );

  const onSetPage = useCallback((page: number) => {
    viewPagerRef.current?.setPage(page);
    setPage(page);
  }, []);

  /** 初始化 */
  useOnInit({
    callback: async () => {
      BaseInfoManager.changeLoadingModalVisible(true);
      if (page === 1) {
        await fetchCouponsByConditionsAsync(UserEnumsSpace.ECouponsStatus.UNAVAILABLE);
      } else {
        await fetchCouponsByConditionsAsync(UserEnumsSpace.ECouponsStatus.AVAILABLE);
      }
      BaseInfoManager.changeLoadingModalVisible(false);
    },
  });

  /** 根据条件获取优惠券 */
  const fetchCouponsByConditionsAsync = async (
    status: UserEnumsSpace.ECouponsStatus.AVAILABLE | UserEnumsSpace.ECouponsStatus.UNAVAILABLE,
  ) => {
    let result = await fetchAllCouponsByConditions({
      status,
    });
    if (result.code === BaseEnumsSpace.ENetworkStatus.SUCCESS) {
      switch (status) {
        case UserEnumsSpace.ECouponsStatus.AVAILABLE:
          setAvailableCouponList(result.data);
          break;
        case UserEnumsSpace.ECouponsStatus.UNAVAILABLE:
          setUnavailableCouponList(result.data);
          break;
      }
    }
  };

  return {
    onChangePage,
    availableCouponList,
    unavailableCouponList,
    page,
    viewPagerRef,
    onSetPage,
  };
};

const CouponListPage = (): ReactElement => {
  const {
    onChangePage,
    availableCouponList,
    unavailableCouponList,
    page,
    viewPagerRef,
    onSetPage,
  } = useData();

  const TabBarList = [
    {
      key: 0,
      status: UserEnumsSpace.ECouponsStatus.AVAILABLE,
      titleI18n: 'couponListString.available',
      color: 'primary-color-500',
      backgroundColor: 'primary-color-500',
    },
    {
      key: 1,
      status: UserEnumsSpace.ECouponsStatus.UNAVAILABLE,
      titleI18n: 'couponListString.unavailable',
      color: 'text-color-600',
      backgroundColor: 'transparent',
    },
  ];

  /** 顶部tab栏 */
  const $renderTabBar = useMemo(() => {
    return (
      <View
        layoutStrategy="flexRowBetweenCenter"
        width={'100%'}
        height={40}
        cardType="baseType"
        style={
          {
            // borderBottomWidth: 1,
            // borderColor: 'line-color-200',
          }
        }>
        {/* {
          TabBarList.map((TabBarItem, TabBarIndex) => {
            return <TouchableOpacity key={TabBarItem.key} style={{ flex: 1 }} onPress={() => onSetPage(TabBarIndex)}>
              <View
                style={{ flex: 1 }}
                padding="4 0 4 0"
                layoutStrategy="flexColumnStartCenter">
                <View layoutStrategy="flexColumnStartCenter">
                  <Text
                    i18nKey={TabBarItem.titleI18n}
                    style={{
                      color: TabBarItem.color,
                    }}
                  />
                  <View
                    width={40}
                    height={3}
                    margin="9 0 0 0"
                    style={{
                      backgroundColor: TabBarItem.backgroundColor,
                    }}
                  />
                  <View
                    style={{
                      display: 'none',
                      minWidth: 6,
                      minHeight: 6,
                      position: 'absolute',
                      top: 4,
                      right: -6,
                      borderRadius: 3,
                      backgroundColor: 'danger-color-500',
                    }}
                  />
                </View>
              </View>
            </TouchableOpacity>
          })
        } */}
        <TouchableOpacity style={{ flex: 1 }} onPress={() => onSetPage(0)}>
          <View style={{ flex: 1 }} padding="4 0 4 0" layoutStrategy="flexColumnStartCenter">
            <View layoutStrategy="flexColumnStartCenter">
              <Text
                i18nKey="couponListString.available"
                style={{
                  color: page === 0 ? 'primary-color-500' : 'text-color-600',
                }}
              />
              <View
                width={40}
                height={3}
                margin="9 0 0 0"
                style={{
                  backgroundColor: page === 0 ? 'primary-color-500' : 'transparent',
                }}
              />
              <View
                style={{
                  display: 'none',
                  minWidth: 6,
                  minHeight: 6,
                  position: 'absolute',
                  top: 4,
                  right: -6,
                  borderRadius: 3,
                  backgroundColor: 'danger-color-500',
                }}
              />
            </View>
          </View>
        </TouchableOpacity>
        <TouchableOpacity style={{ flex: 1 }} onPress={() => onSetPage(1)}>
          <View style={{ flex: 1 }} padding="4 0 4 0" layoutStrategy="flexColumnStartCenter">
            <View layoutStrategy="flexColumnStartCenter">
              <Text
                i18nKey="couponListString.unavailable"
                style={{
                  color: page === 1 ? 'primary-color-500' : 'text-color-600',
                }}
              />
              <View
                width={40}
                height={3}
                margin="9 0 0 0"
                style={{
                  backgroundColor: page === 1 ? 'primary-color-500' : 'transparent',
                }}
              />
              <View
                style={{
                  display: 'none',
                  minWidth: 6,
                  minHeight: 6,
                  position: 'absolute',
                  top: 4,
                  right: -6,
                  borderRadius: 3,
                  backgroundColor: 'danger-color-500',
                }}
              />
            </View>
          </View>
        </TouchableOpacity>
      </View>
    );
  }, [page]);

  return (
    <>
      <Layout pLevel="0" level="1">
        <TopNavigation titleKey="couponListString.title" bottomLine={false} />
        {$renderTabBar}
        <ReactNativeViewPager
          ref={viewPagerRef}
          style={{ flex: 1 }}
          initialPage={page}
          orientation="horizontal"
          onPageSelected={onChangePage}
          useNext={false}>
          {TabBarList.map((TabBaItem, TabBarIndex) => {
            switch (TabBaItem.status) {
              case UserEnumsSpace.ECouponsStatus.AVAILABLE:
                return (
                  <CouponList key={TabBaItem.key} page={page} dataList={availableCouponList} />
                );
              case UserEnumsSpace.ECouponsStatus.UNAVAILABLE:
                return (
                  <CouponList key={TabBaItem.key} page={page} dataList={unavailableCouponList} />
                );
            }
          })}
        </ReactNativeViewPager>
      </Layout>
    </>
  );
};

export default memo(CouponListPage);
