import {
  Button,
  Image,
  Layout,
  SeaInput,
  Text,
  TopNavigation,
  View,
  AnimatedSmsCodeInput,
  PasswordInput,
} from '@/components';
import { ScreenProps } from '@/types';
import {
  isStringExist,
  verifyPhoneNumber,
  checkTextLength,
  isMoreThanLength,
  isEqual,
} from '@/utils';
import React, { ReactElement, useMemo } from 'react';
import { ScrollView } from 'react-native';
import useData from './useData';
import { Strings } from '@/i18n';
import { t } from 'i18next';
import OtpModal from '../../login/getSmsCodePage/components/OtpModal';
import { CallYouSendSmsModal, ReciveOtpByCallModal } from '@/modals';
import { useLoginInfo } from '@/hooks';
import { HitPointEnumsSpace } from '@/enums';

export default ({ route }: ScreenProps<{}>): ReactElement => {
  const { secureTextEntry, onToggleSecureEntry } = useLoginInfo();
  const {
    phoneNumber,
    setPhoneNumber,
    clickSendPreMethod,
    phoneNumberRef,
    smsCodeRef,
    disabled,
    handleNext,
    handleOtpChannel,
    otpVisible,
    onCloseOtpModal,
    smsCode,
    setSmsCode,
    password,
    rePassword,
    setPassword,
    setRePassword,
    onChangeSecureTextEntry,
    secureTextEntry: reSecureTextEntry,
  } = useData();
  const renderTop = () => {
    return (
      <View
        padding="12 12 12 12"
        style={{ backgroundColor: 'primary-color-100', borderRadius: 8 }}
        layoutStrategy="flexRowStartCenter">
        <Image name="_safeTipIcon" />
        <Text padding="0 12 0 8" i18nKey={Strings.validateBasicInfoString.tip} category="c1" />
      </View>
    );
  };
  const renderPhoneNumberForm = useMemo(() => {
    return (
      <View padding="12 12 12 12" cardType="baseType" margin="12 0 0 0">
        <SeaInput
          type="line"
          ref={phoneNumberRef}
          keyboardType="number-pad"
          prefixMargin="12 0 0 0"
          placeholderKey={Strings.validateBasicInfoString.newPhoneNumber}
          value={phoneNumber}
          setValue={setPhoneNumber}
          validateConfig={[
            {
              condition: isStringExist(phoneNumber),
              info: t('verificationString.required'),
              status: 'danger',
            },
            {
              condition: verifyPhoneNumber(phoneNumber),
              info: t(Strings.verificationString.error),
              status: 'danger',
            },
          ]}
          isShowPlaceholderKey
          pageKey={HitPointEnumsSpace.EPageKey.P_CHANGE_MOBILE_INPUT_NEW}
          eventKey={HitPointEnumsSpace.EEventKey.E_PHONE_NUMBER}
        />
        <AnimatedSmsCodeInput
          type="line"
          ref={smsCodeRef}
          placeholderKey={Strings.validateBasicInfoString.code}
          prefixMargin={'16 0 0 0'}
          smsCode={smsCode}
          setSmsCode={setSmsCode}
          clickSendPreMethod={clickSendPreMethod}
          scenesId="register"
          maxLength={4}
          validateConfig={[
            {
              condition: isStringExist(smsCode),
              info: t('verificationString.required'),
              status: 'danger',
            },
            {
              condition: checkTextLength(smsCode, 4),
              info: t(Strings.verificationString.error),
              status: 'danger',
            },
          ]}
          pageKey={HitPointEnumsSpace.EPageKey.P_CHANGE_MOBILE_INPUT_NEW}
          eventKey={HitPointEnumsSpace.EEventKey.E_PHONE_OTP}
        />
      </View>
    );
  }, [smsCode, phoneNumber]);
  const renderPasswordForm = useMemo(() => {
    return (
      <View padding="12 12 12 12" cardType="baseType" margin="12 0 0 0">
        <Text
          padding="0 0 12 0"
          i18nKey={Strings.validateBasicInfoString.passwordCardTitle}
          style={{ color: 'text-color-600' }}
        />
        <PasswordInput
          type="line"
          validateConfig={[
            {
              condition: isMoreThanLength(password, 6),
              info: t(Strings.validateBasicInfoString.pwdErrorTip),
              status: 'danger',
            },
          ]}
          value={password}
          placeholderKey={Strings.validateBasicInfoString.setPwd}
          secureTextEntry={secureTextEntry}
          toggleSecureEntry={onToggleSecureEntry}
          onChangeText={setPassword}
          pageKey={HitPointEnumsSpace.EPageKey.P_CHANGE_MOBILE_INPUT_NEW}
          eventKey={HitPointEnumsSpace.EEventKey.E_PASSWORD}
        />
        <PasswordInput
          type="line"
          validateConfig={[
            // {
            //   condition: isMoreThanLength(rePassword, 6),
            //   info: t(Strings.validateBasicInfoString.pwdErrorTip),
            //   status: 'danger',
            // },
            {
              condition: isEqual(password, rePassword),
              info: t(Strings.validateBasicInfoString.notEqual),
              status: 'danger',
            },
          ]}
          value={rePassword}
          placeholderKey={Strings.validateBasicInfoString.resetPwd}
          secureTextEntry={reSecureTextEntry}
          toggleSecureEntry={onChangeSecureTextEntry}
          onChangeText={setRePassword}
          pageKey={HitPointEnumsSpace.EPageKey.P_CHANGE_MOBILE_INPUT_NEW}
          eventKey={HitPointEnumsSpace.EEventKey.E_CONFIRM_PASSWORD}
        />
      </View>
    );
  }, [password, rePassword, secureTextEntry, reSecureTextEntry]);
  const renderButton = () => {
    return (
      <View padding="12 32 16 32" style={{ backgroundColor: 'fill-color-0' }}>
        <Button textI18nKey={Strings.btnString.continue} onPress={handleNext} disabled={disabled} />
      </View>
    );
  };
  const renderPwdTip = () => {
    return (
      <View layoutStrategy="flexRowStart" padding="8 0 0 12">
        <Image name="_grayInfo" />
        <Text
          i18nKey={Strings.validateBasicInfoString.setPwdTip}
          style={{ color: 'text-color-600', fontStyle: 'italic' }}
          category="c2"
          padding="0 0 0 8"
        />
      </View>
    );
  };
  return (
    <Layout pLevel="0" level="1">
      <TopNavigation titleKey={Strings.validateBasicInfoString.title} bottomLine />
      <ScrollView
        style={{ paddingHorizontal: 16, paddingTop: 22 }}
        keyboardShouldPersistTaps={'always'}>
        {renderTop()}
        {renderPhoneNumberForm}
        {renderPasswordForm}
        {renderPwdTip()}
      </ScrollView>
      {renderButton()}
      <ReciveOtpByCallModal />
      <CallYouSendSmsModal />
      <OtpModal
        visible={otpVisible}
        onClose={onCloseOtpModal}
        handleOtpChannel={handleOtpChannel}
      />
    </Layout>
  );
};
