import { HitPointEnumsSpace, UserEnumsSpace } from '@/enums';
import { useOnInit, useSubscribeFilter } from '@/hooks';
import { Toast } from '@/nativeComponents';
import { responseHandler, fetchOtp, fetchChangeMobile } from '@/server';
import {
  BaseInfoManager,
  ModalList,
  UserInfoContextType,
  UserInfoManager,
  modalDataStoreInstance,
  BaseInfoContextType,
} from '@/managers';
import { trackCommonEvent } from '@/trackEvent';
import {
  TrackEvent,
  nav,
  verifyPhoneNumber,
  isMoreThanLength,
  checkTextLength,
  isEqual,
} from '@/utils';
import { useCallback, useEffect, useMemo, useState, useRef } from 'react';
import { State } from './type';
import { RouterConfig } from '@/routes';
import { Strings, useNameSpace } from '@/i18n';
import { useSetState } from 'ahooks';
import useAction from './useAction';
import { useSmsCodeHandler } from '../../../hooks/useSmsCodeHandler';

export default function useData() {
  const { actionState, phoneNumberRef, smsCodeRef, ...restAction } = useAction();
  const [state, setState] = useSetState<State>({
    otpVisible: false,
  });
  const { phoneNumber, smsCode, password, rePassword } = {
    ...actionState,
    ...state,
  };
  const ref = useRef({
    phoneNumber: '',
    isStart: false,
  });
  useOnInit({
    pageKey: HitPointEnumsSpace.EPageKey.P_CHANGE_MOBILE_INPUT_NEW,
    callback() {
      smsCodeRef.current?.reStart();
    },
  });
  useEffect(() => {
    ref.current.phoneNumber = phoneNumber;
  }, [phoneNumber]);
  const t = useNameSpace().t;
  useSmsCodeHandler(restAction.setSmsCode);
  const onSendOtp = async (options: {
    otpChannel: UserEnumsSpace.OtpChannel;
    callback?: Function;
    mobile?: string;
  }): Promise<boolean> => {
    const { otpChannel, callback, mobile = '' } = options;
    const params = {
      otpChannel,
      bizType: UserEnumsSpace.BizType.CHANGE_MOBILE,
      mobile,
    };
    BaseInfoManager.changeLoadingModalVisible(true);
    const { error, data } = await responseHandler(fetchOtp(params));
    BaseInfoManager.changeLoadingModalVisible(false);
    if (error) {
      return false;
    }
    callback?.(data);
    return true;
  };
  const onTrackEvent = (event: keyof typeof HitPointEnumsSpace.EEventKey, status: string = '') => {
    TrackEvent.trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_CHANGE_MOBILE_INPUT_NEW,
        e: HitPointEnumsSpace.EEventKey[event],
      },
      status,
    );
  };
  const onSendVoiceCode = async (): Promise<boolean> => {
    onTrackEvent('BTN_OBTAIN_OTP_CALL');
    const params = {
      otpChannel: UserEnumsSpace.OtpChannel.VOICE_PHONE,
      callback: () => {
        modalDataStoreInstance.openModal({
          key: ModalList.RECIVE_OTP_BY_CALL,
          i18nKey: 'modalString.callYouSendOTP',
          confirmBtnName: 'btnString.OK',
          confirmBtnCallback: () => {
            onTrackEvent('BTN_OBTAIN_OTP_CALL_SI');
          },
        });
      },
      mobile: ref.current.phoneNumber,
    };
    return onSendOtp(params);
  };
  const onSendSmsCode = async (): Promise<boolean> => {
    const mobile = ref.current.phoneNumber;
    const params = {
      otpChannel: UserEnumsSpace.OtpChannel.SMS,
      callback: () => {
        ref.current.isStart = true;
        Toast(`${t('homeString.peso_otp_success_tip')}${mobile}`);
      },
      mobile,
    };
    return onSendOtp(params);
  };
  const onSendWhatsappCode = async (): Promise<boolean> => {
    const mobile = ref.current.phoneNumber;
    const params = {
      otpChannel: UserEnumsSpace.OtpChannel.WHATS_APP,
      callback: () => {
        Toast(`${t('homeString.peso_otp_success_tip')}${mobile}`);
      },
      mobile,
    };
    return onSendOtp(params);
  };
  const handleNext = useCallback(async () => {
    const params = {
      mobile: phoneNumber,
      smsCode,
      password,
    };
    onTrackEvent('BTN_NEXT');
    BaseInfoManager.changeLoadingModalVisible(true);
    const { error } = await responseHandler(fetchChangeMobile(params));
    BaseInfoManager.changeLoadingModalVisible(false);
    if (error) {
      return;
    }
    UserInfoManager.updateMobile(phoneNumber);
    modalDataStoreInstance.openModal({
      imageKey: '_changeMobileSuccessIcon',
      key: ModalList.INFO_PROMPT_CONFIRM,
      i18nKey: 'myString.changeMobileTip',
      confirmBtnName: 'btnString.OK',
      isBackdropClose: false,
      confirmBtnCallback: () => {
        nav.resetRouteNavigate(RouterConfig.ENTER_PHONE_NUMBER as any);
      },
    });
  }, [phoneNumber, smsCode, password, rePassword]);
  const disabled = useMemo(() => {
    return !(
      verifyPhoneNumber(phoneNumber) &&
      checkTextLength(smsCode, 4) &&
      (password
        ? isMoreThanLength(password, 6) &&
          isMoreThanLength(rePassword, 6) &&
          isEqual(password, rePassword)
        : true)
    );
  }, [phoneNumber, smsCode, password, rePassword]);
  const onCloseOtpModal = useCallback(() => {
    setState(prevState => {
      return {
        ...prevState,
        otpVisible: false,
      };
    });
  }, []);
  const handleOtpChannel = useCallback(async (otpChannel: UserEnumsSpace.OtpChannel) => {
    onCloseOtpModal();
    const sendMap = {
      [UserEnumsSpace.OtpChannel.VOICE_PHONE]: onSendVoiceCode,
      [UserEnumsSpace.OtpChannel.SMS]: onSendSmsCode,
      [UserEnumsSpace.OtpChannel.WHATS_APP]: onSendWhatsappCode,
    };
    (await sendMap[otpChannel]()) && smsCodeRef.current?.reStartCountDown();
    return true;
  }, []);
  const clickSendPreMethod = useCallback(async () => {
    const { phoneNumber, isStart } = ref.current;
    if (!verifyPhoneNumber(phoneNumber)) {
      phoneNumberRef.current?.emitErrorStatus(
        t('verificationString.failed', {
          name: t(Strings.validateBasicInfoString.newPhoneNumber),
        }),
      );
      return false;
    }
    onTrackEvent('BTN_OBTAIN_OTP');
    await handleOtpChannel(UserEnumsSpace.OtpChannel.SMS);
    // if (!isStart) {
    //   await handleOtpChannel(UserEnumsSpace.OtpChannel.SMS);
    //   return true;
    // }
    // setState(prevState => {
    //   return {
    //     ...prevState,
    //     otpVisible: true,
    //   };
    // });
    return true;
  }, []);

  return {
    ...state,
    ...restAction,
    handleNext,
    clickSendPreMethod,
    disabled,
    phoneNumberRef,
    smsCodeRef,
    handleOtpChannel,
    onCloseOtpModal,
  };
}
