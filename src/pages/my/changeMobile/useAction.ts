import { HitPointEnumsSpace, UserEnumsSpace } from '@/enums';
import { useOnInit } from '@/hooks';
import { Toast } from '@/nativeComponents';
import { responseHandler, fetchOtp, fetchChangeMobile, fetchEmailOtp } from '@/server';
import { ModalList, UserInfoManager } from '@/managers';
import { trackCommonEvent } from '@/trackEvent';
import { TrackEvent, nav, verifyPhoneNumber, checkTextLength } from '@/utils';
import { useCallback, useEffect, useMemo, useState, useRef } from 'react';
import { State, ActionState } from './type';
import { RouterConfig } from '@/routes';
import { Strings } from '@/i18n';
import { BaseInputRefType, AnimatedSmsCodeInputRefType } from '@/components';
import { useSetState } from 'ahooks';
import { t } from 'i18next';

export default function useAction() {
  const [state, setState] = useSetState<ActionState>({
    phoneNumber: '',
    password: '',
    rePassword: '',
    smsCode: '',
    secureTextEntry: true,
  });
  const { phoneNumber } = state;
  const phoneNumberRef = useRef<BaseInputRefType>(null);
  const smsCodeRef = useRef<AnimatedSmsCodeInputRefType>(null);
  const ref = useRef({
    phoneNumber: '',
  });
  useEffect(() => {
    ref.current.phoneNumber = phoneNumber;
  }, [phoneNumber]);
  const setPhoneNumber = useCallback((phoneNumber: string) => {
    setState(prevState => {
      return {
        ...prevState,
        phoneNumber,
      };
    });
  }, []);
  const setSmsCode = useCallback((smsCode: string) => {
    setState(prevState => {
      return {
        ...prevState,
        smsCode,
      };
    });
  }, []);
  const setPassword = useCallback((password: string) => {
    setState(prevState => {
      return {
        ...prevState,
        password,
      };
    });
  }, []);
  const setRePassword = useCallback((rePassword: string) => {
    setState(prevState => {
      return {
        ...prevState,
        rePassword,
      };
    });
  }, []);
  const onChangeSecureTextEntry = useCallback(() => {
    setState(prevState => {
      return {
        ...prevState,
        secureTextEntry: !prevState.secureTextEntry,
      };
    });
  }, []);
  return {
    actionState: state,
    ...state,
    phoneNumberRef,
    smsCodeRef,
    setPhoneNumber,
    setSmsCode,
    setPassword,
    setRePassword,
    onChangeSecureTextEntry,
  };
}
