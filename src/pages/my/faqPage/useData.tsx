import { BaseInfoManager } from '@/managers';
import { fetchFaqConfig } from '@/server';
import { UserVOSpace } from '@/types';
import { useCallback, useState, useTransition } from 'react';
import { useOnInit } from '@/hooks';
import { BaseEnumsSpace } from '@/enums';
import { itNotNull } from '@/utils';

export type FaqType = {
  id: number;
  category: string;
  qaList: UserVOSpace.FaqDataType[];
};
export default function useData() {
  const [isPending, startTransition] = useTransition();
  const [faqData, setFaqData] = useState<FaqType[]>();

  /** 初始化 */
  useOnInit({
    callback: async () => {
      onGetFaq();
    },
  });

  const onGetFaq = async () => {
    BaseInfoManager.changeLoadingModalVisible(true);
    let result = await fetchFaqConfig();

    if (result.code === BaseEnumsSpace.ENetworkStatus.SUCCESS) {
      return await itNotNull(result?.data)
        .then(data => {
          startTransition(() => {
            const list: FaqType[] = [];

            /** 添加目录 */
            data.forEach(item => {
              if (!item.parentId) {
                list.push({
                  id: item.id,
                  category: item.content,
                  qaList: [],
                });
              }
            });

            /** 添加问题 */
            list.forEach(category => {
              data.forEach(item => {
                if (category.id === item.parentId) {
                  category.qaList.push({
                    ...item,
                    content: item.content,
                  });
                }
              });
            });

            /** 添加答案 */
            list.forEach(category => {
              category.qaList.forEach(cateItem => {
                data.forEach(item => {
                  if (cateItem.id === item.parentId) {
                    cateItem.answer = item.content;
                  }
                });
              });
            });

            setFaqData(list);
            BaseInfoManager.changeLoadingModalVisible(false);
          });
        })
        .catch(() => {
          BaseInfoManager.changeLoadingModalVisible(false);
        });
    }
    BaseInfoManager.changeLoadingModalVisible(false);
  };

  const handleClickCate = useCallback(async (question: string) => {}, []);

  return { isPending, handleClickCate, faqData };
}
