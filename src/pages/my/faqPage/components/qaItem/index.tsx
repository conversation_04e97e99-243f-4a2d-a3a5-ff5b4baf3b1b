/* eslint-disable react-native/no-inline-styles */
/* eslint-disable react/react-in-jsx-scope */

import { Image, Text, View, ViewProps } from '@/components';
import React, { memo, ReactElement, useCallback, useState } from 'react';
import { TouchableWithoutFeedback } from 'react-native';

interface IProps extends ViewProps {
  question: string;
  answer: string;
  cateClickCb: (question: string) => void;
}
export default memo((props: IProps): ReactElement => {
  const { question, answer, cateClickCb, ...rest } = props;

  const [expand, setExpand] = useState<boolean>(false);

  const onChangeExpand = useCallback(() => {
    setExpand(preState => !preState);
  }, []);

  const QuesView = (
    <View margin="0 0 16 0">
      <TouchableWithoutFeedback onPress={onChangeExpand}>
        <View
          style={{
            backgroundColor: 'background-color-100',
            borderRadius: 8,
          }}
          padding="8 12 8 12"
          layoutStrategy="flexRowBetweenCenter"
          {...rest}>
          <Text
            textContent={question}
            category="c1"
            bold={'bold'}
            style={{
              color: 'text-color-600',
            }}
          />
          {/* @ts-ignore */}
          {answer && <Image name={expand ? '_arrowBottom' : '_arrowRight'} />}
        </View>
      </TouchableWithoutFeedback>
      {answer && (
        <View
          margin="6 0 0 0"
          style={{
            borderRadius: 8,
            backgroundColor: 'background-color-100',
            display: expand ? 'flex' : 'none',
          }}
          padding="8 12 8 12">
          <Text
            textContent={answer}
            category="c1"
            bold={'bold'}
            style={{
              color: 'text-color-600',
            }}
          />
        </View>
      )}
    </View>
  );

  return <>{QuesView}</>;
});
