/* eslint-disable react-native/no-inline-styles */
/* eslint-disable react/react-in-jsx-scope */

import { Layout, Text, TopNavigation, View } from '@/components';
import { ScreenProps } from '@/types';
import React, { ReactElement } from 'react';
import { ScrollView } from 'react-native';
import { default as QaItem } from './components/qaItem';
import useData from './useData';

export default ({ navigation, route }: ScreenProps<{}>): ReactElement => {
  const { isPending, handleClickCate, faqData } = useData();

  const FaqView = faqData?.map(cate => (
    <View key={cate.id} padding="24 8 24 8" margin="0 0 24 0" cardType="baseType">
      <Text textContent={cate.category} category="p1" bold={'bold'} />
      {cate?.qaList.map(faq => (
        <QaItem
          key={faq.id}
          question={faq.content}
          answer={faq.answer || ''}
          cateClickCb={handleClickCate}
        />
      ))}
    </View>
  ));

  if (isPending) {
    return <></>;
  }

  return (
    <>
      <Layout pLevel="0" level="1">
        <TopNavigation titleKey={'faqString.title'} />
        <ScrollView fadingEdgeLength={10} style={{ marginHorizontal: 16, marginVertical: 16 }}>
          {FaqView}
        </ScrollView>
      </Layout>
    </>
  );
};
