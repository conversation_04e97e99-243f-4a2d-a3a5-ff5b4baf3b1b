import {
  Button,
  Layout,
  PhoneNumberInput,
  SmsCodeInputRefType,
  Text,
  TopNavigation,
  View,
  Image,
} from '@/components';
import { HitPointEnumsSpace } from '@/enums';
import { useLoginInfo, useOnInit } from '@/hooks';
import { CallYouSendSmsModal, ReciveOtpByCallModal } from '@/modals';
import { RouterConfig } from '@/routes';
import { ScreenProps } from '@/types';
import { nav, verifyPhoneNumber } from '@/utils';
import _ from 'lodash';
import { ReactElement, Ref, memo, useMemo, useRef } from 'react';
import { ScrollView } from 'react-native';
import { Strings } from '@/i18n';
import { useSmsCodeHandler } from '../../../hooks/useSmsCodeHandler';

interface IProps {
  password: string;
}
const useData = (props: IProps) => {
  const { password } = props;
  const {
    phoneNumber,
    onSetPhoneNumber,
    smsCode,
    onSetSmsCode,
    readPrivacyNoticeBool,
    onChangeReadPrivacyNoticeBool,
  } = useLoginInfo();
  const smsCodeInputRef = useRef<SmsCodeInputRefType>(null);
  useSmsCodeHandler(onSetSmsCode);

  let phoneRef = useRef<string>('');
  /** 初始化 */
  useOnInit({
    callback: () => {
      onChangeReadPrivacyNoticeBool(true);
    },
    pageKey: HitPointEnumsSpace.EPageKey.P_MODIFY_NUMBER_CONFIRM,
  });

  const handleOpenPrivacyNotice = _.debounce(() => {
    nav.navigate(RouterConfig.PRIVACY_SCREEN as any);
  }, 100);

  const setPhoneNumberHandle = (p: string) => {
    onSetPhoneNumber(p);
    phoneRef.current = p;
  };

  const buttonDisable = useMemo(() => {
    return !verifyPhoneNumber(phoneNumber);
  }, [phoneNumber]);

  const handleNext = async () => {
    return nav.navigate(RouterConfig.NEW_MOBILE_CMS_CODE as any, {
      phoneNumber: phoneRef.current,
      password,
    });
  };

  return {
    phoneNumber,
    setPhoneNumberHandle,
    smsCode,
    onSetSmsCode,
    smsCodeInputRef,
    handleNext,
    buttonDisable,
    readPrivacyNoticeBool,
    onChangeReadPrivacyNoticeBool,
    handleOpenPrivacyNotice,
  };
};

const NewMobilePage = ({ route }: ScreenProps<{ password: string }>): ReactElement => {
  const { password = '' } = route.params;
  const { phoneNumber, setPhoneNumberHandle, handleNext, buttonDisable, handleOpenPrivacyNotice } =
    useData({ password });

  const renderTop = () => {
    return (
      <View
        margin="24 0 24 0"
        padding="12 12 12 12"
        style={{ backgroundColor: 'primary-color-100', borderRadius: 8 }}
        layoutStrategy="flexRowStartCenter">
        <Image name="_safeTipIcon" />
        <Text padding="0 12 0 8" i18nKey={Strings.validateBasicInfoString.tip} category="c1" />
      </View>
    );
  };
  return (
    <>
      <Layout pLevel="0" level="0">
        <TopNavigation titleKey="myString.my_modify_mobile" />
        <ScrollView
          style={{ paddingHorizontal: 16 }}
          fadingEdgeLength={10}
          keyboardShouldPersistTaps="always">
          {renderTop()}
          <View margin="0 16 0 16">
            <Text i18nKey={'myString.pls_input_mobile_and_smscode'} />
            <PhoneNumberInput
              type="line"
              prefixMargin="32 0 0 0"
              placeholderKey={'loginString.phoneNumber'}
              pageKey={HitPointEnumsSpace.EPageKey.P_MODIFY_NUMBER_CONFIRM}
              eventKey={HitPointEnumsSpace.EEventKey.E_NEW_NUMBER}
              phoneNumber={phoneNumber}
              setPhoneNumber={setPhoneNumberHandle}
            />
            <Button
              margin="32 0 0 0"
              status="primary"
              disabled={buttonDisable}
              onPress={handleNext}
              padding={'16 0 16 0'}
              textI18nKey="btnString.next"
            />
            <Text padding="16 0 0 0" isCenter>
              <Text
                category="c2"
                i18nKey={'loginString.readPrivacyNoticeTabOne'}
                style={{
                  color: 'text-color-700',
                }}
              />
              <Text
                category="c2"
                onPress={handleOpenPrivacyNotice}
                i18nKey={'loginString.readPrivacyNoticeTabTwo'}
                style={{
                  color: 'primary-color-500',
                }}
              />
              <Text
                category="c2"
                i18nKey={'loginString.readPrivacyNoticeTabThree'}
                style={{
                  color: 'text-color-700',
                }}
              />
            </Text>
          </View>
        </ScrollView>
      </Layout>
      <ReciveOtpByCallModal />
      <CallYouSendSmsModal />
    </>
  );
};

export default memo(NewMobilePage);
