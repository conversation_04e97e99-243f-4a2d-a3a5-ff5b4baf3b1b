import { Card, Divider, Image, Layout, Layouts, Text, TopNavigation, View } from '@/components';
import { ImageNames } from '@/config';
import { Strings } from '@/i18n';
import { UserInfoManager } from '@/managers';
import { Colors } from '@/themes';
import React from 'react';
import { ScrollView, TouchableOpacity } from 'react-native';
import { EmailUpdateModal } from './components/EmailUpdateModal';
import useData from './useData';
import { maskCurp, maskEmail, maskName } from '@/utils';

export default () => {
  const {
    go2INEValidityUpdatePage,
    showEmailUpdateModal,
    onEmailClose,
    onEmailConfirm,
    emailVisible,
  } = useData();
  const ITEM_DATA = [
    [
      {
        title: Strings.myString.fatherName,
        value: maskName('xxx'),
      },
      {
        title: Strings.myString.motherName,
        value: maskName('xxx'),
      },
      {
        title: Strings.myString.name,
        value: maskName('xxx'),
      },
      {
        title: Strings.myString.email,
        value: maskEmail('xxx@gmail'),
        onPress: showEmailUpdateModal,
      },
    ],
    [
      {
        title: Strings.myString.curpNo,
        // 生成一个假的 墨西哥curp number
        value: maskCurp('MAXJ881103HNLNSX24'),
      },
      {
        title: Strings.myString.ineValidity,
        value: 'xxx',
        onPress: go2INEValidityUpdatePage,
        note: Strings.myString.ineValidityUpdateTip,
      },
    ],
  ];

  const renderItem = (item: any, showDivider: boolean) => (
    <TouchableOpacity key={item.title} disabled={!item.onPress} onPress={item.onPress}>
      <View padding="16 12 16 12">
        <View layoutStrategy={Layouts.flexRowBetweenCenter}>
          <View>
            <Text
              i18nKey={item.title}
              category="p1"
              style={{ fontSize: 14, color: Colors.TEXT_COLOR_800 }}
            />
          </View>
          <View layoutStrategy={Layouts.flexRowStartCenter}>
            <Text
              category="p1"
              style={{ fontSize: 14, color: Colors.TEXT_COLOR_600 }}
              textContent={`${item.value}(Validado)`}
            />
            {item.onPress ? <Image name={ImageNames._arrowRight} /> : null}
          </View>
        </View>
        {item.note ? (
          <View layoutStrategy={Layouts.flexRowStartCenter} margin="10 0 0 0">
            <Image name={ImageNames._grayInfo} />
            <Text
              category="c1"
              style={{ fontSize: 10, color: Colors.TEXT_COLOR_600 }}
              i18nKey={item.note}
            />
          </View>
        ) : null}
      </View>
      {showDivider ? <Divider style={{ width: undefined }} margin="0 12 0 12" /> : null}
    </TouchableOpacity>
  );

  // 没有数据显示EmptyView
  const EmptyView = (
    <View style={{ alignItems: 'center' }}>
      <Image name={ImageNames._emptyIcon} />
      <Text
        margin="32 0 0 0"
        i18nKey={Strings.myString.noPersonalInfo}
        style={{ color: Colors.TEXT_COLOR_600, fontSize: 14 }}
        category="p1"
      />
    </View>
  );

  return (
    <>
      <Layout pLevel="0" level="1">
        <TopNavigation titleKey={'faqString.title'} />
        {UserInfoManager.context.userModel.userState.name ? (
          <ScrollView showsVerticalScrollIndicator={false}>
            <Card margin="20 16 0 16" padding="0 0 0 0">
              {ITEM_DATA[0].map((item, index) =>
                renderItem(item, index !== ITEM_DATA[0].length - 1),
              )}
            </Card>
            <Card margin="24 16 24 16" padding="0 0 0 0">
              {ITEM_DATA[1].map((item, index) =>
                renderItem(item, index !== ITEM_DATA[1].length - 1),
              )}
            </Card>
          </ScrollView>
        ) : (
          EmptyView
        )}
        <EmailUpdateModal
          visible={emailVisible}
          onConfirm={onEmailConfirm}
          onClose={onEmailClose}
        />
      </Layout>
    </>
  );
};
