import { BaseInfoManager } from '@/managers';
import { fetchFaqConfig } from '@/server';
import { UserVOSpace } from '@/types';
import { useCallback, useState, useTransition } from 'react';
import { useOnInit } from '@/hooks';
import { BaseEnumsSpace } from '@/enums';
import { itNotNull, nav } from '@/utils';
import { RouterConfig } from '@/routes';
import { Toast } from '@/nativeComponents';
import { t } from 'i18next';
import { Strings } from '@/i18n';

export type FaqType = {
  id: number;
  category: string;
  qaList: UserVOSpace.FaqDataType[];
};
export default function useData() {
  const [emailVisible, setEmailVisible] = useState<boolean>(false);

  const onEmailClose = () => {
    setEmailVisible(false);
  };

  const onEmailConfirm = async (smsCode?: string) => {
    // 请求接口，成功则隐藏
    setEmailVisible(false);
    Toast(t(Strings.myString.emailUpdateSuccessTips));
  };

  // ocr 更新成功回调
  const ocrUpdateCallback = () => {
    // 后续调用刷新接口
    console.log('ocr 更新成功回调');
    Toast(Strings.myString.ineUpdateSuccessTips);
  };

  // ocr 更新成功回调
  const ocrFailedCallback = () => {
    // 后续调用刷新接口
    console.log('ocr 三次更新失败回调');
    Toast(Strings.myString.ineUpdateFailedTips);
  };

  // 调整 INE 有效期更新页面
  const go2INEValidityUpdatePage = () => {
    nav.navigate(RouterConfig.OCR_INFO_UPDATE as any, { ocrUpdateCallback, ocrFailedCallback });
  };

  // 显示修改邮箱弹窗
  const showEmailUpdateModal = () => {
    setEmailVisible(true);
  };

  return {
    go2INEValidityUpdatePage,
    showEmailUpdateModal,
    onEmailClose,
    onEmailConfirm,
    emailVisible,
  };
}
