/**
 * @description 验证码更新邮箱弹窗
 */
import React, { useCallback, useMemo, useRef, useState } from 'react';
import {
  AnimatedSmsCodeInput,
  AnimatedSmsCodeInputRefType,
  CommonModal,
  PrefixInput,
  Text,
  View,
} from '@/components';
import { Colors } from '@/themes';
import { Strings } from '@/i18n';
import { HitPointEnumsSpace, UserEnumsSpace } from '@/enums';
import { isStringExist, checkTextLength, isInviteCodeExist } from '@/utils';
import { t } from 'i18next';
import { BaseInfoManager } from '@/managers';
import { responseHandler, fetchEmailOtp } from '@/server';

type IProps = {
  visible: boolean;
  onConfirm: (smsCode?: string) => Promise<void>;
  onClose: () => void;
};

export const EmailUpdateModal = (props: IProps) => {
  const { visible, onConfirm, onClose } = props;
  const [email, setEmail] = useState<string>('');
  const [emailCode, setEmailCode] = useState<string>('');
  const emailCodeRef = useRef<AnimatedSmsCodeInputRefType>(null);
  const clickSendPreMethodForEmail = useCallback(async () => {
    return onSendEmailCode();
  }, []);

  const onSendEmailCode = async () => {
    const params = {
      bizType: UserEnumsSpace.BizType.CHANGE_MOBILE,
      email,
    };
    BaseInfoManager.changeLoadingModalVisible(true);
    const { error } = await responseHandler(fetchEmailOtp(params));
    BaseInfoManager.changeLoadingModalVisible(false);
    if (error) {
      return false;
    }
    emailCodeRef.current?.reStartCountDown();
    return true;
  };

  return (
    <CommonModal
      visible={visible}
      confirmBtnName={Strings.btnString.confirm}
      confirmCallback={onConfirm}
      onBackdropPress={onClose}>
      <View padding="24 16 24 16">
        <Text i18nKey={Strings.myString.changeEmail} />
        <PrefixInput
          margin="8 0 0 0"
          type="line"
          placeholderKey={Strings.basicInfoString.placeholder}
          value={email}
          setValue={setEmail}
        />
        <AnimatedSmsCodeInput
          ref={emailCodeRef}
          type="line"
          placeholderKey={Strings.validateBasicInfoString.code}
          prefixMargin={'16 0 0 0'}
          smsCode={emailCode}
          setSmsCode={setEmailCode}
          clickSendPreMethod={clickSendPreMethodForEmail}
          scenesId="register"
          maxLength={6}
          validateConfig={[
            {
              condition: isStringExist(emailCode),
              info: t(Strings.verificationString.required),
              status: 'danger',
            },
            {
              condition: isInviteCodeExist(emailCode),
              info: t(Strings.verificationString.error),
              status: 'danger',
            },
          ]}
          style={{ backgroundColor: Colors.FILL_COLOR_0 }}
          dangerStyle={{ backgroundColor: Colors.FILL_COLOR_100 }}
        />
      </View>
    </CommonModal>
  );
};
