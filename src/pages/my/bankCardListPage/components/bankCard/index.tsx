import { ReactElement, useCallback, useMemo } from 'react';
import { View, Text, ImageBackground, Card, Button } from '@/components';
import React from 'react';
import { TrackEvent } from '@/utils';
import { HitPointEnumsSpace } from '@/enums';
import { BaseInfoManager, modalDataStoreInstance, ModalList } from '@/managers';
import { EvidenceVOSpace } from '@/types';

interface IProps {
  BankCardItem: EvidenceVOSpace.MeBankcardDataType;
  openAutoWithhold: (cardNo: string, bankName: string) => void;
  openWithholdAuthorization: (ardNo: string) => void;
  closeWithholdAuthorization: (ardNo: string) => void;
}
const BankCard = (props: IProps): ReactElement => {
  const {
    BankCardItem: { bankName, isSelf, cardNo, withholdAuthorizeStatus },
    openAutoWithhold,
    openWithholdAuthorization,
    closeWithholdAuthorization,
  } = props;

  const onModifyBanCard = useCallback(() => {
    /** 点击修改银行卡按钮事件 */
    TrackEvent.trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_BANK_CARD,
        e: HitPointEnumsSpace.EEventKey.BTN_BANK_MODIFY,
      },
      '1',
    );
    modalDataStoreInstance.openModal({
      key: ModalList.INFO_PROMPT_CONFIRM,
      i18nKey: 'modalString.modifybankCard',
      imageKey: '_epModalNormalIcon',
      confirmBtnName: 'btnString.agree',
      isBackdropClose: false,
    });
  }, []);

  const $withholdAuthorizeView = useMemo(() => {
    if (!BaseInfoManager.context.baseModel.isWithholdSwitch) {
      return null;
    } else {
      let withholdAuthorize = withholdAuthorizeStatus === 'YES';
      return (
        <Card
          margin="12 0 0 0"
          padding="12 12 12 12"
          style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            borderTopLeftRadius: 0,
            borderTopRightRadius: 0,
          }}>
          <Text i18nKey="autoWithholdString.withholdAuthorize" />
          <Text
            onPress={() => {
              if (withholdAuthorize) {
                closeWithholdAuthorization(cardNo);
              } else {
                if (isSelf === 'YES') {
                  openAutoWithhold(cardNo, bankName);
                } else {
                  modalDataStoreInstance.openModal({
                    key: ModalList.INFO_PROMPT_CONFIRM,
                    imageKey: '_epModalFailIcon',
                    i18nKey: 'autoWithholdString.notSelfBankCardTip',
                    confirmBtnName: 'btnString.agree',
                    isBackdropClose: false,
                  });
                }
              }
            }}
            status="primary"
            i18nKey={withholdAuthorize ? 'autoWithholdString.close' : 'autoWithholdString.activate'}
          />
        </Card>
      );
    }
  }, [withholdAuthorizeStatus, bankName, cardNo, isSelf]);

  return (
    <>
      <ImageBackground
        name="_clabeBg"
        resizeMode="stretch"
        margin="0 0 16 0"
        style={{
          borderRadius: 12,
          // height: 100,
        }}>
        <View padding="18 18 6 18">
          <Text
            category="p2"
            textContent={bankName}
            style={{
              color: 'text-color-0',
            }}
          />
          <View layoutStrategy="flexRowBetweenCenter">
            <Text
              margin="18 0 0 0"
              category="p1"
              textContent={cardNo.toFormatClabe(true)}
              style={{
                color: 'text-color-0',
              }}
            />
            <Button
              onPress={onModifyBanCard}
              appearance="outline"
              status="primary"
              textI18nKey="btnString.modify"
            />
          </View>
        </View>
        {$withholdAuthorizeView}
      </ImageBackground>
    </>
  );
};

export default BankCard;
