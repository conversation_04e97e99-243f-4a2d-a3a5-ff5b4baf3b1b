import { Card, Layout, Text, TopNavigation } from '@/components';
import { BaseEnumsSpace, HitPointEnumsSpace } from '@/enums';
import { useOnInit } from '@/hooks';
import { BaseInfoManager, modalDataStoreInstance, ModalList } from '@/managers';
import {
  fetchUserBankcard,
  fetchWithholdAuthorizationClose,
  fetchWithholdAuthorizationOpen,
  fetchWithholdContract,
} from '@/server';
import { EvidenceVOSpace, OrderVOSpace, ScreenProps, EWithholderTipType } from '@/types';
import { ReactElement, memo, useState, useTransition } from 'react';
import { ScrollView } from 'react-native';
import BankCard from './components/bankCard/index';
import React from 'react';
import { nav, TrackEvent } from '@/utils';
import { RouterConfig } from '@/routes';
import CryptoJS from 'crypto-js';

interface IState {
  bankCardList: EvidenceVOSpace.MeBankcardDataType[];
}
const useDate = () => {
  const [isPending, startTransition] = useTransition();
  const [state, setState] = useState<IState>({
    bankCardList: [],
  });

  /** 初始化 */
  useOnInit({
    callback: async () => {
      BaseInfoManager.changeLoadingModalVisible(true);
      onGetBankCard();
      BaseInfoManager.changeLoadingModalVisible(false);
    },
    pageKey: HitPointEnumsSpace.EPageKey.P_BANK_CARD,
  });

  const onGetBankCard = async () => {
    BaseInfoManager.changeLoadingModalVisible(true);
    let result = await fetchUserBankcard();

    if (result.code !== 0) {
      BaseInfoManager.changeLoadingModalVisible(false);
      return false;
    }

    startTransition(() => {
      setState((prevState: IState) => ({
        ...prevState,
        bankCardList: result.data,
      }));
    });

    BaseInfoManager.changeLoadingModalVisible(false);
    return true;
  };

  /** 跳转到自动代扣协议 */
  const openAutoWithhold = async (cardNo: string, bankName: string) => {
    TrackEvent.trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_BANK_CARD,
        e: HitPointEnumsSpace.EEventKey.BTN_NEGOTIATE_CHECK,
      },
      '1',
    );
    let result = await fetchWithholdContract({ cardNo, bankName });
    if (result.code === BaseEnumsSpace.ENetworkStatus.SUCCESS) {
      nav.navigate(RouterConfig.AUTOMATIC_WITHHOLD_PROTOCOL as any, {
        html: CryptoJS.enc.Base64.parse(String(result.data)).toString(CryptoJS.enc.Utf8),
        currentRoute: RouterConfig.BANK_CARD_LIST,
        acceptHandle: () => openWithholdAuthorization(cardNo),
      });
    }
    return result?.code === BaseEnumsSpace.ENetworkStatus.SUCCESS;
  };

  /** 开启自动代扣功能 */
  const openWithholdAuthorization = async (cardNo: string) => {
    BaseInfoManager.changeLoadingModalVisible(true);
    TrackEvent.trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_BANK_CARD,
        e: HitPointEnumsSpace.EEventKey.BTN_AUTOMATIC_REPAY_CLICK,
      },
      '1',
    );
    const { code } = await fetchWithholdAuthorizationOpen({ cardNo });
    if (code === 0) {
      onGetBankCard();
      modalDataStoreInstance.openModal({
        key: ModalList.INFO_PROMPT_CONFIRM,
        i18nKey: 'autoWithholdString.activateSuccess',
        imageKey: '_epModalSuccessIcon',
        confirmBtnName: 'btnString.agree',
        isBackdropClose: false,
      });
    }
    BaseInfoManager.changeLoadingModalVisible(false);
  };

  /** 关闭自动代扣功能 */
  const closeWithholdAuthorization = async (cardNo: string) => {
    modalDataStoreInstance.openModal({
      key: ModalList.WITHHOLDER_TIP,
      extra: EWithholderTipType.CLOSE,
      confirmBtnCallback: async () => {
        BaseInfoManager.changeLoadingModalVisible(true);
        /** 点击修改银行卡按钮事件 */
        TrackEvent.trackCommonEvent(
          {
            p: HitPointEnumsSpace.EPageKey.P_BANK_CARD,
            e: HitPointEnumsSpace.EEventKey.BTN_AUTOMATIC_REPAY_CLICK,
          },
          '0',
        );
        const { code } = await fetchWithholdAuthorizationClose({ cardNo });
        if (code === 0) {
          onGetBankCard();
        }
        BaseInfoManager.changeLoadingModalVisible(false);
      },
    });
  };

  return {
    isPending,
    ...state,
    openAutoWithhold,
    openWithholdAuthorization,
    closeWithholdAuthorization,
  };
};

const BankCardListPage = ({ route }: ScreenProps<{ agreeAutoWithHold: boolean }>): ReactElement => {
  const {
    isPending,
    bankCardList,
    openAutoWithhold,
    openWithholdAuthorization,
    closeWithholdAuthorization,
  } = useDate();

  const BankCardListView = bankCardList?.map((BankCardItem: EvidenceVOSpace.MeBankcardDataType) => (
    <BankCard
      BankCardItem={BankCardItem}
      openAutoWithhold={openAutoWithhold}
      openWithholdAuthorization={openWithholdAuthorization}
      closeWithholdAuthorization={closeWithholdAuthorization}
      key={BankCardItem.id}
    />
  ));

  if (isPending) {
    return <></>;
  }

  return (
    <Layout pLevel="0" level="1">
      <TopNavigation titleKey="myString.bank_card" />
      <ScrollView
        fadingEdgeLength={10}
        keyboardShouldPersistTaps="always"
        style={{ marginHorizontal: 16, marginVertical: 16 }}>
        {BankCardListView}
      </ScrollView>
    </Layout>
  );
};

export default memo(BankCardListPage);
