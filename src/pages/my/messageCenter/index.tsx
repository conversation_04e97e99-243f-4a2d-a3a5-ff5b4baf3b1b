import { Layout, Text, TopNavigation, View } from '@/components';
import { HitPointEnumsSpace } from '@/enums';
import { useOnInit } from '@/hooks';
import { BaseInfoManager, MessageDataStoreInstance } from '@/managers';
import { fetchMessageRead, getMessageList } from '@/server';
import { UserVOSpace } from '@/types';
import ReactNativeViewPager, { PagerViewOnPageSelectedEvent } from 'react-native-pager-view';
import { memo, ReactElement, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { TouchableOpacity } from 'react-native';
import MessageListPage from './components/messagePage';

const useData = () => {
  // 活动消息
  const [activityMessageList, setActivityMessageList] = useState<UserVOSpace.MessageListItem[]>([]);

  const [messageList, setMessageList] = useState<UserVOSpace.MessageListItem[]>([]);

  const [unReadMessage, setUnReadMessage] = useState<UserVOSpace.UnReadMessageCount>({
    total: 0,
    activity: 0,
    message: 0,
  });

  const viewPagerRef = useRef<ReactNativeViewPager>(null);

  useEffect(() => {
    const subscribe = MessageDataStoreInstance.messageCenter.subscribe(({ messageUndoSate }) => {
      setUnReadMessage(messageUndoSate);
    });
    return () => {
      subscribe && subscribe.unsubscribe();
    };
  }, []);

  const [page, setPage] = useState<number>(0);

  const onChangePage = useCallback(
    async (event: PagerViewOnPageSelectedEvent) => {
      const position = event.nativeEvent.position;
      if (position === 1) {
        await getUserActivityMessageList();
        await fetchMessageRead({ messageType: 'ACTIVITY' });
        MessageDataStoreInstance.updateMessageUnReadState();
      } else {
        await getUserMessageList();
        await fetchMessageRead({ messageType: 'MESSAGE' });
        MessageDataStoreInstance.updateMessageUnReadState();
      }
      setPage(position);
    },
    [setMessageList],
  );

  const onSetPage = useCallback((page: number) => {
    viewPagerRef.current?.setPage(page);
    setPage(page);
  }, []);

  //

  /** 初始化 */
  useOnInit({
    callback: async () => {
      BaseInfoManager.changeLoadingModalVisible(true);
      // 获取用户消息列表
      await getUserMessageList();
      await getUserActivityMessageList();
      BaseInfoManager.changeLoadingModalVisible(false);
    },
    pageKey: HitPointEnumsSpace.EPageKey.P_MESSAGE_CENTER,
  });

  /** 获取用户的通知消息列表 */
  const getUserMessageList = async () => {
    let result = await getMessageList({ messageType: 'MESSAGE' });
    if (result.code === 0) {
      if (Array.isArray(result.data)) {
        setMessageList(result.data);
      }
    }
  };

  /** 获取用户的活动消息列表 */
  const getUserActivityMessageList = async () => {
    let result = await getMessageList({ messageType: 'ACTIVITY' });
    if (result.code === 0) {
      if (Array.isArray(result.data)) {
        setActivityMessageList(result.data);
      }
    }
  };

  return {
    onChangePage,
    activityMessageList,
    messageList,
    page,
    unReadMessage,
    viewPagerRef,
    onSetPage,
  };
};

const MessagePage = (): ReactElement => {
  const {
    onChangePage,
    activityMessageList,
    messageList,
    page,
    unReadMessage,
    viewPagerRef,
    onSetPage,
  } = useData();

  /** 顶部tab栏 */
  const $renderTabBar = useMemo(() => {
    return (
      <View
        layoutStrategy="flexRowBetweenCenter"
        width={'100%'}
        height={40}
        style={
          {
            // borderBottomWidth: 1,
            // borderColor: 'line-color-200',
          }
        }>
        <TouchableOpacity style={{ flex: 1 }} onPress={() => onSetPage(0)}>
          <View style={{ flex: 1 }} padding="4 0 4 0" layoutStrategy="flexColumnStartCenter">
            <View layoutStrategy="flexColumnStartCenter">
              <Text
                i18nKey="messageCenterString.notifyLabel"
                style={{
                  color: page === 0 ? 'primary-color-600' : 'text-color-600',
                }}
              />
              <View
                width={40}
                height={3}
                margin="9 0 0 0"
                style={{
                  backgroundColor: page === 0 ? 'primary-color-600' : 'transparent',
                }}
              />
              <View
                style={{
                  display: unReadMessage.message > 0 ? 'flex' : 'none',
                  minWidth: 6,
                  minHeight: 6,
                  position: 'absolute',
                  top: 4,
                  right: -6,
                  borderRadius: 3,
                  backgroundColor: 'danger-color-500',
                }}
              />
            </View>
          </View>
        </TouchableOpacity>
        <TouchableOpacity style={{ flex: 1 }} onPress={() => onSetPage(1)}>
          <View style={{ flex: 1 }} padding="4 0 4 0" layoutStrategy="flexColumnStartCenter">
            <View layoutStrategy="flexColumnStartCenter">
              <Text
                i18nKey="messageCenterString.activityLabel"
                style={{
                  color: page === 1 ? 'primary-color-600' : 'text-color-600',
                }}
              />
              <View
                width={40}
                height={3}
                margin="9 0 0 0"
                style={{
                  backgroundColor: page === 1 ? 'primary-color-600' : 'transparent',
                }}
              />
              <View
                style={{
                  display: unReadMessage.activity > 0 ? 'flex' : 'none',
                  minWidth: 6,
                  minHeight: 6,
                  position: 'absolute',
                  top: 4,
                  right: -6,
                  borderRadius: 3,
                  backgroundColor: 'danger-color-500',
                }}
              />
            </View>
          </View>
        </TouchableOpacity>
      </View>
    );
  }, [page, unReadMessage]);

  return (
    <>
      <Layout pLevel="0" level="0">
        <TopNavigation titleKey="messageCenterString.title" />
        {$renderTabBar}
        <ReactNativeViewPager
          ref={viewPagerRef}
          style={{ flex: 1 }}
          orientation="horizontal"
          onPageSelected={onChangePage}
          useNext={false}>
          <MessageListPage key={1} dataList={messageList} />
          <MessageListPage key={2} dataList={activityMessageList} />
        </ReactNativeViewPager>
      </Layout>
    </>
  );
};

export default memo(MessagePage);
