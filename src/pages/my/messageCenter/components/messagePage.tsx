import { Text, View } from '@/components';
import { UserVOSpace } from '@/types';
import React, { useMemo } from 'react';
import { FlatList } from 'react-native';
type MessagePagePropsType = {
  dataList: UserVOSpace.MessageListItem[];
};

type MessageItemPropsType = {
  data: UserVOSpace.MessageListItem;
};

const MessageItem = React.memo((props: MessageItemPropsType) => {
  return (
    <View padding="12 16 12 16" margin="0 16 24 16" cardType="baseType">
      <View layoutStrategy="flexRowStartCenter">
        <View
          margin="4 12 4 4"
          width={8}
          height={8}
          style={{
            borderRadius: 4,
            display: props.data.readFlag === 'YES' ? 'none' : 'flex',
            backgroundColor: 'danger-color-500',
          }}
        />
        <Text bold="700" textContent={props.data.title} />
      </View>
      <Text margin="12 0 0 0" textContent={props.data.body} style={{ color: 'text-color-600' }} />
      <Text
        margin="12 0 0 0"
        category="c1"
        textContent={props.data.createTime}
        style={{ color: 'text-color-600' }}
      />
    </View>
  );
});

const MessageListPage = (props: MessagePagePropsType) => {
  const $renderFooter = useMemo(() => {
    if (props.dataList.length === 0) {
      return (
        <Text
          width={'100%'}
          margin="0 0 24 0"
          isCenter
          category="p2"
          style={{ color: 'text-color-600' }}
          i18nKey="messageCenterString.noData"
        />
      );
    } else {
      return (
        <Text
          width={'100%'}
          margin="0 0 24 0"
          isCenter
          category="p2"
          style={{ color: 'text-color-600' }}
          i18nKey="messageCenterString.footerTip"
        />
      );
    }
  }, [props.dataList]);

  return (
    <FlatList
      contentContainerStyle={{ paddingTop: 20 }}
      style={{ flex: 1, height: '100%' }}
      data={props.dataList}
      renderItem={({ item }) => {
        return <MessageItem data={item} key={item.id} />;
      }}
      ListFooterComponent={$renderFooter}
    />
  );
};

export default React.memo(MessageListPage);
