/** 优惠券使用说明弹窗 */

import { ActionSheet, Button, Image, View } from '@/components';
import React, { useState } from 'react';
import { Dimensions, ScrollView } from 'react-native';

interface IProps {
  onCancel: () => void;
  visible: boolean;
}

export default React.memo((props: IProps) => {
  const { visible, onCancel } = props;

  const [height, setHeight] = useState(
    (Dimensions.get('window').height * 3) / 4 > 700
      ? 700
      : (Dimensions.get('window').height * 3) / 4,
  );

  return (
    <ActionSheet visible={visible} height={height} onClose={onCancel}>
      <View
        height={height}
        padding="10 0 10 0"
        style={{
          display: 'flex',
          borderTopLeftRadius: 8,
          borderTopRightRadius: 8,
          width: Dimensions.get('window').width,
          backgroundColor: 'background-color-0',
        }}>
        <ScrollView style={{ flex: 1 }} fadingEdgeLength={10}>
          <Image name="_evidenceOcrCurpDes" />
        </ScrollView>
        <Button
          margin={'8 16 0 16'}
          status="primary"
          onPress={onCancel}
          textI18nKey="btnString.agree"
        />
      </View>
    </ActionSheet>
  );
});
