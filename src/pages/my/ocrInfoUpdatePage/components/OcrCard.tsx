/* eslint-disable react-native/no-inline-styles */

import { Button, Image, Text, View } from '@/components';
import React, { ReactElement, useMemo } from 'react';
import { Dimensions, GestureResponderEvent, Image as RNImage } from 'react-native';

interface IOcrCard {
  handleClickFrondCurp?: (event: GestureResponderEvent) => void;
  handleClickBackCurp?: (event: GestureResponderEvent) => void;
  frontFile?: string;
  backFile?: string;
  /** ocr 识别状态 'SUCCEEDED' | 'FAILED' | '' */
  status: string;
  /** 是否用户手动填写用户信息 'YES' | 'NO' | '' */
  annul?: string;
  ocrFrontState: 'success' | 'fail' | '';
  ocrBackState: 'success' | 'fail' | '';
}
export default function OcrCard(props: IOcrCard): ReactElement {
  const {
    handleClickFrondCurp = () => {},
    handleClickBackCurp = () => {},
    frontFile,
    backFile,
    status,
    annul,
    ocrFrontState,
    ocrBackState,
  } = props;

  const success = useMemo(() => {
    return status === 'SUCCEEDED' && annul === 'NO';
  }, [status, annul]);

  const ocrFrontFail = useMemo(() => ocrFrontState === 'fail', [ocrFrontState]);

  const ocrBackFail = useMemo(() => ocrBackState === 'fail', [ocrBackState]);

  const isCompleted = useMemo(() => {
    return status === 'SUCCEEDED';
  }, [status]);

  return (
    <View layoutStrategy="flexRowBetweenCenter">
      <View layoutStrategy="flexColumnCenterCenter">
        <View style={{ borderRadius: 8, overflow: 'hidden' }}>
          {frontFile ? (
            <RNImage
              source={{ uri: frontFile }}
              style={{
                height: 85,
                width: 154,
                borderRadius: 6,
              }}
            />
          ) : (
            <Image name="_evidenceFrontCard" />
          )}
          {ocrFrontFail && (
            <Text
              padding="4 0 4 0"
              category="c2"
              status="control"
              isCenter
              style={{
                position: 'absolute',
                left: 0,
                right: 0,
                bottom: 0,
                backgroundColor: 'danger-color-500',
              }}
              i18nKey="ocrString.ocrFail"
            />
          )}
          {success && (
            <Text
              padding="4 0 4 0"
              category="c2"
              status="control"
              isCenter
              style={{
                position: 'absolute',
                left: 0,
                right: 0,
                bottom: 0,
                backgroundColor: 'rgba(0,0,0,0.3)',
              }}
              i18nKey="ocrString.ocrPhotoSuccessTip"
            />
          )}
        </View>

        <View layoutStrategy="flexRowCenterCenter">
          <Text
            margin="4 0 8 0"
            category="c2"
            style={{ color: 'text-color-600' }}
            i18nKey="ocrString.front"
          />
          {success && <Image margin="0 0 0 6" name="_evidenceOcrPhotoSuccess" />}
          {ocrFrontFail && <Image margin="0 0 0 6" name="_evidenceOcrPhotoFailed" />}
        </View>

        {!isCompleted && (
          <Button
            size="small"
            height={30}
            padding="0 12 0 12"
            accessoryLeft={<Image margin="0 6 0 0" name="_evidenceCameraIcon" />}
            status="primary"
            onPress={handleClickFrondCurp}
            textI18nKey="ocrString.takePhoto"
            textCategory="p2"
          />
        )}
      </View>

      <View layoutStrategy="flexColumnCenterCenter">
        <View style={{ borderRadius: 8, overflow: 'hidden' }}>
          {backFile ? (
            <RNImage
              source={{ uri: backFile }}
              style={{
                height: 85,
                width: 154,
                borderRadius: 6,
              }}
            />
          ) : (
            <Image margin="0 0 0 6" name="_evidenceBankCard" />
          )}
          {ocrBackFail && (
            <Text
              padding="4 0 4 0"
              category="c2"
              status="control"
              isCenter
              style={{
                position: 'absolute',
                left: 6,
                right: 0,
                bottom: 0,
                backgroundColor: 'danger-color-500',
                borderBottomLeftRadius: 8,
              }}
              i18nKey="ocrString.ocrFail"
            />
          )}
          {success && (
            <Text
              padding="4 0 4 0"
              category="c2"
              status="control"
              isCenter
              style={{
                position: 'absolute',
                left: 0,
                right: 0,
                bottom: 0,
                backgroundColor: 'rgba(0,0,0,0.3)',
              }}
              i18nKey="ocrString.ocrPhotoSuccessTip"
            />
          )}
        </View>

        <View layoutStrategy="flexRowCenterCenter">
          <Text
            margin="4 0 8 0"
            category="c2"
            style={{ color: 'text-color-600' }}
            i18nKey="ocrString.back"
          />
          {success && <Image margin="0 0 0 6" name="_evidenceOcrPhotoSuccess" />}
          {ocrBackFail && <Image margin="0 0 0 6" name="_evidenceOcrPhotoFailed" />}
        </View>

        {!isCompleted && (
          <Button
            size="small"
            height={30}
            padding="0 12 0 12"
            accessoryLeft={<Image margin="0 6 0 0" name="_evidenceCameraIcon" />}
            status="primary"
            onPress={handleClickBackCurp}
            textI18nKey="ocrString.takePhoto"
            textCategory="p2"
          />
        )}
      </View>
    </View>
  );
}
