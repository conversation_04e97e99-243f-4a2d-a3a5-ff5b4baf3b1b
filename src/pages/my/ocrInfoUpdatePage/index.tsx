import { Image, Layout, Text, TopNavigation, View } from '@/components';
import { Strings } from '@/i18n';
import React from 'react';
import { Colors } from '@/themes';
import OcrCard from './components/OcrCard';
import { useData } from './useData';
import { ScreenProps } from '@/types';

export default ({
  route,
}: ScreenProps<{
  ocrUpdateCallback: () => void;
  ocrFailedCallback: () => void;
}>) => {
  const { ocrUpdateCallback, ocrFailedCallback } = route.params;
  const TipCardData = [
    [
      {
        imageName: '_evidenceOcrErrorTip1',
        text: Strings.ocrString.ocrErrorTip1,
      },
      {
        imageName: '_evidenceOcrErrorTip2',
        text: Strings.ocrString.ocrErrorTip2,
      },
    ],
    [
      {
        imageName: '_evidenceOcrErrorTip3',
        text: Strings.ocrString.ocrErrorTip3,
      },
      {
        imageName: '_evidenceOcrErrorTip4',
        text: Strings.ocrString.ocrErrorTip4,
      },
    ],
  ];
  const {
    status,
    annul,
    frontFile,
    backFile,
    localBackFile,
    localFrontFile,
    handleClickFrondCurp,
    handleClickBackCurp,
    pageState,
  } = useData({ ocrUpdateCallback, ocrFailedCallback });
  const TipsInfoView = (
    <View
      layoutStrategy="flexRowCenterCenter"
      margin="0 0 16 0"
      padding="12 12 12 12"
      style={{ borderRadius: 4, backgroundColor: Colors.PRIMARY_COLOR_100, zIndex: 2 }}>
      <Text
        style={{ flex: 1, color: Colors.TEXT_COLOR_800 }}
        category="p1"
        i18nKey={Strings.ocrString.flowTip}
      />
    </View>
  );

  const OCRView = (
    <View margin="16 12 0 12" style={{ backgroundColor: 'background-color-0', borderRadius: 8 }}>
      {TipsInfoView}
      <View padding="0 12 16 12">
        <OcrCard
          frontFile={frontFile || localFrontFile}
          backFile={backFile || localBackFile}
          handleClickFrondCurp={handleClickFrondCurp}
          handleClickBackCurp={handleClickBackCurp}
          status={status}
          annul={annul}
          ocrFrontState={pageState.ocrFrontState}
          ocrBackState={pageState.ocrBackState}
        />
      </View>
    </View>
  );

  const renderTipImageCard = (params: { imageName: string; text: string }) => {
    const { imageName, text } = params;
    return (
      <View key={text} style={{ flex: 1 }} layoutStrategy="flexColumnCenterCenter">
        <Image name={imageName} />
        <Image style={{ top: -7 }} name="_evidenceSelfieErrorIcon" />
        <Text padding="8 0 8 0" category="c1" i18nKey={text} />
      </View>
    );
  };

  const TipCardView = (
    <View
      margin="12 12 12 12"
      padding="16 12 16 12"
      style={{ backgroundColor: '#fff', borderRadius: 8 }}>
      <View layoutStrategy="flexRowStart">
        {TipCardData[0].map(item => renderTipImageCard(item))}
      </View>
      <View margin="8 0 0 0" layoutStrategy="flexRowStart">
        {TipCardData[1].map(item => renderTipImageCard(item))}
      </View>
      <View margin="12 0 12 0" layoutStrategy="flexRowStartCenter">
        <Image name="_grayInfo" margin="0 6 0 0" />
        <Text
          i18nKey="ocrString.warnTip"
          style={{ flex: 1, color: 'text-color-600' }}
          category="c2"
          bold="600"
        />
      </View>
    </View>
  );

  return (
    <>
      <Layout pLevel="0" level="1">
        <TopNavigation titleKey={Strings.myString.ocrInfoUpdate} />
        {OCRView}
        {TipCardView}
      </Layout>
    </>
  );
};
