import { EvidenceEnumsSpace, BaseEnumsSpace, HitPointEnumsSpace } from '@/enums';
import { modalDataStoreInstance, ModalList, BaseInfoManager } from '@/managers';
import { RouterConfig } from '@/routes';
import { fetchUploadImageAndOCR, fetchUploadImage } from '@/server';
import { TrackEvent, nav } from '@/utils';
import { useSetState } from 'ahooks';
import { useCallback, useState } from 'react';
import { Strings } from '@/i18n';
import { OrderVOSpace } from '@/types';

export type PageStateType = {
  ocrFrontState: 'fail' | 'success' | '';
  ocrBackState: 'fail' | 'success' | '';
};
export type IState = OrderVOSpace.ImageOcrDataV7Type;
export function useData(initData: {
  ocrUpdateCallback: () => void;
  ocrFailedCallback: () => void;
}) {
  const { ocrUpdateCallback, ocrFailedCallback } = initData;
  const [pageState, setPageState] = useState<PageStateType>({
    ocrFrontState: '',
    ocrBackState: '',
  });
  const [state, setState] = useSetState<IState>({
    status: '',
    annul: '',
    curp: '',
    validity: '',
    name: '',
    fatherName: '',
    motherName: '',
    birthDate: '',
    frontFile: '',
    backFile: '',
  });
  // 选择的设备本地图片
  const [localFileState, setLocalFileState] = useSetState({
    localFrontFile: '',
    localBackFile: '',
  });

  /** 证件两面都上传成功, 开始OCR识别 */
  const onOcrGetCurb = async (fontFile: string, backFile: string) => {
    if (fontFile && backFile) {
      const modalId = modalDataStoreInstance.openModal({
        key: ModalList.COUNT_UP,
        i18nKey: Strings.ocrString.ocrIdentifyLoadingTip,
        titleKey: Strings.ocrString.ocrIdentifyTimeoutTip,
        confirmBtnCallback: async () => {
          onOcrGetCurb(fontFile, backFile);
        },
      });

      let result = await fetchUploadImageAndOCR({
        imageFrontUrl: fontFile,
        imageBackUrl: backFile,
        cardType: EvidenceEnumsSpace.ETakePicture.FRONT,
        timeout: 1000 * 60,
      });

      if (result?.code === BaseEnumsSpace.ENetworkStatus.SUCCESS) {
        if (result.data.status === 'SUCCEEDED') {
          // ocr 识别成功
          // setState({
          //   ...result?.data,
          // });
          if (result.data.annul === 'YES') {
            // 手动当做三次识别失败
            ocrFailedCallback?.();
          } else {
            ocrUpdateCallback?.();
          }
          nav.navigationGoBack();
        } else if (result.data.status === 'FAILED') {
          setPageState({
            ocrBackState: 'fail',
            ocrFrontState: 'fail',
          });

          // 重置展示的图片数据
          setLocalFileState({
            localFrontFile: '',
            localBackFile: '',
          });
          setState({
            ...result?.data,
            frontFile: '',
            backFile: '',
          });
        }
      }
      modalDataStoreInstance.closeModal(modalId);
    }
  };

  /** 上传CURP证件正面 */
  const handleClickFrondCurp = useCallback(async () => {
    setPageState((preState: any) => ({
      ...preState,
      ocrFrontState: 'success',
    }));
    nav.navigate(RouterConfig.OCR_TAKE_PHOTO as any, {
      cardType: BaseEnumsSpace.CardType.frontCard,
      onTakePhoto: async ({ fileUri }: { fileUri: string; error: string }) => {
        if (!fileUri) {
          return;
        }

        BaseInfoManager.changeLoadingModalVisible(true);
        let res = await fetchUploadImage(fileUri, {
          cardType: EvidenceEnumsSpace.ETakePicture.FRONT,
          fileType: 'image/jpg',
        });
        if (res?.code === BaseEnumsSpace.ENetworkStatus.SUCCESS) {
          setState({
            frontFile: res?.data,
          });
        }
        if (res?.data && state.backFile) {
          onOcrGetCurb(res?.data, state.backFile);
        } else {
          BaseInfoManager.changeLoadingModalVisible(false);
        }
      },
    });
  }, [state.frontFile, state.backFile]);

  /** 上传CURP证件背面 */
  const handleClickBackCurp = useCallback(async () => {
    // 拍摄idcard背面
    setPageState((preState: any) => ({
      ...preState,
      ocrBackState: 'success',
    }));
    nav.navigate(RouterConfig.OCR_TAKE_PHOTO as any, {
      cardType: BaseEnumsSpace.CardType.backCard,
      onTakePhoto: async ({ fileUri }: { fileUri: string }) => {
        if (!fileUri) {
          return;
        }

        BaseInfoManager.changeLoadingModalVisible(true);
        let res = await fetchUploadImage(fileUri, {
          cardType: EvidenceEnumsSpace.ETakePicture.BACK,
          fileType: 'image/jpg',
        });
        if (res?.code === BaseEnumsSpace.ENetworkStatus.SUCCESS) {
          // 上传背面照片成功
          setLocalFileState({
            localBackFile: fileUri,
          });
          setState({ backFile: res?.data });
        }
        if (state.backFile && res?.data) {
          onOcrGetCurb(state.backFile, res?.data);
        } else {
          BaseInfoManager.changeLoadingModalVisible(false);
        }
      },
    });
  }, [state.frontFile, state.backFile]);

  return {
    ...localFileState,
    ...state,
    handleClickFrondCurp,
    handleClickBackCurp,
    pageState,
  };
}
