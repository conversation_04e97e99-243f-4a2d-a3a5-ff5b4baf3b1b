import { Button, Image, Layout, PasswordInput, Text, TopNavigation, View } from '@/components';
import { BaseEnumsSpace, HitPointEnumsSpace } from '@/enums';
import { useLoginInfo, useOnInit, useSubscribeFilter } from '@/hooks';
import {
  BaseInfoContextType,
  BaseInfoManager,
  UserInfoContextType,
  UserInfoManager,
} from '@/managers';
import { RouterConfig } from '@/routes';
import { fetchModifyMobileVerifyPwd } from '@/server';
import { isMoreThanLength, nav } from '@/utils';
import { ReactElement, memo, useCallback, useMemo } from 'react';
import { ScrollView, TouchableWithoutFeedback } from 'react-native';
import { Strings, useNameSpace } from '@/i18n';
import { Colors } from '@/themes';

type TBaseInfoSubject = {
  isSetPassword: boolean;
};
const useData = () => {
  const { secureTextEntry, onToggleSecureEntry, onPasswordChange, password } = useLoginInfo();

  const phoneNumber = useSubscribeFilter({
    subject: UserInfoManager.messageCenter,
    filter: (subject: UserInfoContextType) => {
      return subject.userModel.mobile;
    },
  }) as string;

  /** 初始化 */
  useOnInit({
    pageKey: HitPointEnumsSpace.EPageKey.P_MODIFY_NUMBER_PASSWORD,
  });

  const handleNext = async () => {
    BaseInfoManager.changeLoadingModalVisible(true);

    if (!(await onVerifyPwd())) {
      BaseInfoManager.changeLoadingModalVisible(false);
      return;
    }

    BaseInfoManager.changeLoadingModalVisible(false);
  };

  const handleGoModifyPwd = useCallback(() => {
    nav.navigate(RouterConfig.MODIFY_PWD as any);
  }, []);

  const onVerifyPwd = async () => {
    let params = {
      mobile: phoneNumber,
      password: password,
    };
    let result = await fetchModifyMobileVerifyPwd(params);

    if (result?.code === BaseEnumsSpace.ENetworkStatus.SUCCESS) {
      nav.navigate(RouterConfig.NEW_MOBILE as any, {
        password,
      });
    }

    return result?.code === BaseEnumsSpace.ENetworkStatus.SUCCESS;
  };

  const buttonDisable = useMemo(() => {
    return !isMoreThanLength(password, 6);
  }, [password]);

  const handleClick = useCallback(() => {
    nav.navigate(RouterConfig.MODIFY_PWD as any, {
      fromPage: RouterConfig.VERIFY_PWD,
    });
  }, []);

  return {
    secureTextEntry,
    onToggleSecureEntry,
    onPasswordChange,
    password,
    handleNext,
    handleGoModifyPwd,
    buttonDisable,
    handleClick,
  };
};

const VerifyPwdPage = (): ReactElement => {
  const {
    secureTextEntry,
    onToggleSecureEntry,
    onPasswordChange,
    password,
    handleNext,
    handleGoModifyPwd,
    buttonDisable,
    handleClick,
  } = useData();
  const t = useNameSpace().t;

  const { isSetPassword } = useSubscribeFilter({
    subject: BaseInfoManager.messageCenter,
    filter: (subject: BaseInfoContextType) => {
      return {
        isSetPassword: subject.baseModel.isSetPassword,
      };
    },
  }) as TBaseInfoSubject;
  const renderTop = () => {
    return (
      <View
        margin="24 0 24 0"
        padding="12 12 12 12"
        style={{ backgroundColor: 'primary-color-100', borderRadius: 8 }}
        layoutStrategy="flexRowStartCenter">
        <Image name="_safeTipIcon" />
        <Text padding="0 12 0 8" i18nKey={Strings.validateBasicInfoString.tip} category="c1" />
      </View>
    );
  };
  const renderPwdTip = () => {
    return (
      <View layoutStrategy="flexRowStart" margin="16 0 32 0">
        <Image name="_grayInfo" />
        <Text
          i18nKey={Strings.validateBasicInfoString.setPwdTip}
          style={{ color: 'text-color-700' }}
          category="c2"
          padding="0 0 0 8"
        />
      </View>
    );
  };
  const $renderIsSetPwd = useMemo(() => {
    if (isSetPassword) {
      return (
        <View margin="0 16 0 16">
          <Text i18nKey={'myString.input_app_login_pwd'} />
          <PasswordInput
            type="line"
            validateConfig={[
              {
                condition: isMoreThanLength(password, 6),
                info: t('myString.pwd'),
                status: 'danger',
              },
            ]}
            prefixMargin={'24 0 0 0'}
            value={password}
            placeholderKey={'myString.pwd'}
            secureTextEntry={secureTextEntry}
            toggleSecureEntry={onToggleSecureEntry}
            onChangeText={onPasswordChange}
          />
          {renderPwdTip()}
          <Button
            status="primary"
            disabled={buttonDisable}
            onPress={handleNext}
            padding={'16 0 16 0'}
            textI18nKey="btnString.next"
          />
          <TouchableWithoutFeedback onPress={handleGoModifyPwd}>
            <Text
              margin="24 0 0 0"
              isCenter={true}
              i18nKey={'myString.forget_pwd'}
              style={{
                color: 'primary-color-500',
                textDecorationLine: 'underline',
              }}
            />
          </TouchableWithoutFeedback>
        </View>
      );
    }
    return <></>;
  }, [
    isSetPassword,
    password,
    secureTextEntry,
    onToggleSecureEntry,
    onPasswordChange,
    buttonDisable,
    handleNext,
    handleGoModifyPwd,
  ]);

  const $renderIsNotSetPwd = useMemo(() => {
    if (!isSetPassword) {
      return (
        <View>
          <View
            padding="16 12 16 12"
            margin="0 0 32 0"
            layoutStrategy="flexRowStartCenter"
            style={{ backgroundColor: Colors.WARN_COLOR_100, borderRadius: 8 }}>
            <Image name="_infoYellowMiddle" />
            <Text
              padding="0 0 0 1"
              margin="0 12 0 12"
              i18nKey={Strings.myString.verifyPwdDescIsNotPwd}
              category="p2"
            />
          </View>
          <Button
            status="primary"
            onPress={handleClick}
            textI18nKey={'myString.verifyPwdIsNotPwd'}
          />
        </View>
      );
    }
    return <></>;
  }, [isSetPassword, handleClick]);

  return (
    <Layout pLevel="0" level="0">
      <TopNavigation titleKey="myString.my_modify_mobile" />
      <ScrollView
        style={{ paddingHorizontal: 16 }}
        fadingEdgeLength={10}
        keyboardShouldPersistTaps="always">
        {renderTop()}
        {$renderIsSetPwd}
        {$renderIsNotSetPwd}
      </ScrollView>
    </Layout>
  );
};

export default memo(VerifyPwdPage);
