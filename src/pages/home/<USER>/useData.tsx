import { HitPointEnumsSpace } from '@/enums';
import { useOnInit } from '@/hooks';
import { BaseInfoManager } from '@/managers';
import { RouterConfig } from '@/routes';
import { fetchProductList } from '@/server';
import { OrderVOSpace } from '@/types';
import { nav, TrackEvent } from '@/utils';
import _ from 'lodash';
import { useState } from 'react';

export default function useData() {
  /** 初始化 */
  const [productList, setProductList] = useState<OrderVOSpace.ProductTypeItem[]>([]);

  /** 提示 */
  const [tips, setTips] = useState<string>('');

  /** 初始化 */
  useOnInit({
    pageKey: HitPointEnumsSpace.EPageKey.P_RELOAN_PRODUCT_SELECT,
    callback: async () => {
      getProductList();
    },
  });

  const getProductList = async () => {
    BaseInfoManager.changeLoadingModalVisible(true);
    const { code, data } = await fetchProductList();
    if (code === 0) {
      if (typeof data.tips === 'string') {
        setTips(data.tips);
      }
      if (Array.isArray(data.productList)) {
        setProductList(data.productList);
      }
    }
    BaseInfoManager.changeLoadingModalVisible(false);
  };

  const handleNextSingleTermProduct = (type: string) => {
    TrackEvent.trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_RELOAN_PRODUCT_SELECT,
        e: HitPointEnumsSpace.EEventKey.BTN_SUBMIT_SELECTION,
      },
      '1',
    );
    nav.resetRouteNavigateCanGoback(RouterConfig.SINGLE_TERM_RELOAN_PAGE as any, { type });
  };

  const handleNextMultiTermProduct = () => {
    TrackEvent.trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_RELOAN_PRODUCT_SELECT,
        e: HitPointEnumsSpace.EEventKey.BTN_SUBMIT_SELECTION,
      },
      '1',
    );
    nav.resetRouteNavigateCanGoback(RouterConfig.MULTI_PERIOD_RELOAN_PAGE as any, undefined);
  };

  return {
    productList,
    handleNextSingleTermProduct,
    handleNextMultiTermProduct,
    tips,
  };
}
