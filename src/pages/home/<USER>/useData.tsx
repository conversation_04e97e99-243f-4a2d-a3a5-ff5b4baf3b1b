import { HitPointEnumsSpace } from '@/enums';
import { useOnInit } from '@/hooks';
import { BaseInfoManager, UserInfoManager } from '@/managers';
import { Toast } from '@/nativeComponents';
import { RouterConfig } from '@/routes';
import { fetchProductList } from '@/server';
import { OrderVOSpace } from '@/types';
import { nav, TrackEvent } from '@/utils';
import _ from 'lodash';
import { useState } from 'react';

type productSelectType = 'single' | 'multi' | '';

export default function useData() {
  const [productList, setProductList] = useState<OrderVOSpace.ProductTypeItem[]>([]);

  const [tips, setTips] = useState<string>('');

  const [selectProduct, setSelectProduct] = useState<productSelectType>('');

  /** 初始化 */
  useOnInit({
    pageKey: HitPointEnumsSpace.EPageKey.P_FIRST_LOAN_PRODUCT_SELECT,
    callback: async () => {
      getProductList();
    },
  });

  const getProductList = async () => {
    BaseInfoManager.changeLoadingModalVisible(true);
    const { code, data } = await fetchProductList();
    if (code === 0) {
      if (typeof data.tips === 'string') {
        setTips(data.tips);
      }
      if (Array.isArray(data.productList)) {
        setProductList(data.productList);
      }
    }
    BaseInfoManager.changeLoadingModalVisible(false);
  };

  const handleNext = async () => {
    TrackEvent.trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_FIRST_LOAN_PRODUCT_SELECT,
        e: HitPointEnumsSpace.EEventKey.BTN_SUBMIT_SELECTION,
      },
      '1',
    );
    // 没有订单的新户进件支持回退
    let navigateNext = UserInfoManager.context.userModel.isNoApplyIdNewUser
      ? nav.navigate
      : nav.resetRouteNavigateCanGoback;
    if (selectProduct === 'single') {
      if (BaseInfoManager.context.baseModel.firstSelectAmountSlideSwitch) {
        navigateNext(RouterConfig.SELECT_AMOUNT_SLIDER as any, undefined);
      } else {
        navigateNext(RouterConfig.SELECT_AMOUNT as any, undefined);
      }
    } else if (selectProduct === 'multi') {
      navigateNext(RouterConfig.MULTI_PERIOD_FIRST_LOAN_PAGE as any, undefined);
    } else {
      // Toast('');
    }
  };

  return {
    selectProduct,
    setSelectProduct,
    productList,
    handleNext,
    tips,
  };
}
