/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-shadow */
import { BaseEnumsSpace, HitPointEnumsSpace, EProductType } from '@/enums';
import { useCheckAllPermissionAndRequestPermission, useDeviceDataReport, useOnInit } from '@/hooks';
import { useNameSpace } from '@/i18n';
import { BaseInfoManager, ModalList, UserInfoManager, modalDataStoreInstance } from '@/managers';
import { Toast } from '@/nativeComponents';
import { RouterConfig } from '@/routes';
import {
  OrderResponseSpace,
  fetchFirstLoanMultiPeriodProductPrice,
  fetchFirstPreLoanApply,
  fetchHomePageAmount,
  fetchMultiPeriodCalculate,
  fetchRKPushQuestionnaire,
  saveFirstLoanAmountConfig,
} from '@/server';
import { trackCommonEvent } from '@/trackEvent';
import { OrderVOSpace } from '@/types';
import { TrackEvent, isHasAllPermission, nav } from '@/utils';
import { useMemo, useState, useTransition } from 'react';

export interface IState extends OrderResponseSpace.RooutAmountData, OrderVOSpace.ProductPriceItem {
  loanAmount: number;
  calculateCost: OrderVOSpace.MultiPeriodCalculateType;
}

const defaultCalculateCost: OrderVOSpace.MultiPeriodCalculateType = {
  loanPeriods: 0,
  loanPlan: [],
  days: 0,
  realRepaymentAmount: '0',
  loanAmount: '0',
  firstRepayDate: '',
  firstRepaymentAmount: '',
  loanCostTips: '',
};

const defaultAmountState: IState = {
  amount: '---',
  term: '---',
  days: 0,
  minAmount: 0,
  maxAmount: 0,
  loanAmount: 0,
  defaultAmount: 0,
  increment: 0,
  enable: 'YES',
  calculateCost: defaultCalculateCost,
  installmentCount: 0,
  productType: EProductType.FIXED_DAY,
};

export default function useData() {
  const t = useNameSpace().t;
  const [isPending, startTransition] = useTransition();
  // 选中的额度配置编号
  const [selectedAmountConfigNumber, setSelectedAmountConfigNumber] = useState<number>(0);

  const [loanAmountConfigList, setLoanAmountConfigList] = useState<IState[]>([]);

  // 新户
  const [state, setState] = useState<IState>(defaultAmountState);

  /** 提示 */
  const [tips, setTips] = useState<string>('');

  // 复贷
  const amountState = useMemo(() => {
    if (loanAmountConfigList.length === 0) {
      // 新户或者老户未获取额度
      return state;
    } else {
      // 老用户已经获取额度配置
      return loanAmountConfigList[selectedAmountConfigNumber];
    }
  }, [selectedAmountConfigNumber, loanAmountConfigList, state]);

  const { onPreApplyReportDeviceData } = useDeviceDataReport();

  const onInit = async (refreshing: boolean = false) => {
    BaseInfoManager.changeLoadingModalVisible(true);
    /** 进入首页上报一次数据 */
    await TrackEvent.uploadEventLog();

    if (!refreshing) {
      if (!(await UserInfoManager.updateUserState())) {
        loading.current = false;
        BaseInfoManager.changeLoadingModalVisible(false);
        return;
      }

      /** 老用户获取滚动条额度 */
      if (!(await onGetFirstLoanAmountConfig())) {
        loading.current = false;
        BaseInfoManager.changeLoadingModalVisible(false);
        return;
      }
    }

    if (refreshing) {
      if (!(await UserInfoManager.updateUserState())) {
        loading.current = false;
        BaseInfoManager.changeLoadingModalVisible(false);
        return;
      }

      /** 老用户获取滚动条额度 */
      if (!(await onGetFirstLoanAmountConfig())) {
        loading.current = false;
        BaseInfoManager.changeLoadingModalVisible(false);
        return;
      }
    }
    loading.current = false;
    BaseInfoManager.changeLoadingModalVisible(false);
  };

  /** 初始化方法 */
  const { loading, refreshing, onRefresh } = useOnInit({
    callback: onInit,
    pageKey: HitPointEnumsSpace.EPageKey.P_MULTI_PERIOD_FIRST_LOAN,
    isBackAutoRefresh: true,
  });

  /** 获取首页信息 */
  const onGetHomePageAmout = async () => {
    onGetFirstLoanAmountConfig();
  };

  /** 创建预申请单 */
  const onSaveProductAmount = (): Promise<string> => {
    TrackEvent.trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_MULTI_PERIOD_FIRST_LOAN,
        e: HitPointEnumsSpace.EEventKey.BTN_FIRST_LOAN_APPLY,
      },
      '1',
    );

    return new Promise(async resolve => {
      let result = await saveFirstLoanAmountConfig({
        amount: amountState.loanAmount,
        loanTerm: amountState.days,
        productType: amountState.productType,
      });
      if (result?.code === BaseEnumsSpace.ENetworkStatus.SUCCESS) {
        await UserInfoManager.updateUserState();
        nav.nextToTopRouter(RouterConfig.MULTI_PERIOD_FIRST_LOAN_PAGE);
        BaseInfoManager.changeLoadingModalVisible(false);
      }
      resolve('');
    });
  };

  /** 获取复贷额度配置 */
  const onGetFirstLoanAmountConfig = async () => {
    const DEFAULT_MIN_AMOUNT = 200;
    const DEFAULT_MAX_AMOUNT = 500;
    const DEFAULT_INCREMENT = 100;
    if (loanAmountConfigList.length === 0) {
      let result = await fetchFirstLoanMultiPeriodProductPrice();
      if (result?.code === BaseEnumsSpace.ENetworkStatus.SUCCESS) {
        setTips(result?.data.tips || '');
        if (result?.data.productPriceList?.length !== 0) {
          let amountConfigList: IState[] = [];
          result.data.productPriceList.forEach((amountConfig: OrderVOSpace.ProductPriceItem) => {
            let {
              days,
              minAmount,
              maxAmount,
              increment,
              defaultAmount,
              installmentCount,
              productType,
            } = amountConfig;
            // 步进值纠正
            increment = Math.floor(increment ? DEFAULT_INCREMENT : Number(increment));
            // 最小值修正
            minAmount = Math.floor(Number(minAmount));
            // 最大值修正
            maxAmount = Math.floor(Number(maxAmount));
            // 最小值整除于步进值
            if (minAmount % increment != 0) {
              minAmount = Math.floor(minAmount) * increment;
            }
            // 最大值整除于步进值
            if (maxAmount % increment != 0) {
              maxAmount = Math.floor(maxAmount / increment) * increment;
            }
            // 最小金额最小值修正
            if (minAmount < DEFAULT_MIN_AMOUNT) {
              minAmount = DEFAULT_MIN_AMOUNT;
            }
            // 最大金额最小值修正;
            if (maxAmount < DEFAULT_MAX_AMOUNT) {
              maxAmount = DEFAULT_MAX_AMOUNT;
            }
            // 最大值必须大于最小值一个步进值
            if (minAmount >= maxAmount) {
              maxAmount = minAmount + increment;
            }
            const defaultMinAmount = minAmount || DEFAULT_MIN_AMOUNT;
            const defaultSelectedAmount =
              Number(defaultAmount) >= minAmount && Number(defaultAmount) <= maxAmount
                ? defaultAmount
                : maxAmount - increment;
            amountConfigList.push({
              ...defaultAmountState,
              ...amountConfig,
              days,
              minAmount: defaultMinAmount,
              maxAmount,
              defaultAmount: defaultSelectedAmount,
              increment,
              calculateCost: defaultCalculateCost,
              installmentCount,
              productType,
            });
          });
          onChangeAmount(
            amountConfigList[0].defaultAmount,
            amountConfigList[0].days,
            amountConfigList[0].installmentCount,
          );
          startTransition(() => {
            setState((preState: IState) => ({
              ...preState,
              ...amountConfigList[0],
            }));
            setLoanAmountConfigList(amountConfigList);
          });
        }
      }
      return result?.code === BaseEnumsSpace.ENetworkStatus.SUCCESS;
    }
    return true;
  };

  /** 获取复贷试算 */
  const onGetCalculateCost = async (
    selectedAmount: number = 0,
    selectedDays: number = 8,
    selectedPeriods: number = 1,
  ) => {
    if (selectedAmount === 0) {
      return;
    }
    BaseInfoManager.changeLoadingModalVisible(true);
    let result = await fetchMultiPeriodCalculate({
      applyAmount: selectedAmount,
      applyDays: selectedDays,
      applyPeriods: selectedPeriods,
    });
    BaseInfoManager.changeLoadingModalVisible(false);
    if (result?.code === BaseEnumsSpace.ENetworkStatus.SUCCESS) {
      return result.data;
    }

    return undefined;
  };

  /** 刷新首贷额度配置 */
  const onReferFirstLoanAmountConfig = async () => {
    loading.current = true;
    BaseInfoManager.changeLoadingModalVisible(true);
    await onGetFirstLoanAmountConfig();
    loading.current = false;
    BaseInfoManager.changeLoadingModalVisible(false);
  };

  const [checkPermission] = useCheckAllPermissionAndRequestPermission();

  const handleNext = async () => {
    BaseInfoManager.changeLoadingModalVisible(true);

    if (!(await UserInfoManager.updateUserState())) {
      BaseInfoManager.changeLoadingModalVisible(false);
      return;
    }

    nextLogic();
  };

  /** 开始贷款点击 */
  const handleClick = async () => {
    loading.current = true;

    // if (UserInfoManager.context.userModel.isUserTypeNew) {
    //   // 请求风控数据
    //   fetchRKPushQuestionnaire();
    // }

    if (!(await isHasAllPermission())) {
      let modalId = modalDataStoreInstance.openModal({
        key: ModalList.PERMISSION_APPLICATION,
        confirmBtnCallback: async () => {
          TrackEvent.trackCommonEvent(
            {
              p: HitPointEnumsSpace.EPageKey.P_MULTI_PERIOD_FIRST_LOAN,
              e: HitPointEnumsSpace.EEventKey.BTN_POST_PERMISSION,
            },
            '1',
          );

          if ((await checkPermission()) === 'agree') {
            await handleNext();
          }
        },
        cancelBtnCallback: () => {
          TrackEvent.trackCommonEvent(
            {
              p: HitPointEnumsSpace.EPageKey.P_MULTI_PERIOD_FIRST_LOAN,
              e: HitPointEnumsSpace.EEventKey.BTN_POST_PERMISSION,
            },
            '0',
          );
          modalDataStoreInstance.openModal({
            key: ModalList.INFO_PROMPT_CONFIRM,
            i18nKey: 'permissionAgreeString.peso_per_apply_again_tips',
            imageKey: '_notify',
            confirmBtnName: 'btnString.accept',
            cancelBtnName: 'btnString.noAccept',
            isBackdropClose: false,
            confirmBtnCallback: async () => {
              modalDataStoreInstance.closeModal(modalId);
              TrackEvent.trackCommonEvent(
                {
                  p: HitPointEnumsSpace.EPageKey.P_MULTI_PERIOD_FIRST_LOAN,
                  e: HitPointEnumsSpace.EEventKey.BTN_POST_PERMISSION,
                },
                '1',
              );

              if ((await checkPermission()) === 'agree') {
                await handleNext();
              }
            },
            cancelBtnCallback: () => {
              modalDataStoreInstance.closeModal(modalId);
              TrackEvent.trackCommonEvent(
                {
                  p: HitPointEnumsSpace.EPageKey.P_MULTI_PERIOD_FIRST_LOAN,
                  e: HitPointEnumsSpace.EEventKey.BTN_POST_PERMISSION,
                },
                '0',
              );
            },
          });
        },
      });
      loading.current = false;
      return;
    } else {
      await handleNext();
    }
  };

  /** 点击下一步逻辑 */
  const nextLogic = async () => {
    const { loanAmount, increment } = amountState;
    if (increment == 0 || loanAmount === 0) {
      modalDataStoreInstance.openModal({
        key: ModalList.INFO_PROMPT_CONFIRM,
        imageKey: '_refreshModal',
        i18nKey: 'homeString.update_select_amount',
        confirmBtnName: 'btnString.OK',
        isBackdropClose: false,
        confirmBtnCallback: () => {
          if (loanAmountConfigList.length === 0) {
            // 只有额度配置为空时，才去获取额度配置
            onReferFirstLoanAmountConfig();
          }
        },
      });
      BaseInfoManager.changeLoadingModalVisible(false);
      return;
    }
    await onSaveProductAmount();
    BaseInfoManager.changeLoadingModalVisible(false);
  };

  /** 修改选择额度 */
  const onChangeAmount = async (defaultAmount: number, day?: number, periods?: number) => {
    let result = await onGetCalculateCost(
      defaultAmount,
      day || amountState.days,
      periods || amountState.installmentCount,
    );
    setLoanAmountConfigList((preState: IState[]) => {
      preState[selectedAmountConfigNumber] = {
        ...preState[selectedAmountConfigNumber],
        calculateCost: result || defaultCalculateCost,
        defaultAmount,
        loanAmount: defaultAmount,
      };
      return [...preState];
    });
  };

  const onUpdateAmount = async (defaultAmount: number) => {
    setLoanAmountConfigList((preState: IState[]) => {
      preState[selectedAmountConfigNumber] = {
        ...preState[selectedAmountConfigNumber],
        defaultAmount,
        loanAmount: defaultAmount,
      };
      return [...preState];
    });
  };

  const onChangeAmountConfigNumber = async (index: number) => {
    let amountConfig = loanAmountConfigList[index];
    let loanAmount = amountState.loanAmount;
    let defaultAmount = amountConfig.defaultAmount;
    let maxAmount = amountConfig.maxAmount;
    let minAmount = amountConfig.minAmount;
    let defaultDays = amountConfig.days;
    let installmentCount = amountConfig.installmentCount;
    let selectAmount = 0;

    if (loanAmount != 0 && loanAmount <= maxAmount && loanAmount >= minAmount) {
      selectAmount = loanAmount;
    } else if (defaultAmount >= minAmount && defaultAmount <= maxAmount) {
      selectAmount = defaultAmount;
    } else {
      selectAmount = minAmount;
    }

    // if (loanAmount != 0) {
    loanAmount = selectAmount;
    // }

    let result = await onGetCalculateCost(loanAmount, defaultDays, installmentCount);

    setLoanAmountConfigList((preState: IState[]) => {
      preState[index] = {
        ...preState[index],
        calculateCost: result || defaultCalculateCost,
        defaultAmount: selectAmount,
        loanAmount: loanAmount,
        installmentCount: installmentCount,
      };
      return [...preState];
    });

    setSelectedAmountConfigNumber(index);
  };
  return {
    ...amountState,
    calculateCost: amountState.calculateCost || defaultCalculateCost,
    selectedAmountConfigNumber,
    tips,
    onChangeAmountConfigNumber,
    loanAmountConfigList,
    isPending,
    handleClick,
    refreshing,
    onRefresh,
    onChangeAmount,
    onUpdateAmount,
    onGetHomePageAmout,
  };
}
