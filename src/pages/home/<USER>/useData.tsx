/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-shadow */
import { BaseEnumsSpace, HitPointEnumsSpace, UserEnumsSpace } from '@/enums';
import {
  useCheckAllPermissionAndRequestPermission,
  useDeviceDataReport,
  useNavigationFocusAndBlurHandle,
  useOnInit,
} from '@/hooks';
import { useNameSpace } from '@/i18n';
import {
  BaseInfoManager,
  ModalList,
  UserInfoManager,
  modalDataStoreInstance,
  KVManager,
  WalletInfoManager,
} from '@/managers';
import { Toast } from '@/nativeComponents';
import { RouterConfig } from '@/routes';
import {
  OrderResponseSpace,
  confirmReLoanApply,
  fetchCalculateCostOnReLoan,
  fetchCreateApply,
  fetchHomePageAmount,
  fetchRKPushQuestionnaire,
  fetchReLoanAmountConfig,
  fetchReLoanAutoWithholdBankCard,
  fetchWithholdContract,
  reLoanQuestionSubmit,
  fetchCouponsByConditions,
  saveReLoanAmountConfig,
  responseHandler,
  fetchLoanFixedDateHoliday,
} from '@/server';
import { trackCommonEvent } from '@/trackEvent';
import { OrderVOSpace, UserVOSpace, EWithholderTipType } from '@/types';
import { TrackEvent, isHasAllPermission, nav } from '@/utils';
import { useCallback, useMemo, useRef, useState, useTransition } from 'react';
import { RefType as ActivitySwiperRefType } from '../../components/activitySwiper';
import CryptoJS from 'crypto-js';
import { ELocalKey } from '@/localStorage';
import { useUpdateEffect } from 'ahooks';

export interface IState
  extends OrderResponseSpace.RooutAmountData,
  OrderResponseSpace.ReloanConfigData {
  loanAmount: number;
  calculateCost: OrderVOSpace.CalculateCostOnReLoanType;
  repayDate?: string;
}

const defaultCalculateCost: OrderVOSpace.CalculateCostOnReLoanType = {
  loanAmount: '-',
  transferFee: '-',
  transferFeeVat: '-',
  processFee: '-',
  processFeeVat: '-',
  repaymentAmount: '-',
  days: '-',
  loanDate: '-',
  loanDateNewFormat: '-',
  repayDate: '-',
  repayDateNewFormat: '-',
  realAmount: '--',
  realRepaymentAmount: '--',
  creditCouponAmount: '--',
  creditCouponPercent: '--',
  couponUseType: 'amount',
};

const defaultAmountState: IState = {
  amount: '---',
  term: '---',
  days: 0,
  minAmount: 0,
  maxAmount: 0,
  loanAmount: 0,
  defaultAmount: 0,
  increment: 0,
  enable: 'YES',
  calculateCost: defaultCalculateCost,
};

export default function useData(props: { type: string }) {
  const { type } = props;
  const t = useNameSpace().t;
  let activitySwiperRef = useRef<ActivitySwiperRefType>(null);
  const [isPending, startTransition] = useTransition();
  // 选中的额度配置编号
  const [selectedAmountConfigNumber, setSelectedAmountConfigNumber] = useState<number>(0);

  const [reLoanAmountConfigList, setReLoanAmountConfigList] = useState<IState[]>([]);
  const [reloanTips, setReloanTips] = useState<string>('');
  const [serviceFeePerDayRate, setServiceFeePerDayRate] = useState('-');

  // 新户
  const [state, setState] = useState<IState>(defaultAmountState);

  /** 是否开通自动代扣 */
  const [autoWithhold, setAutoWithhold] = useState<boolean>(
    BaseInfoManager.context.baseModel.autoWithholdReLoanHomeDefaultValue,
  );

  const [reloanHomeWithholdBankCardInfo, setReloanHomeWithholdBankCardInfo] =
    useState<OrderVOSpace.ReloanHomeWithholdBankCardInfo>({
      isShowCard: '',
      cardNo: '',
      bankName: '',
      bankCode: '',
      type: '',
      isDef: '',
      isSelf: '',
      othersFatherName: '',
      othersMotherName: '',
      othersCurpNumber: '',
      othersName: '',
      othersRelation: '',
      withholdAuthorizeStatus: '',
    });
  // 优惠券
  const [couponList, setCouponList] = useState<UserVOSpace.CouponsItem[]>([]);
  // 选择的优惠券
  const [couponSelected, setCouponSelected] = useState<UserVOSpace.CouponsItem[]>([]);
  const couponSelectedRef = useRef<UserVOSpace.CouponsItem[]>([]);

  const [showCouponHelpModal, setShowCouponHelpModal] = useState<boolean>(false);
  const [showCouponSelectModal, setShowCouponSelectModal] = useState<boolean>(false);
  // 复贷额度或还款日期是否变化，用于判断是否需要刷新，true 则不刷新
  const reloanAmountConfigChangedRef = useRef<boolean>(false);

  // 复贷可选日期范围和不可选的节假日
  const [reloanRepayDays, setReloanRepayDays] = useState<OrderVOSpace.LoanRepayDays>({
    startDate: '',
    endDate: '',
    holidays: [],
  });
  const [repayDate, setRepayDate] = useState<string>('');
  const repayDateRef = useRef<string>('');

  useUpdateEffect(() => {
    repayDateRef.current = repayDate;
  }, [repayDate]);


  const onChangeRepayDate = async (repayDate: string) => {
    if (repayDate === repayDateRef.current) {
      return
    }
    trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_HOMEPAGE,
        e: HitPointEnumsSpace.EEventKey.BTN_CALENDAR_SELECT_DATE,
      },
      repayDate,
    );

    reloanAmountConfigChangedRef.current = true;
    let result = await onGetCalculateCost(
      String(amountState.loanAmount),
      String(amountState.days),
      repayDate,
    );
    setReLoanAmountConfigList((preState: IState[]) => {
      preState[selectedAmountConfigNumber] = {
        ...preState[selectedAmountConfigNumber],
        calculateCost: result || defaultCalculateCost,
      };
      return [...preState];
    });
    setRepayDate(repayDate);
  };

  const onOpenAutoWithhold = useCallback(() => {
    modalDataStoreInstance.openModal({
      key: ModalList.WITHHOLDER_TIP,
      extra: EWithholderTipType.OPEN,
      confirmBtnCallback: async () => {
        TrackEvent.trackCommonEvent(
          {
            p: HitPointEnumsSpace.EPageKey.P_HOMEPAGE,
            e: HitPointEnumsSpace.EEventKey.BTN_AUTOMATIC_REPAYMENT_CLICK,
          },
          '1',
        );
        setAutoWithhold(true);
      },
    });
  }, []);

  /** 获取可选日期范围和节假日（日历组件不可选） */
  const onGetLoanHoliday = async () => {
    const result = await fetchLoanFixedDateHoliday();
    if (result?.code === BaseEnumsSpace.ENetworkStatus.SUCCESS) {
      setReloanRepayDays(result.data);
    }
  };
  const onCloseAutoWithhold = useCallback(() => {
    modalDataStoreInstance.openModal({
      key: ModalList.WITHHOLDER_TIP,
      extra: EWithholderTipType.CLOSE,
      confirmBtnCallback: () => {
        TrackEvent.trackCommonEvent(
          {
            p: HitPointEnumsSpace.EPageKey.P_HOMEPAGE,
            e: HitPointEnumsSpace.EEventKey.BTN_AUTOMATIC_REPAYMENT_CLICK,
          },
          '0',
        );
        setAutoWithhold(false);
      },
    });
  }, []);

  /** 跳转到自动代扣协议 */
  const openAutoWithholdContract = async () => {
    TrackEvent.trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_HOMEPAGE,
        e: HitPointEnumsSpace.EEventKey.BTN_NEGOTIATE_CHECK,
      },
      '1',
    );
    let result = await fetchWithholdContract({
      cardNo: reloanHomeWithholdBankCardInfo.cardNo,
      bankName: reloanHomeWithholdBankCardInfo.bankName,
    });
    if (result.code === BaseEnumsSpace.ENetworkStatus.SUCCESS) {
      nav.navigate(RouterConfig.AUTOMATIC_WITHHOLD_PROTOCOL as any, {
        html: CryptoJS.enc.Base64.parse(String(result.data)).toString(CryptoJS.enc.Utf8),
        currentRoute: RouterConfig.HOME_SCREEN,
        acceptHandle: onOpenAutoWithhold,
        rejectHandle: onCloseAutoWithhold,
      });
    }

    return result?.code === BaseEnumsSpace.ENetworkStatus.SUCCESS;
  };

  const amountStateRef = useRef<IState>(state);
  // 复贷
  const amountState = useMemo(() => {
    if (reLoanAmountConfigList.length === 0 || !UserInfoManager.context.userModel.isUserTypeOld) {
      // 新户或者老户未获取额度
      amountStateRef.current = state;
      return state;
    } else {
      // 老用户已经获取额度配置
      amountStateRef.current = reLoanAmountConfigList[selectedAmountConfigNumber];
      return reLoanAmountConfigList[selectedAmountConfigNumber];
    }
  }, [selectedAmountConfigNumber, reLoanAmountConfigList, state]);

  // 复贷合同是否阅读状态
  const [isReadContract, setIsReadContract] = useState<boolean>(true);
  const onChangeIsReadContract = useCallback(
    (isReadContract: boolean) => {
      TrackEvent.trackCommonEvent(
        {
          p: HitPointEnumsSpace.EPageKey.P_HOMEPAGE,
          e: HitPointEnumsSpace.EEventKey.BTN_LOAN_CONTRACT_CHECK,
        },
        isReadContract ? '1' : '0',
      );
      setIsReadContract(isReadContract);
    },
    [isReadContract],
  );

  // 复贷增信弹窗
  const [reloanQuestionModalVisible, setReloanQuestionModalVisible] = useState<boolean>(false);

  /** 关闭复贷增信弹窗 */
  const onCloseReloanQuestionModal = useCallback(() => {
    setReloanQuestionModalVisible(false);
  }, []);

  const onSubmitReloanQuestion = useCallback(
    async (data: any) => {
      BaseInfoManager.changeLoadingModalVisible(true);
      // 提交复贷增信
      const result = await reLoanQuestionSubmit({ answerList: data });
      if (result?.code === BaseEnumsSpace.ENetworkStatus.SUCCESS) {
        // 复贷增信提交成功，开始创建复贷申请
        await oldUserCreateApplyLogic();
      }
      BaseInfoManager.changeLoadingModalVisible(false);
    },
    [selectedAmountConfigNumber, reloanQuestionModalVisible],
  );

  const { onApplyReportDeviceData, onInApplyReportDeviceData } = useDeviceDataReport();

  const onInit = async (refreshing: boolean = false) => {
    BaseInfoManager.changeLoadingModalVisible(true);
    /** 进入首页上报一次数据 */
    await TrackEvent.uploadEventLog();

    if (!refreshing) {
      if (!(await UserInfoManager.updateUserState())) {
        loading.current = false;
        BaseInfoManager.changeLoadingModalVisible(false);
        return;
      }
      /** 可还款日期：可选范围+不可选的节假日 */
      if (UserInfoManager.context.userModel.isUserTypeOld) {
        onGetLoanHoliday();
      }
      getReloanHomeWhithholdBankCard();
      WalletInfoManager.updateBankCardInfo();

      /** 老用户获取滚动条额度 */
      if (UserInfoManager.context.userModel.isUserTypeOld && !(await onGetReLoanAmountConfig())) {
        loading.current = false;
        BaseInfoManager.changeLoadingModalVisible(false);
        return;
      }
    }

    if (refreshing) {
      refreshData();
    }

    loading.current = false;
    BaseInfoManager.changeLoadingModalVisible(false);
  };
  /** 刷新数据 */
  const refreshData = async () => {
    if (!(await UserInfoManager.updateUserState())) {
      loading.current = false;
      BaseInfoManager.changeLoadingModalVisible(false);
      return;
    }

    if (UserInfoManager.context.userModel.isUserTypeOld) {
      activitySwiperRef.current?.resetValue();
      /** 复贷可还款日期：可选范围+不可选的节假日 */
      onGetLoanHoliday();
    }

    /** 新用户获取页面额度 */
    // if (UserInfoManager.context.userModel.isUserTypeNew && !(await onGetHomePageAmout())) {
    //   loading.current = false;
    //   BaseInfoManager.changeLoadingModalVisible(false);
    //   return;
    // }
    getRepayCouponList();
    getReloanHomeWhithholdBankCard();
    WalletInfoManager.updateBankCardInfo();

    /** 复贷用户获取额度配置 */
    if (UserInfoManager.context.userModel.isUserTypeOld) {
      if (!(await onGetReLoanAmountConfig())) {
        loading.current = false;
        BaseInfoManager.changeLoadingModalVisible(false);
      }
      /**
       * 重新试算
       */
      if (!!amountStateRef.current?.amount) {
        if (BaseInfoManager.context.baseModel.repayDateSwitch && !repayDateRef.current) {
          return;
        }

        await onChangeAmount(
          amountStateRef.current?.loanAmount,
          amountStateRef.current?.days,
          repayDateRef.current,
        );
      }
    }

    loading.current = false;
    BaseInfoManager.changeLoadingModalVisible(false);
  };
  /** 初始化方法 */
  const { loading, refreshing, onRefresh } = useOnInit({
    callback: onInit,
    refreshCallback: () => {
      if (UserInfoManager.context.userModel.isUserTypeOld) {
        refreshData();
      }
    },
    pageKey: HitPointEnumsSpace.EPageKey.P_HOMEPAGE,
    // isActivityAutoRefresh: false,
    // isBackAutoRefresh: true,
    /** isBackAutoRefresh: false 为了合同页返回可以看到用户选择的优惠券 */
    isBackAutoRefresh: false,
    buryCallback: () => {
      // checkCanceledApplyRemind()
    },
  });
  const checkCanceledApplyRemind = async () => {
    if (!(await UserInfoManager.updateUserState())) {
      return;
    }
    if (
      UserInfoManager.context.userModel.isApplyCancel &&
      UserInfoManager.context.userModel.isUserTypeOld
    ) {
      // 复贷用户取消订单, 取消的申请单没有显示过就显示提示弹窗
      const lastApplyList = KVManager.action.getArray(ELocalKey.CANCEL_APPLY_LIST) || [];
      let theApplyExist = false;
      if (lastApplyList && lastApplyList.length > 0) {
        theApplyExist = !!lastApplyList.find(
          (apply: any) => apply.applyId === UserInfoManager.context.userModel.applyOrderId,
        );
      }
      if (!theApplyExist && UserInfoManager.context.userModel.applyOrderId) {
        lastApplyList.push({
          applyId: UserInfoManager.context.userModel.applyOrderId,
        });
        KVManager.action.setArray(ELocalKey.CANCEL_APPLY_LIST, lastApplyList);
        modalDataStoreInstance.openModal({
          key: ModalList.REMIND_RELOAN,
        });
      }
    }
  };
  /** 获取首页信息 */
  const onGetHomePageAmout = async () => {
    BaseInfoManager.changeLoadingModalVisible(true);
    let result = await fetchHomePageAmount();
    BaseInfoManager.changeLoadingModalVisible(false);

    if (result?.code === BaseEnumsSpace.ENetworkStatus.SUCCESS) {
      let { amount, term } = result?.data;
      startTransition(() => {
        setState((preState: IState) => ({
          ...preState,
          amount: amount || '---',
          term: term || '---',
        }));
      });
    }

    return result?.code === BaseEnumsSpace.ENetworkStatus.SUCCESS;
  };

  /** 获取复贷首页是否需要展示银行卡自动代扣绑定服务 */
  const getReloanHomeWhithholdBankCard = async () => {
    // 自动代扣功能开关打开，并且是老用户
    if (
      BaseInfoManager.context.baseModel.isWithholdSwitch &&
      UserInfoManager.context.userModel.isUserTypeOld
    ) {
      const { code, data } = await fetchReLoanAutoWithholdBankCard();
      if (code === 0 && data) {
        setReloanHomeWithholdBankCardInfo(data);
      }
    }
  };

  /** 获取复贷额度配置 */
  const onGetReLoanAmountConfig = async () => {
    const DEFAULT_MIN_AMOUNT = 200;
    const DEFAULT_MAX_AMOUNT = 500;
    const DEFAULT_INCREMENT = 100;
    if (reLoanAmountConfigList.length === 0) {
      let result = await fetchReLoanAmountConfig();
      if (result?.code === BaseEnumsSpace.ENetworkStatus.SUCCESS) {
        if (result?.data?.productPriceList?.length !== 0) {
          let amountConfigList: IState[] = [];
          result.data.productPriceList.forEach(
            (amountConfig: OrderResponseSpace.ReloanConfigData) => {
              let { days, minAmount, maxAmount, increment, defaultAmount } = amountConfig;
              // 步进值纠正
              increment = Math.floor(increment ? DEFAULT_INCREMENT : Number(increment));
              // 最小值修正
              minAmount = Math.floor(Number(minAmount));
              // 最大值修正
              maxAmount = Math.floor(Number(maxAmount));
              // 最小值整除于步进值
              if (minAmount % increment != 0) {
                minAmount = Math.floor(minAmount) * increment;
              }
              // 最大值整除于步进值
              if (maxAmount % increment != 0) {
                maxAmount = Math.floor(maxAmount / increment) * increment;
              }
              // 最小金额最小值修正
              if (minAmount < DEFAULT_MIN_AMOUNT) {
                minAmount = DEFAULT_MIN_AMOUNT;
              }
              // 最大金额最小值修正;
              if (maxAmount < DEFAULT_MAX_AMOUNT) {
                maxAmount = DEFAULT_MAX_AMOUNT;
              }
              // 最大值必须大于最小值一个步进值
              if (minAmount >= maxAmount) {
                maxAmount = minAmount + increment;
              }
              const defaultMinAmount = minAmount || DEFAULT_MIN_AMOUNT;
              const defaultSelectedAmount =
                Number(defaultAmount) >= minAmount && Number(defaultAmount) <= maxAmount
                  ? defaultAmount
                  : maxAmount - increment;
              amountConfigList.push({
                ...defaultAmountState,
                ...amountConfig,
                days,
                minAmount: defaultMinAmount,
                maxAmount,
                defaultAmount: defaultSelectedAmount,
                increment,
                calculateCost: defaultCalculateCost,
              });
            },
          );
          onChangeAmount(
            amountConfigList[0].defaultAmount,
            amountConfigList[0].days,
            amountConfigList[0].repayDate,
          );
          startTransition(() => {
            setState((preState: IState) => ({
              ...preState,
              ...amountConfigList[0],
            }));
            setReLoanAmountConfigList(amountConfigList);
            setReloanTips(result?.data?.tips || '');
            setServiceFeePerDayRate(result?.data?.serviceFeePerDayRate || '-');
          });
        }
      }
      return result?.code === BaseEnumsSpace.ENetworkStatus.SUCCESS;
    }
    return true;
  };
  const getCouponSerialNumbers = (
    couponList: UserVOSpace.CouponsItem[] = couponSelectedRef.current,
  ) => {
    const serialNumbers = couponList.map(i => i.serialNumber).join(',') || '';
    return serialNumbers;
  };
  /** 获取复贷试算 */
  const onGetCalculateCost = async (
    selectedAmount: string = '0',
    selectedDays: string = '8',
    repayDate: string = '',
  ) => {
    if (selectedAmount === '0') {
      return;
    }
    /** 还款日期没有选择 */
    if (BaseInfoManager.context.baseModel.repayDateSwitch && repayDate === '') {
      return;
    }
    BaseInfoManager.changeLoadingModalVisible(true);
    let result = await fetchCalculateCostOnReLoan({
      selectedAmount,
      selectedDays,
      couponSerialNumber: getCouponSerialNumbers(),
      repayDate,
    });
    BaseInfoManager.changeLoadingModalVisible(false);
    if (result?.code === BaseEnumsSpace.ENetworkStatus.SUCCESS) {
      setReLoanAmountConfigList((preState: IState[]) => {
        preState[selectedAmountConfigNumber] = {
          ...preState[selectedAmountConfigNumber],
          calculateCost: result.data,
        };
        return [...preState];
      });
      await getRepayCouponList();
      return result.data;
    }

    return undefined;
  };

  /** 刷新复贷额度 */
  const onReferReLoanAmountConfig = async () => {
    loading.current = true;
    BaseInfoManager.changeLoadingModalVisible(true);
    await onGetReLoanAmountConfig();
    loading.current = false;
    BaseInfoManager.changeLoadingModalVisible(false);
  };

  /** 复贷创建申请单 */
  const onCreateReLoanApply = async (): Promise<string> => {
    const { days, loanAmount } = amountState;
    if (Number(loanAmount) === 0) {
      modalDataStoreInstance.openModal({
        key: ModalList.INFO_PROMPT_CONFIRM,
        imageKey: '_refreshModal',
        i18nKey: 'homeString.update_select_amount',
        confirmBtnName: 'btnString.OKay',
        isBackdropClose: false,
        confirmBtnCallback: () => { },
      });
      return '';
    } else {
      TrackEvent.trackCommonEvent(
        {
          p: HitPointEnumsSpace.EPageKey.P_APPLY_PAGE,
          e: HitPointEnumsSpace.EEventKey.BTN_SUBMIT_RELOABN_APPLY,
        },
        String(loanAmount),
      );
      let withholdAuthorizeStatus =
        reloanHomeWithholdBankCardInfo.isShowCard === 'YES' ? (autoWithhold ? 'YES' : 'NO') : '';
      let params = {
        days,
        amount: loanAmount,
        cardNo: reloanHomeWithholdBankCardInfo.cardNo,
        withholdAuthorizeStatus: withholdAuthorizeStatus,
        repayDate: repayDate,
        repayType: BaseInfoManager.context.baseModel.repayDateSwitch ? 'FIXED_DATE' : 'FIXED_DAY',
      };
      let result = await confirmReLoanApply(params);
      if (result?.code === BaseEnumsSpace.ENetworkStatus.SUCCESS) {
        // 复贷申请
        return result.data;
      }
    }

    return '';
  };
  /** 获取可用的还款优惠券 */
  const getRepayCouponList = useCallback(async () => {
    const { code, data } = await fetchCouponsByConditions({
      status: UserEnumsSpace.ECouponsStatus.AVAILABLE,
    });
    if (code === 0) {
      // 这里根据data需要判断是不是需要打开添加还款日历提醒的弹窗
      if (
        data.length > 0
        // && !UserInfoManager.context.userModel.isCreditSuccessWaitFace
      ) {
        const _data = data.filter(item => item.canCheck === 'YES');
        const selectCoupons = _data.filter(
          item => item.couponStatus === UserEnumsSpace.ECouponsStatus.BINDING,
        );
        const _selectCoupons = couponSelectedRef.current.length
          ? couponSelectedRef.current
          : selectCoupons;
        setCouponSelected(_selectCoupons);
        setCouponList(_data);
      }
    }
    return true;
  }, []);

  const [checkPermission] = useCheckAllPermissionAndRequestPermission();

  const handleNext = async () => {
    BaseInfoManager.changeLoadingModalVisible(true);

    if (!(await UserInfoManager.updateUserState())) {
      BaseInfoManager.changeLoadingModalVisible(false);
      return;
    }

    // 没有勾选合同且打开了<是否跳过选择额度(预申请,直接放款)>的开关
    if (
      !isReadContract &&
      UserInfoManager.context.userModel.isUserTypeOld &&
      BaseInfoManager.context.baseModel.isSkipConfirmAcceptCreditSwitch
    ) {
      Toast(t('homeString.not_check_contract'));
      BaseInfoManager.changeLoadingModalVisible(false);
      return;
    }
    const { error } = await responseHandler(
      saveReLoanAmountConfig({
        amount: amountState.loanAmount,
        loanTerm: amountState.days,
        productType: type,
        couponSerialNumber: getCouponSerialNumbers(),
        repayDate: repayDateRef.current,
      }),
    );
    if (error) {
      return;
    }
    /** 老户 */
    UserInfoManager.context.userModel.isUserTypeOld && oldUserLogic();
    /** 新户 */
    UserInfoManager.context.userModel.isUserTypeNew && newUserLogic();
  };

  /** 开始贷款点击 */
  const handleClick = async () => {
    loading.current = true;

    if (!(await isHasAllPermission())) {
      let modalId = modalDataStoreInstance.openModal({
        key: ModalList.PERMISSION_APPLICATION,
        confirmBtnCallback: async () => {
          TrackEvent.trackCommonEvent(
            {
              p: HitPointEnumsSpace.EPageKey.P_APPLY_PAGE,
              e: HitPointEnumsSpace.EEventKey.BTN_PRIVACY_DISCLOSURE_NEXT_STEP,
            },
            '1',
          );

          let checkResult = await checkPermission();

          switch (checkResult) {
            case 'agree':
              await handleNext();
              break;
            case 'denied':
              modalDataStoreInstance.openModal({
                key: ModalList.INFO_PROMPT_CONFIRM,
                i18nKey: 'permissionAgreeString.peso_per_apply_again_tips',
                imageKey: '_notify',
                confirmBtnName: 'btnString.accept',
                cancelBtnName: 'btnString.noAccept',
                isBackdropClose: false,
                confirmBtnCallback: async () => {
                  modalDataStoreInstance.closeModal(modalId);
                  TrackEvent.trackCommonEvent(
                    {
                      p: HitPointEnumsSpace.EPageKey.P_APPLY_PAGE,
                      e: HitPointEnumsSpace.EEventKey.BTN_PERMISSION_SECONDASK,
                    },
                    '1',
                  );

                  let checkResult = await checkPermission();
                  switch (checkResult) {
                    case 'agree':
                      await handleNext();
                      break;
                    case 'denied':
                      break;
                    case 'neverAsk':
                      nav.navigate(RouterConfig.PERMISSION_REFUSE_TO_SETTING as any, {
                        toNext: handleNext,
                      });
                      break;
                  }
                },
                cancelBtnCallback: () => {
                  modalDataStoreInstance.closeModal(modalId);
                  TrackEvent.trackCommonEvent(
                    {
                      p: HitPointEnumsSpace.EPageKey.P_APPLY_PAGE,
                      e: HitPointEnumsSpace.EEventKey.BTN_PERMISSION_SECONDASK,
                    },
                    '0',
                  );
                },
              });
              break;
            case 'neverAsk':
              nav.navigate(RouterConfig.PERMISSION_REFUSE_TO_SETTING as any, {
                toNext: handleNext,
              });
              break;
          }
        },
        // cancelBtnCallback: () => {
        //   TrackEvent.trackCommonEvent(
        //     {
        //       p: HitPointEnumsSpace.EPageKey.P_APPLY_PAGE,
        //       e: HitPointEnumsSpace.EEventKey.BTN_POST_PERMISSION,
        //     },
        //     '0',
        //   );
        //   modalDataStoreInstance.openModal({
        //     key: ModalList.INFO_PROMPT_CONFIRM,
        //     i18nKey: 'permissionAgreeString.peso_per_apply_again_tips',
        //     imageKey: '_notify',
        //     confirmBtnName: 'btnString.accept',
        //     cancelBtnName: 'btnString.noAccept',
        //     isBackdropClose: false,
        //     confirmBtnCallback: async () => {
        //       modalDataStoreInstance.closeModal(modalId);
        //       TrackEvent.trackCommonEvent(
        //         {
        //           p: HitPointEnumsSpace.EPageKey.P_APPLY_PAGE,
        //           e: HitPointEnumsSpace.EEventKey.BTN_POST_PERMISSION,
        //         },
        //         '1',
        //       );

        //       if (await checkPermission()) {
        //         await handleNext();
        //       }
        //     },
        //     cancelBtnCallback: () => {
        //       modalDataStoreInstance.closeModal(modalId);
        //       TrackEvent.trackCommonEvent(
        //         {
        //           p: HitPointEnumsSpace.EPageKey.P_APPLY_PAGE,
        //           e: HitPointEnumsSpace.EEventKey.BTN_POST_PERMISSION,
        //         },
        //         '0',
        //       );
        //     },
        //   });
        // },
      });
      loading.current = false;
      return;
    } else {
      await handleNext();
    }
  };

  /** 老户点击按钮逻辑 */
  const oldUserLogic = async () => {
    if (UserInfoManager.context.userModel.isReview) {
      TrackEvent.trackCommonEvent(
        {
          p: HitPointEnumsSpace.EPageKey.P_APPLY_PAGE,
          e: HitPointEnumsSpace.EEventKey.BTN_IN_APPLY,
        },
        '1',
      );
      oldUserReviewLogic();
      await onInApplyReportDeviceData();
      await TrackEvent.uploadEventLog();
    } else {
      const { loanAmount, increment } = amountState;
      if (increment == 0 || loanAmount === 0) {
        modalDataStoreInstance.openModal({
          key: ModalList.INFO_PROMPT_CONFIRM,
          imageKey: '_refreshModal',
          i18nKey: 'homeString.update_select_amount',
          confirmBtnName: 'btnString.OK',
          isBackdropClose: false,
          confirmBtnCallback: () => {
            if (reLoanAmountConfigList.length === 0) {
              // 只有额度配置为空时，才去获取额度配置
              onReferReLoanAmountConfig();
            }
          },
        });
        BaseInfoManager.changeLoadingModalVisible(false);
        return;
      }
      if (UserInfoManager.context.userModel.loanCount === 1) {
        BaseInfoManager.changeLoadingModalVisible(false);
        // if (await BaseInfoManager.updateAppConfig()) {
        if (BaseInfoManager.context.baseModel.isReloanQuestionOpen) {
          // 复贷二单增信
          setReloanQuestionModalVisible(true);
          TrackEvent.trackCommonEvent(
            {
              p: HitPointEnumsSpace.EPageKey.P_APPLY_PAGE,
              e: HitPointEnumsSpace.EEventKey.BTN_IN_APPLY,
            },
            '1',
          );
          await TrackEvent.uploadEventLog();
          await onInApplyReportDeviceData();
          return;
        } else {
          oldUserCreateApplyLogic();
        }
        // }
        return;
      }
      oldUserCreateApplyLogic();
    }
  };

  /** 老户review逻辑 */
  const oldUserReviewLogic = async () => {
    BaseInfoManager.changeLoadingModalVisible(true);
    if (!(await UserInfoManager.updateUserState())) {
      BaseInfoManager.changeLoadingModalVisible(false);
      return;
    }

    /** 是否谷歌审核账号 */
    BaseInfoManager.changeLoadingModalVisible(false);
    nav.nextToTopRouter(RouterConfig.HOME_SCREEN);
    return;
  };
  /** 新户点击按钮逻辑 */
  const newUserLogic = async () => {
    /** 新户提交申请埋点 */
    if (UserInfoManager.context.userModel.isNewUserCreateApplyError) {
      newUserCreateApplyErrorLogic();
    } else {
      /** 埋点 */
      TrackEvent.trackCommonEvent(
        {
          p: HitPointEnumsSpace.EPageKey.P_APPLY_PAGE,
          e: HitPointEnumsSpace.EEventKey.BTN_IN_APPLY,
        },
        '1',
      );
      await TrackEvent.uploadEventLog();

      neUserInApplyOrReviewLogic();
    }
  };

  /** 创建申请单 */
  const onCreateApply = (): Promise<string> => {
    return new Promise(async resolve => {
      let result = await fetchCreateApply();
      if (result?.code === BaseEnumsSpace.ENetworkStatus.SUCCESS) {
        resolve(result.data);
      }
      resolve('');
    });
  };

  /** 新户创建申请单异常逻辑 */
  const newUserCreateApplyErrorLogic = async () => {
    BaseInfoManager.changeLoadingModalVisible(true);
    let applyId: string = await onCreateApply();
    if (!applyId) {
      BaseInfoManager.changeLoadingModalVisible(false);
      return;
    }

    if (!(await UserInfoManager.updateUserState())) {
      BaseInfoManager.changeLoadingModalVisible(false);
      return;
    }

    await onApplyReportDeviceData(applyId);
    TrackEvent.uploadEventLog();

    BaseInfoManager.changeLoadingModalVisible(false);

    if (BaseInfoManager.context.baseModel.isEnhanceCredit) {
      nav.resetRouteNavigate(RouterConfig.INCREASE_CREDIT as any);
    } else {
      nav.nextToTopRouter(RouterConfig.HOME_SCREEN);
    }
    return;
  };

  /** 新户进件或者reivew逻辑 */
  const neUserInApplyOrReviewLogic = async () => {
    if (!(await UserInfoManager.updateUserState())) {
      BaseInfoManager.changeLoadingModalVisible(false);
      return;
    }
    BaseInfoManager.changeLoadingModalVisible(false);
    // 第三方账户绑定
    nav.nextToTopRouter(RouterConfig.HOME_SCREEN);

    await onInApplyReportDeviceData();
  };

  /** 老用户创建申请单逻辑 */
  const oldUserCreateApplyLogic = async () => {
    BaseInfoManager.changeLoadingModalVisible(true);
    let applyId: string = await onCreateReLoanApply();

    if (!applyId) {
      BaseInfoManager.changeLoadingModalVisible(false);
      return;
    }

    setReloanQuestionModalVisible(false);

    await onApplyReportDeviceData(applyId);
    await TrackEvent.uploadEventLog();

    if (!(await UserInfoManager.updateUserState())) {
      BaseInfoManager.changeLoadingModalVisible(false);
      return;
    }
    /** 是否谷歌审核账号 */
    BaseInfoManager.changeLoadingModalVisible(false);
    nav.nextToTopRouter(RouterConfig.HOME_SCREEN);
    return;
  };

  /** 修改选择额度 */
  const onChangeAmount = async (defaultAmount: number, day?: number, fRepayDate?: string) => {
    couponSelectedRef.current = [];
    let result = await onGetCalculateCost(
      String(defaultAmount),
      String(day || amountState.days),
      fRepayDate || repayDate,
    );
    setReLoanAmountConfigList((preState: IState[]) => {
      preState[selectedAmountConfigNumber] = {
        ...preState[selectedAmountConfigNumber],
        calculateCost: result || defaultCalculateCost,
        defaultAmount,
        loanAmount: defaultAmount,
      };
      return [...preState];
    });
    setCouponSelected([]);
  };

  const onUpdateAmount = async (defaultAmount: number) => {
    setReLoanAmountConfigList((preState: IState[]) => {
      preState[selectedAmountConfigNumber] = {
        ...preState[selectedAmountConfigNumber],
        defaultAmount,
        loanAmount: defaultAmount,
      };
      return [...preState];
    });
  };

  const onChangeAmountConfigNumber = async (index: number) => {
    couponSelectedRef.current = [];
    let amountConfig = reLoanAmountConfigList[index];
    let loanAmount = amountState.loanAmount;
    let defaultAmount = amountConfig.defaultAmount;
    let maxAmount = amountConfig.maxAmount;
    let minAmount = amountConfig.minAmount;
    let defaultDays = amountConfig.days;
    let selectAmount = 0;

    if (loanAmount != 0 && loanAmount <= maxAmount && loanAmount >= minAmount) {
      selectAmount = loanAmount;
    } else if (defaultAmount >= minAmount && defaultAmount <= maxAmount) {
      selectAmount = defaultAmount;
    } else {
      selectAmount = minAmount;
    }

    // if (loanAmount != 0) {
    loanAmount = selectAmount;
    // }

    let result = await onGetCalculateCost(String(loanAmount), String(defaultDays));

    setReLoanAmountConfigList((preState: IState[]) => {
      preState[index] = {
        ...preState[index],
        calculateCost: result || defaultCalculateCost,
        defaultAmount: selectAmount,
        loanAmount: loanAmount,
      };
      return [...preState];
    });
    setCouponSelected([]);
    setSelectedAmountConfigNumber(index);
  };
  const onCloseCouponHelpModal = useCallback(() => {
    setShowCouponHelpModal(false);
  }, []);
  const onCloseCouponSelectModal = useCallback(() => {
    setShowCouponSelectModal(false);
  }, []);
  const onShowCouponHelpModal = useCallback(() => {
    setShowCouponHelpModal(true);
  }, []);
  const onShowCouponSelectModal = useCallback(() => {
    TrackEvent.trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_APPLY_PAGE,
        e: HitPointEnumsSpace.EEventKey.BTN_USE_COUPON,
      },
      '1',
    );
    setShowCouponSelectModal(true);
  }, []);
  const onConfirmSelectCoupon = useCallback(
    async (coupons?: UserVOSpace.CouponsItem[]) => {
      coupons = coupons || [];
      couponSelectedRef.current = coupons;
      setCouponSelected(coupons);
      onGetCalculateCost(String(amountState.loanAmount), String(amountState.days), repayDate);
    },
    [amountState, repayDate],
  );

  const onToggleRepayDateModal = useCallback(() => {
    TrackEvent.trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_HOMEPAGE,
        e: HitPointEnumsSpace.EEventKey.F_SELECT_REPAYMENT_DATE,
      },
      '',
    );
  }, []);

  const disable = BaseInfoManager.context.baseModel.repayDateSwitch ? !repayDate : false;

  return {
    ...amountState,
    amountStateRef,
    activitySwiperRef,
    isReadContract,
    onChangeIsReadContract,
    selectedAmountConfigNumber,
    onChangeAmountConfigNumber,
    reLoanAmountConfigList,
    isPending,
    handleClick,
    refreshing,
    onRefresh,
    onChangeAmount,
    onUpdateAmount,
    onGetHomePageAmout,
    reloanQuestionModalVisible,
    onCloseReloanQuestionModal,
    onSubmitReloanQuestion,
    autoWithhold,
    onOpenAutoWithhold,
    onCloseAutoWithhold,
    openAutoWithholdContract,
    reloanHomeWithholdBankCardInfo,
    onCloseCouponHelpModal,
    onCloseCouponSelectModal,
    onShowCouponHelpModal,
    onShowCouponSelectModal,
    onConfirmSelectCoupon,
    couponList,
    couponSelected,
    showCouponHelpModal,
    showCouponSelectModal,
    getCouponSerialNumbers,
    reloanTips,
    serviceFeePerDayRate,
    reloanRepayDays,
    repayDate,
    repayDateRef,
    onChangeRepayDate,
    disable,
    onToggleRepayDateModal,
  };
}
