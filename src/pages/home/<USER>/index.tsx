/* eslint-disable react-native/no-inline-styles */

import {
  Button,
  DashedLine,
  Image,
  Layout,
  LinearGradient,
  Swiper,
  Text,
  TopNavigation,
  View,
} from '@/components';
import { HitPointEnumsSpace } from '@/enums';
import { ScreenProps } from '@/types';
import React, { ReactElement, useMemo } from 'react';
import { Dimensions, ScrollView, TouchableWithoutFeedback } from 'react-native';
import useData from './useData';
import { useNameSpace } from '@/i18n';
import RenderHtml from 'react-native-render-html';
import { TrackEvent } from '@/utils';

const rewardList: string[] = [
  'C*"a acaba de obtener un préstamo de MXN$3000',
  'E*"y acaba de obtener un préstamo de MXN$1200',
  'M*a acaba de obtener un préstamo de MXN$5000',
  'J*"t acaba de obtener un préstamo de MXN$3500',
  'L*"a acaba de obtener un préstamo de MXN$1500',
  'L*"y acaba de obtener un préstamo de MXN$1000',
  'A*"d acaba de obtener un préstamo de MXN$2500',
  'S*"e acaba de obtener un préstamo de MXN$5000',
  'A*"l acaba de obtener un préstamo de MXN$6800',
  'R**y acaba de obtener un préstamo de MXN$4000',
  'C*"e acaba de obtener un préstamo de MXN$2800',
  'L*"e acaba de obtener un préstamo de MXN$3000',
  'H*"n acaba de obtener un préstamo de MXN$4500',
  'S*"a acaba de obtener un préstamo de MXN$3500',
  'E*"n acaba de obtener un préstamo de MXN$5800',
  'B*"y acaba de obtener un préstamo de MXN$1800',
  'M*"e acaba de obtener un préstamo de MXN$2500',
  'I**a acaba de obtener un préstamo de MXN$3600',
  'G*"e acaba de obtener un préstamo de MXN$1500',
  'P*"e acaba de obtener un préstamo de MXN$2400',
  'F*"a acaba de obtener un préstamo de MXN$4800',
];

export default ({ route }: ScreenProps<{}>): ReactElement => {
  const { selectProduct, setSelectProduct, productList, handleNext, tips } = useData();

  const t = useNameSpace().t;

  const $productList = useMemo(() => {
    const selectedSingle = selectProduct === 'single';

    const selectedMulti = selectProduct === 'multi';

    return productList.map((item, index) => {
      const {
        /** 产品名 */
        name,
        /** 产品图标地址 */
        logoUrl,
        /** 产品类型 FIXED_DATE(固定日期),FIXED_DAY（固定天数）,INSTALLMENT（分期） */
        type,
        /** 是否启用 */
        enable,
        /** 最大天数 */
        minDay,
        /** 最小天数 */
        maxDay,
        /** 账期数量 */
        minInstallmentCount,
        maxInstallmentCount,
        /** 是否限量 */
        limitedLogo,
        /** 产品描述 */
        desc,
      } = item;
      if (type === 'INSTALLMENT' && enable === 'YES') {
        return (
          <TouchableWithoutFeedback
            key={index.toString()}
            onPress={() => {
              TrackEvent.trackCommonEvent(
                {
                  p: HitPointEnumsSpace.EPageKey.P_FIRST_LOAN_PRODUCT_SELECT,
                  e: HitPointEnumsSpace.EEventKey.BTN_SELECT_INSTALLMENT,
                },
                '1',
              );
              setSelectProduct('multi');
            }}>
            <View
              margin="12 0  0"
              style={{
                borderRadius: 8,
                backgroundColor: 'background-color-0',
                borderColor: selectedMulti ? 'primary-color-500' : 'background-color-0',
                borderWidth: 1,
              }}>
              <LinearGradient
                style={{ borderRadius: 8 }}
                start={{ x: 0.5, y: 0 }} // 起点：顶部中心
                end={{ x: 0.5, y: 1 }} // 终点：底部中心
                colors={['#DBE4FF', '#F6F9FF']}>
                <View layoutStrategy="flexRowStartCenter">
                  <Image margin="0 8 0 0" name="_flexIcon" />
                  <Text>
                    <Text
                      category="p2"
                      style={{ color: 'primary-color-700' }}
                      i18nKey="productSelectString.flex"
                    />
                    <Text
                      category="p2"
                      style={{ color: 'primary-color-700' }}
                      i18nKey="productSelectString.flexExpand"
                    />
                  </Text>
                </View>
              </LinearGradient>

              <View margin="16 16 0 16">
                <View layoutStrategy="flexRowStart">
                  <Text>
                    <Text category="h1" bold="600" i18nKey="productSelectString.amountUpTo" />
                    <Text category="h1" bold="600" textContent={'25,000'} />
                  </Text>
                  {limitedLogo === 'YES' && (
                    <Image margin="0 0 0 6" resizeMode="contain" name="_flexLimit" />
                  )}
                </View>
                <View layoutStrategy="flexRowBetweenCenter">
                  <View layoutStrategy="flexRowStartCenter">
                    <View
                      style={{
                        backgroundColor: 'success-color-500',
                        width: 6,
                        height: 6,
                        marginRight: 6,
                        borderRadius: 3,
                      }}
                    />
                    <Text
                      category="c1"
                      textContent={t('productSelectString.flexLoanDaysLimit', {
                        min: minDay,
                        max: maxDay,
                      })}
                    />
                  </View>
                  <View layoutStrategy="flexRowStartCenter">
                    <View
                      style={{
                        backgroundColor: 'success-color-500',
                        width: 6,
                        height: 6,
                        marginRight: 6,
                        borderRadius: 3,
                      }}
                    />
                    <Text
                      category="c1"
                      textContent={t('productSelectString.flexBillingPeriod', {
                        min: minInstallmentCount,
                        max: maxInstallmentCount,
                      })}
                    />
                  </View>
                </View>
                <View margin="16 0 0 0">
                  <DashedLine height={0.5} dashColor="line-color-200" />
                </View>
              </View>
              <View margin="12 16 16 16">
                <Text
                  category="c2"
                  style={{
                    color: 'text-color-600',
                  }}
                  i18nKey="productSelectString.flexLoanTips"
                />
              </View>
              <Image
                name={selectedMulti ? '_productSelected' : '_productUnSelected'}
                style={{
                  position: 'absolute',
                  right: 0,
                  bottom: 0,
                }}
              />
            </View>
          </TouchableWithoutFeedback>
        );
      } else if (enable === 'YES') {
        return (
          <TouchableWithoutFeedback
            key={index.toString()}
            onPress={() => {
              TrackEvent.trackCommonEvent(
                {
                  p: HitPointEnumsSpace.EPageKey.P_FIRST_LOAN_PRODUCT_SELECT,
                  e: HitPointEnumsSpace.EEventKey.BTN_SELECT_SHORTLOAN,
                },
                '1',
              );
              setSelectProduct('single');
            }}>
            <View
              margin="12 0 0 0"
              style={{
                borderRadius: 8,
                backgroundColor: 'background-color-0',
                borderColor: selectedSingle ? 'primary-color-500' : 'background-color-0',
                borderWidth: 1,
              }}>
              <LinearGradient
                style={{ borderRadius: 8 }}
                start={{ x: 0.5, y: 0 }} // 起点：顶部中心
                end={{ x: 0.5, y: 1 }} // 终点：底部中心
                colors={['#DBE4FF', '#F6F9FF']}>
                <View layoutStrategy="flexRowStartCenter">
                  <Image margin="0 8 0 0" name="_expressIcon" />
                  <Text>
                    <Text
                      category="p2"
                      style={{ color: 'primary-color-700' }}
                      i18nKey="productSelectString.express"
                    />
                    <Text
                      category="p2"
                      style={{ color: 'primary-color-700' }}
                      i18nKey="productSelectString.expressExpand"
                    />
                  </Text>
                </View>
              </LinearGradient>
              <View margin="16 16 16 16">
                <Text>
                  <Text category="h1" bold="600" i18nKey="productSelectString.amountUpTo" />
                  <Text category="h1" bold="600" textContent={'25,000'} />
                </Text>
                <View layoutStrategy="flexRowBetweenCenter">
                  <View layoutStrategy="flexRowStartCenter">
                    <View
                      style={{
                        backgroundColor: 'success-color-500',
                        width: 6,
                        height: 6,
                        marginRight: 6,
                        borderRadius: 3,
                      }}
                    />
                    <Text
                      category="c1"
                      textContent={t('productSelectString.expressLoanDaysLimit', {
                        min: minDay,
                        max: maxDay,
                      })}
                    />
                  </View>
                  <View layoutStrategy="flexRowStartCenter">
                    <View
                      style={{
                        backgroundColor: 'success-color-500',
                        width: 6,
                        height: 6,
                        marginRight: 6,
                        borderRadius: 3,
                      }}
                    />
                    <Text category="c1" i18nKey="productSelectString.expressBillingPeriod" />
                  </View>
                </View>
                <View margin="16 0 0 0">
                  <DashedLine height={0.5} dashColor="line-color-200" />
                </View>
              </View>
              <View margin="0 16 16 16">
                <Text
                  category="c2"
                  style={{
                    color: 'text-color-600',
                  }}
                  i18nKey="productSelectString.expressLoanTips"
                />
              </View>
              <Image
                name={selectedSingle ? '_productSelected' : '_productUnSelected'}
                style={{
                  position: 'absolute',
                  right: 0,
                  bottom: 0,
                }}
              />
            </View>
          </TouchableWithoutFeedback>
        );
      }
    });
  }, [productList, selectProduct, t]);

  return (
    <>
      <Layout pLevel="0" level="1" topCompensateColor="primary-color-500">
        <TopNavigation
          showLogoAction={true}
          bottomLine={false}
          pageKey={HitPointEnumsSpace.EPageKey.P_RELOAN_PRODUCT_SELECT}
          showMessage
          type="primary"
          isShowVip
        />
        <View
          height={40}
          padding="8 16 8 16"
          layoutStrategy="flexRowStartCenter"
          style={{ backgroundColor: 'primary-color-600' }}>
          <Image name="_rewardNotice" />
          <Swiper
            style={{ flex: 1 }}
            height={24}
            backgroundColor="primary-color-600"
            bottomType={'none'}
            isCenter={false}
            category="c2"
            list={rewardList}
          />
        </View>
        <ScrollView keyboardShouldPersistTaps="always">
          <View
            style={{
              height: 140,
              backgroundColor: 'primary-color-500',
            }}
          />
          <View margin="-140 16 12 16">
            <Text
              margin="6 0 0 0"
              category="h3"
              status="control"
              i18nKey="productSelectString.productSelectTip"
            />
            {$productList}
            {/* <View margin="12 0 0 0" padding="12 8 12 8" cardType="baseType">
              <View layoutStrategy="flexRowStartCenter">
                <Image name="_blackNotice" />
                <Text
                  margin="0 10 0 10"
                  style={{color: 'text-color-600'}}
                  category="p1"
                  i18nKey={'loanConfirmString.loan_notice_title'}
                />
              </View>
              <RenderHtml
                contentWidth={Dimensions.get('window').width - 56}
                source={{html: tips || ''}}
              />
            </View> */}
          </View>
        </ScrollView>
        <View padding="12 32 28 32" style={{ backgroundColor: 'background-color-0' }}>
          <Button
            onPress={handleNext}
            disabled={selectProduct === ''}
            textI18nKey="btnString.loanNow"
          />
        </View>
      </Layout>
    </>
  );
};
