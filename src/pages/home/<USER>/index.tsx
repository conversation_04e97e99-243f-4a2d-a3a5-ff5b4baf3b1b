/* eslint-disable react/react-in-jsx-scope */
import React from 'react';
import { Layout, TopNavigation, View, Button, Text, Image } from '@/components';
import { ScreenProps } from '@/types';
import { ReactElement } from 'react';
import { Dimensions, RefreshControl, ScrollView } from 'react-native';
import AmountSliderView from './components/AmountSliderView';
import useData from './useData';

import { HitPointEnumsSpace } from '@/enums';
import { useTheme } from '@/hooks';
import _ from 'lodash';
import RenderHtml from 'react-native-render-html';

export default ({}: ScreenProps<{}>): ReactElement => {
  const theme = useTheme();
  const {
    isPending,
    refreshing,
    onRefresh,
    handleClick,
    loanAmount,
    loanAmountConfigList,
    selectedAmountConfigNumber,
    tips,
    ...props
  } = useData();

  return (
    <>
      <Layout pLevel="0" level="1" topCompensateColor="primary-color-500">
        <TopNavigation
          showLogoAction={true}
          bottomLine={false}
          pageKey={HitPointEnumsSpace.EPageKey.P_MULTI_PERIOD_RELOAN}
          showMessage
          type="primary"
          isShowVip
        />
        <ScrollView
          keyboardShouldPersistTaps="always"
          refreshControl={
            <RefreshControl
              colors={[theme['primary-color-500']]}
              refreshing={refreshing}
              onRefresh={onRefresh}
            />
          }>
          <View margin="0 0 0 0">
            <View
              style={{
                height: 180,
                backgroundColor: 'primary-color-500',
              }}></View>
            <View margin="-180 0 0 0">
              <AmountSliderView
                loanAmount={loanAmount}
                loanAmountConfigList={loanAmountConfigList}
                selectedAmountConfigNumber={selectedAmountConfigNumber}
                isPending={isPending}
                {...props}
              />

              <View margin="12 16 12 16" padding="12 8 12 8" cardType="baseType">
                <View layoutStrategy="flexRowStartCenter">
                  <Image name="_blackNotice" />
                  <Text
                    margin="0 10 0 10"
                    style={{ color: 'text-color-600' }}
                    category="p1"
                    i18nKey={'loanConfirmString.loan_notice_title'}
                  />
                </View>
                <RenderHtml
                  contentWidth={Dimensions.get('window').width - 56}
                  source={{ html: tips || '' }}
                />
              </View>

              {/* <VipUpLevelTipCard
                pageKey={HitPointEnumsSpace.EPageKey.P_HOMEPAGE}
              />
              <ActivitySwiper
                // @ts-ignore
                ref={activitySwiperRef}
                margin="0 16 12 16"
                location={BaseEnumsSpace.EBannerLocationType.RELOAN_HOME}
              /> */}
            </View>
          </View>
        </ScrollView>
        <View
          padding={'16 16 12 16'}
          style={{
            backgroundColor: 'background-color-0',
            borderBottomLeftRadius: 8,
            borderBottomRightRadius: 8,
            // borderLeftWidth: 1,
            // borderRightWidth: 1,
            // borderBottomWidth: 1,
            // borderColor: 'line-color-200',
          }}>
          <Button
            margin="8 0 0 0"
            padding="12 0 12 0"
            status="primary"
            onPress={handleClick}
            textI18nKey={'btnString.loanNow'}
          />
        </View>
      </Layout>
    </>
  );
};
