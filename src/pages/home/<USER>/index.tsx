/* eslint-disable react/react-in-jsx-scope */
import React from 'react';
import { Layout, TopNavigation, View, BusinessUI, Check, Text, Button, Image } from '@/components';
import { BaseInfoManager, UserInfoManager, modalDataStoreInstance, ModalList, AppDefaultConfigManager } from '@/managers';
import { ConfirmPhoneNumberUsage } from '@/modals';
import { ScreenProps } from '@/types';
import { ReactElement } from 'react';
import { Dimensions, RefreshControl, ScrollView, StatusBar } from 'react-native';
import AmountSliderView from '../../components/loanAmount/AmountSliderView';
import LoanAmountView from './components/LoanAmountView';
import LoanDescView from './components/LoanDescView';
import ReloanQuestionnaireModal from '../../components/loanAmount/ReloanQuestionnaireModal';
import useData from './useData';
import ActivitySwiper, { RefType as ActivitySwiperRefType } from '../../components/activitySwiper';
import { BaseEnumsSpace, HitPointEnumsSpace } from '@/enums';
import { useTheme } from '@/hooks';
import ChangePasswordModal from '../../components/loanAmount/ChangePasswordModal';
const { VipUpLevelTipCard } = BusinessUI;
import { ImageNames } from '@/config';
import { fetchPreApplyContract } from '@/server';
import { nav, TrackEvent } from '@/utils';
import { RouterConfig } from '@/routes';
import CryptoJS from 'crypto-js';
import _ from "lodash"
import RenderHtml from 'react-native-render-html';



export default ({ }: ScreenProps<{}>): ReactElement => {
  const theme = useTheme();
  const {
    isPending,
    reloanQuestionModalVisible,
    onCloseReloanQuestionModal,
    onSubmitReloanQuestion,
    refreshing,
    onRefresh,
    activitySwiperRef,
    changePwdModalVisible,
    onChangePwdModalVisible,
    loanAmount,
    repayDateRef,
    reLoanAmountConfigList,
    selectedAmountConfigNumber,
    getCouponSerialNumbers,
    isReadContract,
    onChangeIsReadContract,
    handleClick,
    disable,
    reloanTips,
    ...props
  } = useData();
  /** 打开复贷合同 */
  const onOpenContractOnReloan = async () => {
    if (
      String(loanAmount) === '0' ||
      (BaseInfoManager.context.baseModel.repayDateSwitch && !repayDateRef.current)
    ) {
      modalDataStoreInstance.openModal({
        key: ModalList.INFO_PROMPT_CONFIRM,
        imageKey: ImageNames._notify,
        i18nKey: 'homeString.update_open_contract',
        confirmBtnName: 'btnString.OK',
        isBackdropClose: false,
        confirmBtnCallback: () => { },
      });
      return;
    }
    let result = await fetchPreApplyContract({
      /** 选择额度 */
      selectedAmount: String(loanAmount),
      /** 选择天数 */
      selectedDays: String(reLoanAmountConfigList[selectedAmountConfigNumber].days),
      /** 还款日期 */
      repayDate: repayDateRef.current,
      /** 预申请单号 */
      applyOrderId: '',
      /** 优惠券编号 */
      couponSerialNumber: getCouponSerialNumbers(),
      /** 首贷、复贷 */
      firstApply: 'NO',
    });

    if (result?.code === BaseEnumsSpace.ENetworkStatus.SUCCESS) {
      nav.navigate(RouterConfig.LOAN_CONTRACT as any, {
        html: CryptoJS.enc.Base64.parse(String(result?.data)).toString(CryptoJS.enc.Utf8),
      });
    }

    return result?.code === BaseEnumsSpace.ENetworkStatus.SUCCESS;
  };

  const handleOpenContract = _.debounce(async () => {
    TrackEvent.trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_HOMEPAGE,
        e: HitPointEnumsSpace.EEventKey.BTN_LOAN_AGREEMENT_VIEW,
      },
      '',
    );
    await onOpenContractOnReloan();
  }, 100);
  return (
    <>
      <Layout pLevel="0" level="1" topCompensateColor="primary-color-500">
        <TopNavigation
          showLogoAction={true}
          bottomLine={false}
          pageKey={HitPointEnumsSpace.EPageKey.P_HOMEPAGE}
          showMessage
          type="primary"
          isShowVip
        />
        <ScrollView
          keyboardShouldPersistTaps="always"
          overScrollMode={'never'}
          refreshControl={
            <RefreshControl
              colors={[theme['primary-color-500']]}
              refreshing={refreshing}
              onRefresh={onRefresh}
            />
          }>
          <View margin="0 0 0 0">
            {UserInfoManager.context.userModel.isUserTypeOld && (
              <>
                <View
                  style={{
                    height: 180,
                    backgroundColor: 'primary-color-500',
                  }}></View>
                <View margin='-180 0 0 0'>
                  <AmountSliderView
                    loanAmount={loanAmount}
                    reLoanAmountConfigList={reLoanAmountConfigList}
                    selectedAmountConfigNumber={selectedAmountConfigNumber}
                    isPending={isPending}
                    activitySwipeRef={activitySwiperRef}
                    onOpenContract={handleOpenContract}
                    isReadContract={isReadContract}
                    {...props}
                  />
                  {AppDefaultConfigManager.context.appDefaultConfigModel.defaultConfigState
                    ?.calculateCostConfig?.newStyleSwitch === 'NO' ||
                    !AppDefaultConfigManager.context.appDefaultConfigModel.defaultConfigState
                      ?.calculateCostConfig?.newStyleSwitch ? (
                    <View
                      margin="12 16 0 16"
                      style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                      }}>
                      <Check checked={isReadContract} onChange={onChangeIsReadContract} />
                      <Text margin="2 0 0 0">
                        <Text
                          category="p2"
                          i18nKey={'directPaymentString.read_contract_one'}
                          style={{
                            color: 'text-color-600',
                          }}
                        />
                        <Text
                          category="p2"
                          onPress={handleOpenContract}
                          i18nKey={'directPaymentString.read_contract_two'}
                          style={{
                            color: 'primary-color-500',
                            textDecorationLine: 'underline',
                          }}
                        />
                      </Text>
                    </View>
                  ) : null}

                  <View
                    style={{ display: !!reloanTips ? 'flex' : 'none' }}
                    margin="12 16 0 16"
                    padding="12 12 12 12"
                    cardType="baseType">
                    <View layoutStrategy="flexRowStartCenter">
                      <Image name="_blackNotice" />
                      <Text
                        margin="0 10 0 10"
                        style={{ color: 'text-color-600' }}
                        category="p1"
                        i18nKey={'loanConfirmString.loan_notice_title'}
                      />
                    </View>
                    <RenderHtml
                      contentWidth={Dimensions.get('window').width - 48}
                      source={{ html: reloanTips }}
                    />
                  </View>
                  <VipUpLevelTipCard pageKey={HitPointEnumsSpace.EPageKey.P_HOMEPAGE} />
                  <ActivitySwiper
                    // @ts-ignore
                    ref={activitySwiperRef}
                    margin="0 16 12 16"
                    location={BaseEnumsSpace.EBannerLocationType.RELOAN_HOME}
                  />
                </View>
              </>
            )}
            {(UserInfoManager.context.userModel.isUserTypeNew ||
              !UserInfoManager.context.userModel.isUserTypeOld) && (
                <>
                  <LoanAmountView
                    {...props}
                    handleClick={handleClick}
                  />
                  <LoanDescView />
                  <VipUpLevelTipCard pageKey={HitPointEnumsSpace.EPageKey.P_HOMEPAGE} />
                </>
              )}
          </View>
        </ScrollView>
        {
          UserInfoManager.context.userModel.isUserTypeOld && <View
            padding={'16 16 12 16'}
            style={{
              backgroundColor: 'background-color-0',
              borderBottomLeftRadius: 8,
              borderBottomRightRadius: 8,
            }}>
            <Button
              margin="8 0 0 0"
              padding="12 0 12 0"
              status="primary"
              onPress={handleClick}
              textI18nKey={'btnString.quickGetLoan'}
              disabled={disable}
            />
          </View>
        }
      </Layout>
      <ConfirmPhoneNumberUsage />
      <ReloanQuestionnaireModal
        visible={reloanQuestionModalVisible}
        onCancel={onCloseReloanQuestionModal}
        onConfrim={onSubmitReloanQuestion}
      />
      <ChangePasswordModal
        visible={changePwdModalVisible}
        changeDialogVisibe={onChangePwdModalVisible}
      />
    </>
  );
};
