/* eslint-disable react/react-in-jsx-scope */
import React from 'react';
import { Layout, TopNavigation, View, Button, Check, Text, Image } from '@/components';
import {
  AppDefaultConfigManager,
  BaseInfoManager,
  modalDataStoreInstance,
  ModalList,
} from '@/managers';
import { ConfirmPhoneNumberUsage } from '@/modals';
import { ScreenProps } from '@/types';
import { ReactElement } from 'react';
import { Dimensions, RefreshControl, ScrollView } from 'react-native';
import AmountSliderView from '../../components/loanAmount/AmountSliderView';
import ReloanQuestionnaireModal from '../../components/loanAmount/ReloanQuestionnaireModal';
import useData from './useData';

import { BaseEnumsSpace, HitPointEnumsSpace } from '@/enums';
import { useTheme } from '@/hooks';
import { nav, TrackEvent } from '@/utils';
import { fetchPreApplyContract } from '@/server';
import { RouterConfig } from '@/routes';
import CryptoJS from 'crypto-js'; //replace thie with script tag in browser env
import _ from 'lodash';
import RenderHtml from 'react-native-render-html';
import { ImageNames } from '@/config';

export default ({ route }: ScreenProps<{ type: string }>): ReactElement => {
  const { type } = route?.params || { type: 'FIXED_DATE' };
  const theme = useTheme();
  const {
    isPending,
    reloanQuestionModalVisible,
    onCloseReloanQuestionModal,
    onSubmitReloanQuestion,
    refreshing,
    onRefresh,
    activitySwiperRef,
    handleClick,
    onChangeIsReadContract,
    isReadContract,
    loanAmount,
    reLoanAmountConfigList,
    selectedAmountConfigNumber,
    getCouponSerialNumbers,
    reloanTips,
    disable,
    serviceFeePerDayRate,
    repayDate,
    repayDateRef,
    ...props
  } = useData({ type });

  /** 打开复贷合同 */
  const onOpenContractOnReloan = async () => {
    if (
      String(loanAmount) === '0' ||
      (BaseInfoManager.context.baseModel.repayDateSwitch && !repayDateRef.current)
    ) {
      modalDataStoreInstance.openModal({
        key: ModalList.INFO_PROMPT_CONFIRM,
        imageKey: ImageNames._notify,
        i18nKey: 'homeString.update_open_contract',
        confirmBtnName: 'btnString.OK',
        isBackdropClose: false,
        confirmBtnCallback: () => { },
      });
      return;
    }
    let result = await fetchPreApplyContract({
      /** 选择额度 */
      selectedAmount: String(loanAmount),
      /** 选择天数 */
      selectedDays: String(reLoanAmountConfigList[selectedAmountConfigNumber].days),
      /** 还款日期 */
      repayDate: repayDateRef.current,
      /** 预申请单号 */
      applyOrderId: '',
      /** 优惠券编号 */
      couponSerialNumber: getCouponSerialNumbers(),
      /** 首贷、复贷 */
      firstApply: 'NO',
    });

    if (result?.code === BaseEnumsSpace.ENetworkStatus.SUCCESS) {
      nav.navigate(RouterConfig.LOAN_CONTRACT as any, {
        html: CryptoJS.enc.Base64.parse(String(result?.data)).toString(CryptoJS.enc.Utf8),
      });
    }

    return result?.code === BaseEnumsSpace.ENetworkStatus.SUCCESS;
  };

  const handleOpenContract = _.debounce(async () => {
    TrackEvent.trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_HOMEPAGE,
        e: HitPointEnumsSpace.EEventKey.BTN_LOAN_AGREEMENT_VIEW,
      },
      '',
    );
    await onOpenContractOnReloan();
  }, 100);

  return (
    <>
      <Layout pLevel="0" level="1" topCompensateColor="primary-color-500">
        <TopNavigation
          showLogoAction={true}
          bottomLine={false}
          pageKey={HitPointEnumsSpace.EPageKey.P_HOMEPAGE}
          showMessage
          type="primary"
          isShowVip
        />
        <ScrollView
          keyboardShouldPersistTaps="always"
          overScrollMode={'never'}
          refreshControl={
            <RefreshControl
              colors={[theme['primary-color-500']]}
              refreshing={refreshing}
              onRefresh={onRefresh}
            />
          }>
          <View margin="0 0 0 0">
            <View
              style={{
                height: 180,
                backgroundColor: 'primary-color-500',
              }}></View>
            <View margin="-180 0 0 0">
              <AmountSliderView
                loanAmount={loanAmount}
                reLoanAmountConfigList={reLoanAmountConfigList}
                selectedAmountConfigNumber={selectedAmountConfigNumber}
                isReadContract={false}
                isPending={isPending}
                activitySwipeRef={activitySwiperRef}
                onOpenContract={handleOpenContract}
                serviceFeePerDayRate={serviceFeePerDayRate}
                repayDate={repayDate}
                {...props}
              />
              {AppDefaultConfigManager.context.appDefaultConfigModel.defaultConfigState
                ?.calculateCostConfig?.newStyleSwitch === 'NO' ||
                !AppDefaultConfigManager.context.appDefaultConfigModel.defaultConfigState
                  ?.calculateCostConfig?.newStyleSwitch ? (
                <View
                  margin="12 16 0 16"
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                  }}>
                  <Check checked={isReadContract} onChange={onChangeIsReadContract} />
                  <Text margin="2 0 0 0">
                    <Text
                      category="p2"
                      i18nKey={'directPaymentString.read_contract_one'}
                      style={{
                        color: 'text-color-600',
                      }}
                    />
                    <Text
                      category="p2"
                      onPress={handleOpenContract}
                      i18nKey={'directPaymentString.read_contract_two'}
                      style={{
                        color: 'primary-color-500',
                        textDecorationLine: 'underline',
                      }}
                    />
                  </Text>
                </View>
              ) : null}

              <View
                style={{ display: !!reloanTips ? 'flex' : 'none' }}
                margin="12 16 12 16"
                padding="12 12 12 12"
                cardType="baseType">
                <View layoutStrategy="flexRowStartCenter">
                  <Image name="_blackNotice" />
                  <Text
                    margin="0 10 0 10"
                    style={{ color: 'text-color-600' }}
                    category="p1"
                    i18nKey={'loanConfirmString.loan_notice_title'}
                  />
                </View>
                <RenderHtml
                  contentWidth={Dimensions.get('window').width - 48}
                  source={{ html: reloanTips }}
                />
              </View>
            </View>
          </View>
        </ScrollView>
        <View
          padding={'16 16 12 16'}
          style={{
            backgroundColor: 'background-color-0',
            borderBottomLeftRadius: 8,
            borderBottomRightRadius: 8,
          }}>
          <Button
            margin="8 0 0 0"
            padding="12 0 12 0"
            status="primary"
            onPress={handleClick}
            textI18nKey={'btnString.quickGetLoan'}
            disabled={disable}
          />
        </View>
      </Layout>
      <ConfirmPhoneNumberUsage />
      <ReloanQuestionnaireModal
        visible={reloanQuestionModalVisible}
        onCancel={onCloseReloanQuestionModal}
        onConfrim={onSubmitReloanQuestion}
      />
    </>
  );
};
