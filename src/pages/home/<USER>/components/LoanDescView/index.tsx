import { Card, Image, Text, View } from '@/components';
import { memo } from 'react';
import { useTheme, useVipFuncSwitch } from '@/hooks';

const LoanDescView = function LoanDescView() {
  const theme = useTheme();

  const isVipSwith = useVipFuncSwitch();

  if (isVipSwith) return null;

  return (
    <View
      margin={'16 16 0 16'}
      padding={'16 16 16 16'}
      style={{
        backgroundColor: 'background-color-0',
        borderRadius: 8,
        // borderWidth: 1,
        // borderColor: 'line-color-200',
      }}>
      <View
        style={{
          width: '100%',
          display: 'flex',
          flexDirection: 'row',
          justifyContent: 'center',
          alignItems: 'center',
          flexWrap: 'wrap',
        }}>
        <Image name="_homeCardLeftDecoration" />
        <Text
          margin="0 12 0 12"
          isCenter={true}
          category="p1"
          i18nKey={'homeString.inThreeEasySteps'}
          style={{
            color: 'text-color-700',
          }}
        />
        <Image name="_homeCardRightDecoration" />
      </View>

      <View margin="16 0 0 0" layoutStrategy={'flexRowBetweenCenter'}>
        <View layoutStrategy={'flexColumnBetweenCenter'}>
          <Image name="_sendProfile" />
          <Text
            margin="8 0 0 0"
            category="p1"
            i18nKey={'homeString.sendProfile'}
            isCenter={true}
            width={75}
            style={{
              color: 'text-color-600',
            }}
          />
        </View>
        <Image tintColor={theme['fill-color-500']} name="_rightTrigel" />
        <View layoutStrategy={'flexColumnBetweenCenter'}>
          <Image name="_beEvaluated" />
          <Text
            margin="8 0 0 0"
            category="p1"
            i18nKey={'homeString.beEvaluated'}
            isCenter={true}
            width={75}
            style={{
              color: 'text-color-600',
            }}
          />
        </View>
        <Image tintColor={theme['fill-color-500']} name="_rightTrigel" />
        <View layoutStrategy={'flexColumnBetweenCenter'}>
          <Image name="_optainEvaluatedLoan" />
          <Text
            margin="8 0 0 0"
            category="p1"
            i18nKey={'homeString.obtainLoan'}
            isCenter={true}
            width={80}
            style={{
              color: 'text-color-600',
            }}
          />
        </View>
      </View>
    </View>
  );
};

/** 贷款信息展示 */
export default memo(LoanDescView);
