/* eslint-disable react-native/no-inline-styles */
import { DashedLine, Image, Text, View } from '@/components';
import { OrderVOSpace } from '@/types';
import React, { ReactElement, memo, useCallback, useState } from 'react';
import { TouchableWithoutFeedback } from 'react-native';
import { useNameSpace } from '@/i18n';
import { TrackEvent } from '@/utils';
import { HitPointEnumsSpace } from '@/enums';

interface ILoanDetailCardProps {
  calculateCost: OrderVOSpace.MultiPeriodCalculateType;
}
/**
 * 贷款详细卡片
 */
const MultiLoanDetailCard = (props: ILoanDetailCardProps): ReactElement => {
  const { calculateCost } = props;
  const { loanPlan, firstRepayDate, loanCostTips } = calculateCost;

  const [isSpreadOut, setIsSpreadOut] = useState<boolean>(false);

  // const handleOpenTips = useCallback(() => {
  //   Toast(t('directPaymentString.loan_amount_calc_tips'));
  // }, []);

  const onChangeIsSpreadOut = useCallback(() => {
    setIsSpreadOut(preState => {
      TrackEvent.trackCommonEvent(
        {
          p: HitPointEnumsSpace.EPageKey.P_MULTI_PERIOD_RELOAN,
          e: HitPointEnumsSpace.EEventKey.BTN_EXPAND_REPAYMENT_PLAN,
        },
        !preState ? '1' : '0',
      );
      return !preState;
    });
  }, []);

  const t = useNameSpace().t;
  return (
    <View
      margin="12 16 0 16"
      padding="12 12 12 12"
      style={{
        backgroundColor: 'background-color-0',
        borderRadius: 8,
      }}>
      <View margin="0 0 0 0" layoutStrategy="flexColumnStart">
        <View layoutStrategy="flexRowBetweenCenter">
          <Text
            margin="0 8 0 0"
            style={{ color: 'text-color-800' }}
            category="p1"
            i18nKey={'multiPeriodString.loanDetailTitle'}
          />
          {isSpreadOut && (
            <TouchableWithoutFeedback onPress={onChangeIsSpreadOut}>
              <Image name={'_triangleUpIcon'} />
            </TouchableWithoutFeedback>
          )}
        </View>

        {!isSpreadOut && (
          <View width={'100%'} layoutStrategy="flexRowBetweenCenter">
            <View></View>
            <TouchableWithoutFeedback onPress={onChangeIsSpreadOut}>
              <View layoutStrategy="flexRowBetweenCenter">
                <View layoutStrategy="flexColumnCenterCenter">
                  <Text
                    width={'100%'}
                    textContent={firstRepayDate}
                    style={{ textAlign: 'right' }}
                  />
                  <Text
                    category="c1"
                    textContent={
                      t('multiPeriodString.amount') +
                      ' ' +
                      String(loanPlan[0]?.curPeriodPrincipal).toFormatFinance(false) +
                      ' + ' +
                      t('multiPeriodString.serverFee') +
                      ' ' +
                      String(loanPlan[0]?.curPeriodProcessFee).toFormatFinance(false) +
                      ' + ' +
                      t('multiPeriodString.serverIVA') +
                      ' ' +
                      String(loanPlan[0]?.curPeriodProcessFeeVat).toFormatFinance(false)
                    }
                  />
                </View>
                <Image margin="0 0 0 12" name={'_triangleDownIcon'} />
              </View>
            </TouchableWithoutFeedback>
          </View>
        )}
      </View>
      {isSpreadOut && (
        <View margin="0 0 0 0" padding="8 0 8 0">
          <View
            margin="12 0 12 0"
            padding="12 8 12 8"
            height={100 * (loanPlan.length - 1) - (loanPlan.length - 3) * 20}
            layoutStrategy="flexRowStartCenter"
            style={{
              borderRadius: 8,
              overflow: 'hidden',
              backgroundColor: 'info-color-100',
            }}>
            <View height={'100%'} layoutStrategy="flexRowCenterCenter">
              <View height={'100%'} layoutStrategy="flexColumnStartCenter">
                {loanPlan.map((item, index) => {
                  if (index === 0) {
                    return (
                      <Text
                        key={index.toString()}
                        category="c1"
                        textContent={item.curPeriodRepayDate}
                        style={{ color: 'text-color-600' }}
                      />
                    );
                  } else {
                    return (
                      <Text
                        key={index.toString()}
                        category="c1"
                        margin="50 0 0 0"
                        textContent={item.curPeriodRepayDate}
                        style={{ color: 'text-color-600' }}
                      />
                    );
                  }
                })}
              </View>
              {/* 中心圆点 */}
              <View height={'100%'} margin="0 12 0 12" layoutStrategy="flexColumnStartCenter">
                {loanPlan.map((item, index) => {
                  if (index === 0) {
                    return (
                      <View
                        margin="6 0 0 0"
                        key={index.toString()}
                        style={{
                          width: 12,
                          height: 12,
                          borderRadius: 99,
                          backgroundColor: 'primary-color-500',
                        }}
                      />
                    );
                  } else {
                    return (
                      <View layoutStrategy="flexColumnCenterCenter" key={index.toString()}>
                        <DashedLine
                          axis="vertical"
                          width={0.5}
                          height={
                            (100 * (loanPlan.length - 1) -
                              (loanPlan.length - 3) * 20 -
                              (loanPlan.length - 1) * 12 +
                              12) /
                              loanPlan.length -
                            1
                          }
                        />
                        <View
                          margin={index === loanPlan.length - 1 ? '0 0 6 0' : '0 0 0 0'}
                          style={{
                            width: 12,
                            height: 12,
                            borderRadius: 99,
                            backgroundColor: 'primary-color-500',
                          }}
                        />
                      </View>
                    );
                  }
                })}
              </View>
            </View>

            {/* 金额和税费 */}
            <View height={'100%'} layoutStrategy="flexColumnBetweenCenter">
              {loanPlan.map((item, index) => {
                if (index === 0) {
                  return (
                    <View
                      // margin={index === 0 ? '0 0 12 0' : '12 0 12 0'}
                      layoutStrategy="flexColumnStart"
                      key={index.toString()}>
                      <Text
                        category="c1"
                        numberOfLines={2}
                        width={180}
                        textContent={
                          t('multiPeriodString.amount') +
                          ' ' +
                          String(item.curPeriodPrincipal).toFormatFinance(false) +
                          ' + ' +
                          t('multiPeriodString.serverFee') +
                          ' ' +
                          String(item.curPeriodProcessFee).toFormatFinance(false) +
                          ' + ' +
                          t('multiPeriodString.serverIVA') +
                          ' ' +
                          String(item.curPeriodProcessFeeVat).toFormatFinance(false)
                        }
                        style={{ color: 'text-color-600' }}
                      />
                    </View>
                  );
                } else {
                  return (
                    <View layoutStrategy="flexColumnStart" key={index.toString()}>
                      <Text
                        category="c1"
                        numberOfLines={2}
                        width={180}
                        textContent={
                          t('multiPeriodString.amount') +
                          ' ' +
                          String(item.curPeriodPrincipal).toFormatFinance(false) +
                          ' + ' +
                          t('multiPeriodString.serverFee') +
                          ' ' +
                          String(item.curPeriodProcessFee).toFormatFinance(false) +
                          ' + ' +
                          t('multiPeriodString.serverIVA') +
                          ' ' +
                          String(item.curPeriodProcessFeeVat).toFormatFinance(false)
                        }
                        style={{ color: 'text-color-600' }}
                      />
                    </View>
                  );
                }
              })}
            </View>
          </View>
          {loanCostTips && (
            <View layoutStrategy="flexRowStartCenter">
              <Text margin="0 8 0 0" category="c1" status="danger" textContent="*" />
              <Text
                category="c1"
                // i18nKey="multiPeriodString.loanCalculateDetailTips"
                textContent={loanCostTips}
              />
            </View>
          )}
        </View>
      )}
    </View>
  );
};
export type LoanDetailLayoutType = {};
export default memo(MultiLoanDetailCard);
