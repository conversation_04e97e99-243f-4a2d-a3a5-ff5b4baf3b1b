/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react-native/no-inline-styles */
/* eslint-disable react/react-in-jsx-scope */
import { AmountSlider, Button, Image, Text, View, BusinessUI } from '@/components';
import { HitPointEnumsSpace } from '@/enums';
import { useNameSpace } from '@/i18n';
import { OrderResponseSpace } from '@/server';
import { trackCommonEvent } from '@/trackEvent';
import _ from 'lodash';
import { memo, useCallback, useMemo } from 'react';
import { Dimensions, GestureResponderEvent, ScrollView } from 'react-native';
import { IState } from '../../useData';
import MultiLoanDetailCard from './MultiLoanDetailCard';
import React from 'react';
import { OrderVOSpace } from '@/types';
import { modalDataStoreInstance, ModalList } from '@/managers';

const { AutoWithholdCardAutoBind } = BusinessUI;

interface IProps extends OrderResponseSpace.ReloanConfigData {
  onChangeAmount: (defaultAmount: number) => void;
  onUpdateAmount: (defaultAmount: number) => void;
  amount: string;
  term: string;
  onGetHomePageAmout: (event: GestureResponderEvent) => void;
  isPending: boolean;
  loanAmount: number;
  loanAmountConfigList: IState[];
  selectedAmountConfigNumber: number;
  onChangeAmountConfigNumber: (num: number) => void;
  calculateCost: OrderVOSpace.MultiPeriodCalculateType;
  autoWithhold: boolean;
  onOpenAutoWithhold: () => void;
  onCloseAutoWithhold: () => void;
  openAutoWithholdContract: () => void;
  reloanHomeWithholdBankCardInfo: OrderVOSpace.ReloanHomeWithholdBankCardInfo;
}
const AmountSliderView = function AmountSliderView(props: IProps) {
  const {
    days = 0,
    minAmount = 0,
    maxAmount = 0,
    defaultAmount = 0,
    loanAmount = 0,
    increment = 0,
    onChangeAmount,
    onUpdateAmount,
    onGetHomePageAmout,
    isPending,
    loanAmountConfigList,
    selectedAmountConfigNumber,
    onChangeAmountConfigNumber,
    calculateCost,
    autoWithhold,
    onOpenAutoWithhold,
    onCloseAutoWithhold,
    openAutoWithholdContract,
    reloanHomeWithholdBankCardInfo,
  } = props;

  const onSlidingComplete = (value: number) => {
    trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_MULTI_PERIOD_RELOAN,
        e: HitPointEnumsSpace.EEventKey.E_BTN_CHOOSE_AMOUNT,
      },
      String(value),
    );
    onChangeAmount(value);
  };

  const onValueChange = (value: number) => {
    onUpdateAmount(value);
  }

  const currentSelAmount = useMemo(() => {
    if (loanAmount != 0) {
      return String(loanAmount).toFormatFinance();
    } else {
      return '--'.toFormatFinance();
    }
  }, [loanAmount]);

  const t = useNameSpace().t;

  const renderTermButtonView = useCallback(
    (loanAmountConfig: IState, index: number) => {
      const { days, enable, productType, installmentCount } = loanAmountConfig;

      const enabled = enable === 'YES';

      const seleted = selectedAmountConfigNumber === index;
      const onSelect = _.throttle(() => {
        trackCommonEvent(
          {
            p: HitPointEnumsSpace.EPageKey.P_MULTI_PERIOD_RELOAN,
            e: HitPointEnumsSpace.EEventKey.BTN_DATE_CHANGE,
          },
          String(days),
          enable ? '1' : '0',
        );
        if (!enabled) {
          modalDataStoreInstance.openModal({
            key: ModalList.INFO_PROMPT_CONFIRM,
            i18nKey: 'multiPeriodString.disabledTips',
            imageKey: '_epModalNormalIcon',
            confirmBtnName: 'btnString.agree',
            isBackdropClose: false,
          });
          // Toast(t('homeString.lockLoanTremClickTip'));
        }
        if (enabled && !seleted) {
          // 记录用户选择额度的事件

          onChangeAmountConfigNumber(index);
        }
      }, 300);

      // const onDisabledCallback = () => {
      //   modalDataStoreInstance.openModal({
      //     key: ModalList.INFO_PROMPT_CONFIRM,
      //     i18nKey: 'multiPeriodString.disabledTips',
      //     imageKey: '_epModalNormalIcon',
      //     confirmBtnName: 'btnString.agree',
      //     isBackdropClose: false,
      //   });
      // };

      const textColor = seleted ? '#FFF' : enabled ? 'primary-color-500' : 'primary-color-300';
      return (
        <Button
          margin="0 3 0 3"
          padding="0 12 0 12"
          onPress={onSelect}
          // onDisabledCallback={onDisabledCallback}
          key={index.toString()}
          style={{
            maxWidth: (Dimensions.get('window').width - 80) / 3,
            position: 'relative',
            borderColor: textColor,
            borderRadius: 8,
          }}
          appearance={selectedAmountConfigNumber === index ? 'filled' : 'outline'}>
          {!enabled && (
            <Image
              name="_lockTipIcon"
              style={{
                position: 'absolute',
                top: 6,
                right: 6,
                tintColor: 'primary-color-300',
              }}
            />
          )}
          <View layoutStrategy="flexColumnCenterCenter">
            <Text
              category="p1"
              bold="bold"
              style={{ color: textColor }}
              status="primary"
              textContent={String(days).toFormatMonth()}
            />
            {productType === 'INSTALLMENT' && (
              <Text
                category="c2"
                bold="bold"
                style={{ color: textColor }}
                status="primary"
                textContent={String(installmentCount).toFormatPeriod()}
              />
            )}
          </View>
        </Button>
      );
    },
    [selectedAmountConfigNumber, onChangeAmountConfigNumber, t],
  );

  const $selectTermView = useMemo(() => {
    const amountConfigListLen = loanAmountConfigList.length;
    if (amountConfigListLen > 3) {
      return (
        <ScrollView horizontal fadingEdgeLength={10} keyboardShouldPersistTaps="always">
          {(loanAmountConfigList || []).map((loanAmountConfig, index) => {
            return renderTermButtonView(loanAmountConfig, index);
          })}
        </ScrollView>
      );
    } else {
      return (
        <View padding="8 0 8 0" layoutStrategy="flexRowBetweenCenter">
          {(loanAmountConfigList || []).map((loanAmountConfig, index) => {
            return renderTermButtonView(loanAmountConfig, index);
          })}
        </View>
      );
    }
  }, [loanAmountConfigList, selectedAmountConfigNumber, onChangeAmountConfigNumber]);

  /** 刷新选择额度 view */
  const $selectAmountView = useMemo(() => {
    /** @todo 数据加载时默认展示, 后续可优化加载过程 */
    if (!isPending) {
      return (
        <>
          <AmountSlider
            animationType="spring"
            value={defaultAmount}
            minimumValue={minAmount}
            maximumValue={maxAmount}
            onSlidingComplete={value => onSlidingComplete(Number(value))}
            onValueChange={value => onValueChange(Number(value))}
            step={increment}
          />
        </>
      );
    }

    if (!days || !defaultAmount) {
      return (
        <>
          <View
            margin="4 0 0 0"
            style={{
              width: '100%',
              display: 'flex',
              flexDirection: 'row',
              justifyContent: 'center',
              alignItems: 'center',
              flexWrap: 'wrap',
            }}>
            <Button
              onPress={onGetHomePageAmout}
              style={{
                width: 144,
              }}>
              <Image name="_reCycle" />
              <Text
                i18nKey="homeString.update"
                style={{
                  color: 'text-color-0',
                  zIndex: 1,
                }}
              />
            </Button>
          </View>
          <View
            margin="12 0 0 0"
            padding="8 12 8 12"
            style={{
              backgroundColor: 'secondary-color-100',
              borderRadius: 10,
            }}>
            <Text
              isCenter={true}
              i18nKey="homeString.update_select_amount"
              style={{
                color: 'text-color-800',
                zIndex: 1,
              }}
            />
          </View>
        </>
      );
    }
    return (
      <>
        <AmountSlider
          animationType="spring"
          value={defaultAmount}
          minimumValue={minAmount}
          maximumValue={maxAmount}
          onSlidingComplete={value => onSlidingComplete(Number(value))}
          onValueChange={value => onValueChange(Number(value))}
          step={increment}
        />
      </>
    );
  }, [days, defaultAmount, isPending, selectedAmountConfigNumber]);

  return (
    <>
      <View margin={'16 16 0 16'} cardType="baseType">
        <View
          padding="4 8 4 8"
          style={{
            backgroundColor: 'background-color-100',
            borderRadius: 8,
            overflow: 'hidden',
          }}
          layoutStrategy="flexRowBetweenCenter">
          <Image margin="0 10 0 0" name="_soundBlackIcon" />
          <Text
            style={{ flex: 1, color: 'text-color-700' }}
            category="c1"
            i18nKey={'homeString.reloanTip'}
          />
        </View>
        <View padding={'12 16 12 16'}>
          <View layoutStrategy="flexRowCenterCenter">
            <View>
              <Text
                category="p1"
                i18nKey={'homeString.loanAmountUpToNew'}
                style={{
                  color: 'text-color-800',
                  zIndex: 1,
                }}
              />
              <Text
                category="h1"
                isCenter={true}
                textContent={currentSelAmount}
                bold={'bold'}
                margin={'12 12 0 0'}
                status="basic"
              />
            </View>
          </View>
          {$selectAmountView}
          <Text
            category="p1"
            isCenter
            i18nKey={'homeString.loanTermUpToMultiPeirod'}
            style={{
              color: 'text-color-800',
              zIndex: 1,
            }}
            margin={'12 0 0 0'}
          />
          {$selectTermView}
        </View>
      </View>
      {reloanHomeWithholdBankCardInfo.isShowCard === 'YES' && (
        <AutoWithholdCardAutoBind
          openAutoWithholdContract={openAutoWithholdContract}
          cardNo={reloanHomeWithholdBankCardInfo.cardNo}
          withholdState={autoWithhold}
          openWithholdAuthorization={onOpenAutoWithhold}
          closeWithholdAuthorization={onCloseAutoWithhold}
        />
      )}
      <MultiLoanDetailCard calculateCost={calculateCost} />
    </>
  );
};

export default memo(AmountSliderView);
