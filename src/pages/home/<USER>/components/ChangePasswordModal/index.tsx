import { CommonModal } from '@/components';
import { HitPointEnumsSpace } from '@/enums';
import { BaseInfoManager } from '@/managers';
import { RouterConfig } from '@/routes';
import { trackCommonEvent } from '@/trackEvent';
import { nav } from '@/utils';
import React, { useCallback } from 'react';

interface IProps {
  changeDialogVisibe: () => void;
  visible: boolean;
}

export default React.memo((props: IProps) => {
  const { visible, changeDialogVisibe } = props;

  const confirmCallback = useCallback(() => {
    trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_PASSWORD_POPUP,
        e: HitPointEnumsSpace.EEventKey.BTN_PASSWORD_POPUP,
      },
      '0',
    );
    BaseInfoManager.updateSkipSetPasswordStatus(true);
    changeDialogVisibe();
  }, [changeDialogVisibe]);

  const cacelCallback = useCallback(() => {
    trackCommonEvent(
      {
        p: HitPointEnumsSpace.EPageKey.P_PASSWORD_POPUP,
        e: HitPointEnumsSpace.EEventKey.BTN_PASSWORD_POPUP,
      },
      '1',
    );
    BaseInfoManager.updateSkipSetPasswordStatus(true);
    nav.navigate(RouterConfig.SET_PWD as any, {
      password: '',
      repassword: '',
      fromPage: RouterConfig.HOME_SCREEN,
    });
    changeDialogVisibe();
  }, [changeDialogVisibe]);

  return (
    <CommonModal
      visible={visible}
      hasLinearGradient={false}
      i18nKey={'basicInfoString.set_password'}
      imageKey={'_passwordLock'}
      confirmBtnName="btnString.NowNo"
      cancelBtnName="btnString.OK"
      confirmCallback={confirmCallback}
      cancelCallback={cacelCallback}></CommonModal>
  );
});
