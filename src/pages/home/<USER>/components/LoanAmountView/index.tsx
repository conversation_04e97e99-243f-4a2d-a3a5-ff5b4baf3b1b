import { Image, Text, View, Button } from '@/components';
import { useTheme } from '@/hooks';
import { UserInfoManager } from '@/managers';
import React from 'react';
import { memo, useMemo } from 'react';

interface IProps {
  amount: string;
  term: string;
  handleClick: () => void;
}

const LoanAmountView = function LoanAmountView({ amount, term, handleClick }: IProps) {
  const theme = useTheme();
  const btnName = useMemo(() => {
    return UserInfoManager.context.userModel.isOrderCancel
      ? 'homeString.peso_apply_now_last_acceot_credit'
      : 'btnString.quickGetLoan';
  }, []);

  return (
    <>
      <View
        style={{
          backgroundColor: 'primary-color-500',
        }}>
        <View
          margin="0 16 16 16"
          style={{
            backgroundColor: 'primary-color-900',
            borderRadius: 8,
          }}>
          {/* <View
          padding="4 8 4 8"
          style={{
            backgroundColor: 'sound-background',
            borderTopLeftRadius: 8,
            borderTopRightRadius: 8,
          }}
          layoutStrategy="flexRowBetweenCenter">
          <Image margin="0 10 0 0" name="_sound" />
          <Text
            style={{flex: 1, color: 'text-color-700'}}
            category="c1"
            i18nKey={'homeString.tip'}
          />
        </View> */}
          <View
            padding={'16 16 16 16'}
            style={{
              borderColor: 'primary-color-500',
            }}>
            <Text category="p1" i18nKey={'homeString.loanAmountUpTo'} status="control" />
            <View margin={'8 0 0 0'} layoutStrategy="flexRowStartCenter">
              <Text
                margin="8 4 0 0"
                category="h3"
                status="control"
                textContent={'$'}
                bold={'bold'}
                style={{
                  zIndex: 1,
                }}
              />
              <Text
                category="h1"
                textContent={amount}
                bold={'bold'}
                status="control"
                style={{
                  fontSize: 40,
                  lineHeight: 48,
                  zIndex: 1,
                }}
              />
            </View>
            <Text category="p1" i18nKey={'homeString.loanTermUpTo'} status="control" bold="500" />
            <View margin="16 0 0 0" layoutStrategy="flexRowBetweenCenterWrap">
              <Text category="p1" textContent={term} bold="500" status="control" />
            </View>
          </View>
        </View>
      </View>
      <View margin={'16 16 0 16'} padding={'16 16 16 16'} cardType="baseType">
        <View margin="24 16 0 16">
          <View layoutStrategy={'flexRowBetweenCenter'}>
            <View layoutStrategy={'flexColumnBetweenCenter'}>
              <Image name="_quickLoan" />
              <Text
                category="p2"
                margin="8 0 0 0"
                i18nKey={'homeString.quickLoan'}
                isCenter={true}
                style={{
                  color: 'text-color-600',
                }}
              />
            </View>
            <View
              style={{
                width: 2,
                height: 30,
                backgroundColor: theme['fill-color-500'],
              }}
            />
            <View layoutStrategy={'flexColumnStartCenter'}>
              <Image name="_lowerInterest" />
              <Text
                category="p2"
                margin="8 0 0 0"
                i18nKey={'homeString.lowestInterest'}
                isCenter={true}
                style={{
                  color: 'text-color-600',
                }}
              />
            </View>
          </View>
          <Button
            margin="20 0 0 0"
            padding="12 0 12 0"
            status="primary"
            onPress={handleClick}
            textI18nKey={btnName}
          />
        </View>
      </View>
    </>
  );
};

/** 贷款数量 && 天数展示 */
export default memo(LoanAmountView);
