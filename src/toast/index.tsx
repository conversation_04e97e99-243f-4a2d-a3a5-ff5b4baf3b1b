import { Image, Text, View } from '@/components';
import { ToastDataStoreInstance, ToastItemType } from '@/managers';
import React, { useEffect, useMemo, useState } from 'react';
import { Dimensions, StyleSheet } from 'react-native';

const ToastContainer = () => {
  const [toastList, setToastList] = useState<ToastItemType[]>([]);

  useEffect(() => {
    const subscribe = ToastDataStoreInstance.messageCenter.subscribe(nextContent => {
      setToastList([...nextContent.toastList]);
    });

    return () => {
      subscribe.unsubscribe();
    };
  }, []);

  const $ToastList = useMemo(() => {
    const componentList = [];
    const len = toastList.length;
    for (let i = 0; i < len; i++) {
      const { i18nKey, content, imageKey, id } = toastList[i];
      if (i > 2) break;
      componentList.push(
        <View
          margin="0 0 12 0"
          padding="0 12 0 12"
          key={id}
          style={{
            flexDirection: 'row',
            justifyContent: 'center',
            alignItems: 'center',
            width: 'auto',
          }}>
          {imageKey && <Image name={imageKey as any} />}
          <Text
            style={{
              borderRadius: 8,
              backgroundColor: 'toast-color-500',
            }}
            status="control"
            padding="12 12 12 12"
            category="p2"
            i18nKey={i18nKey}
            textContent={content}
          />
        </View>,
      );
    }
    return componentList;
  }, [toastList]);

  return <View style={styles.toastContainer}>{$ToastList}</View>;
};

const SCREEN_WIDTH = Dimensions.get('window').width;

const styles = StyleSheet.create({
  toastContainer: {
    position: 'absolute',
    width: SCREEN_WIDTH,
    bottom: 60,
    left: SCREEN_WIDTH / 2,
    transform: [{ translateX: -(SCREEN_WIDTH / 2) }],
    right: 20,
  },
});

export default React.memo(ToastContainer);
