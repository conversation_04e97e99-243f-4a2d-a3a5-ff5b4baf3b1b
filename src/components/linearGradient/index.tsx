/* eslint-disable react/self-closing-comp */
import React from 'react';
import * as ReactNative from 'react-native';
import LinearGradient from 'react-native-linear-gradient';

export interface LinearGradientProps extends ReactNative.ViewProps {
  colors: (string | number)[];
  start?: { x: number; y: number };
  end?: { x: number; y: number };
  locations?: number[];
  useAngle?: boolean;
  angleCenter?: { x: number; y: number };
  angle?: number;
}
/**
 * Basic linear gradient
 *
 * @example
        <LinearGradient
          style={[styles.gradientBlue]}
          start={{x: 0, y: 0}}
          end={{x: 1, y: 0}}
          colors={['#4C6EFF', '#4C6EFF']}
        />
 */
export default React.memo((props: LinearGradientProps): React.ReactElement => {
  return <LinearGradient {...props} />;
});
