/* eslint-disable react-native/no-inline-styles */
import { useAppicatioStateActiveAndPageFocusHandle, useTheme } from '@/hooks';
import { ELocalKey } from '@/localStorage';
import { KVManager } from '@/managers';
import _ from 'lodash';
import React, {
  forwardRef,
  memo,
  Ref,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react';
import { TouchableWithoutFeedback, Animated } from 'react-native';
import { useNameSpace } from '../../../i18n';
import Text from '../../text';
import View from '../../view';
import Input, { RefType as BasicInputRefType, InputProps } from '../baseInput';
import { useThemeManager } from '@/managers';

interface IPrpos extends InputProps {
  smsCode: string;
  setSmsCode: (value: any) => void;
  initMethod?: () => Promise<boolean>;
  clickSendPreMethod: () => Promise<boolean>;
  countDownOverCallback?: () => void;
  placeholderKey?: string;
  prefixKey?: string;
  prefixMargin?: string;
  scenesId: 'register' | 'login' | 'first_loan' | 're_loan' | 'modify' | 'new_mobile';
  type?: 'base' | 'line';
  accessoryRightTextKey?: string;
}

export type RefType = {
  reStartCountDown: Function;
  isStart: Function;
  blur: Function;
  focus: Function;
  isFocused: Function;
  emitErrorStatus: Function;
  reStart: Function;
};
const defaultProps = {
  inputPadding: 10,
  animationDuration: 300,
};

/**
 * 验证码组件
 * @property smsCode
 * @property setSmsCode
 * @property {function} initMethod 初始化回调
 * @property {function} clickSendPreMethod 点击发送前回调
 * @property {function} countDownOverCallback 倒计时结束回调
 * @property {string} placeholderKey 默认文字i18nKey
 * @property {string} prefixKey 待删除
 * @property {string} prefixMargin 顶部边距
 */
export default memo(
  forwardRef((_props: IPrpos, ref: Ref<RefType>): React.ReactElement => {
    const {
      smsCode,
      setSmsCode,
      initMethod = undefined,
      clickSendPreMethod = undefined,
      countDownOverCallback = undefined,
      placeholderKey = 'loginString.inputPlaceholder',
      prefixKey,
      prefixMargin,
      scenesId,
      type = 'base',
      onFocus,
      onBlur,
      style = {},
      dangerStyle = {},
      accessoryRightTextKey = 'send',
      ...props
    } = _props;

    const theme = useTheme();

    const inputRef = useRef<BasicInputRefType>(null);

    const t = useNameSpace('btnString').t;

    const [smsSendBool, setSmsSendBool] = useState(false);
    const [count, setCount] = useState(60);
    let isEnterBackend = useRef<boolean>(false).current;
    let timer = useRef<any>(null).current;
    let recordTime = useRef<number>(0).current;
    const [isFocused, setIsFocued] = useState<boolean>(false);

    const focusedAnim = useRef(new Animated.Value(0)).current;

    useImperativeHandle(ref, () => ({
      emitErrorStatus(tips: string, linkInfo?: string, linkAction?: () => void) {
        inputRef.current?.emitErrorStatus(tips, linkInfo, linkAction);
      },
      /** 重新开始倒计时 */
      reStartCountDown() {
        onSetCount();
      },
      reStart() {
        setSmsSendBool(false);
      },
      isStart() {
        return smsSendBool;
      },
      blur() {
        inputRef.current?.blur();
      },
      focus() {
        inputRef.current?.focus();
      },
      isFocused() {
        return inputRef.current?.isFocused?.() || false;
      },
    }));

    useEffect(() => {
      onInit();

      return () => {
        clearInterval(timer);
      };
    }, []);

    useEffect(() => {
      KVManager.action.setInt(ELocalKey.SMS_CODE_COUNT_RECORD + scenesId, new Date().getTime());
      KVManager.action.setInt(ELocalKey.SMS_CODE_COUNT_LEAVE + scenesId, count);
      if (count === 0) {
        if (typeof countDownOverCallback === 'function') {
          countDownOverCallback();
        }
      }
    }, [smsSendBool, count]);

    /** 组件初始化逻辑 */
    const onInit = () => {
      if (onRecordCountDownFormCache()) {
        return;
      }

      initMethod && initMethod();
    };

    const handleClickPreAction = _.debounce(async () => {
      if (typeof clickSendPreMethod === 'function' && !smsSendBool) {
        clickSendPreMethod();
      }
    }, 100);

    /** 从缓存中获取记录时间进行计算, 重启计数器 */
    const onRecordCountDownFormCache = (): boolean => {
      let curTime = new Date().getTime();
      /** 避免不同账号或者不同场景使用同一个验证码状态 */
      let lastCount = KVManager.action.getInt(ELocalKey.SMS_CODE_COUNT_LEAVE + scenesId) || 0;
      let recordTime =
        KVManager.action.getInt(ELocalKey.SMS_CODE_COUNT_RECORD + scenesId) || curTime;
      let timeDifference = Math.floor((curTime - recordTime) / 1000);
      if (lastCount - timeDifference <= 0 || lastCount === 60) {
        setSmsSendBool(false);
        return false;
      }

      setCount(lastCount - timeDifference);
      setSmsSendBool(true);
      clearInterval(timer);
      timer = setInterval(() => {
        setCount(prevCount => {
          if (prevCount > 0) {
            return prevCount - 1;
          } else {
            clearInterval(timer);
            setSmsSendBool(false);
            return 0;
          }
        });
      }, 1000);

      return true;
    };

    /** 进入后台时, 记录倒计时时间 */
    const onRecordCountDown = () => {
      clearInterval(timer);
      recordTime = new Date().getTime();
      isEnterBackend = true;
    };

    /** 从后台回到前台后台, 重新开始倒计时时间 */
    const onReplayCountDown = () => {
      if (!isEnterBackend) {
        return;
      }

      isEnterBackend = false;
      let curTime = new Date().getTime();
      let lastCount = KVManager.action.getInt(ELocalKey.SMS_CODE_COUNT_LEAVE + scenesId) || 0;
      let recordTime =
        KVManager.action.getInt(ELocalKey.SMS_CODE_COUNT_RECORD + scenesId) || curTime;
      let timeDifference = Math.floor((curTime - recordTime) / 1000);

      if (lastCount - timeDifference <= 0 || lastCount === 60) {
        setSmsSendBool(false);
        return;
      }

      setCount(lastCount - timeDifference);
      setSmsSendBool(true);
      clearInterval(timer);
      timer = setInterval(() => {
        setCount(prevCount => {
          if (prevCount > 0) {
            return prevCount - 1;
          } else {
            clearInterval(timer);
            setSmsSendBool(false);
            return 0;
          }
        });
      }, 1000);
    };

    /** 开始倒计时 */
    const onSetCount = async () => {
      setCount(60);
      setSmsSendBool(true);
      clearInterval(timer);
      timer = setInterval(() => {
        setCount(prevCount => {
          if (prevCount > 0) {
            return prevCount - 1;
          } else {
            clearInterval(timer);
            setSmsSendBool(false);
            return 0;
          }
        });
      }, 1000);
    };

    const onInputChange = (text: string) => {
      setSmsCode(text);
    };

    const computedButtonName = React.useMemo(() => {
      return smsSendBool ? `${count} s` : `${t(accessoryRightTextKey)}`;
    }, [smsSendBool, count]);

    const computedButtonBackground = React.useMemo(() => {
      return !smsSendBool ? `primary-color-500` : `fill-color-500`;
    }, [smsSendBool]);

    useAppicatioStateActiveAndPageFocusHandle(onReplayCountDown, onRecordCountDown);
    const focus = (e: any) => {
      setIsFocued(true);
      inputRef.current?.focus(e);
      onFocus && onFocus(e);
      toggle(true);
    };

    const blur = (e: any) => {
      setIsFocued(false);
      onBlur && onBlur(e);
      toggle(false);
    };
    const clear = () => {
      setSmsCode('');
      const focused = inputRef.current?.isFocused?.() || false;
      if (!focused) {
        focusedAnim.setValue(0);
      }
    };
    // Layout handler
    useEffect(() => {
      if (smsCode) {
        focusedAnim.setValue(1);
      } else {
        const focused = inputRef.current?.isFocused?.() || false;
        if (!focused) {
          focusedAnim.setValue(0);
        }
      }
    }, [smsCode]);

    const toggle = (isActive: boolean) => {
      Animated.timing(focusedAnim, {
        toValue: isActive ? 1 : 0,
        duration: defaultProps.animationDuration,
        useNativeDriver: false,
      }).start();
    };
    const {
      value: { applicationTheme },
    } = useThemeManager();

    const renderRight = (): React.ReactElement => (
      <TouchableWithoutFeedback onPress={handleClickPreAction}>
        <View
          layoutStrategy="flexRowCenterCenter"
          padding="8 12 8 12"
          margin="0 0 10 0"
          style={{
            borderRadius: 8,
            backgroundColor: computedButtonBackground,
          }}>
          <Text
            style={{
              color: 'text-color-0',
            }}
            isCenter={true}
            category="p2"
            textContent={`${computedButtonName}`}
          />
        </View>
      </TouchableWithoutFeedback>
    );

    const $input = useMemo(() => {
      switch (type) {
        case 'base':
          return (
            <Input
              {...props}
              ref={inputRef}
              keyboardType="number-pad"
              placeholderKey={placeholderKey}
              accessoryRight={renderRight}
              onChangeText={onInputChange}
              value={smsCode}
            />
          );
        case 'line':
          return (
            <Input
              {...props}
              ref={inputRef}
              keyboardType="number-pad"
              placeholderKey={placeholderKey}
              accessoryRight={renderRight}
              onChangeText={onInputChange}
              value={smsCode}
              onFocus={focus}
              onBlur={blur}
              textStyle={{
                fontSize: 16,
                color: theme['text-color-800'],
              }}
              dangerTextStyle={{
                fontSize: 14,
                lineHeight: 22,
                flex: 1,
              }}
              style={{
                backgroundColor: 'background-color-0',
                borderBottomWidth: 1,
                borderWidth: 0,
                borderRadius: 0,
                borderBottomColor: 'line-color-200',
                ...style,
              }}
              dangerStyle={{
                backgroundColor: 'background-color-0',
                borderBottomWidth: 1,
                borderWidth: 0,
                borderRadius: 0,
                borderBottomColor: 'danger-color-500',
                ...dangerStyle,
              }}
            />
          );
      }
    }, [type, inputRef, placeholderKey, smsCode, smsSendBool, count]);

    return (
      <View
        margin={prefixMargin}
        style={{
          width: '100%',
        }}>
        {$input}
        <TouchableWithoutFeedback onPress={focus}>
          <Animated.View
            style={{
              position: 'absolute',
              left: 6,
              top: focusedAnim.interpolate({
                inputRange: [0, 1],
                outputRange: [!smsCode ? defaultProps.inputPadding : -6, -6],
              }),
            }}>
            <Animated.Text
              style={[
                {
                  fontSize: focusedAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: [!smsCode ? 16 : 14, 14],
                  }),
                  color: applicationTheme['text-color-500'],
                },
              ]}>
              {t(prefixKey)}
            </Animated.Text>
          </Animated.View>
        </TouchableWithoutFeedback>
      </View>
    );
  }),
);
