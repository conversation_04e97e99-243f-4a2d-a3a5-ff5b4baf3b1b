/* eslint-disable react/jsx-no-undef */
/* eslint-disable react-native/no-inline-styles */
import { HitPointEnumsSpace } from '@/enums';
import { useThemeManager } from '@/managers';
import { trackInputEventEnd, trackInputEventStart } from '@/trackEvent';
import { useCustomStyleSheet } from '@/utils';
import React, {
  ReactElement,
  Ref,
  forwardRef,
  useCallback,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react';
import { Dimensions, TouchableWithoutFeedback, VirtualizedList } from 'react-native';
import Image from '../../image';
import CommonModal from '../../modal/commonModal';
import Text from '../../text';
import View from '../../view';
import Input, { RefType as BasicInputRefType } from '../baseInput';

interface IPrpos {
  value: string;
  options: {
    label: string;
    value: string;
  }[];
  setValue: (val: string) => void;
  changeSelectCallback?: Function;
  disabled?: boolean;
  prefixKey?: string;
  prefixMargin?: string;
  placeholderKey?: string;
  /** 埋点事件对象 */
  pageKey?: HitPointEnumsSpace.EPageKey;
  eventKey?: HitPointEnumsSpace.EEventKey;
}

export type RefType = {
  emitErrorStatus: Function;
  blur: Function;
  focus: Function;
  clearErrorStatus: Function;
  configSetup: Function;
};

/**
 * Prefix prefix select input
 */
export default React.memo(
  forwardRef((_props: IPrpos, ref: Ref<RefType>): React.ReactElement => {
    const {
      value,
      setValue,
      prefixKey = '',
      prefixMargin = '0 0 0 0',
      placeholderKey = '',
      disabled,
      options,
      changeSelectCallback,
      pageKey,
      eventKey,
      ...props
    } = _props;
    // @ts-ignore
    const styles = useCustomStyleSheet(useGetStyle({}));
    const [visible, setVisible] = useState<boolean>(false);
    const [configVal, setConfigVal] = useState<string>(options[0].value);

    const inputRef = useRef<BasicInputRefType>(null);

    const {
      value: { applicationTheme },
    } = useThemeManager();

    useImperativeHandle(ref, () => ({
      emitErrorStatus(tips: string) {
        inputRef.current?.emitErrorStatus(tips);
      },
      clearErrorStatus() {
        inputRef.current?.clearErrorStatus();
      },
      blur() {
        inputRef.current?.blur();
      },
      focus() {
        inputRef.current?.focus();
      },
      configSetup() {
        let result;
        options.map(option => {
          if (option.value === configVal) {
            result = option.label;
          }
        });
        return result;
      },
    }));

    const onClickItem = useCallback((value: string) => {
      setConfigVal(value);
      changeSelectCallback && changeSelectCallback(value);
      closeModal();
    }, []);

    const openModal = useCallback(() => {
      if (pageKey && eventKey) {
        trackInputEventStart({
          p: pageKey,
          e: eventKey,
        });
      }
      setVisible(true);
    }, []);

    const closeModal = useCallback(() => {
      setVisible(false);
      if (pageKey && eventKey) {
        trackInputEventEnd(
          {
            p: pageKey,
            e: eventKey,
          },
          value,
        );
      }
    }, []);

    const renderRight = (): ReactElement => (
      <TouchableWithoutFeedback disabled={disabled} onPress={openModal}>
        <View layoutStrategy="flexRowStartCenter">
          <Text category="p2" textContent={configVal} style={{ color: 'text-color-500' }} />
          <Image margin="4 0 0 2" name="_suffix" />
        </View>
      </TouchableWithoutFeedback>
    );

    const defineSelect = (
      <Input
        {...props}
        ref={inputRef}
        margin={prefixMargin}
        placeholderKey={placeholderKey}
        value={value}
        onChangeText={setValue}
        disabled={disabled}
        textStyle={{
          color: applicationTheme['text-color-800'],
        }}
        accessoryRight={renderRight}
      />
    );

    const DATA = Array.from(options, item => item.value);

    const getItem = (data: string, index: number) => {
      return {
        id: Math.random().toString(12).substring(0),
        data: data[index],
      };
    };

    const getItemCount = (data: string[]) => data.length;
    const modal = (
      <CommonModal visible={visible} onBackdropPress={closeModal}>
        <View style={[styles.modal]}>
          <Text
            margin="0 16 0 16"
            padding="19 0 19 0"
            isCenter={true}
            i18nKey={prefixKey}
            style={{
              color: 'text-color-600',
            }}
          />
          <VirtualizedList
            fadingEdgeLength={10}
            data={[...new Set(DATA)]}
            initialNumToRender={10}
            renderItem={({ item }) => (
              <TouchableWithoutFeedback
                key={item.id}
                onPress={() => {
                  onClickItem(item.data);
                }}>
                <Text
                  margin="0 16 0 16"
                  padding="19 0 19 0"
                  style={{
                    borderBottomWidth: 1,
                    borderColor: 'line-color-100',
                  }}
                  isCenter={true}
                  textContent={item.data}
                />
              </TouchableWithoutFeedback>
            )}
            keyExtractor={item => item.id}
            getItemCount={getItemCount}
            getItem={getItem}
          />
        </View>
      </CommonModal>
    );

    return (
      <>
        {defineSelect}
        {modal}
      </>
    );
  }),
);

const useGetStyle = (props: {}) => {
  return useMemo(() => {
    return {
      modal: {
        backgroundColor: 'background-color-0',
        borderRadius: 16,
        maxHeight: Dimensions.get('window').height * 0.8,
        width: Dimensions.get('window').width * 0.8,
      },
    };
  }, [props]);
};
