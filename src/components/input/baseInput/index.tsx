import { HitPointEnumsSpace } from '@/enums';
import { useThemeManager } from '@/managers';
import { trackCommonEvent, trackInputEventEnd, trackInputEventStart } from '@/trackEvent';
import { BasicStyleType, Log, useCustomStyleSheet } from '@/utils';
import React, {
  Ref,
  forwardRef,
  useCallback,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react';
import {
  NativeSyntheticEvent,
  TextInput,
  TextInputFocusEventData,
  TextStyle,
  TextInputProps as _InputProps,
} from 'react-native';
import { useNameSpace } from '../../../i18n';
import Text from '../../text';
import View from '../../view';

export interface IValidateItem {
  condition: boolean | ((val: string) => boolean);
  info: string;
  linkInfo?: string;
  linkAction?: () => void;
  status: 'primary' | 'warning' | 'success' | 'danger' | 'info';
}

export interface InputProps extends BasicStyleType<TextStyle>, _InputProps {
  /** 表单默认值 i18nkey值 */
  placeholderKey?: string;
  focusKey?: string | number;
  blurKey?: string | number;
  /** 表单验证配置 */
  validateConfig?: IValidateItem[];
  /** 文本更新回调方法 */
  onChangeText?: (val: string) => void;
  value?: string;
  placeholderTextColor?: string;
  borderColor?: string;
  disabled?: boolean;
  /** 弹出键盘类型 */
  keyboardType?:
    | 'default'
    | 'number-pad'
    | 'decimal-pad'
    | 'numeric'
    | 'email-address'
    | 'phone-pad';
  /** 埋点事件对象 */
  pageKey?: HitPointEnumsSpace.EPageKey;
  eventKey?: HitPointEnumsSpace.EEventKey;
  accessoryLeft?: () => React.ReactElement;
  accessoryRight?: () => React.ReactElement;
  textStyle?: TextStyle;
  style?: object;
  dangerStyle?: object;
  dangerTextStyle?: object;
  uxCallback?: (uxStatus: string) => void;
  focusIsUpdate?: boolean;
}

export type RefType = {
  blur: Function;
  focus: Function;
  emitErrorStatus: Function;
  clearErrorStatus: Function;
  isFocused: Function;
};
/**
 * Basic base input
 */
export default React.memo(
  forwardRef((_props: InputProps, ref: Ref<RefType>): React.ReactElement => {
    const {
      padding = '0 0 0 0',
      margin = '0 0 0 0',
      placeholderKey,
      focusKey,
      blurKey,
      onChangeText,
      onBlur,
      onFocus,
      validateConfig = [],
      value = '',
      placeholderTextColor = 'text-color-500',
      disabled,
      width,
      height,
      pageKey,
      eventKey,
      accessoryLeft,
      accessoryRight,
      textStyle,
      style = {},
      dangerStyle = {},
      dangerTextStyle = {},
      uxCallback = () => {},
      focusIsUpdate = true,
      multiline,
      ...props
    } = _props;

    const inputRef = useRef<TextInput>(null);
    const linkActionRef = useRef<Function>(undefined);

    useImperativeHandle(ref, () => ({
      getValue() {
        const text =
          // @ts-ignore
          inputRef?.current?.value || inputRef?.current?.getNativeText() || '';
        return text;
      },
      blur() {
        conutInputStyleOnBlur();
        inputRef.current?.blur();
      },
      focus() {
        inputRef.current?.focus();
      },
      emitErrorStatus(tips: string, linkInfo?: string, linkAction?: () => void) {
        setIsValidate(true);
        setValidateInfo(tips);
        setLinkInfo(linkInfo || '');
        linkActionRef.current = linkAction;
        setUxStatus('danger');
        uxCallback && uxCallback('danger');
      },
      clearErrorStatus() {
        setIsValidate(false);
        setValidateInfo('');
        setLinkInfo('');
        linkActionRef.current = undefined;
        setUxStatus('success');
        uxCallback && uxCallback('success');
      },
      isFocused() {
        return inputRef.current?.isFocused?.();
      },
    }));

    const {
      value: { applicationTheme },
    } = useThemeManager();

    const t = useNameSpace().t;

    const [isValidate, setIsValidate] = useState<boolean>(false);
    const [validateInfo, setValidateInfo] = useState<string>('');
    const [linkInfo, setLinkInfo] = useState<string>('');
    const [uxStatus, setUxStatus] = useState<string>('basic');

    const [isFocus, setIsFocus] = useState<boolean>(false);

    const styles = useCustomStyleSheet(useGetStyle(style, dangerStyle));

    /** 聚焦时, 交互状态更新 */
    const conutInputStyleOnFoucs = () => {
      if (focusIsUpdate) {
        setIsValidate(false);
        setUxStatus('basic');
        uxCallback && uxCallback('basic');
        setValidateInfo('');
        setLinkInfo('');
        linkActionRef.current = undefined;
      }
    };

    /** 失去焦点时, 交互状态更新 */
    const conutInputStyleOnBlur = () => {
      let validatePass = true;
      validateConfig &&
        validateConfig?.forEach((validateItem: IValidateItem) => {
          if (
            validatePass &&
            (typeof validateItem.condition === 'function'
              ? !validateItem.condition(value)
              : !validateItem.condition)
          ) {
            setIsValidate(true);
            uxCallback && uxCallback(validateItem.status);
            validatePass = false;
            setValidateInfo(validateItem.info);
            setLinkInfo(validateItem.linkInfo || '');
            linkActionRef.current = validateItem.linkAction;
          }
        });
      if (validatePass) {
        setUxStatus('success');
        uxCallback && uxCallback('success');
      } else {
        setUxStatus('danger');
        uxCallback && uxCallback('danger');
      }
    };

    const onWrapperFocus = (e: NativeSyntheticEvent<TextInputFocusEventData>) => {
      setIsFocus(true);
      onFocus && onFocus(e);
      conutInputStyleOnFoucs();
      if (pageKey && eventKey) {
        trackInputEventStart({
          p: pageKey,
          e: eventKey,
        });
      }
    };

    const onWrapperBlur = (e: NativeSyntheticEvent<TextInputFocusEventData>) => {
      setIsFocus(false);
      onBlur && onBlur(e);
      if (pageKey && eventKey) {
        trackInputEventEnd(
          {
            p: pageKey,
            e: eventKey,
          },
          value,
        );
      }
      conutInputStyleOnBlur();
    };
    /** 记录输入框的文本长度 用作用户的复制行为记录 */
    const textLengthRef = useRef(0);

    const _onChangeText = useCallback(
      (val: string) => {
        onChangeText && onChangeText(val);
        const len = val.length;
        if (pageKey && eventKey) {
          if (len - textLengthRef.current > 1) {
            // 复制
            trackCommonEvent(
              {
                p: pageKey,
                e: eventKey,
              },
              val,
              '1',
            );
          }
        }
        // 赋值
        textLengthRef.current = len;
      },
      [onChangeText],
    );

    const inputStyle = useMemo(() => {
      if (uxStatus === 'danger') {
        return [styles.dangerInput, style, isFocus ? styles.focus : null];
      } else {
        return [styles.input, style, isFocus ? styles.focus : null];
      }
    }, [uxStatus, styles, style, isFocus]);

    const ErrorText = isValidate && validateInfo && (
      <Text padding="8 0 0 0">
        <Text
          category="c2"
          textContent={validateInfo}
          style={{
            color: 'danger-color-500',
            ...dangerTextStyle,
          }}
        />
        {linkInfo ? (
          <Text
            category="c2"
            textContent={linkInfo}
            onPress={() => {
              linkActionRef.current?.();
            }}
            style={{
              color: 'danger-color-500',
              textDecorationLine: 'underline',
              ...dangerTextStyle,
            }}
          />
        ) : null}
      </Text>
    );

    return (
      <View margin={margin} layoutStrategy="flexColumnStart">
        <View width={width} height={height} style={inputStyle} padding="0 12 0 12">
          {accessoryLeft && accessoryLeft()}
          <TextInput
            ref={inputRef}
            placeholderTextColor={applicationTheme[placeholderTextColor]}
            onChangeText={_onChangeText}
            onFocus={onWrapperFocus}
            onBlur={onWrapperBlur}
            value={value}
            editable={!disabled}
            placeholder={t(placeholderKey || '')}
            multiline={multiline}
            style={[
              {
                textAlignVertical: multiline ? 'top' : 'center',
                flex: 1,
                padding: 0,
                color: applicationTheme['text-color-800'],
              },
              textStyle,
            ]}
            {...props}
          />
          {accessoryRight && accessoryRight()}
        </View>
        {ErrorText}
      </View>
    );
  }),
);

const useGetStyle = (style: object, dangerStyle: object) => {
  return useMemo(() => {
    return {
      input: {
        fontSize: 32,
        lineHeight: 48,
        fontWeight: 'bold',
        height: 48,
        width: '100%',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        borderRadius: 16,
        borderWidth: 1,
        borderColor: 'line-color-200',
        backgroundColor: 'background-color-100',
        color: 'text-color-800',
        ...style,
      },
      dangerInput: {
        fontSize: 32,
        lineHeight: 48,
        fontWeight: 'bold',
        height: 48,
        width: '100%',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        borderRadius: 16,
        borderWidth: 1,
        borderColor: 'danger-color-500',
        backgroundColor: 'background-color-100',
        color: 'text-color-800',
        ...dangerStyle,
      },
      focus: {
        borderColor: 'primary-color-500',
      },
      unFocus: {
        borderColor: 'line-color-200',
      },
    };
  }, [style, dangerStyle]);
};
