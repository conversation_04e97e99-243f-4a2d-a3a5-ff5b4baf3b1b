import { HitPointEnumsSpace } from '@/enums';
import { trackInputEventEnd, trackInputEventStart } from '@/trackEvent';
import { Log, useCustomStyleSheet } from '@/utils';
import React, {
  ReactElement,
  Ref,
  forwardRef,
  memo,
  useEffect,
  useImperativeHandle,
  useMemo,
  useState,
} from 'react';
import { Dimensions, TouchableWithoutFeedback } from 'react-native';
import { WheelPicker } from 'react-native-wheel-picker-android';
import Image from '../../image';
import CommonModal from '../../modal/commonModal';
import Text from '../../text';
import View from '../../view';
import { RefType } from '../baseInput';
import _ from 'lodash';

interface IPrpos {
  value: string;
  setValue: Function;
  changeSelectCallback?: Function;
  disabled?: boolean;
  prefixKey?: string;
  prefixMargin?: string;
  placeholderKey?: string;
  type: 'year' | 'datetime';
  format: 'DD/MM/YYYY' | 'YYYY';
  onFocus?: () => void;
  onBlur?: () => void;
  /** 埋点事件对象 */
  pageKey?: HitPointEnumsSpace.EPageKey;
  eventKey?: HitPointEnumsSpace.EEventKey;
  uiType?: 'base' | 'line';
}

interface IState {
  visible: boolean | null;
  yearSelectItem: number;
  monthSelectItem: number;
  daySelectItem: number;
}

export default memo(
  forwardRef((props: IPrpos, ref: Ref<RefType>): ReactElement => {
    const {
      value,
      setValue,
      prefixKey = '',
      type = 'datetime',
      format = 'DD/MM/YYYY',
      prefixMargin = '0 0 0 0',
      placeholderKey = '',
      changeSelectCallback,
      pageKey,
      eventKey,
      onFocus,
      onBlur,
      uiType = 'base',
    } = props;

    const styles = useCustomStyleSheet(useGetStyle({}));
    const [uxStatus, setUxStatus] = useState<string>('basic');

    useImperativeHandle(ref, () => ({
      blur() {},
      focus(_pageKey: HitPointEnumsSpace.EPageKey, _eventKey: HitPointEnumsSpace.EEventKey) {
        setVisible(true);
        if (_pageKey && _eventKey) {
          trackInputEventStart({
            p: _pageKey,
            e: _eventKey,
          });
        }
      },
      emitErrorStatus(tips: string) {
        setUxStatus('danger');
      },
      clearErrorStatus() {
        setUxStatus('basic');
      },
      isFocused: () => {
        return visible;
      },
    }));

    const [state, setState] = useState<IState>({
      visible: null,
      yearSelectItem: 0,
      monthSelectItem: 0,
      daySelectItem: 0,
    });
    const [visible, setVisible] = useState<boolean | null>(null);

    useEffect(() => {
      if (_.isNil(visible)) {
        return;
      }

      if (visible) {
        typeof onFocus === 'function' && onFocus();
      } else {
        typeof onBlur === 'function' && onBlur();
      }
    }, [visible]);

    /** 计算可选年份 */
    const yearList = useMemo(() => {
      let curYear = new Date().getFullYear();
      let result: string[] = [];
      for (let i = 0; i <= 100; i++) {
        result.push(String(curYear - 15 - i));
      }
      setState((prevState: IState) => ({
        ...prevState,
        yearSelectItem: 100,
      }));
      return result;
    }, []);

    /** 计算可选月份 */
    const monthList = useMemo(() => {
      let result: string[] = [];
      for (let i = 1; i <= 12; i++) {
        result.push(String(i));
      }
      return result;
    }, []);

    /** 计算可选天数 */
    const dayList = useMemo(() => {
      const { monthSelectItem, yearSelectItem } = state;
      let curDays = new Date(
        Number(yearList[yearSelectItem]),
        Number(monthList[monthSelectItem]),
        0,
      ).getDate();
      let result: string[] = [];
      for (let i = 1; i <= curDays; i++) {
        result.push(String(i));
      }
      return result;
    }, [state.monthSelectItem, state.yearSelectItem]);

    /** 计算年、月、日 */
    const countDataFromParams = () => {
      if (!value) {
        return;
      }

      try {
        switch (format) {
          case 'DD/MM/YYYY':
            if (!/^\d{2}\/\d{2}\/\d{4}$/.test(value)) {
              setValue('');
              throw 'invalid format!';
            }

            let dateArray: string[] = value.trim().split('/');
            setState((prevState: IState) => ({
              ...prevState,
              yearSelectItem: yearList.indexOf(dateArray[2]),
              monthSelectItem: monthList.indexOf(String(Number(dateArray[1]))),
              daySelectItem: dayList.indexOf(String(Number(dateArray[0]))),
            }));
            break;

          case 'YYYY':
            if (!/^\d{4}$/.test(value)) {
              setValue('');
              throw 'invalid format!';
            }

            setState((prevState: IState) => ({
              ...prevState,
              yearSelectItem: yearList.indexOf(value),
            }));
            break;
          default:
        }
      } catch (error) {}
    };

    /** 根据选择值拼装所需要的数据 */
    const generateParams = () => {
      const { daySelectItem, monthSelectItem, yearSelectItem } = state;

      switch (format) {
        case 'DD/MM/YYYY':
          setValue(
            `${String(dayList[daySelectItem]).padStart(2, '0')}/${String(
              monthList[monthSelectItem],
            ).padStart(2, '0')}/${String(yearList[yearSelectItem]).padStart(4, '0')}`,
          );
          break;

        case 'YYYY':
          setValue(String(yearList[yearSelectItem]));
          break;
        default:
      }
    };

    /** 开启弹窗, 如果有埋点则上报数据 */
    const openModal = () => {
      countDataFromParams();
      setVisible(true);
      if (pageKey && eventKey) {
        trackInputEventStart({
          p: pageKey,
          e: eventKey,
        });
      }
    };

    /** 关闭弹窗, 如果有埋点则上报数据 */
    const closeModal = () => {
      generateParams();
      setUxStatus('basic');
      setVisible(false);
      if (pageKey && eventKey) {
        trackInputEventEnd(
          {
            p: pageKey,
            e: eventKey,
          },
          value,
        );
      }
    };

    const $prefixTitle = useMemo(() => {
      let style = uxStatus === 'danger' ? [styles.dangerTitle] : [styles.title];
      if (prefixKey) {
        return (
          <Text style={style} category="p1" bold="bold" margin="0 0 6 8" i18nKey={prefixKey} />
        );
      }

      return <></>;
    }, [prefixKey, uxStatus]);

    const $input = useMemo(() => {
      switch (uiType) {
        case 'base':
          return (
            <>
              <TouchableWithoutFeedback onPress={openModal}>
                <View
                  margin={prefixMargin}
                  padding="10 12 10 12"
                  style={{
                    width: '100%',
                    borderRadius: 16,
                    borderColor: 'background-color-100',
                    backgroundColor: 'background-color-100',
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                  }}>
                  {value && (
                    <Text
                      category="p1"
                      isCenter={false}
                      textContent={value}
                      style={{
                        color: 'text-color-800',
                      }}
                    />
                  )}
                  {!value && (
                    <Text
                      category="p1"
                      isCenter={false}
                      i18nKey={placeholderKey}
                      style={{
                        color: 'text-color-500',
                      }}
                    />
                  )}
                  <Image name="_suffix" />
                </View>
              </TouchableWithoutFeedback>
            </>
          );
        case 'line':
          let style = uxStatus === 'danger' ? styles.dangerInput : styles.input;
          return (
            <>
              <View
                margin={prefixMargin}
                style={{
                  width: '100%',
                }}>
                {$prefixTitle}
                <TouchableWithoutFeedback onPress={openModal}>
                  <View padding="10 12 10 12" style={style}>
                    {value && (
                      <Text
                        category="p1"
                        isCenter={false}
                        textContent={value}
                        style={{
                          color: 'text-color-800',
                        }}
                      />
                    )}
                    {!value && (
                      <Text
                        category="p1"
                        isCenter={false}
                        i18nKey={placeholderKey}
                        style={{
                          color: 'text-color-500',
                        }}
                      />
                    )}
                    <Image name="_suffix" />
                  </View>
                </TouchableWithoutFeedback>
              </View>
            </>
          );
      }
    }, [styles, type, prefixMargin, openModal, value, placeholderKey, prefixKey, uxStatus]);

    const DatetimeModal = useMemo(() => {
      if (type !== 'datetime') {
        return <></>;
      }

      if (_.isNil(visible)) {
        return <></>;
      }

      return (
        <CommonModal visible={visible} onBackdropPress={closeModal}>
          <View style={[styles.modal]} layoutStrategy="flexRowCenterCenter">
            <WheelPicker
              style={{ width: 100, height: 100 }}
              data={yearList}
              selectedItem={state.yearSelectItem}
              onItemSelected={(index: number) => {
                setState((prevState: IState) => ({
                  ...prevState,
                  yearSelectItem: index,
                }));
              }}
            />
            <WheelPicker
              style={{ width: 100, height: 100 }}
              data={monthList}
              selectedItem={state.monthSelectItem}
              onItemSelected={(index: number) => {
                setState((prevState: IState) => ({
                  ...prevState,
                  monthSelectItem: index,
                }));
              }}
            />
            <WheelPicker
              style={{ width: 100, height: 100 }}
              data={dayList}
              selectedItem={state.daySelectItem}
              onItemSelected={(index: number) => {
                setState((prevState: IState) => ({
                  ...prevState,
                  daySelectItem: index,
                }));
              }}
            />
          </View>
        </CommonModal>
      );
    }, [visible, state, type]);

    const YearModal = useMemo(() => {
      if (type !== 'year') {
        return <></>;
      }

      if (_.isNil(visible)) {
        return <></>;
      }

      return (
        <CommonModal visible={visible} onBackdropPress={closeModal}>
          <View style={[styles.modal]} layoutStrategy="flexRowCenterCenter">
            <WheelPicker
              style={{ width: 200, height: 100 }}
              data={yearList}
              selectedItem={state.yearSelectItem}
              onItemSelected={(index: number) => {
                setState((prevState: IState) => ({
                  ...prevState,
                  yearSelectItem: index,
                }));
              }}
            />
          </View>
        </CommonModal>
      );
    }, [visible, state, type]);

    return (
      <>
        {$input}
        {DatetimeModal}
        {YearModal}
      </>
    );
  }),
);

const useGetStyle = (props: {}) => {
  return useMemo(() => {
    return {
      modal: {
        paddingTop: 32,
        paddingBottom: 32,
        borderRadius: 16,
        backgroundColor: 'background-color-0',
        maxHeight: Dimensions.get('window').height * 0.8,
        maxWidth: Dimensions.get('window').width * 0.8,
      },
      input: {
        width: '100%',
        height: 48,
        borderRadius: 8,
        borderBottomColor: 'line-color-200',
        borderBottomWidth: 1,
        backgroundColor: 'background-color-0',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        alignSelf: 'center',
      },
      dangerInput: {
        width: '100%',
        height: 48,
        borderRadius: 8,
        borderBottomColor: 'danger-color-500',
        borderWidth: 1,
        backgroundColor: 'background-color-0',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        alignSelf: 'center',
      },
      title: {
        color: 'text-color-800',
      },
      dangerTitle: {
        color: 'danger-color-500',
      },
    };
  }, [props]);
};
