/* eslint-disable react-native/no-inline-styles */
import { useThemeManager } from '@/managers';
import React, { Ref, forwardRef, useImperativeHandle, useMemo, useRef } from 'react';
import { TouchableWithoutFeedback } from 'react-native';
import Image from '../../image';
import Text from '../../text';
import View from '../../view';
import Input, { RefType as BasicInputRefType, InputProps } from '../baseInput';
import { useTheme } from '@/hooks';
interface IPrpos extends InputProps {
  placeholderKey?: string;
  prefixKey?: string;
  prefixMargin?: string;
  secureTextEntry?: boolean;
  toggleSecureEntry?: () => void | undefined;
  type?: 'base' | 'line';
}

export type RefType = {
  emitErrorStatus: Function;
  blur: Function;
  focus: Function;
};

/**
 * Password Input
 *
 * @property {string} placeholderKey - Can be use a string. like a key(homeString.loanAmountUpTo)
 * @property {string} prefixKey - Can be use a string. like a key(homeString.loanAmountUpTo)
 * @property {string} prefixMargin - Can be use a string, like `12 0 0 0`
 * @property {boolean} secureTextEntry
 * @method toggleSecureEntry
 *
 */
export default React.memo(
  forwardRef((_props: IPrpos, ref: Ref<RefType>): React.ReactElement => {
    const {
      prefixKey,
      prefixMargin,
      secureTextEntry,
      toggleSecureEntry,
      value,
      onChangeText,
      type = 'base',
      style = {},
      ...props
    } = _props;

    const theme = useTheme();

    const inputRef = useRef<BasicInputRefType>(null);

    const {
      value: { applicationTheme },
    } = useThemeManager();

    useImperativeHandle(ref, () => ({
      emitErrorStatus(tips: string) {
        inputRef.current?.emitErrorStatus(tips);
      },
      blur() {
        inputRef.current?.blur();
      },
      focus() {
        inputRef.current?.focus();
      },
    }));

    const eyeIcon = () => (
      <TouchableWithoutFeedback
        onPress={() => {
          toggleSecureEntry && toggleSecureEntry();
        }}>
        <Image tintColor={theme['primary-color-500']} name="_openEye" />
      </TouchableWithoutFeedback>
    );

    const eyeOffIcon = () => (
      <TouchableWithoutFeedback
        onPress={() => {
          toggleSecureEntry && toggleSecureEntry();
        }}>
        <Image tintColor={theme['primary-color-500']} name="_closeEye" />
      </TouchableWithoutFeedback>
    );

    const onInputChange = (text: string) => {
      onChangeText && onChangeText(text);
    };

    const $prefixTitle = useMemo(() => {
      if (prefixKey) {
        return <Text bold="bold" margin="0 0 6 8" i18nKey={prefixKey} />;
      }

      return <></>;
    }, [prefixKey]);

    const $input = useMemo(() => {
      switch (type) {
        case 'base':
          return (
            <Input
              ref={inputRef}
              {...props}
              value={value}
              secureTextEntry={secureTextEntry}
              accessoryRight={secureTextEntry ? eyeOffIcon : eyeIcon}
              onChangeText={onInputChange}
            />
          );
        case 'line':
          return (
            <Input
              ref={inputRef}
              {...props}
              value={value}
              secureTextEntry={secureTextEntry}
              accessoryRight={secureTextEntry ? eyeOffIcon : eyeIcon}
              onChangeText={onInputChange}
              textStyle={{
                color: theme['text-color-800'],
                fontSize: 16,
              }}
              style={{
                backgroundColor: 'background-color-0',
                borderBottomWidth: 1,
                borderWidth: 0,
                borderRadius: 0,
                borderBottomColor: 'line-color-200',
                ...style,
              }}
              dangerStyle={{
                backgroundColor: 'background-color-0',
                borderBottomWidth: 1,
                borderWidth: 0,
                borderRadius: 0,
                borderBottomColor: 'danger-color-500',
              }}
            />
          );
      }
    }, [type, secureTextEntry, value]);

    return (
      <View margin={prefixMargin} style={{ width: '100%' }}>
        {$prefixTitle}
        {$input}
      </View>
    );
  }),
);
