/* eslint-disable react/jsx-no-undef */
/* eslint-disable react-native/no-inline-styles */
import { HitPointEnumsSpace } from '@/enums';
import { trackInputEventEnd, trackInputEventStart } from '@/trackEvent';
import { useCustomStyleSheet } from '@/utils';
import _ from 'lodash';
import React, {
  Ref,
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react';
import { Dimensions, Pressable, TouchableWithoutFeedback, VirtualizedList } from 'react-native';
import Image from '../../image';
import CommonModal from '../../modal/commonModal';
import Text from '../../text';
import View from '../../view';
import Input, { RefType as BasicInputRefType, IValidateItem } from '../baseInput';
import { ActionSheet, Divider } from '@/components';

interface IPrpos {
  value: string;
  options: OptionItemType[];
  setValue: Function;
  changeSelectCallback?: Function;
  disabled?: boolean;
  infoText?: string;
  prefixKey?: string;
  prefixMargin?: string;
  placeholderKey?: string;
  onFocus?: () => void;
  onBlur?: () => void;
  /** 埋点事件对象 */
  pageKey?: HitPointEnumsSpace.EPageKey;
  eventKey?: HitPointEnumsSpace.EEventKey;
  type?: 'base' | 'line';
  selectModel?: 'modal' | 'actionSheet' | 'page';
  validateConfig?: IValidateItem[];
  // 当该属性不为空的时候，会在点击的时候触发，该组件不会有焦点的获取
  onPress?: () => void;
}

// OptionItemType 可以是对象或字符串类型
type OptionItemType =
  | {
      label?: string;
      labelI18nKey?: string;
      value: string;
    }
  | string;

export type SelectRefType = {
  onClickItem: Function;
  onCloseModal: Function;
};
/**
 * Prefix prefix select input
 */
export default React.memo(
  forwardRef((props: IPrpos, ref: Ref<SelectRefType | BasicInputRefType>): React.ReactElement => {
    const {
      value,
      setValue,
      prefixKey = '',
      prefixMargin = '0 0 0 0',
      placeholderKey = '',
      disabled = false,
      infoText = '',
      options,
      changeSelectCallback,
      pageKey,
      eventKey,
      onFocus,
      onBlur,
      type = 'base',
      selectModel = 'actionSheet',
      onPress,
    } = props;
    // @ts-ignore
    const [visible, setVisible] = useState<boolean | null>(null);
    const [uxStatus, setUxStatus] = useState<string>('basic');
    const inputRef = useRef<BasicInputRefType>(null);
    const styles = useCustomStyleSheet(useGetStyle());

    useImperativeHandle(ref, () => ({
      blur() {},
      focus(_pageKey: HitPointEnumsSpace.EPageKey, _eventKey: HitPointEnumsSpace.EEventKey) {
        setVisible(true);
        if (_pageKey && _eventKey) {
          trackInputEventStart({
            p: _pageKey,
            e: _eventKey,
          });
        }
      },
      emitErrorStatus(tips: string, linkInfo?: string, linkAction?: () => void) {
        inputRef.current?.emitErrorStatus(tips, linkInfo, linkAction);
        // setUxStatus('danger');
      },
      clearErrorStatus() {
        inputRef.current?.clearErrorStatus();
        // setUxStatus('basic');
      },
      isFocused() {
        // return visible;
      },
      clear() {
        setValue('');
      },
      onClickItem,
      onCloseModal: closeModal,
    }));

    useEffect(() => {
      if (_.isNil(visible)) {
        return;
      }

      if (visible) {
        typeof onFocus === 'function' && onFocus();
      } else {
        typeof onBlur === 'function' && onBlur();
      }
    }, [visible]);

    const getUxStatusFromChildComp = (uxStatus: string) => {
      setUxStatus(uxStatus);
    };

    const onChangeText = useCallback((text: string) => {
      setValue(text);
    }, []);

    const onClickItem = useCallback((name: string) => {
      setUxStatus('basic');
      onChangeText(name);
      changeSelectCallback && changeSelectCallback(name);
      closeModal(name);
    }, []);

    const openModal = useCallback(() => {
      inputRef.current?.clearErrorStatus();
      if (!onPress) {
        setVisible(true);
      } else {
        onPress();
      }
      if (pageKey && eventKey) {
        trackInputEventStart({
          p: pageKey,
          e: eventKey,
        });
      }
    }, []);

    const closeModal = useCallback((name?: string) => {
      if (pageKey && eventKey) {
        trackInputEventEnd(
          {
            p: pageKey,
            e: eventKey,
          },
          typeof name === 'string' ? name : '',
        );
      }
      setVisible(false);
    }, []);

    const $prefixTitle = useMemo(() => {
      let style = styles.title;
      if (prefixKey) {
        return (
          <Text
            style={[style, { zIndex: 2, marginBottom: type === 'line' ? -14 : 0 }]}
            category="p1"
            margin="0 0 0 6"
            i18nKey={prefixKey}
          />
        );
      }

      return <></>;
    }, [prefixKey, uxStatus, type]);

    const seletedOption = useMemo(() => {
      let selOp = options.filter(
        //@ts-ignore
        (option: OptionItemType) => option?.value === value || option === value,
      );
      if (selOp.length === 0) {
        return null;
      } else {
        return selOp[0];
      }
    }, [options, value]);

    const $defineSelect = useMemo(() => {
      switch (type) {
        case 'base':
          return (
            <>
              <View
                margin={prefixMargin}
                style={{
                  width: '100%',
                }}>
                {$prefixTitle}
                <TouchableWithoutFeedback disabled={disabled} onPress={openModal}>
                  <View
                    margin={prefixMargin}
                    padding="10 12 10 12"
                    style={{
                      width: '100%',
                      borderRadius: 16,
                      borderColor: 'background-color-100',
                      backgroundColor: 'background-color-100',
                      flexDirection: 'row',
                      alignItems: 'center',
                      justifyContent: 'space-between',
                    }}>
                    {value && (
                      <Text
                        category="p1"
                        isCenter={false}
                        textContent={value}
                        style={{
                          color: 'text-color-800',
                        }}
                      />
                    )}
                    {!value && (
                      <Text
                        category="p1"
                        isCenter={false}
                        i18nKey={placeholderKey}
                        style={{
                          color: 'text-color-500',
                        }}
                      />
                    )}
                    <Image name="_suffix" />
                  </View>
                </TouchableWithoutFeedback>
                {infoText && (
                  <View
                    layoutStrategy="flexRowStartCenter"
                    margin="2 0 0 0"
                    style={{
                      width: '100%',
                    }}>
                    <Image margin="0 4 0 0" name="_infoIcon" width={16} height={16} />
                    <Text
                      category="c1"
                      i18nKey={infoText}
                      style={{
                        color: 'text-color-500',
                      }}
                    />
                  </View>
                )}
              </View>
            </>
          );
        case 'line':
          return (
            <>
              <View
                margin={prefixMargin}
                style={{
                  width: '100%',
                }}>
                {value ? $prefixTitle : null}
                <TouchableWithoutFeedback
                  style={{ padding: 0, margin: 0 }}
                  disabled={disabled}
                  onPress={openModal}>
                  <View>
                    <Input
                      {...props}
                      ref={inputRef}
                      placeholderKey={value ? '' : prefixKey}
                      placeholderTextColor="text-color-500"
                      value={
                        //@ts-ignore
                        seletedOption?.label || value
                      }
                      accessoryRight={() => <Image name={'_arrowRight'} />}
                      uxCallback={getUxStatusFromChildComp}
                      disabled={disabled}
                      textStyle={{
                        color: 'text-color-800',
                        fontSize: 16,
                      }}
                      style={{
                        pointerEvents: 'none',
                        borderRadius: 0,
                        borderWidth: 0,
                        paddingLeft: 6,
                        borderBottomWidth: 1,
                        color: 'text-color-800',
                        borderColor: 'fill-color-500',
                        backgroundColor: 'background-color-0',
                      }}
                      dangerStyle={{
                        backgroundColor: 'background-color-0',
                        borderRadius: 0,
                        borderWidth: 0,
                        borderBottomWidth: 1,
                        borderColor: 'danger-color-500',
                        fontSize: 14,
                        marginTop: 8,
                      }}
                      dangerTextStyle={{
                        fontSize: 14,
                        flex: 1,
                        marginTop: 8,
                      }}
                    />
                  </View>
                </TouchableWithoutFeedback>
                {infoText && (
                  <View
                    layoutStrategy="flexRowStartCenter"
                    margin="4 0 0 0"
                    style={{
                      width: '100%',
                    }}>
                    <Image margin="0 4 0 0" name="_blueInfo" />
                    <Text
                      category="c1"
                      i18nKey={infoText}
                      style={{
                        color: 'text-color-500',
                      }}
                    />
                  </View>
                )}
              </View>
            </>
          );
      }
    }, [
      openModal,
      prefixMargin,
      placeholderKey,
      infoText,
      value,
      type,
      prefixKey,
      uxStatus,
      disabled,
    ]);

    const DATA = options;

    const getItem = (data: string, index: number) => {
      return {
        id: Math.random().toString(12).substring(0),
        data: data[index],
      };
    };

    const getItemCount = (data: string[]) => data.length;

    const SelectModal = useMemo(() => {
      if (selectModel != 'modal') {
        return <></>;
      }
      if (_.isNil(visible)) {
        return <></>;
      }

      return (
        <CommonModal visible={visible} onBackdropPress={closeModal}>
          <View style={[styles.modal]}>
            <Text
              margin="0 16 0 16"
              padding="19 0 19 0"
              isCenter={true}
              i18nKey={prefixKey}
              style={{
                color: 'text-color-600',
              }}
            />
            <VirtualizedList
              fadingEdgeLength={10}
              data={[...new Set(DATA)]}
              initialNumToRender={10}
              renderItem={({ item }) => (
                <TouchableWithoutFeedback
                  key={item.id}
                  onPress={() => {
                    onClickItem(item.data);
                  }}>
                  <Text
                    margin="0 16 0 16"
                    padding="19 0 19 0"
                    style={{
                      borderBottomWidth: 1,
                      borderColor: 'line-color-100',
                    }}
                    isCenter={true}
                    textContent={item.data}
                  />
                </TouchableWithoutFeedback>
              )}
              keyExtractor={item => item.id}
              getItemCount={getItemCount}
              getItem={getItem}
            />
          </View>
        </CommonModal>
      );
    }, [visible, selectModel]);

    const $ActionSheetModal = useMemo(() => {
      if (selectModel != 'actionSheet') {
        return <></>;
      }

      if (_.isNil(visible)) {
        return <></>;
      }

      return (
        <ActionSheet visible={visible} onClose={closeModal.bind(this, undefined)}>
          <View padding="0 0 16 0" style={[styles.modal]}>
            <View margin="0 16 0 16" layoutStrategy="flexRowBetweenCenter">
              <Text
                padding="19 0 19 0"
                isCenter={true}
                category="h3"
                style={{
                  color: 'text-color-800',
                }}
                i18nKey={prefixKey}
              />
              {/* @ts-ignore */}
              <TouchableWithoutFeedback onPress={closeModal.bind(this, undefined)}>
                <Image name="_clearIcon" />
              </TouchableWithoutFeedback>
            </View>
            <VirtualizedList
              contentContainerStyle={{ paddingHorizontal: 16 }}
              ItemSeparatorComponent={() => <Divider />}
              fadingEdgeLength={10}
              data={[...new Set(DATA)]}
              initialNumToRender={10}
              renderItem={({ item: { data, id } }) => (
                <TouchableWithoutFeedback
                  key={id}
                  onPress={() => {
                    //@ts-ignore
                    onClickItem(data?.value || data);
                  }}>
                  <Text
                    margin="0 16 0 16"
                    padding="19 0 19 0"
                    status={
                      //@ts-ignore
                      value === data?.value || value === data ? 'primary' : 'basic'
                      // value === (optionType === 'object' ? data.value : data)
                      //   ? 'primary'
                      //   : 'basic'
                    }
                    isCenter={true}
                    //@ts-ignore
                    textContent={data?.label || data}
                    //@ts-ignore
                    i18nKey={data?.labelI18nKey}
                  />
                </TouchableWithoutFeedback>
              )}
              keyExtractor={item => item.id}
              getItemCount={getItemCount}
              getItem={getItem}
            />
          </View>
        </ActionSheet>
      );
    }, [visible, selectModel, options]);

    return (
      <>
        {$defineSelect}
        {SelectModal}
        {$ActionSheetModal}
      </>
    );
  }),
);

const useGetStyle = () => {
  return useMemo(() => {
    return {
      modal: {
        backgroundColor: 'background-color-0',
        borderRadius: 16,
        // maxHeight: Dimensions.get('window').height * 0.8,
      },
      input: {
        width: '100%',
        height: 48,
        borderBottomColor: 'line-color-200',
        borderBottomWidth: 1,
        backgroundColor: 'background-color-0',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        alignSelf: 'center',
      },
      dangerInput: {
        width: '100%',
        height: 48,
        borderRadius: 0,
        borderBottomColor: 'danger-color-500',
        borderWidth: 0,
        borderBottomWidth: 1,
        backgroundColor: 'background-color-0',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        alignSelf: 'center',
      },
      title: {
        color: 'text-color-500',
      },
      dangerTitle: {
        color: 'danger-color-500',
      },
    };
  }, []);
};
