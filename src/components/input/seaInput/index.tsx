import React, {
  useState,
  useRef,
  useEffect,
  useMemo,
  forwardRef,
  Ref,
  useCallback,
  useImperativeHandle,
} from 'react';
import { Animated, TouchableWithoutFeedback } from 'react-native';
import { useThemeManager } from '@/managers';
import { useNameSpace } from '@/i18n';
import Input, { RefType as BasicInputRefType, InputProps } from '../baseInput';
import View from '../../view';
import Image from '../../image';
import Text from '../../text';
// 定义 Props 类型
interface IPrpos extends InputProps {
  value: string;
  setValue: Function;
  disabled?: boolean;
  prefixKey?: string;
  prefixMargin?: string;
  limitReg?: (value: string | number) => boolean;
  type?: 'base' | 'line';
  isShowPlaceholderKey?: boolean;
}

export type RefType = {
  emitErrorStatus: Function;
  blur: Function;
  focus: Function;
  clearErrorStatus: Function;
  isFocused: Function;
};

const defaultProps = {
  inputPadding: 10,
  animationDuration: 300,
};

const SeaInput = React.memo(
  forwardRef((_props: IPrpos, ref: Ref<RefType>): React.ReactElement => {
    const {
      value,
      setValue,
      disabled = false,
      prefixKey = '',
      prefixMargin = '0 0 0 0',
      placeholderKey = '',
      type = 'base',
      maxLength,
      accessoryRight,
      onFocus,
      onBlur,
      style = {},
      dangerStyle = {},
      textStyle = {},
      isShowPlaceholderKey = false,
      ...props
    } = _props;

    const [uxStatus, setUxStatus] = useState<string>('basic');

    const [isFocused, setIsFocued] = useState<boolean>(false);

    const focusedAnim = useRef(new Animated.Value(0)).current;
    const inputRef = useRef<RefType>(null);

    const onInputChange = (text: string) => {
      setValue(text);
    };

    const getUxStatusFromChildComp = (uxStatus: string) => {
      setUxStatus(uxStatus);
    };

    useImperativeHandle(ref, () => ({
      emitErrorStatus(tips: string, linkInfo?: string, linkAction?: () => void) {
        inputRef.current?.emitErrorStatus(tips, linkInfo, linkAction);
      },
      clearErrorStatus() {
        inputRef.current?.clearErrorStatus();
      },
      blur() {
        inputRef.current?.blur();
      },
      focus() {
        inputRef.current?.focus();
      },
      isFocused() {
        return inputRef.current?.isFocused?.();
      },
      clear() {
        setValue('');
      },
    }));

    // Focus handler
    const focus = (e: any) => {
      setIsFocued(true);
      inputRef.current?.focus(e);
      onFocus && onFocus(e);
      toggle(true);
    };

    const blur = (e: any) => {
      setIsFocued(false);
      onBlur && onBlur(e);
      toggle(false);
    };

    const clear = () => {
      setValue('');
      inputRef.current?.clearErrorStatus();
      const focused = inputRef.current?.isFocused?.();
      if (!focused) {
        toggle(false);
      }
    };
    useEffect(() => {
      if (value) {
        focusedAnim.setValue(1);
      } else {
        const focused = inputRef.current?.isFocused?.();
        if (!focused) {
          focusedAnim.setValue(0);
        }
      }
    }, [value]);

    const toggle = (isActive: boolean) => {
      Animated.timing(focusedAnim, {
        toValue: isActive ? 1 : 0,
        duration: defaultProps.animationDuration,
        useNativeDriver: false,
      }).start();
    };

    const {
      value: { applicationTheme },
    } = useThemeManager();

    const t = useNameSpace().t;

    const $limitReg = useMemo(() => {
      if (maxLength) {
        return (
          <Text
            margin="0 0 0 12"
            style={{ color: 'text-color-500' }}
            textContent={`${value?.length || 0}/${maxLength}`}
          />
        );
      }
      return null;
    }, [value, maxLength]);

    const _accessoryRight = useCallback(() => {
      if (!value) {
        return (
          <View layoutStrategy="flexRowStartCenter">
            {$limitReg}
            {accessoryRight && accessoryRight()}
          </View>
        );
      } else {
        return (
          <View layoutStrategy="flexRowStartCenter">
            {isFocused && (
              <TouchableWithoutFeedback onPress={clear}>
                <Image name="_iconCloseRound" />
              </TouchableWithoutFeedback>
            )}
            {$limitReg}
            {accessoryRight && accessoryRight()}
          </View>
        );
      }
    }, [value, maxLength, $limitReg, accessoryRight, isFocused]);

    return (
      <View
        margin={prefixMargin}
        style={{
          width: '100%',
        }}>
        <Input
          {...props}
          maxLength={maxLength}
          accessoryRight={_accessoryRight}
          onFocus={focus}
          onBlur={blur}
          ref={inputRef}
          uxCallback={getUxStatusFromChildComp}
          value={value}
          disabled={disabled}
          textStyle={{
            color: 'text-color-800',
            fontSize: 16,
            ...textStyle,
          }}
          dangerTextStyle={{
            fontSize: 14,
            lineHeight: 22,
            flex: 1,
          }}
          style={{
            borderRadius: 0,
            borderWidth: 0,
            paddingLeft: 6,
            borderBottomWidth: 1,
            color: 'text-color-800',
            borderColor: isFocused ? 'text-color-700' : 'fill-color-500',
            backgroundColor: 'background-color-0',
            ...style,
          }}
          dangerStyle={{
            backgroundColor: 'background-color-0',
            borderRadius: 0,
            borderWidth: 0,
            borderBottomWidth: 1,
            borderColor: 'danger-color-500',
            ...dangerStyle,
          }}
          onChangeText={onInputChange}
          placeholderKey={isShowPlaceholderKey ? placeholderKey : ''}
        />
        <TouchableWithoutFeedback onPress={focus}>
          <Animated.View
            style={{
              position: 'absolute',
              left: 6,
              top: focusedAnim.interpolate({
                inputRange: [0, 1],
                outputRange: [!value ? defaultProps.inputPadding : -8, -8],
              }),
            }}>
            <Text
              style={[
                {
                  fontSize: 16,
                  color: applicationTheme['text-color-500'],
                },
              ]}>
              {!isShowPlaceholderKey ? t(prefixKey) : ''}
            </Text>
          </Animated.View>
        </TouchableWithoutFeedback>
      </View>
    );
  }),
);

export default SeaInput;
