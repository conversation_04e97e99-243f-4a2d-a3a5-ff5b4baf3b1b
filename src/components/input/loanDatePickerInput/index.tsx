import { DatePickerCalendarInput, View } from '@/components';
import { OrderVOSpace } from '@/types';
import React, { memo, useEffect, useMemo } from 'react';
import moment from 'moment';
import { AppDefaultConfigManager } from '@/managers';
import { Colors } from '@/themes';

interface IProps {
  repayDate?: string;
  onChangeRepayDate: (date: string) => void;
  loanRepayDays?: OrderVOSpace.LoanRepayDays;
  // 是否为首贷
  isFirstLoan?: boolean;
  onFocus?: () => void;
  onBlur?: () => void;
  extraData?: any;
}

export default memo(
  ({
    repayDate = '',
    onChangeRepayDate,
    loanRepayDays,
    isFirstLoan = true,
    onFocus,
    onBlur,
    extraData,
  }: IProps) => {
    useEffect(() => {
      console.log(
        'loanRepayDays',
        'startDate',
        moment(loanRepayDays?.startDate, 'DD/MM/YYYY').toDate(),
        'endDate',
        moment(loanRepayDays?.endDate, 'DD/MM/YYYY').subtract(1, 'day').toDate(),
      );
    }, [loanRepayDays]);
    const defaultDate = useMemo(() => {
      if (
        !AppDefaultConfigManager.context.appDefaultConfigModel.defaultConfigState?.repayDateConfig
      ) {
        return null;
      }
      if (isFirstLoan) {
        return {
          startDate:
            AppDefaultConfigManager.context.appDefaultConfigModel.defaultConfigState
              ?.repayDateConfig?.firstLoanStartDate,
          endDate:
            AppDefaultConfigManager?.context.appDefaultConfigModel.defaultConfigState
              ?.repayDateConfig?.firstLoanEndDate,
        };
      } else {
        return {
          startDate:
            AppDefaultConfigManager.context.appDefaultConfigModel.defaultConfigState
              ?.repayDateConfig?.reLoanStartDate,
          endDate:
            AppDefaultConfigManager.context.appDefaultConfigModel.defaultConfigState
              ?.repayDateConfig?.reLoanEndDate,
        };
      }
    }, [
      AppDefaultConfigManager.context.appDefaultConfigModel.defaultConfigState?.repayDateConfig,
      isFirstLoan,
    ]);
    return (
      <View
        style={{
          backgroundColor: 'background-color-0',
          borderRadius: 12,
        }}>
        <DatePickerCalendarInput
          prefixMargin={'0 0 0 0'}
          inputStyle={{
            backgroundColor: !repayDate ? '#F9FBFF' : '#fff',
            borderColor: !repayDate ? Colors.PRIMARY_COLOR_400 : Colors.FILL_COLOR_500,
          }}
          calendarIconStyle={{
            tintColor: !repayDate ? Colors.PRIMARY_COLOR_500 : Colors.FILL_COLOR_500,
          }}
          calendarNoValueTextStyle={{
            color: !repayDate ? Colors.PRIMARY_COLOR_300 : Colors.TEXT_COLOR_500,
          }}
          uiType="line"
          placeholderKey={'homeString.plsSelectLoanDateTips'}
          value={repayDate}
          setValue={onChangeRepayDate}
          type={'date'}
          disabledDate={loanRepayDays?.holidays}
          extraData={extraData}
          format={'DD/MM/YYYY'}
          minDate={loanRepayDays?.startDate || defaultDate?.startDate || undefined}
          maxDate={
            loanRepayDays?.endDate || defaultDate?.endDate
              ? moment(loanRepayDays?.endDate || defaultDate?.endDate, 'DD/MM/YYYY')
                  .subtract(1, 'day')
                  .toDate()
              : undefined
          }
          onFocus={onFocus}
          onBlur={onBlur}
        />
      </View>
    );
  },
);
