/* eslint-disable react-native/no-inline-styles */
import { HitPointEnumsSpace } from '@/enums';
import { useCustomStyleSheet, verifyPhoneNumber } from '@/utils';
import React, { Ref, forwardRef, useImperativeHandle, useMemo, useRef, useState } from 'react';
import { useNameSpace } from '../../../i18n';
import Text from '../../text';
import View from '../../view';
import Input, { RefType as BasicInputRefType, InputProps } from '../baseInput';

interface IPrpos extends InputProps {
  phoneNumber: string;
  setPhoneNumber: Function;
  disabled?: boolean;
  prefixKey?: string;
  prefixMargin?: string;
  prefixPhoneNumber?: number;
  inputLengthLimit?: number;
  pageKey?: HitPointEnumsSpace.EPageKey;
  eventKey?: HitPointEnumsSpace.EEventKey;
  type?: 'base' | 'line';
}

export type RefType = {
  emitErrorStatus: Function;
  blur: Function;
  focus: Function;
  clearErrorStatus: Function;
};

const PREFIX_MOBILE = '52';
const MAX_LENGTH = 11;
/**
 * Phonenumber Input
 * @property {string} phoneNumber
 * @property {Function} setPhoneNumber
 * @property {boolean} disabled
 * @property {string} prefixKey - Can be use a string. like a key(homeString.loanAmountUpTo)
 * @property {string} prefixMargin - Can be use a string, like `12 0 0 0`
 * @property {string} prefixPhoneNumber - can be use 52
 * @property {number} inputLengthLimit
 *
 */
export default React.memo(
  forwardRef((_props: IPrpos, ref: Ref<RefType>): React.ReactElement => {
    const {
      phoneNumber,
      setPhoneNumber,
      disabled = false,
      prefixKey = '',
      prefixMargin = '0 0 0 0',
      placeholderKey = '',
      prefixPhoneNumber = PREFIX_MOBILE,
      type = 'base',
      ...props
    } = _props;

    const styles = useCustomStyleSheet(useGetStyle());
    const inputRef = useRef<BasicInputRefType>(null);
    const [uxStatus, setUxStatus] = useState<string>('basic');

    useImperativeHandle(ref, () => ({
      emitErrorStatus(tips: string) {
        inputRef.current?.emitErrorStatus(tips);
      },
      clearErrorStatus() {
        inputRef.current?.clearErrorStatus();
      },
      blur() {
        inputRef.current?.blur();
      },
      focus() {
        inputRef.current?.focus();
      },
    }));

    const getUxStatusFromChildComp = (uxStatus: string) => {
      setUxStatus(uxStatus);
    };

    const t = useNameSpace('loginString').t;

    const onInputChange = (text: string) => {
      if (text.length <= MAX_LENGTH) {
        setPhoneNumber(text);
      }
    };

    const renderLeft = (): React.ReactElement => (
      <Text
        category="p1"
        margin="0 8 0 0"
        padding="0 14 0 0"
        bold="bold"
        style={{
          color: 'primary-color-500',
          borderRightWidth: 2,
          borderColor: 'fill-color-400',
        }}>
        {`+${prefixPhoneNumber}`}
      </Text>
    );

    const $prefixTitle = useMemo(() => {
      let style = uxStatus === 'danger' ? styles.dangerTitle : styles.title;
      if (disabled) style = styles.disabledTitle;
      if (prefixKey) {
        return <Text category="p1" bold="500" style={style} margin="0 0 6 8" i18nKey={prefixKey} />;
      }

      return <></>;
    }, [prefixKey, uxStatus, disabled]);

    const $input = useMemo(() => {
      switch (type) {
        case 'base':
          return (
            <Input
              {...props}
              ref={inputRef}
              keyboardType="number-pad"
              validateConfig={[
                {
                  condition: verifyPhoneNumber(phoneNumber),
                  info: t('peso_enter_phone_error_tips'),
                  status: 'danger',
                },
              ]}
              placeholderKey={placeholderKey}
              value={phoneNumber}
              disabled={disabled}
              accessoryLeft={renderLeft}
              onChangeText={onInputChange}
              maxLength={MAX_LENGTH}
            />
          );
        case 'line':
          return (
            <Input
              {...props}
              ref={inputRef}
              uxCallback={getUxStatusFromChildComp}
              keyboardType="number-pad"
              validateConfig={[
                {
                  condition: verifyPhoneNumber(phoneNumber),
                  info: t('peso_enter_phone_error_tips'),
                  status: 'danger',
                },
              ]}
              style={styles.input}
              dangerStyle={styles.dangerInput}
              placeholderKey={placeholderKey}
              value={phoneNumber}
              disabled={disabled}
              accessoryLeft={renderLeft}
              onChangeText={onInputChange}
              maxLength={MAX_LENGTH}
            />
          );
      }
    }, [type, inputRef, phoneNumber, placeholderKey, phoneNumber, disabled, uxStatus]);

    return (
      <View
        margin={prefixMargin}
        style={{
          width: '100%',
        }}>
        {$prefixTitle}
        {$input}
      </View>
    );
  }),
);

const useGetStyle = () => {
  return useMemo(() => {
    return {
      input: {
        backgroundColor: 'background-color-0',
        borderBottomWidth: 1,
        borderWidth: 0,
        borderRadius: 0,
        borderBottomColor: 'line-color-200',
      },
      dangerInput: {
        backgroundColor: 'background-color-0',
        borderBottomWidth: 1,
        borderWidth: 0,
        borderRadius: 0,
        borderBottomColor: 'danger-color-500',
      },
      title: {
        color: 'text-color-800',
      },
      dangerTitle: {
        color: 'danger-color-500',
      },
      disabledTitle: {
        color: 'text-color-800',
      },
    };
  }, []);
};
