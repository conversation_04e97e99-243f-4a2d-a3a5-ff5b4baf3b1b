/**
 * @description: 日历日期选择器
 * 时间范围是前闭后开
 */
import { HitPointEnumsSpace } from '@/enums';
import { trackInputEventEnd, trackInputEventStart } from '@/trackEvent';
import { useCustomStyleSheet } from '@/utils';
import React, {
  ReactElement,
  Ref,
  forwardRef,
  memo,
  useEffect,
  useImperativeHandle,
  useMemo,
  useState,
} from 'react';
import {
  Dimensions,
  ImageStyle,
  Pressable,
  TextStyle,
  TouchableWithoutFeedback,
  ViewStyle,
} from 'react-native';
import Image from '../../image';
import Text from '../../text';
import View from '../../view';
import { RefType } from '../baseInput';
import _ from 'lodash';
import { useTheme } from '@/hooks';
import dayjs from 'dayjs';
import 'dayjs/locale/es';
import { Colors } from '@/themes';
import { CalendarActionSheet } from '@/components';
import { Strings } from '@/i18n';

interface IPrpos {
  value: string;
  setValue: Function;
  changeSelectCallback?: Function;
  disabled?: boolean;
  prefixKey?: string;
  prefixMargin?: string;
  placeholderKey?: string;
  type: 'year' | 'datetime' | 'date' | 'day';
  format: 'DD/MM' | 'DD/MM/YYYY' | 'YYYY';
  onFocus?: () => void;
  onBlur?: () => void;
  /** 埋点事件对象 */
  pageKey?: HitPointEnumsSpace.EPageKey;
  eventKey?: HitPointEnumsSpace.EEventKey;
  uiType?: 'base' | 'line';
  disabledDate?: string[];
  minDate?: string | Date;
  maxDate?: string | Date;
  inputStyle?: ViewStyle;
  calendarIconStyle?: ImageStyle;
  inputArrowIconStyle?: ImageStyle;
  calendarValueTextStyle?: TextStyle;
  calendarNoValueTextStyle?: TextStyle;
  extraData?: any;
}

interface IState {
  visible: boolean | null;
}

export default memo(
  forwardRef((props: IPrpos, ref: Ref<RefType>): ReactElement => {
    const {
      value,
      setValue,
      prefixKey = '',
      type = 'datetime',
      format = 'DD/MM/YYYY',
      prefixMargin = '0 0 0 0',
      placeholderKey = '',
      changeSelectCallback,
      pageKey,
      eventKey,
      onFocus,
      onBlur,
      uiType = 'base',
      disabledDate = [],
      minDate,
      maxDate,
      inputStyle,
      calendarIconStyle,
      inputArrowIconStyle,
      calendarValueTextStyle,
      calendarNoValueTextStyle,
    } = props;
    const styles = useCustomStyleSheet(
      useGetStyle({
        inputStyle,
        calendarIconStyle,
        calendarValueTextStyle,
        calendarNoValueTextStyle,
        inputArrowIconStyle,
      }),
    );
    const [uxStatus, setUxStatus] = useState<string>('basic');

    const defaultDate = useMemo(() => {
      if (!value) {
        return undefined;
      }
      if (value) {
        const [d, m, y] = value.split('/');
        return new Date(Number(y), Number(m) - 1, Number(d));
      }
    }, [value, disabledDate]);

    const [date, setDate] = useState<any>(defaultDate);

    const onChangeDate = (date: any) => {
      setDate(date);
      setUxStatus('basic');
      setVisible(false);
      let resultValue = dayjs(date).format(format);
      if (!disabledDate.includes(resultValue)) {
        setValue(resultValue);
        if (pageKey && eventKey) {
          trackInputEventEnd(
            {
              p: pageKey,
              e: eventKey,
            },
            resultValue,
          );
          if (pageKey && eventKey) {
            trackInputEventEnd(
              {
                p: pageKey,
                e: eventKey,
              },
              resultValue,
            );
          }
        }
      }
    };

    useImperativeHandle(ref, () => ({
      blur() {},
      focus(_pageKey: HitPointEnumsSpace.EPageKey, _eventKey: HitPointEnumsSpace.EEventKey) {
        setVisible(true);
        if (_pageKey && _eventKey) {
          trackInputEventStart({
            p: _pageKey,
            e: _eventKey,
          });
        }
      },
      emitErrorStatus(tips: string) {
        setUxStatus('danger');
      },
      clearErrorStatus() {
        setUxStatus('basic');
      },
      clear() {},
      isFocused() {
        return visible;
      },
    }));

    const [visible, setVisible] = useState<boolean | null>(null);
    const [extraData, setExtraData] = useState('');

    useEffect(() => {
      if (_.isNil(visible)) {
        return;
      }
      setTimeout(() => {
        setExtraData(Date.now().toString());
      }, 1000);

      if (visible) {
        typeof onFocus === 'function' && onFocus();
      } else {
        typeof onBlur === 'function' && onBlur();
      }
    }, [visible]);

    const onSetValue = () => {
      if (!date) {
        return;
      }
      let resultValue = dayjs(date).format(format);
      if (!disabledDate.includes(resultValue)) {
        setValue(resultValue);
        if (pageKey && eventKey) {
          trackInputEventEnd(
            {
              p: pageKey,
              e: eventKey,
            },
            resultValue,
          );
        }
      }
    };

    /** 开启弹窗, 如果有埋点则上报数据 */
    const openModal = () => {
      setVisible(true);
      if (pageKey && eventKey) {
        trackInputEventStart({
          p: pageKey,
          e: eventKey,
        });
      }
    };

    /** 关闭弹窗, 如果有埋点则上报数据 */
    const closeModal = () => {
      setUxStatus('basic');
      setVisible(false);
      onSetValue();
      if (pageKey && eventKey) {
        trackInputEventEnd(
          {
            p: pageKey,
            e: eventKey,
          },
          dayjs(date).format(format),
        );
      }
    };

    const $prefixTitle = useMemo(() => {
      let style = styles.title;
      if (prefixKey) {
        return (
          <Text
            style={[style, { zIndex: 2, marginBottom: uiType === 'line' ? -14 : 0 }]}
            category="p1"
            margin="0 0 0 6"
            i18nKey={prefixKey}
          />
        );
      }

      return <></>;
    }, [prefixKey]);

    const $input = useMemo(() => {
      const isDangerStatus = uxStatus === 'danger';
      switch (uiType) {
        case 'base':
          return (
            <>
              <TouchableWithoutFeedback onPress={openModal}>
                <View
                  margin={prefixMargin}
                  padding="10 12 10 12"
                  style={{
                    width: '100%',
                    borderRadius: 16,
                    borderColor: 'background-color-100',
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                  }}>
                  <View layoutStrategy="flexRowStartCenter">
                    <Image name="_dateIcon" margin="0 6 0 0" />
                    {value && (
                      <Text
                        category="p1"
                        isCenter={false}
                        textContent={value}
                        style={{
                          color: 'text-color-800',
                        }}
                      />
                    )}
                    {!value && (
                      <Text
                        category="p1"
                        isCenter={false}
                        i18nKey={placeholderKey}
                        style={{
                          color: 'text-color-400',
                        }}
                      />
                    )}
                  </View>
                  <Image name="_suffix" />
                </View>
              </TouchableWithoutFeedback>
            </>
          );
        case 'line':
          return (
            <>
              <View
                margin={prefixMargin}
                style={{
                  width: '100%',
                }}>
                {value ? $prefixTitle : null}
                <Pressable onPress={openModal}>
                  <View
                    padding="10 12 10 12"
                    style={[
                      styles.input,
                      {
                        borderColor: isDangerStatus
                          ? Colors.DANGER_COLOR_500
                          : styles.input.borderColor,
                      },
                    ]}>
                    <View layoutStrategy="flexRowStartCenter">
                      <Image name="_dateIcon" margin="0 6 0 0" style={styles.calendarIcon} />
                      {value && (
                        <Text
                          category="p1"
                          isCenter={false}
                          textContent={value}
                          style={styles.valueInputText}
                        />
                      )}
                      {!value && (
                        <Text
                          category="p1"
                          isCenter={false}
                          i18nKey={placeholderKey}
                          style={styles.novalueInputText}
                        />
                      )}
                    </View>
                    <Image name="_grayArrowRightIcon" style={styles.inputArrowIcon} />
                  </View>
                  <Text
                    category="c2"
                    style={{
                      display: isDangerStatus ? 'flex' : 'none',
                      fontSize: 14,
                      flex: 1,
                      marginTop: 8,
                      color: Colors.DANGER_COLOR_500,
                    }}
                    i18nKey={Strings.verificationString.required}
                  />
                </Pressable>
              </View>
            </>
          );
      }
    }, [type, prefixMargin, openModal, value, placeholderKey, prefixKey, uxStatus]);

    const theme = useTheme();

    const disabledDates = useMemo(() => {
      return disabledDate.map(date => {
        if (!!date) {
          const [d, m, y] = date.split('/');
          return new Date(Number(y), Number(m) - 1, Number(d));
        }
      });
    }, [disabledDate]);

    const DateModal = useMemo(() => {
      if (_.isNil(visible)) {
        return <></>;
      }
      return (
        <CalendarActionSheet
          visible={visible}
          onClose={closeModal}
          onChange={date => {
            onChangeDate(date);
          }}
          minDate={minDate}
          maxDate={maxDate}
          disabledDates={disabledDate}
          extraData={extraData}
        />
      );
    }, [
      visible,
      date,
      type,
      theme,
      format,
      minDate,
      maxDate,
      disabledDates,
      disabledDate,
      value,
      extraData,
    ]);

    return (
      <>
        {$input}
        {DateModal}
      </>
    );
  }),
);

const useGetStyle = (props: {
  inputStyle?: ViewStyle;
  calendarIconStyle?: ImageStyle;
  inputArrowIconStyle?: ImageStyle;
  calendarValueTextStyle?: TextStyle;
  calendarNoValueTextStyle?: TextStyle;
}) => {
  const {
    inputStyle,
    calendarIconStyle,
    inputArrowIconStyle,
    calendarValueTextStyle,
    calendarNoValueTextStyle,
  } = props;
  return useMemo(() => {
    return {
      modal: {
        paddingTop: 32,
        paddingBottom: 32,
        borderRadius: 16,
        backgroundColor: 'background-color-0',
        maxHeight: Dimensions.get('window').height * 0.8,
        maxWidth: Dimensions.get('window').width * 0.8,
      },
      input: {
        width: '100%',
        height: 48,
        borderRadius: 8,
        borderColor: 'fill-color-500',
        borderWidth: 1,
        backgroundColor: 'background-color-0',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        alignSelf: 'center',
        ...inputStyle,
      },
      dangerInput: {
        width: '100%',
        height: 48,
        borderRadius: 8,
        borderColor: 'danger-color-500',
        borderWidth: 0,
        backgroundColor: 'background-color-0',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        alignSelf: 'center',
      },
      title: {
        color: 'text-color-700',
      },
      dangerTitle: {
        color: 'danger-color-500',
      },
      novalueInputText: {
        color: Colors.TEXT_COLOR_500,
        ...calendarNoValueTextStyle,
      },
      valueInputText: {
        color: Colors.TEXT_COLOR_800,
        ...calendarValueTextStyle,
      },
      calendarIcon: {
        width: 20,
        height: 20,
        ...calendarIconStyle,
      },
      inputArrowIcon: {
        width: 20,
        height: 20,
        ...inputArrowIconStyle,
      },
    };
  }, [props]);
};
