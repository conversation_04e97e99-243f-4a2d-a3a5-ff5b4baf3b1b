/* eslint-disable react-native/no-inline-styles */
import { useThemeManager } from '@/managers';
import { useCustomStyleSheet } from '@/utils';
import React, { Ref, forwardRef, useImperativeHandle, useMemo, useRef, useState } from 'react';
import Text from '../../text';
import View from '../../view';
import Input, { RefType as BasicInputRefType, InputProps } from '../baseInput';

interface IPrpos extends InputProps {
  value: string;
  setValue: Function;
  disabled?: boolean;
  prefixKey?: string;
  prefixMargin?: string;
  limitReg?: (value: string | number) => boolean;
  type?: 'base' | 'line';
}

export type RefType = {
  emitErrorStatus: Function;
  blur: Function;
  focus: Function;
  clearErrorStatus: Function;
};

/**
 * Prefix Input
 * @property {string} value
 * @property {Function} setValue
 * @property {boolean} disabled
 * @property {string} prefixKey - Can be use a string. like a key(homeString.loanAmountUpTo)
 * @property {string} prefixMargin - Can be use a string, like `12 0 0 0`
 * @method limitReg - use a regular expression
 */
export default React.memo(
  forwardRef((_props: IPrpos, ref: Ref<RefType>): React.ReactElement => {
    const {
      value,
      setValue,
      disabled = false,
      prefixKey = '',
      prefixMargin = '0 0 0 0',
      placeholderKey = '',
      type = 'base',
      ...props
    } = _props;

    const inputRef = useRef<BasicInputRefType>(null);

    const {
      value: { applicationTheme },
    } = useThemeManager();
    const [uxStatus, setUxStatus] = useState<string>('basic');
    const styles = useCustomStyleSheet(useGetStyle());

    const onInputChange = (text: string) => {
      setValue(text);
    };

    useImperativeHandle(ref, () => ({
      emitErrorStatus(tips: string) {
        inputRef.current?.emitErrorStatus(tips);
      },
      clearErrorStatus() {
        inputRef.current?.clearErrorStatus();
      },
      blur() {
        inputRef.current?.blur();
      },
      focus() {
        inputRef.current?.focus();
      },
    }));

    const getUxStatusFromChildComp = (uxStatus: string) => {
      setUxStatus(uxStatus);
    };

    const $prefixTitle = useMemo(() => {
      let style = uxStatus === 'danger' ? styles.dangerTitle : styles.title;
      if (disabled) style = styles.disabledTitle;
      if (prefixKey) {
        return (
          <Text category="p1" bold="bold" style={style} margin="0 0 6 8" i18nKey={prefixKey} />
        );
      }

      return <></>;
    }, [prefixKey, uxStatus, disabled]);

    const $input = useMemo(() => {
      switch (type) {
        case 'base':
          return (
            <Input
              {...props}
              ref={inputRef}
              placeholderKey={placeholderKey}
              value={value}
              disabled={disabled}
              textStyle={{
                color: applicationTheme['text-color-800'],
              }}
              onChangeText={onInputChange}
            />
          );
        case 'line':
          return (
            <Input
              {...props}
              ref={inputRef}
              uxCallback={getUxStatusFromChildComp}
              placeholderKey={placeholderKey}
              value={value}
              disabled={disabled}
              textStyle={{
                color: applicationTheme['text-color-800'],
              }}
              style={{
                backgroundColor: 'background-color-0',
                borderBottomWidth: 1,
                borderWidth: 0,
                borderRadius: 0,
                borderBottomColor: 'line-color-200',
              }}
              dangerStyle={{
                backgroundColor: 'background-color-0',
                borderBottomWidth: 1,
                borderWidth: 0,
                borderRadius: 0,
                borderBottomColor: 'danger-color-500',
              }}
              onChangeText={onInputChange}
            />
          );
      }
    }, [type, value, disabled, applicationTheme, inputRef, onInputChange]);

    return (
      <>
        <View
          margin={prefixMargin}
          style={{
            width: '100%',
          }}>
          {$prefixTitle}
          {$input}
        </View>
      </>
    );
  }),
);

const useGetStyle = () => {
  return useMemo(() => {
    return {
      title: {
        color: 'text-color-800',
      },
      dangerTitle: {
        color: 'danger-color-500',
      },
      disabledTitle: {
        color: 'fill-color-500',
      },
    };
  }, []);
};
