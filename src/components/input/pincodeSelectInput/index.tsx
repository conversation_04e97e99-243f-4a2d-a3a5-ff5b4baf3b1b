/* eslint-disable react/jsx-no-undef */
/* eslint-disable react-native/no-inline-styles */
import { HitPointEnumsSpace } from '@/enums';
import { trackInputEventEnd, trackInputEventStart } from '@/trackEvent';
import { useCustomStyleSheet } from '@/utils';
import _ from 'lodash';
import React, {
  Ref,
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useState,
} from 'react';
import { Dimensions, FlatList, Keyboard, ScrollView, TouchableWithoutFeedback } from 'react-native';
import Image from '../../image';
import CommonModal from '../../modal/commonModal';
import Text from '../../text';
import View from '../../view';
import Input, { RefType } from '../baseInput';
import { pincodeData } from './pincodeData';

interface IPrpos {
  value: string;
  options: string[];
  setValue: Function;
  changeSelectCallback?: Function;
  searchCallback?: Function;
  disabled?: boolean;
  prefixKey?: string;
  prefixMargin?: string;
  placeholderKey?: string;
  onFocus?: () => void;
  onBlur?: () => void;
  /** 埋点事件对象 */
  pageKey?: HitPointEnumsSpace.EPageKey;
  eventKey?: HitPointEnumsSpace.EEventKey;
  type?: 'base' | 'line';
}

export default React.memo(
  forwardRef((props: IPrpos, ref: Ref<RefType>): React.ReactElement => {
    const {
      value,
      setValue,
      prefixKey = '',
      prefixMargin = '0 0 0 0',
      placeholderKey = '',
      options,
      changeSelectCallback,
      pageKey,
      eventKey,
      type = 'base',
      onFocus,
      onBlur,
      disabled,
    } = props;
    // @ts-ignore
    const [uxStatus, setUxStatus] = useState<string>('basic');
    const styles = useCustomStyleSheet(useGetStyle({}));
    const [visible, setVisible] = useState<boolean | null>(null);
    const [filterVal, setFilterVal] = useState<string>('');

    useImperativeHandle(ref, () => ({
      blur() {
        if (value) {
          setUxStatus('basic');
        }
      },
      focus(_pageKey: HitPointEnumsSpace.EPageKey, _eventKey: HitPointEnumsSpace.EEventKey) {
        setVisible(true);
        if (_pageKey && _eventKey) {
          trackInputEventStart({
            p: _pageKey,
            e: _eventKey,
          });
        }
      },
      emitErrorStatus(tips: string) {
        setUxStatus('danger');
      },
      clearErrorStatus() {
        setUxStatus('basic');
      },
      isFocused() {
        return visible;
      },
    }));

    useEffect(() => {
      if (_.isNil(visible)) {
        return;
      }
      if (visible) {
        typeof onFocus === 'function' && onFocus();
      } else {
        typeof onBlur === 'function' && onBlur();
      }
    }, [visible]);

    const onChangeText = useCallback((text: string) => {
      setValue(text);
    }, []);

    const onClickItem = useCallback((zipcode: string) => {
      setUxStatus('basic');
      onChangeText(zipcode);
      changeSelectCallback && changeSelectCallback(zipcode);
      closeModal(zipcode);
    }, []);

    const openModal = useCallback(() => {
      setFilterVal('');
      setVisible(true);
      if (pageKey && eventKey) {
        trackInputEventStart({
          p: pageKey,
          e: eventKey,
        });
      }
    }, []);

    const closeModal = useCallback((zipcode?: string) => {
      if (pageKey && eventKey) {
        const c = typeof zipcode == 'string' ? zipcode : '';
        trackInputEventEnd(
          {
            p: pageKey,
            e: eventKey,
          },
          c,
        );
      }
      setVisible(false);
    }, []);

    const $prefixTitle = useMemo(() => {
      let style = uxStatus === 'danger' ? styles.dangerTitle : styles.title;
      if (disabled) style = styles.disabledTitle;
      if (prefixKey) {
        return (
          <Text style={style} category="p1" bold="bold" margin="0 0 6 8" i18nKey={prefixKey} />
        );
      }

      return <></>;
    }, [prefixKey, uxStatus, disabled]);

    const $defineSelect = useMemo(() => {
      switch (type) {
        case 'base':
          return (
            <>
              <View
                margin={prefixMargin}
                style={{
                  width: '100%',
                }}>
                {$prefixTitle}
                <TouchableWithoutFeedback disabled={disabled} onPress={openModal}>
                  <View
                    margin={prefixMargin}
                    padding="10 12 10 12"
                    style={{
                      width: '100%',
                      borderRadius: 16,
                      borderColor: 'background-color-100',
                      backgroundColor: 'background-color-100',
                      flexDirection: 'row',
                      alignItems: 'center',
                      justifyContent: 'space-between',
                    }}>
                    {value && (
                      <Text
                        category="p1"
                        isCenter={false}
                        textContent={value}
                        style={{
                          color: 'text-color-800',
                        }}
                      />
                    )}
                    {!value && (
                      <Text
                        category="p1"
                        isCenter={false}
                        i18nKey={placeholderKey}
                        style={{
                          color: 'text-color-500',
                        }}
                      />
                    )}
                    <Image name="_suffix" />
                  </View>
                </TouchableWithoutFeedback>
              </View>
            </>
          );
        case 'line':
          let style = uxStatus === 'danger' ? [styles.dangerInput] : [styles.input];
          return (
            <>
              <View
                margin={prefixMargin}
                style={{
                  width: '100%',
                }}>
                {$prefixTitle}
                <TouchableWithoutFeedback disabled={disabled} onPress={openModal}>
                  <View padding="10 12 10 12" style={style}>
                    {value && (
                      <Text
                        category="p1"
                        isCenter={false}
                        textContent={value}
                        style={{
                          color: 'text-color-800',
                        }}
                      />
                    )}
                    {!value && (
                      <Text
                        category="p1"
                        isCenter={false}
                        i18nKey={placeholderKey}
                        style={{
                          color: 'text-color-500',
                        }}
                      />
                    )}
                    <Image name="_suffix" />
                  </View>
                </TouchableWithoutFeedback>
              </View>
            </>
          );
      }
    }, [type, prefixMargin, value, placeholderKey, openModal, prefixKey, uxStatus]);

    const DATA =
      filterVal.length > 0 ? pincodeData.filter(item => item?.includes(filterVal)) : options;

    const PincodeModal = useMemo(() => {
      if (_.isNil(visible)) {
        return <></>;
      }

      return (
        <CommonModal visible={visible} onBackdropPress={closeModal}>
          <View style={[styles.modal]}>
            <Text
              margin="0 16 0 16"
              padding="19 0 19 0"
              isCenter={true}
              i18nKey={prefixKey}
              style={{
                color: 'text-color-600',
              }}
            />
            <View>
              <ScrollView keyboardShouldPersistTaps="always">
                <Input
                  value={filterVal}
                  onChangeText={setFilterVal}
                  margin={'0 16 16 16'}
                  style={{
                    backgroundColor: 'background-color-0',
                    borderBottomWidth: 1,
                    borderWidth: 0,
                    borderRadius: 0,
                    borderBottomColor: 'line-color-200',
                  }}
                />
              </ScrollView>
            </View>
            <FlatList
              fadingEdgeLength={10}
              data={[...new Set(DATA)]}
              keyboardShouldPersistTaps="handled"
              renderItem={({ item }) => (
                <TouchableWithoutFeedback
                  key={item}
                  onPress={() => {
                    onClickItem(item);
                    Keyboard.dismiss();
                  }}>
                  <Text
                    margin="0 16 0 16"
                    padding="19 0 19 0"
                    style={{
                      borderBottomWidth: 1,
                      borderColor: 'line-color-100',
                    }}
                    isCenter={true}
                    textContent={item}
                  />
                </TouchableWithoutFeedback>
              )}
              keyExtractor={item => item}
            />
          </View>
        </CommonModal>
      );
    }, [visible, filterVal]);

    return (
      <>
        {$defineSelect}
        {PincodeModal}
      </>
    );
  }),
);

const useGetStyle = (props: {}) => {
  return useMemo(() => {
    return {
      modal: {
        backgroundColor: 'background-color-0',
        borderRadius: 16,
        maxHeight: Dimensions.get('window').height * 0.8,
        width: Dimensions.get('window').width * 0.8,
      },
      input: {
        width: '100%',
        height: 48,
        borderBottomColor: 'line-color-200',
        borderBottomWidth: 1,
        backgroundColor: 'background-color-0',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        alignSelf: 'center',
      },
      dangerInput: {
        width: '100%',
        height: 48,
        borderBottomColor: 'danger-color-500',
        borderBottomWidth: 1,
        backgroundColor: 'background-color-0',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        alignSelf: 'center',
      },
      title: {
        color: 'text-color-800',
      },
      dangerTitle: {
        color: 'danger-color-500',
      },
      disabledTitle: {
        color: 'line-color-200',
      },
    };
  }, [props]);
};
