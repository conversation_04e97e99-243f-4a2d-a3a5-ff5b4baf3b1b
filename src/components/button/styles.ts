import { TextStyle, ViewStyle } from 'react-native';

export type TStatus =
  | 'basic'
  | 'primary'
  | 'secondary'
  | 'tertiary'
  | 'success'
  | 'info'
  | 'warning'
  | 'danger'
  | 'control';

export type TAppearance = 'filled' | 'outline' | 'ghost';

export type TSize = 'tiny' | 'small' | 'medium' | 'large' | 'giant' | 'custom';

export const buttonCustomSizeStyles: Record<TSize, ViewStyle> = {
  tiny: {
    height: 22,
    minHeight: 22,
    minWidth: 44,
    width: '100%',
  },
  small: {},
  medium: {
    height: 44,
    minHeight: 44,
    minWidth: 100,
    width: '100%',
  },
  large: {},
  giant: {},
  custom: {},
};

export const textCustomSizeStyles: Record<TSize, TextStyle> = {
  tiny: {
    fontSize: 10,
    lineHeight: 20,
    fontWeight: 'normal',
  },
  small: {},
  medium: {
    fontSize: 16,
    lineHeight: 20,
    fontWeight: 'normal',
  },
  large: {},
  giant: {},
  custom: {},
};

export const buttonCustomStatusStyles: Record<TAppearance, Record<TStatus, ViewStyle>> = {
  filled: {
    basic: {
      backgroundColor: 'fill-color-400',
    },
    primary: {
      backgroundColor: 'primary-color-500',
    },
    secondary: {
      backgroundColor: 'secondary-color-500',
    },
    tertiary: {
      backgroundColor: 'tertiary-color-500',
    },
    success: {
      backgroundColor: 'success-color-500',
    },
    info: {
      backgroundColor: 'info-color-500',
    },
    warning: {
      backgroundColor: 'warn-color-500',
    },
    danger: {
      backgroundColor: 'danger-color-500',
    },
    control: {
      backgroundColor: 'fill-color-400',
    },
  },
  outline: {
    basic: {
      borderColor: 'fill-color-400',
      borderWidth: 1,
    },
    primary: {
      borderColor: 'primary-color-500',
      backgroundColor: 'background-color-0',
      borderWidth: 1,
    },
    secondary: {
      borderColor: 'secondary-color-500',
      backgroundColor: 'background-color-0',
      borderWidth: 1,
    },
    tertiary: {
      borderColor: 'tertiary-color-500',
      backgroundColor: 'background-color-0',
      borderWidth: 1,
    },
    success: {
      borderColor: 'success-color-500',
      backgroundColor: 'background-color-0',
      borderWidth: 1,
    },
    info: {
      borderColor: 'info-color-500',
      backgroundColor: 'background-color-0',
      borderWidth: 1,
    },
    warning: {
      borderColor: 'warn-color-500',
      backgroundColor: 'background-color-0',
      borderWidth: 1,
    },
    danger: {
      borderColor: 'danger-color-500',
      backgroundColor: 'background-color-0',
      borderWidth: 1,
    },
    control: {
      borderColor: 'background-color-0',
      borderWidth: 1,
    },
  },
  ghost: {
    basic: {},
    primary: {},
    secondary: {},
    tertiary: {},
    success: {},
    info: {},
    warning: {},
    danger: {},
    control: {},
  },
};

export const activityCustomBackgroundColor: Record<TStatus, Record<TAppearance, string>> = {
  basic: {
    filled: 'fill-color-800',
    outline: 'fill-color-500',
    ghost: 'fill-color-300',
  },
  primary: {
    filled: 'primary-color-600',
    outline: 'primary-color-200',
    ghost: 'fill-color-300',
  },
  secondary: {
    filled: 'secondary-color-100',
    outline: 'fill-color-200',
    ghost: 'fill-color-300',
  },
  tertiary: {
    filled: 'tertiary-color-100',
    outline: 'fill-color-200',
    ghost: 'fill-color-300',
  },
  success: {
    filled: 'success-color-100',
    outline: 'fill-color-200',
    ghost: 'fill-color-300',
  },
  info: {
    filled: 'info-color-100',
    outline: 'fill-color-200',
    ghost: 'fill-color-300',
  },
  warning: {
    filled: 'secondary-color-600',
    outline: 'fill-color-200',
    ghost: 'fill-color-300',
  },
  danger: {
    filled: 'danger-color-100',
    outline: 'fill-color-200',
    ghost: 'fill-color-300',
  },
  control: {
    filled: 'fill-color-500',
    outline: 'fill-color-200',
    ghost: 'fill-color-300',
  },
};

export const textCustomColorStyles: Record<TAppearance, Record<TStatus, TextStyle>> = {
  filled: {
    basic: {
      color: 'text-color-800',
    },
    primary: {
      color: 'text-color-0',
    },
    secondary: {
      color: 'text-color-0',
    },
    tertiary: {
      color: 'text-color-0',
    },
    success: {
      color: 'text-color-0',
    },
    info: {
      color: 'text-color-0',
    },
    warning: {
      color: 'text-color-0',
    },
    danger: {
      color: 'text-color-0',
    },
    control: {
      color: 'text-color-0',
    },
  },
  outline: {
    basic: {
      color: 'text-color-800',
    },
    primary: {
      color: 'primary-color-500',
    },
    secondary: {
      color: 'secondary-color-500',
    },
    tertiary: {
      color: 'tertiary-color-500',
    },
    success: {
      color: 'success-color-500',
    },
    info: {
      color: 'info-color-500',
    },
    warning: {
      color: 'warn-color-500',
    },
    danger: {
      color: 'danger-color-500',
    },
    control: {
      color: 'text-color-0',
    },
  },
  ghost: {
    basic: {
      color: 'text-color-800',
    },
    primary: {
      color: 'primary-color-500',
    },
    secondary: {
      color: 'secondary-color-500',
    },
    tertiary: {
      color: 'tertiary-color-500',
    },
    success: {
      color: 'success-color-500',
    },
    info: {
      color: 'info-color-500',
    },
    warning: {
      color: 'warn-color-500',
    },
    danger: {
      color: 'danger-color-500',
    },
    control: {
      color: 'text-color-0',
    },
  },
};

// const buttonCustomDisabledStyles: Record<TAppearance, ViewStyle> = {
// 	filled: {
// 		backgroundColor: 'fill-color-500'
// 	},
// 	outline: {
// 		borderColor: "fill-color-500",
// 		backgroundColor: "fill-color-500"
// 	},
// 	ghost: {
// 		borderColor: "fill-color-500",
// 		backgroundColor: "fill-color-500"
// 	}
// }

// const textCustomDisabledStyles: Record<TAppearance, TextStyle> = {
// 	filled: {
// 		color: "text-color-50"
// 	},
// 	outline: {
// 		color: "text-color-50"
// 	},
// 	ghost: {
// 		color: "text-color-50"
// 	}
// }
