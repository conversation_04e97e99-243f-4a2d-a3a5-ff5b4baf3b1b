import React, { useCallback, useMemo } from 'react';

import { useNameSpace } from '@/i18n';
import { useThemeManager } from '@/managers';
import { Toast } from '@/nativeComponents';
import { BasicStyleType, generateStyle, useCustomStyleSheet } from '@/utils';
import {
  GestureResponderEvent,
  TouchableHighlight,
  TouchableHighlightProps,
  TouchableNativeFeedback,
  TouchableNativeFeedbackProps,
  TouchableOpacity,
  TouchableOpacityProps,
  TouchableWithoutFeedback,
  TouchableWithoutFeedbackProps,
  View,
  ViewStyle,
} from 'react-native';
import Text from '../text';
import {
  TAppearance,
  TSize,
  TStatus,
  activityCustomBackgroundColor,
  buttonCustomSizeStyles,
  buttonCustomStatusStyles,
  textCustomColorStyles,
  textCustomSizeStyles,
} from './styles';
import { TCategory } from '../text/style';
// todo 使用 UI-Kitten组件有样式问题
export interface ButtonProps
  extends TouchableHighlightProps,
    TouchableNativeFeedbackProps,
    TouchableWithoutFeedbackProps,
    TouchableOpacityProps,
    BasicStyleType<ViewStyle> {
  /** 按钮类型 */
  type?: 'opacity' | 'nativeFeedback' | 'withoutFeedback' | 'highlight';
  /** 禁用状态 */
  disabled?: boolean;
  /** 禁用按钮的提示 */
  disabledTipText?: string;
  /** 禁用按钮的提示文案 i18nKey 配置 */
  disabledTipTextI18nKey?: string;
  /** 按钮禁用状态下 toast 提示时间 长短 */
  disabledTipShowDuration?: 'LONG' | 'SHORT';
  /** 禁用状态下的按钮点击事件回掉 */
  onDisabledCallback?: () => void;
  /** 按钮风格 */
  appearance?: TAppearance;
  accessoryLeft?: React.ReactNode;
  accessoryRight?: React.ReactNode;
  status?: TStatus;
  size?: TSize;
  activeColor?: string;
  children?: React.ReactNode;
  textI18nKey?: string;
  text?: string;
  textCategory?: TCategory;
}
/**
 * Basic button
 *
 * @property {string} padding - Can be use a string, like `12 0 0 0`
 * @property {string} margin - Can be use a string, like `12 0 0 0`
 * @property {number} width - Can be use a number
 * @property {number} height - Can be use a number
 * @property {object} style - can be use a object, like { popsition: 'absolute', right: 0, bottom: 0 }
 *
 * @example
          <Button margin="20 0 0 0" status="primary" onPress={handleLoan}>
            <Text i18nKey={'btnString.quickGetLoan'} />
          </Button>
 */
export default React.memo((_props: ButtonProps): React.ReactElement => {
  const {
    children,
    padding = '12 24 12 24',
    margin,
    width,
    height,
    style,
    type = 'highlight',
    status = 'primary',
    size = 'medium',
    appearance = 'filled',
    activeColor = '',
    textI18nKey,
    text,
    accessoryLeft,
    accessoryRight,
    disabled = false,
    disabledTipText = '',
    disabledTipTextI18nKey = '',
    disabledTipShowDuration = 'SHORT',
    textCategory = 'p1',
    onDisabledCallback,
    onPress,
    ...props
  } = _props;

  const styles = useCustomStyleSheet(
    useGetStyle({
      padding,
      margin,
      width,
      height,
      style,
      status,
      size,
      appearance,
    }),
  );
  const applicationTheme = useThemeManager().value.applicationTheme;

  const t = useNameSpace().t;

  const buttonViewStyle = useMemo(() => {
    if (disabled) {
      return [styles.view, styles.disabledView];
    } else {
      return styles.view;
    }
  }, [styles.view, styles.disabledView, disabled]);

  const buttonTextStyle = useMemo(() => {
    if (disabled) {
      return [styles.text, styles.disabledText];
    } else {
      return styles.text;
    }
  }, [styles.text, styles.disabledText, disabled, size]);

  const activityBackgroundColor = useMemo(() => {
    if (disabled) return applicationTheme['fill-color-500'];
    return activeColor
      ? applicationTheme[activeColor]
      : applicationTheme[activityCustomBackgroundColor[status][appearance]];
  }, [appearance, status, applicationTheme, activeColor, disabled]);

  const handleButtonPress = useCallback(
    (event: GestureResponderEvent) => {
      if (disabled) {
        const disabledText = disabledTipText || t(disabledTipTextI18nKey);
        disabledText && Toast(disabledText, disabledTipShowDuration);
        onDisabledCallback && onDisabledCallback();
      } else {
        onPress && onPress(event);
      }
    },
    [
      disabled,
      disabledTipText,
      onDisabledCallback,
      disabledTipTextI18nKey,
      disabledTipShowDuration,
      onPress,
      t,
    ],
  );

  const $children = useMemo(
    () => (
      <>
        {accessoryLeft && accessoryLeft}
        {/* 如果有文本存在就渲染文本 */}
        {(text || textI18nKey) && (
          <Text
            category={textCategory}
            textContent={text}
            i18nKey={textI18nKey}
            style={buttonTextStyle}
          />
        )}
        {children && children}
        {accessoryRight && accessoryLeft}
      </>
    ),
    [
      accessoryLeft,
      text,
      buttonTextStyle,
      appearance,
      status,
      textI18nKey,
      textCategory,
      children,
      accessoryRight,
    ],
  );

  switch (type) {
    case 'highlight':
      return (
        <TouchableHighlight
          style={buttonViewStyle}
          underlayColor={activityBackgroundColor}
          onPress={handleButtonPress}
          {...props}>
          {$children}
        </TouchableHighlight>
      );
    case 'opacity':
      return (
        <TouchableOpacity
          activeOpacity={0.6}
          onPress={handleButtonPress}
          style={buttonViewStyle}
          {...props}>
          {$children}
        </TouchableOpacity>
      );
    case 'withoutFeedback':
      return (
        <TouchableWithoutFeedback onPress={handleButtonPress} {...props}>
          <View style={buttonViewStyle}>{$children}</View>
        </TouchableWithoutFeedback>
      );
    case 'nativeFeedback':
    default:
      return (
        <TouchableNativeFeedback
          {...props}
          background={TouchableNativeFeedback.Ripple(activityBackgroundColor, false)}>
          <View style={buttonViewStyle}>{$children}</View>
        </TouchableNativeFeedback>
      );
  }
});

const useGetStyle = (props: Partial<ButtonProps>) => {
  return useMemo(() => {
    const { appearance = 'filled', status = 'primary', size = 'medium', ...reset } = props;
    /** 对于size的处理 */
    return {
      view: {
        ...buttonCustomSizeStyles[size],
        ...buttonCustomStatusStyles[appearance][status],
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        overflow: 'hidden',
        borderRadius: 8,
        ...generateStyle(reset),
      },
      text: {
        ...textCustomSizeStyles[size],
        ...textCustomColorStyles[appearance][status],
      },
      disabledView: {
        borderColor: 'fill-color-400',
        backgroundColor: 'fill-color-500',
      },
      disabledText: {
        color: 'fill-color-0',
      },
    };
  }, [props]);
};
