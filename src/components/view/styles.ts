import { ViewStyle } from 'react-native';

export const enum Layouts {
  flexRowStart = 'flexRowStart',
  flexRowStartCenter = 'flexRowStartCenter',
  flexRowStartCenterWarp = 'flexRowStartCenterWarp',
  flexRowCenterCenter = 'flexRowCenterCenter',
  flexRowBetweenCenter = 'flexRowBetweenCenter',
  flexRowBetweenTop = 'flexRowBetweenTop',
  flexRowBetweenCenterWrap = 'flexRowBetweenCenterWrap',
  flexRowAroundCenter = 'flexRowAroundCenter',
  flexRowAroundCenterWarp = 'flexRowAroundCenterWarp',
  flexRowEvenlyCenter = 'flexRowEvenlyCenter',
  flexColumnStart = 'flexColumnStart',
  flexColumnStartCenter = 'flexColumnStartCenter',
  flexRowColumnCenterWarp = 'flexRowColumnCenterWarp',
  flexColumnCenterCenter = 'flexColumnCenterCenter',
  flexColumnBetweenCenter = 'flexColumnBetweenCenter',
  flexColumnBetweenStart = 'flexColumnBetweenStart',
  flexColumnAroundCenter = 'flexColumnAroundCenter',
  flexColumnEvenlyCenter = 'flexColumnEvenlyCenter',
}

// 生成一个 TLayoutStrategy 类型的字符串
export type TLayoutStrategyString = `${Layouts}`;

export type TLayoutStrategy = Layouts | TLayoutStrategyString;

export const customViewStrategyStyles: Record<TLayoutStrategy, ViewStyle> = {
  flexRowStart: {
    position: 'relative',
    flexDirection: 'row',
    // justifyContent: "flex-start",
    // alignItems: 'center',
    // flexWrap: 'nowrap'
  },
  flexRowStartCenter: {
    position: 'relative',
    flexDirection: 'row',
    // justifyContent: "flex-start",
    alignItems: 'center',
    // flexWrap: 'nowrap'
  },
  flexRowStartCenterWarp: {
    position: 'relative',
    flexDirection: 'row',
    // justifyContent: "flex-start",
    alignItems: 'center',
    flexWrap: 'wrap',
  },
  flexRowCenterCenter: {
    position: 'relative',
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  flexRowBetweenTop: {
    position: 'relative',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  flexRowBetweenCenter: {
    position: 'relative',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  flexRowBetweenCenterWrap: {
    position: 'relative',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    flexWrap: 'wrap',
  },
  flexRowAroundCenter: {
    position: 'relative',
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
  },
  flexRowAroundCenterWarp: {
    position: 'relative',
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    flexWrap: 'wrap',
  },
  flexRowEvenlyCenter: {
    position: 'relative',
    flexDirection: 'row',
    justifyContent: 'space-evenly',
    alignItems: 'center',
  },
  flexColumnStart: {
    position: 'relative',
  },
  flexColumnStartCenter: {
    position: 'relative',
    // justifyContent: "flex-start",
    alignItems: 'center',
    // flexWrap: 'nowrap'
  },
  flexRowColumnCenterWarp: {
    position: 'relative',
    justifyContent: 'flex-start',
    flexWrap: 'wrap',
    alignItems: 'center',
  },
  flexColumnCenterCenter: {
    position: 'relative',
    justifyContent: 'center',
    alignItems: 'center',
  },
  flexColumnBetweenStart: {
    position: 'relative',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  flexColumnBetweenCenter: {
    position: 'relative',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  flexColumnAroundCenter: {
    position: 'relative',
    justifyContent: 'space-around',
    alignItems: 'center',
  },
  flexColumnEvenlyCenter: {
    position: 'relative',
    justifyContent: 'space-evenly',
    alignItems: 'center',
  },
};

export type TCardType = 'lineType' | 'noneType' | 'baseType';

export const customViewCardTypeStyles: Record<TCardType, ViewStyle> = {
  lineType: {
    borderRadius: 8,
    backgroundColor: 'background-color-0',
    borderWidth: 1,
    borderColor: 'line-color-200',
  },
  noneType: {},
  baseType: {
    borderRadius: 8,
    backgroundColor: 'background-color-0',
  },
};
