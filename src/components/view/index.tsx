import { BasicStyleType, generateStyle, useCustomStyleSheet } from '@/utils';
import React, { useMemo } from 'react';
import { View, ViewStyle, ViewProps as _ViewProps } from 'react-native';
import {
  TLayoutStrategy,
  customViewStrategyStyles,
  TCardType,
  customViewCardTypeStyles,
} from './styles';

export type ViewProps = ExtendProps & _ViewProps;
type ExtendProps = {
  layoutStrategy?: TLayoutStrategy;
  cardType?: TCardType;
} & BasicStyleType<ViewStyle>;
/**
 * Basic view
 *
 * @property {string} layoutStrategy
 * @property {string} padding - Can be use a string, like `12 0 0 0`
 * @property {string} margin - Can be use a string, like `12 0 0 0`
 * @property {number} width - Can be use a number
 * @property {number} height - Can be use a number
 * @property {object} style - can be use a object, like { popsition: 'absolute', right: 0, bottom: 0 }
 *
 */
export default React.memo((_props: ViewProps): React.ReactElement => {
  const {
    layoutStrategy,
    cardType = 'noneType',
    children,
    padding,
    margin,
    width,
    height,
    style,
    ...props
  } = _props;
  const styles = useCustomStyleSheet(
    useGetStyle({
      layoutStrategy,
      cardType,
      padding,
      margin,
      width,
      height,
      style,
    }),
  );
  return (
    <View {...props} style={[styles.view]}>
      {children}
    </View>
  );
});

const useGetStyle = (props: Partial<ExtendProps>) => {
  return useMemo(() => {
    const { layoutStrategy = 'flexColumnStart', cardType = 'baseType', ...rest } = props;
    return {
      view: {
        ...customViewStrategyStyles[layoutStrategy],
        ...generateStyle(rest),
        ...customViewCardTypeStyles[cardType],
      },
    };
  }, [props]);
};
