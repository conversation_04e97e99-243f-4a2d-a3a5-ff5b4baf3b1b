import { BasicStyleType, generateStyle, useCustomStyleSheet } from '@/utils';
import React, { useMemo } from 'react';
import { TouchableWithoutFeedback, ViewStyle } from 'react-native';
import { Images } from '../../config/assetIcons';
import Image from '../image';
import Text from '../text';
import View from '../view';

export type CellProps = BasicStyleType<ViewStyle> & {
  /** click cell callback */
  onClick: () => void;
  /** left icons */
  leftIconKey?: keyof typeof Images;
  /** right icons */
  rightIconKey?: keyof typeof Images;
  /** title key */
  titleKey?: string;
  /** content key */
  contentKey?: string;
  /** type */
  type?: 'cell' | 'grid';
  /** right content */
  rigthAmount?: string;
};
/**
 * @description Cell component
 */
export default React.memo((_props: CellProps): React.ReactElement => {
  const {
    onClick = () => {},
    leftIconKey,
    rightIconKey,
    titleKey = '',
    contentKey = '',
    type = 'cell',
    padding,
    margin,
    width,
    height,
    style,
    rigthAmount,
  } = _props;

  const styles = useCustomStyleSheet(
    useGetStyle({
      padding,
      margin,
      width,
      height,
      style,
    }),
  );

  const $leftView = useMemo(() => {
    return (
      <View layoutStrategy="flexRowStartCenterWarp">
        {leftIconKey && <Image name={leftIconKey} margin="0 8 0 0" />}
        {titleKey && (
          <Text
            category="p2"
            i18nKey={titleKey}
            style={{
              color: 'text-color-800',
            }}
          />
        )}
      </View>
    );
  }, [leftIconKey, titleKey]);

  const $rightView = useMemo(() => {
    return (
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          flexWrap: 'wrap',
        }}>
        {contentKey && (
          <Text
            category="c1"
            i18nKey={contentKey}
            style={{
              width: 125,
              color: 'text-color-700',
            }}
          />
        )}
        {rigthAmount && (
          <Text
            category="p2"
            textContent={rigthAmount.toFormatFinance(false)}
            style={{
              color: 'danger-color-500',
            }}
          />
        )}
        {rightIconKey && <Image name={rightIconKey} margin="0 8 0 0" />}
        <Image name="_rightIcon" />
      </View>
    );
  }, [contentKey, rightIconKey, rigthAmount]);

  if (type === 'grid') {
    return (
      <TouchableWithoutFeedback onPress={onClick}>
        <View style={[styles.grid]} layoutStrategy="flexRowColumnCenterWarp">
          {leftIconKey && <Image name={leftIconKey} margin="0 0 0 0" />}
          {titleKey && (
            <Text
              margin="8 0 0 0"
              category="c1"
              bold="bold"
              isCenter={true}
              i18nKey={titleKey}
              style={{
                width: 100,
                color: 'text-color-800',
              }}
            />
          )}
        </View>
      </TouchableWithoutFeedback>
    );
  }

  return (
    <TouchableWithoutFeedback onPress={onClick}>
      <View style={[styles.cell]} layoutStrategy="flexRowBetweenCenterWrap">
        {$leftView}
        {$rightView}
      </View>
    </TouchableWithoutFeedback>
  );
});

const useGetStyle = (props: Partial<CellProps>) => {
  const { ...rest } = props;
  return useMemo(() => {
    return {
      cell: {
        ...generateStyle(rest),
      },
      grid: {
        ...generateStyle(rest),
      },
    };
  }, [props]);
};
