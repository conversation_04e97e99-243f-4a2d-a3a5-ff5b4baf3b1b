/* eslint-disable react-native/no-inline-styles */
import { BaseConfig } from '@/baseConfig';
import { HitPointEnumsSpace } from '@/enums';
import { useVipFuncSwitch } from '@/hooks';
import { UserInfoManager } from '@/managers';
import { trackCommonEvent } from '@/trackEvent';
import { nav } from '@/utils';
import React, { useEffect, useMemo, useRef } from 'react';
import {
  BackHandler,
  NativeEventSubscription,
  TouchableOpacity,
  TouchableWithoutFeedback,
} from 'react-native';
import { developerMode } from '../../utils/developerMode';
import BusinessUI from '../business/_export_';
import Image from '../image';
import Text from '../text';
import View from '../view';
import HotLineIconView from './components/HotLineView';
import MessageView from './components/MessageView';
const { VipIcon } = BusinessUI;

const LogoIcon = (props: { name: any }): React.ReactElement => {
  const clickCountRef = useRef<number>(0);
  const onPress = useMemo(
    () => () => {
      clickCountRef.current = clickCountRef.current + 1;
      const openLimit = BaseConfig.isProductionRelease ? 50 : 5;
      if (clickCountRef.current >= openLimit && clickCountRef.current % 5 === 0) {
        if (developerMode.isOpen) {
          developerMode.turnOff();
        } else {
          developerMode.turnOn();
        }
      }
    },
    [],
  );

  return (
    <TouchableWithoutFeedback onPress={onPress}>
      <Image name={props.name} />
    </TouchableWithoutFeedback>
  );
};

const BackIcon = (): React.ReactElement => <Image name="_backIcon" />;

const WhiteBackIcon = (): React.ReactElement => <Image name="_whiteBackIcon" />;

const LeftActions = ({
  isShowVip = false,
  pageKey,
}: {
  isShowVip: boolean;
  pageKey?: HitPointEnumsSpace.EPageKey;
}): React.ReactElement => {
  const isVipFuncSwitch = useVipFuncSwitch();
  if (isVipFuncSwitch && isShowVip)
    return (
      <VipIcon
        onPress={() => {
          UserInfoManager.getVipConfigAndNavigate({});
          if (pageKey) {
            trackCommonEvent({ p: pageKey, e: HitPointEnumsSpace.EEventKey.BTN_HEAD_CLICK }, '1');
          }
        }}
        theme="normal"
      />
    );
  return <LogoIcon name="_logoTitle" />;
};

const LeftGreyActions = ({
  isShowVip = false,
  pageKey,
}: {
  isShowVip: boolean;
  pageKey?: HitPointEnumsSpace.EPageKey;
}): React.ReactElement => {
  const isVipFuncSwitch = useVipFuncSwitch();
  if (isVipFuncSwitch && isShowVip)
    return (
      <VipIcon
        onPress={() => {
          UserInfoManager.getVipConfigAndNavigate({});
          if (pageKey) {
            trackCommonEvent({ p: pageKey, e: HitPointEnumsSpace.EEventKey.BTN_HEAD_CLICK }, '1');
          }
        }}
        theme="primary"
      />
    );
  return <LogoIcon name="_logoGreyTitle" />;
};

const WhiteBackAction = (props: { goBack?: () => void | undefined }): React.ReactElement => {
  const { goBack } = props;
  return (
    <TouchableOpacity
      onPress={() => {
        if (goBack) {
          goBack();
        } else {
          nav.navigationGoBack();
        }
      }}>
      <WhiteBackIcon />
    </TouchableOpacity>
  );
};

const BackAction = (props: { goBack?: () => void | undefined }): React.ReactElement => {
  const { goBack } = props;
  return (
    <TouchableOpacity
      onPress={() => {
        if (goBack) {
          goBack();
        } else {
          nav.navigationGoBack();
        }
      }}>
      <BackIcon />
    </TouchableOpacity>
  );
};

const White = (): React.ReactElement => {
  return <></>;
};

const Title = (props: { titleKey?: string; titleContent?: string; type: 'basic' | 'primary' }) => {
  const { titleKey = '', titleContent = '', type } = props;
  const color = useMemo(() => {
    switch (type) {
      case 'basic':
        return 'text-color-800';
      case 'primary':
        return 'text-color-0';
      default:
        return 'text-color-800';
    }
  }, [type]);

  if (titleKey) {
    return (
      <Text
        category="h3"
        bold="bold"
        i18nKey={titleKey}
        style={{
          color,
        }}
      />
    );
  }

  if (titleContent) {
    return (
      <Text
        category="h3"
        bold="bold"
        textContent={titleContent}
        style={{
          color,
        }}
      />
    );
  }

  return <></>;
};

export type TopNavigationProps = {
  isBack?: boolean;
  titleKey?: string;
  titleContent?: string;
  showRightAction?: boolean;
  showLogoAction?: boolean;
  goBack?: () => void;
  marginTop?: string;
  type?: 'basic' | 'primary';
  title?: string;
  subtitle?: string;
  accessoryLeft?: () => React.ReactElement;
  accessoryRight?: () => React.ReactElement;
  showMessage?: boolean;
  alignment?: 'start' | 'center';
  selfBgKey?: string;
  customerModalType?: '' | 'reLoanOtp';
  bottomLine?: boolean;
  isShowVip?: boolean;
  pageKey?: HitPointEnumsSpace.EPageKey;
};

/**
 * Top Navigation
 *
 * @property {boolean} isBack
 * @property {string} titleKey
 * @property {boolean} showRightAction 是否展示右边的icon, 联系客服icon
 * @property {boolean} showLogoAction 是否展示logo, 用在首页
 * @property {function} goBack 定制返回方法
 * @property {string} type 背景和字体展示方式
 * @property {string} isSelfBg 定制背景
 */
export default React.memo((props: TopNavigationProps): React.ReactElement => {
  const {
    titleKey = '',
    titleContent = '',
    isBack = true,
    alignment = 'center',
    showRightAction = true,
    showLogoAction = false,
    showMessage = false,
    goBack,
    marginTop = 0,
    type = 'basic',
    selfBgKey = '',
    bottomLine = true,
    isShowVip = false,
    pageKey,
  } = props;

  useEffect(() => {
    let backSubscription: NativeEventSubscription;
    if (!!goBack) {
      backSubscription = BackHandler.addEventListener('hardwareBackPress', () => {
        goBack?.();
        return true;
      });
    }
    return () => {
      !!backSubscription && backSubscription.remove();
    };
  }, []);

  const $message = useMemo(() => {
    if (showMessage) {
      switch (type) {
        case 'primary':
          return <MessageView />;
        case 'basic':
        default:
          return <MessageView imageName="_messageBlack" />;
      }
    } else {
      return null;
    }
  }, [type]);

  const $accessoryRight = useMemo(() => {
    if (showRightAction) {
      switch (type) {
        case 'basic':
          return <HotLineIconView />;
        case 'primary':
          return <HotLineIconView imageName={'_hotLineWhite'} />;

        default:
          return <HotLineIconView />;
      }
    } else {
      return null;
    }
  }, [showRightAction, type]);

  const accessoryLeft = useMemo(() => {
    if (isBack) {
      if (showLogoAction) {
        switch (type) {
          case 'primary':
            return () => <LeftActions pageKey={pageKey} isShowVip={isShowVip} />;
          default:
            return () => <LeftGreyActions pageKey={pageKey} isShowVip={isShowVip} />;
        }
      } else {
        switch (type) {
          case 'basic':
            return () => <BackAction goBack={goBack} />;
          case 'primary':
            return () => <WhiteBackAction goBack={goBack} />;
          default:
            return () => <BackAction goBack={goBack} />;
        }
      }
    } else {
      return White;
    }
  }, [isBack, showLogoAction, isShowVip, pageKey, type]);

  const headerStyle = useMemo(() => {
    let bottomLineStyle = {};
    if (bottomLine) {
      bottomLineStyle = {
        borderBottomWidth: 1,
        borderBottomColor: 'line-color-100',
      };
    }
    switch (type) {
      case 'basic':
        return {
          backgroundColor: 'background-color-0',
          color: 'black',
          marginTop: Number(marginTop) || 0,
          ...bottomLineStyle,
        };
      case 'primary':
        return {
          backgroundColor: 'primary-color-500',
          color: 'background-color-0',
          marginTop: Number(marginTop) || 0,
          ...bottomLineStyle,
        };
      default:
        return {
          backgroundColor: 'background-color-0',
          color: 'black',
          marginTop: Number(marginTop) || 0,
          ...bottomLineStyle,
        };
    }
  }, [type, bottomLine]);

  const $selfBg = useMemo(() => {
    if (selfBgKey) {
      return (
        <View
          style={{
            position: 'absolute',
            top: 0,
            zIndex: 1,
          }}>
          <Image name={selfBgKey as any} />
        </View>
      );
    }
    return <></>;
  }, [selfBgKey]);

  const $title = useMemo(() => {
    if (titleContent) {
      return <Title titleContent={titleContent} type={type} />;
    } else if (titleKey) {
      return <Title titleKey={titleKey} type={type} />;
    } else {
      return <></>;
    }
  }, [titleContent, titleKey, type]);

  try {
    return (
      <>
        <View
          layoutStrategy="flexRowBetweenCenter"
          width="100%"
          padding="16 28 16 28"
          style={headerStyle}>
          <View
            style={{
              zIndex: 2,
            }}>
            {accessoryLeft && accessoryLeft()}
          </View>
          <View
            style={{
              zIndex: 2,
            }}>
            {$title}
          </View>
          <View
            layoutStrategy="flexRowStartCenter"
            style={{
              zIndex: 2,
            }}>
            {$accessoryRight}
            {$message}
          </View>
          {$selfBg}
        </View>
      </>
    );
  } catch (error) {
    console.error('[invalid i18n keys]');
    return <></>;
  }
});
