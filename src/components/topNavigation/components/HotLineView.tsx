/**
 * @description 客服热线组件
 */

import { modalDataStoreInstance, ModalList } from '@/managers';
import { Image } from '@/components';
import { TouchableOpacity } from 'react-native';
import { Images } from '../../../config/assetIcons';

export type HotLineIconViewProps = {
  imageName?: keyof typeof Images;
};

const HotLineIconView = (props: HotLineIconViewProps): React.ReactElement => {
  const { imageName = '_hotLineBlack' } = props;
  return (
    <TouchableOpacity
      onPress={() => {
        modalDataStoreInstance.openModal({
          key: ModalList.HOT_LINE,
        });
      }}>
      <Image name={imageName} />
    </TouchableOpacity>
  );
};

export default HotLineIconView;
