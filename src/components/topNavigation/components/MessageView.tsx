/**
 * @description 导航栏消息组件
 */
import { MessageDataStoreInstance } from '@/managers';
import RouterConfig from '../../../routes/routerConfig';
import { nav } from '@/utils';
import { useState, useEffect, useMemo } from 'react';
import { Text, Image } from '@/components';
import { TouchableOpacity } from 'react-native';
import { Images } from '../../../config/assetIcons';

export type MessageViewProps = {
  imageName?: keyof typeof Images;
};

const MessageView = ({ imageName = '_messageWhite' }: MessageViewProps): React.ReactElement => {
  // todo 这里需要拿到未读消息的数量

  const [unReadMessageCount, setUnReadMessageCount] = useState<number>(0);

  useEffect(() => {
    const subscribe = MessageDataStoreInstance.messageCenter.subscribe(({ messageUndoSate }) => {
      setUnReadMessageCount(messageUndoSate.total);
    });
    return () => {
      subscribe && subscribe.unsubscribe();
    };
  }, []);

  const $unReadCount = useMemo(() => {
    let unReadCountString = '';
    if (unReadMessageCount > 10) {
      unReadCountString = '10+';
    } else {
      unReadCountString = unReadMessageCount.toString();
    }
    if (unReadMessageCount !== 0) {
      return (
        <Text
          padding="1 2 1 2"
          category="c2"
          status="control"
          style={{
            minWidth: 15,
            minHeight: 10,
            textAlign: 'center',
            position: 'absolute',
            top: -3,
            left: 12,
            borderRadius: 10,
            backgroundColor: 'danger-color-500',
          }}
          textContent={unReadCountString}
        />
      );
    }

    return null;
  }, [unReadMessageCount]);

  return (
    <TouchableOpacity
      style={{ marginLeft: 8, position: 'relative' }}
      onPress={() => {
        nav.navigate(RouterConfig.MESSAGE_CENTER as any);
      }}>
      <Image name={imageName} />
      {/* 要是有未读消息就展示未读消息的数量 */}
      {$unReadCount}
    </TouchableOpacity>
  );
};

export default MessageView;
