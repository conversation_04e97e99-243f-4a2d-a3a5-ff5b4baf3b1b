/* eslint-disable react-native/no-inline-styles */
import { BasicStyleType } from '@/utils';
import React from 'react';
import { ViewStyle, ViewProps as _ViewProps } from 'react-native';
import Image from '../image';
import Text from '../text';
import View from '../view';

export type ViewProps = ExtendProps & _ViewProps;
type ExtendProps = {
  process: EProcessStatus;
  isVisible?: boolean;
} & BasicStyleType<ViewStyle>;
export enum EProcessStatus {
  BASIC = 10,
  CURP = 20,
  TAKE_PHOTO = 30,
  CLABE = 40,
  DONE = 50,
}
/**
 * Process nav
 */
export default React.memo((_props: ViewProps): React.ReactElement => {
  const { process, isVisible = true, ...props } = _props;

  let glodOneStatus = '_oneCoin';
  let glodTwoStatus = '_twoCoin';
  let glodThreeStatus = '_threeCoin';
  let glodFourStatus = '_fourCoin';

  const computedStatus = () => {
    switch (process) {
      case EProcessStatus.BASIC:
        glodOneStatus = '_oneCoin';
        glodTwoStatus = '_twoCoinDisable';
        glodThreeStatus = '_threeCoinDisable';
        glodFourStatus = '_fourCoinDisable';
        break;
      case EProcessStatus.CURP:
        glodOneStatus = '_oneCoinActive';
        glodTwoStatus = '_twoCoin';
        glodThreeStatus = '_threeCoinDisable';
        glodFourStatus = '_fourCoinDisable';
        break;
      case EProcessStatus.TAKE_PHOTO:
        glodOneStatus = '_oneCoinActive';
        glodTwoStatus = '_twoCoinActive';
        glodThreeStatus = '_threeCoin';
        glodFourStatus = '_fourCoinDisable';
        break;
      case EProcessStatus.CLABE:
        glodOneStatus = '_oneCoinActive';
        glodTwoStatus = '_twoCoinActive';
        glodThreeStatus = '_threeCoinActive';
        glodFourStatus = '_fourCoin';
        break;
    }
  };

  if (!isVisible) {
    return <></>;
  }

  computedStatus();
  return (
    <View
      padding="0 8 4 8"
      style={{
        height: 56,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-around',
        flexWrap: 'nowrap',
        backgroundColor: 'background-color-0',
      }}
      {...props}>
      <View layoutStrategy="flexColumnBetweenCenter">
        <Image name={glodOneStatus as any} />
        <Text
          ellipsizeMode="tail"
          isCenter={true}
          category="c2"
          style={{
            width: 72,
            color: 'text-color-600',
          }}
          i18nKey="basicInfoString._25Percent"
        />
      </View>
      <Image name="_processLine" />
      <View layoutStrategy="flexColumnBetweenCenter">
        <Image name={glodTwoStatus as any} />
        <Text
          ellipsizeMode="tail"
          isCenter={true}
          category="c2"
          style={{
            width: 72,
            color: 'text-color-600',
          }}
          i18nKey="basicInfoString._50Percent"
        />
      </View>
      <Image name="_processLine" />
      <View layoutStrategy="flexColumnBetweenCenter">
        <Image name={glodThreeStatus as any} />
        <Text
          ellipsizeMode="tail"
          isCenter={true}
          category="c2"
          style={{
            width: 72,
            color: 'text-color-600',
          }}
          i18nKey="basicInfoString._75Percent"
        />
      </View>
      <Image name="_processLine" />
      <View layoutStrategy="flexColumnBetweenCenter">
        <Image name={glodFourStatus as any} />
        <Text
          ellipsizeMode="tail"
          isCenter={true}
          category="c2"
          style={{
            width: 72,
            color: 'text-color-600',
          }}
          i18nKey="basicInfoString._100Percent"
        />
      </View>
    </View>
  );
});
