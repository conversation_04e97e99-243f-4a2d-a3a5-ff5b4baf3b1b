import React, { useEffect } from 'react';
import { LogCenter } from '../../logCenter';
import { loggerManagerInstance } from '../../managers/logger/loggerManager';
import { useDeveloperMode } from '../../utils/developerMode';

export default function LogCenterContainer(): React.ReactElement {
  const developerMode = useDeveloperMode();
  useEffect(() => {
    if (developerMode.state.open) {
      loggerManagerInstance.action.openView();
    } else {
      loggerManagerInstance.action.closeView();
    }
  }, [developerMode]);

  return <LogCenter />;
}
