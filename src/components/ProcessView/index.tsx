import React from 'react';
import { View, Text } from '@/components';
import { StyleSheet } from 'react-native';
import Svg, { Circle } from 'react-native-svg';

export interface ProcessViewProps {
  /** 进度, 0-100 */
  progress: number;
  /** 圆环大小,宽==高 */
  size?: number;
  /** 圆环宽度， default:4 */
  strokeWidth?: number;
  /** 半径 default:20 */
  radius?: number;
}

/**
 * 圆形进度组件
 */
const ProgressView = (props: ProcessViewProps) => {
  const { progress = 0, size = 48, strokeWidth = 4, radius = 20 } = props;

  // 确保进度在0-100之间
  const clampedProgress = Math.min(100, Math.max(0, progress));

  // 计算圆的周长
  const circumference = 2 * Math.PI * radius;

  // 调整圆环进度计算，考虑 strokeLinecap="round" 的视觉效果
  const visualCorrection = (strokeWidth / (2 * Math.PI * radius)) * 100;
  const adjustedProgress =
    clampedProgress === 100 ? 100 : Math.max(0, clampedProgress - visualCorrection);

  // 使用调整后的进度计算偏移量
  const strokeDashoffset = circumference * (1 - adjustedProgress / 100);

  // 计算圆心位置
  const center = size / 2;

  return (
    <View
      width={size}
      height={size}
      layoutStrategy="flexColumnCenterCenter"
      style={styles.container}>
      <Svg width={size} height={size}>
        {/* 背景圆环 */}
        <Circle
          stroke="#E8E8E8"
          fill="none"
          cx={center}
          cy={center}
          r={radius}
          strokeWidth={strokeWidth}
        />
        {/* 进度圆环 */}
        <Circle
          stroke="#FF6B00"
          fill="none"
          cx={center}
          cy={center}
          r={radius}
          strokeWidth={strokeWidth}
          strokeDasharray={circumference}
          strokeDashoffset={strokeDashoffset}
          strokeLinecap="round"
          transform={`rotate(-90, ${center}, ${center})`}
          // 添加过渡动画效果
          //@ts-ignore
          style={{ transition: 'stroke-dashoffset 0.3s ease' }}
        />
      </Svg>
      {/* 进度文本 - 绝对定位在圆环中央 */}
      <View style={styles.textContainer}>
        <Text category="p2" bold="bold" status="basic">{`${Math.floor(clampedProgress)}%`}</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'relative',
  },
  textContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default ProgressView;
