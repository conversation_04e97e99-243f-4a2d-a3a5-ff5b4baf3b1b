import { Slider, SliderProps } from '@miblanchard/react-native-slider';
import { useTheme } from '@/hooks';
import { memo } from 'react';
import View from '../view';
import Text from '../text';
import { BaseInfoManager } from '@/managers';

interface IProps extends SliderProps {}
const AmountSlider = function AmountSlider(props: IProps) {
  const theme = useTheme();
  return (
    <View margin="12 0 12 0">
      <View
        style={{
          flex: 1,
          marginLeft: 10,
          marginRight: 10,
          marginBottom: 10,
          alignItems: 'stretch',
          justifyContent: 'center',
        }}
        layoutStrategy="flexColumnCenterCenter">
        <View
          style={{
            position: 'absolute',
            left: 0,
            right: 0,
            backgroundColor: theme['text-color-700'],
            borderColor: theme['text-color-700'],
            borderRadius: 24 / 2,
            height: 8,
          }}
        />
        <View
          style={{
            position: 'absolute',
            width: BaseInfoManager.context.baseModel.isAccUser ? '55%' : '100%',
            left: BaseInfoManager.context.baseModel.isAccUser ? '45%' : 0,
          }}>
          <Slider
            trackStyle={{
              borderRadius: 8,
              height: 8.1,
            }}
            thumbStyle={{
              backgroundColor: theme['background-color-0'],
              borderColor: theme['text-color-700'],
              borderRadius: 24 / 2,
              borderWidth: 4,
              height: 24,
              width: 24,
            }}
            minimumTrackTintColor={theme['text-color-700']}
            maximumTrackTintColor={theme['slider-background']}
            thumbTintColor={theme['text-color-700']}
            {...props}
            minimumValue={BaseInfoManager.context.baseModel.isAccUser ? 90 : props.minimumValue}
          />
        </View>
      </View>
      <View margin="0 12 0 12" layoutStrategy="flexRowBetweenCenterWrap">
        <Text
          category="c2"
          textContent={`${String(props.minimumValue).toFormatDay()}`}
          bold="700"
          style={{
            color: 'text-color-700',
            zIndex: 1,
          }}
        />
        <Text
          category="c2"
          textContent={`${String(props.maximumValue).toFormatDay()}`}
          bold="700"
          style={{
            color: 'text-color-700',
            zIndex: 1,
          }}
        />
      </View>
    </View>
  );
};

export default memo(AmountSlider);
