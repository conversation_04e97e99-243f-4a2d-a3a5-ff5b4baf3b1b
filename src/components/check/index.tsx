import { HitPointEnumsSpace } from '@/enums';
import { trackCommonEvent } from '@/trackEvent';
import { BasicStyleType, generateStyle, useCustomStyleSheet } from '@/utils';
import React, { useCallback, useMemo } from 'react';
import { TouchableHighlight, View, ViewStyle } from 'react-native';
import Image from '../image';
import Text from '../text';
import { TStatus, customCheckIconStyle, customCheckedColorStyle } from './styles';

// @ts-ignore
export interface CheckProps extends BasicStyleType<ViewStyle> {
  children?: React.ReactNode;
  checked?: boolean;
  onChange?: (checked: boolean, indeterminate: boolean) => void;
  /** 不确定的状态 */
  indeterminate?: boolean;
  status?: TStatus;
  label?: string;
  labelI18nKey?: string;
  disabled?: boolean;
  /** 埋点事件对象 */
  pageKey?: HitPointEnumsSpace.EPageKey;
  eventKey?: HitPointEnumsSpace.EEventKey;
}
/**
 * Basic check
 *
 * @property {string} padding - Can be use a string, like `12 0 0 0`
 * @property {string} margin - Can be use a string, like `12 0 0 0`
 * @property {number} width - Can be use a number
 * @property {number} height - Can be use a number
 * @property {object} style - can be use a object, like { popsition: 'absolute', right: 0, bottom: 0 }
 */
export default React.memo((_props: CheckProps): React.ReactElement => {
  const {
    children,
    padding,
    margin,
    width,
    height,
    style,
    checked = false,
    indeterminate = false,
    disabled = false,
    status = 'primary',
    label,
    labelI18nKey,
    pageKey,
    eventKey,
    onChange,
    ...props
  } = _props;
  const styles = useCustomStyleSheet(
    useGetStyle({
      padding,
      margin,
      width,
      status,
      height,
      style,
    }),
  );

  const onCheckedChange = useCallback(() => {
    const nextChecked = !checked;
    if (pageKey && eventKey) {
      trackCommonEvent(
        {
          p: pageKey,
          e: eventKey,
        },
        nextChecked ? '1' : '0',
      );
    }
    onChange && onChange(nextChecked, false);
  }, [checked, pageKey, eventKey]);

  const $checkBox = useMemo(() => {
    if (indeterminate) {
      return <Image name={'_baseIndeterminate'} style={styles.checkedIcon} />;
    }
    if (checked) {
      return <Image name="_baseCheck" style={styles.checkedIcon} />;
    } else {
      return null;
    }
  }, [checked, indeterminate, styles.checkIcon, styles.checkedIcon]);

  const $laber = useMemo(() => {
    return (
      (label || labelI18nKey) && (
        <Text textContent={label} margin="0 0 0 12" i18nKey={labelI18nKey} disabled={disabled} />
      )
    );
  }, [label, labelI18nKey, disabled]);

  const checkViewStyle = useMemo(() => {
    switch (true) {
      case checked && disabled:
        return [styles.check, styles.checked, styles.disabled];
      case !checked && disabled:
        return [styles.check, styles.disabled];
      case checked:
        return [styles.check, styles.checked];
      default:
        return [styles.check];
    }
  }, [checked, disabled, styles.check, styles.disabled, styles.checked]);

  return (
    <View style={[styles.checkbox]}>
      <TouchableHighlight
        onPress={onCheckedChange}
        disabled={disabled}
        underlayColor={styles.checkIcon.tintColor}
        style={{
          padding: 6,
          borderRadius: 4,
          overflow: 'hidden',
        }}>
        <View style={checkViewStyle}>{$checkBox}</View>
      </TouchableHighlight>
      {$laber}
    </View>
  );
});

const useGetStyle = (props: Partial<CheckProps>) => {
  return useMemo(() => {
    const { status = 'primary', ...rest } = props;
    return {
      checkbox: {
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        ...generateStyle(rest),
      },
      check: {
        ...customCheckIconStyle[status],
        width: 20,
        height: 20,
        borderRadius: 4,
        justifyContent: 'center',
        alignItems: 'center',
      },
      checked: {
        ...customCheckedColorStyle[status],
      },
      disabled: {
        borderColor: 'fill-color-400',
        backgroundColor: 'fill-color-500',
      },
      checkIcon: {
        tintColor: 'fill-color-400',
      },
      checkedIcon: {
        tintColor: 'text-color-0',
      },
    };
  }, [props]);
};
