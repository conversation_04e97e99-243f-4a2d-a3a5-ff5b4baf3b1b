/* eslint-disable react-native/no-inline-styles */
import { MessageDataStoreInstance } from '@/managers';
import React from 'react';
import { TouchableOpacity } from 'react-native';
import Text from '../text';
import View from '../view';

/**
 * bottom tabbar
 * @property descriptors
 * @property state
 * @property navigation
 * @property bottomTabs
 * @property focusedIconColor
 * @property iconColor
 * @property navigateCallbak
 * @property tabNumber
 */
export default ({
  state,
  descriptors,
  navigation,
  bottomTabs,
  focusedIconColor,
  iconColor,
  navigateCallbak,
  tabNumber = 3,
}: any): React.ReactElement => {
  const focusedOptions = descriptors[state.routes[state.index].key].options;

  if (focusedOptions?.tabBarStyle?.display === 'none') {
    return <></>;
  }

  return (
    <View
      style={{
        // borderTopColor: 'line-color-200',
        // borderTopWidth: 1,
        backgroundColor: 'background-color-0',
        flexDirection: 'row',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
      }}
      padding={'16 0 16 0'}>
      {state.routes.map((route: any, index: number) => {
        if (index >= tabNumber) {
          return;
        }
        const { options } = descriptors[route.key];
        const label =
          options.tabBarLabel !== undefined
            ? options.tabBarLabel
            : options.title !== undefined
            ? options.title
            : route.name;
        const isFocused = state.index === index;

        const onPress = () => {
          if (isFocused) {
            return;
          }

          // todo 更新未读消息的状态
          MessageDataStoreInstance.updateMessageUnReadState();

          const event = navigation.emit({
            type: 'tabPress',
            target: route.key,
            canPreventDefault: true,
          });

          if (!isFocused && !event.defaultPrevented) {
            // The `merge: true` option makes sure that the params inside the tab screen are preserved
            navigation.navigate({ name: route.name, merge: true });
            navigateCallbak(route.key);
          }
        };

        return (
          <TouchableOpacity
            accessibilityRole="button"
            accessibilityState={isFocused ? { selected: true } : {}}
            accessibilityLabel={options.tabBarAccessibilityLabel}
            testID={options.tabBarTestID}
            onPress={onPress}
            key={route.key}
            style={{
              flex: 1,
              alignItems: 'center',
            }}>
            <View>
              {!isFocused && bottomTabs[`${label}`]?.icon}
              {isFocused && bottomTabs[`${label}`]?.activeIcon}
            </View>
            {isFocused && (
              <Text
                category="c2"
                // eslint-disable-next-line react-native/no-inline-styles
                style={{
                  color: focusedIconColor,
                  paddingTop: 4,
                }}>
                {bottomTabs[`${label}`]?.name || ''}
              </Text>
            )}
            {!isFocused && (
              <Text
                category="c2"
                // eslint-disable-next-line react-native/no-inline-styles
                style={{
                  color: iconColor,
                  paddingTop: 4,
                }}>
                {bottomTabs[`${label}`]?.name || ''}
              </Text>
            )}
          </TouchableOpacity>
        );
      })}
    </View>
  );
};
