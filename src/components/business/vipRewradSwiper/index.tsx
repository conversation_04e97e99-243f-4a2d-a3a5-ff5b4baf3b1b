import React, { useMemo } from 'react';
import { View, Image, Swiper } from '../../_export_';
import { useNameSpace } from '@/i18n';
import _ from 'lodash';

/** VIP奖励提示轮播 */
export default React.memo(() => {
  const t = useNameSpace().t;
  const rewardList = useMemo(() => {
    return Array.from({ length: 50 }).map(() => {
      // 规则一：随机生成 VIP 等级（1-6）
      const vipLevel = Math.floor(Math.random() * 6) + 1;

      // 规则二：随机生成电话号码的尾号，不能全为一样的数字
      const lastFourDigits = () => {
        let digits = '';
        while (new Set(digits).size < 2) {
          // 确保不是全一样的数字
          digits = Array.from({ length: 3 }, () => Math.floor(Math.random() * 10)).join('');
        }
        return digits;
      };

      // 规则三：随机生成兑换优惠券金额
      const AMOUNTS = [100, 200, 400, 600, 800, 1000];
      const randomAmount = _.sample(AMOUNTS);

      // 随机选择文案类型
      const messageType = Math.random() > 0.5 ? 1 : 2;

      if (messageType === 1) {
        // 文案一：***489 canjeó un vale de $XX!
        return `***${lastFourDigits()} canjeó un vale de $${randomAmount}!`;
      } else {
        // 文案二：***229 acaba de subir a VIP1!
        return `***${lastFourDigits()} acaba de subir a VIP${vipLevel}!`;
      }
    });
  }, [t]);

  return (
    <View
      layoutStrategy="flexRowStartCenter"
      padding="8 12 8 12"
      style={{ backgroundColor: 'background-color-0' }}>
      <Image margin="0 6 0 0" name="_whiteNotice" style={{ tintColor: 'text-color-800' }} />
      <View style={{ flex: 1, paddingBottom: 4 }}>
        <Swiper
          backgroundColor="background-color-0"
          bottomType={'none'}
          textColor="text-color-800"
          isCenter={false}
          category="c1"
          list={rewardList}></Swiper>
      </View>
    </View>
  );
});
