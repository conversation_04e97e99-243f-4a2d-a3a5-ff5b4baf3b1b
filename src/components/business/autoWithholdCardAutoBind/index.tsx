/** 复贷首页 和 确认用信页的，自动代扣卡绑定功能卡片 */
import { useMemo } from 'react';
import { View, Text, Image, Card, DashedLine, Button, ImageBackground } from '../../_export_';
import React from 'react';
import { Colors } from '@/themes';
import { StyleSheet } from 'react-native';

type IProps = {
  /** 卡号 */
  cardNo: string;
  /** 开通状态 */
  withholdState: boolean;
  /** 开启授权 */
  openWithholdAuthorization: () => void;
  /** 关闭授权 */
  closeWithholdAuthorization: () => void;
  /** 查看自动代扣协议 */
  openAutoWithholdContract: () => void;
};

/** 用户VIP升级提示弹窗卡片 */
export default React.memo(
  ({
    cardNo,
    withholdState,
    openWithholdAuthorization,
    closeWithholdAuthorization,
    openAutoWithholdContract,
  }: IProps) => {
    const $Data = useMemo(() => {
      return (
        <View padding="6 12 6 12">
          <View margin="0 0 12 0" layoutStrategy="flexRowBetweenCenter">
            <Text
              margin="12 0 0 0"
              i18nKey="autoWithholdString.cardLabel"
              category="p2"
              bold="500"
              style={{
                color: Colors.TEXT_COLOR_600,
              }}
            />
            <Text
              category="c1"
              bold="500"
              i18nKey={
                withholdState ? 'autoWithholdString.activity' : 'autoWithholdString.noActivity'
              }
              style={{
                color: Colors.TEXT_COLOR_600,
              }}
            />
          </View>
          <DashedLine dashStyle={{ height: 1 }} dashColor={Colors.LINE_COLOR_100} />
          <View margin="12 0 12 0" padding="6 0 0 0" layoutStrategy="flexRowBetweenCenter">
            <Text
              textContent={`CLABE ${String(cardNo).toFormatClabe(true)}`}
              category="p2"
              bold="700"
              style={{
                color: Colors.TEXT_COLOR_800,
              }}
            />
            <Button
              status={'primary'}
              appearance={withholdState ? 'outline' : 'filled'}
              style={{
                borderRadius: 4,
              }}
              padding="6 8 6 8"
              textCategory="p2"
              size="custom"
              onPress={() => {
                if (withholdState) {
                  closeWithholdAuthorization();
                } else {
                  openWithholdAuthorization();
                }
              }}
              textI18nKey={
                withholdState ? 'autoWithholdString.cancel' : 'autoWithholdString.activate'
              }
            />
          </View>
        </View>
      );
    }, [cardNo, withholdState, openWithholdAuthorization, closeWithholdAuthorization]);

    return (
      <Card margin="12 16 12 16" padding="0 1 1 1" style={{ overflow: 'hidden' }}>
        {$Data}
        <View
          margin="0 0 0 0"
          padding="8 10 8 10"
          layoutStrategy="flexRowStartCenter"
          style={{
            backgroundColor: Colors.FILL_COLOR_200,
            borderBottomLeftRadius: 8,
            borderBottomRightRadius: 8,
          }}>
          <Image
            margin="2 8 0 0"
            style={{
              tintColor: 'fill-color-500',
            }}
            name="_grayInfo"
            resizeMode="contain"
          />
          <View layoutStrategy="flexRowStartCenter">
            <Text
              category="c2"
              i18nKey={'autoWithholdString.agree'}
              style={{
                color: 'text-color-600',
              }}
            />
            <Text
              category="c2"
              onPress={openAutoWithholdContract}
              i18nKey={'autoWithholdString.automaticWithhold'}
              style={{
                color: 'text-color-600',
                textDecorationLine: 'underline',
              }}
            />
          </View>
        </View>
      </Card>
    );
  },
);
