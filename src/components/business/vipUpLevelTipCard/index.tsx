import { useCallback, useMemo } from 'react';
import { View, Text, Image, Card, Button, Divider } from '../../_export_';
import { useSubscribeFilter, useVipFuncSwitch } from '@/hooks';
import VipRewradSwiper from '../vipRewradSwiper';
import React from 'react';
import { UserInfoContextType, UserInfoManager } from '@/managers';
import { HitPointEnumsSpace } from '@/enums';
import { trackCommonEvent } from '@/trackEvent';

type TUserInfoVipSubject = {
  /** 当banner的状态 */
  displayStatus: 'NORMAL' | 'MAX' | 'RECOVERY';
  /** 当前vip等级 */
  upgradeCount: number;
  /** rate */
  rate: string;
  /** 最大返现比例 */
  maxRate: string;
};

/** 用户VIP升级提示弹窗卡片 */
export default React.memo(({ pageKey }: { pageKey: HitPointEnumsSpace.EPageKey }) => {
  const isVipFuncSwitch = useVipFuncSwitch();
  const { displayStatus, upgradeCount, rate, maxRate } = useSubscribeFilter({
    subject: UserInfoManager.messageCenter,
    filter: (subject: UserInfoContextType) => {
      const {
        /** 当banner的状态 */
        displayStatus,
        /** 当前vip等级 */
        upgradeCount,
        rate,
        maxRate = '50%',
      } = subject.userModel.userState.vip || {
        displayStatus: 'NORMAL',
        upgradeCount: 2,
        rate: '10%',
        maxRate: '50%',
      };

      return {
        displayStatus,
        upgradeCount,
        rate,
        maxRate,
      };
    },
  }) as TUserInfoVipSubject;

  const $cardText = useMemo(() => {
    switch (displayStatus) {
      case 'NORMAL':
        return (
          <Text style={{ flex: 1 }}>
            <Text category="p2" i18nKey="vipString.upToTipStart" />
            <Text status="danger" category="p2" textContent={String(upgradeCount)} />
            <Text category="p2" i18nKey="vipString.upToTipEnd" />
          </Text>
        );
      case 'RECOVERY':
        return (
          <Text style={{ flex: 1 }}>
            <Text category="p2" i18nKey="vipString.resumeTipStart" />
            <Text status="danger" category="p2" textContent={String(upgradeCount)} />
            <Text category="p2" i18nKey="vipString.resumeTipEnd" />
          </Text>
        );
      case 'MAX':
      default:
        return (
          <Text style={{ flex: 1 }}>
            <Text category="p2" i18nKey="vipString.maxTipStart" />
            <Text status="danger" category="p2" textContent={rate} />
            <Text category="p2" i18nKey="vipString.maxTipEnd" />
          </Text>
        );
    }
  }, [displayStatus, upgradeCount, rate]);

  const viewVipRulesHandle = useCallback(() => {
    trackCommonEvent({ p: pageKey, e: HitPointEnumsSpace.EEventKey.BTN_VIP_CASHBACK }, '1');
    UserInfoManager.getVipConfigAndNavigate({});
  }, [pageKey]);

  const $Data = useMemo(() => {
    if (isVipFuncSwitch) {
      return (
        <Card
          padding="0 0 0 0"
          margin="16 16 12 16"
          style={{
            overflow: 'hidden',
          }}>
          <View padding="6 12 6 12">
            <View layoutStrategy="flexRowStartCenter">
              <Text>
                <Text bold="800" i18nKey="vipString.bannerTitle" />
                <Text status="danger" textContent={maxRate} />
              </Text>
              <Image margin="0 0 0 12" resizeMode="contain" name="_vipIcon" />
            </View>
            <View layoutStrategy="flexRowBetweenCenter">
              {$cardText}
              <Button
                padding="0 12 0 12"
                style={{ borderRadius: 99, height: 40 }}
                onPress={viewVipRulesHandle}
                margin="12 0 0 24"
                textI18nKey="vipString.viewRules"
                status="tertiary"
              />
            </View>
          </View>
          <Divider />
          <VipRewradSwiper />
        </Card>
      );
    } else {
      return null;
    }
  }, [isVipFuncSwitch, viewVipRulesHandle, displayStatus, upgradeCount, rate]);

  return <>{$Data}</>;
});
