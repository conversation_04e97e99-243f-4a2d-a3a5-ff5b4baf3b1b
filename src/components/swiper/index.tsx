import { BasicStyleType, Log, generateStyle, useCustomStyleSheet } from '@/utils';
import React, { useEffect, useMemo, useState } from 'react';
import { Animated, PanResponder, Text as rText } from 'react-native';
import Image from '../image/index';
import Text from '../text/index';
import View from '../view/index';
import { TCategory } from '../text/style';

export type SwiperProps = BasicStyleType<{}> & {
  /** 文本列表项 */
  list: string[];
  /** 底部展示效果 */
  bottomType?: 'dot' | 'square' | 'none';
  /** 左边Icon */
  leftIconName?: string;
  /** 背景色 */
  backgroundColor?: string;
  /** 文本颜色 */
  textColor?: string;
  /** 底部标识颜色 */
  bottomMarkColor?: string;
  /** 底部标识激活颜色 */
  bottomMarkActiveColor?: string;
  /** 过渡时间 */
  transitionTime?: number;
  /** 是否展示底部标志 */
  canableShowBottom?: boolean;
  /** 是否开启手势滑动 */
  canableGesture?: boolean;
  /** 是否开启原生加速 */
  canableNativeSpeed?: boolean;
  /** 是否支持切换动画 */
  canableSwiperAnimation?: boolean;
  /** 容器高 */
  type?: 2;
  /** 是否剧中展示 */
  isCenter?: boolean;
  /** 文案大小分类 */
  category?: TCategory;
};

interface IState {
  currentItem: number;
}

/**
 * Swiper component
 */
export default React.memo((_props: SwiperProps): React.ReactElement => {
  const {
    list = [],
    bottomType = 'dot',
    leftIconName = '',
    backgroundColor = 'primary-color-500',
    bottomMarkColor = '#FFFFFF',
    bottomMarkActiveColor = 'warn-color-500',
    textColor = 'text-color-0',
    transitionTime = 5000,
    canableShowBottom = true,
    canableGesture = true,
    canableNativeSpeed = true,
    canableSwiperAnimation = true,
    padding,
    margin,
    width,
    height,
    type = 1,
    style,
    isCenter = true,
    category = 'p1',
    ...props
  } = _props;

  const styles = useCustomStyleSheet(
    useGetStyle({
      backgroundColor,
      bottomMarkColor,
      bottomMarkActiveColor,
      textColor,
      padding,
      margin,
      width,
      height,
      style,
    }),
  );

  const [state, setState] = useState<IState>({
    currentItem: 0,
  });

  /** 手势交互状态。在触摸的时候不允许自动切换 */
  const [touchStatus, setTouchStatus] = useState<number>(0); // 0 未在交互状态，可以自动切换 1 用户正在交互

  /** 切换动画 */
  const animatedValue = new Animated.Value(0);
  Animated.timing(animatedValue, {
    toValue: 1,
    duration: 500,
    useNativeDriver: canableNativeSpeed,
  }).start();
  const animatedStyles = {
    opacity: animatedValue.interpolate({
      inputRange: [0, 1],
      outputRange: [0, 1],
    }),
  };

  /** 手势事件 */
  const panResponder = PanResponder.create({
    onStartShouldSetPanResponder: () => {
      return true;
    },
    onPanResponderStart: () => {
      // 设置静止自动切换
      setTouchStatus(1);
    },
    onPanResponderEnd: () => {
      // 设置静止自动切换
      setTouchStatus(0);
    },
    onMoveShouldSetPanResponder: () => true,
    onPanResponderRelease: (evt: any, gestureState: { dx: number }) => {
      if (!canableNativeSpeed) {
        return;
      }

      if (gestureState.dx > 0) {
        // 右滑处理
        setState((preState: IState) => ({
          ...preState,
          currentItem:
            Number(preState.currentItem) - 1 === -1
              ? list.length - 1
              : Number(preState.currentItem) - 1,
        }));
      } else if (gestureState.dx < 0) {
        // 左滑处理
        setState((preState: IState) => ({
          ...preState,
          currentItem:
            Number(preState.currentItem) + 1 === list.length ? 0 : Number(preState.currentItem) + 1,
        }));
      }
    },
  });

  useEffect(() => {
    if (touchStatus == 0 && list.length > 1) {
      const timer = setInterval(() => {
        setState((preState: IState) => ({
          ...preState,
          currentItem:
            Number(preState.currentItem) + 1 === list.length ? 0 : Number(preState.currentItem) + 1,
        }));
      }, transitionTime);

      return () => {
        timer && clearInterval(timer);
      };
    }
  }, [touchStatus]);

  /** 切换区域 */
  const $swipterItems = useMemo(() => {
    if (Array.isArray(list) && list.length > 0) {
      return list.map((item, index) => {
        if (index === state.currentItem) {
          return (
            <View width={'100%'} key={index}>
              {leftIconName ? (
                <View
                  margin={canableShowBottom ? '8 8 0 8' : '8 8 8 8'}
                  padding="0 8 0 8"
                  style={{
                    position: 'relative',
                    flexDirection: 'row',
                    justifyContent: 'center',
                    alignItems: 'flex-start',
                  }}>
                  <Image name={leftIconName as any} />
                  <Text
                    margin="0 8 0 8"
                    textContent={item}
                    category={category}
                    style={[styles.squareTextItem]}
                  />
                </View>
              ) : (
                <>
                  {type === 1 && (
                    <Text
                      margin={canableShowBottom ? '8 8 0 8' : '8 8 8 8'}
                      bold="bold"
                      style={[styles.textItem]}
                      isCenter={isCenter}
                      category={category}
                      textContent={item}
                    />
                  )}
                  {type === 2 && (
                    <View style={[styles.itemContainer]}>
                      <Text
                        margin={canableShowBottom ? '8 8 0 8' : '8 8 8 8'}
                        bold="bold"
                        style={[styles.textItem]}
                        isCenter={isCenter}
                        category={category}
                        textContent={item}
                      />
                    </View>
                  )}
                </>
              )}
            </View>
          );
        } else {
          return null;
        }
      });
    } else {
      return <></>;
    }
  }, [list, leftIconName, canableShowBottom, state.currentItem, styles, type, isCenter, category]);

  /** 底部区域 */
  const $swiperBottom = useMemo(() => {
    if (Array.isArray(list) && list.length > 0 && canableShowBottom && bottomType !== 'none') {
      let style = styles.dot,
        activeStyle = styles.dotActive;
      switch (bottomType) {
        case 'dot':
          style = styles.dot;
          activeStyle = styles.dotActive;
          break;
        case 'square':
          style = styles.square;
          activeStyle = styles.squareActive;
          break;
      }
      return (
        <View layoutStrategy="flexRowStartCenterWarp" style={[styles.swiperBottom]}>
          {list.map(item => {
            if (list.indexOf(item) == state.currentItem) {
              return <View margin="8 5 8 5" key={item} style={[activeStyle]}></View>;
            } else {
              return <View margin="8 5 8 5" key={item} style={[style]}></View>;
            }
          })}
        </View>
      );
    } else {
      return <></>;
    }
  }, [list, canableShowBottom, bottomType, state.currentItem]);

  return (
    <View
      {...panResponder.panHandlers}
      style={[styles.swiperContainer]}
      layoutStrategy="flexColumnBetweenCenter"
      {...props}>
      {$swipterItems}
      {$swiperBottom}
    </View>
  );
});

const useGetStyle = (props: Partial<SwiperProps>) => {
  return useMemo(() => {
    const { backgroundColor, bottomMarkColor, bottomMarkActiveColor, textColor, ...rest } = props;
    return {
      swiperContainer: {
        ...generateStyle(rest),
        backgroundColor,
      },
      textItem: {
        width: '90%',
        color: textColor,
      },
      squareTextItem: {
        color: textColor,
        width: '90%',
      },
      dot: {
        width: 8,
        height: 8,
        borderRadius: 50,
        backgroundColor: 'background-color-0',
        borderWidth: 1,
        borderColor: bottomMarkColor,
      },
      dotActive: {
        width: 24,
        height: 8,
        borderRadius: 50,
        backgroundColor: bottomMarkActiveColor,
      },
      square: {
        width: 18,
        height: 4,
        borderRadius: 5,
        backgroundColor: bottomMarkColor,
      },
      squareActive: {
        width: 18,
        height: 4,
        borderRadius: 5,
        backgroundColor: bottomMarkActiveColor,
      },
      swiperBottom: {
        position: 'absolute',
        bottom: 0,
      },
      itemContainer: {
        flexDirection: 'row',
        justifyContent: 'space-evenly',
        alignItems: 'center',
        height: 130,
        paddingBottom: 20,
      },
      itemView: {
        with: '100%',
        height: rest.height,
        marginBottom: 8,
        paddingHorizontal: 6,
      },
      item: {
        width: rest.width,
        borderRadius: 12,
        paddingBottom: 20,
      },
    };
  }, [props]);
};
