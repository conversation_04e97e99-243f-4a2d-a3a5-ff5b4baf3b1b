import { BasicStyleType, generateStyle, useCustomStyleSheet } from '@/utils';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { Animated, PanResponder, Image as RNImage, TouchableWithoutFeedback } from 'react-native';
import Swiper from 'react-native-swiper';
import View from '../../view/index';

export type SwiperProps = BasicStyleType<{}> & {
  /** 文本列表项 */
  list: string[];
  /** 底部展示效果 */
  bottomType?: 'dot' | 'square' | 'none';
  /** 背景色 */
  backgroundColor?: string;
  /** 文本颜色 */
  textColor?: string;
  /** 底部标识颜色 */
  bottomMarkColor?: string;
  /** 底部标识激活颜色 */
  bottomMarkActiveColor?: string;
  /** 过渡时间 */
  transitionTime?: number;
  /** 是否展示底部标志 */
  canableShowBottom?: boolean;
  /** 是否开启手势滑动 */
  canableGesture?: boolean;
  /** 是否开启原生加速 */
  canableNativeSpeed?: boolean;
  /** 是否支持切换动画 */
  canableSwiperAnimation?: boolean;
  /** banner item点击回调 */
  bannerItemClickCallback?: (item: string) => void;
};

interface IState {
  currentItem: number;
}

/**
 * Swiper Image component
 */
export default (_props: SwiperProps): React.ReactElement => {
  const {
    list = [],
    bottomType = 'dot',
    backgroundColor = 'primary-color-500',
    bottomMarkColor = '#FFFFFF',
    bottomMarkActiveColor = 'warn-color-500',
    textColor = 'text-color-0',
    transitionTime = 5000,
    canableShowBottom = true,
    canableGesture = true,
    canableNativeSpeed = true,
    canableSwiperAnimation = true,
    bannerItemClickCallback,
    padding,
    margin,
    width,
    height,
    style,
    ...props
  } = _props;

  const styles = useCustomStyleSheet(
    useGetStyle({
      backgroundColor,
      bottomMarkColor,
      bottomMarkActiveColor,
      textColor,
      padding,
      margin,
      width,
      height,
      style,
    }),
  );

  /** 点击banner事件回调 */
  const bannerItemClick = useCallback((bannerItem: string, index: number) => {
    if (typeof bannerItemClickCallback === 'function') {
      bannerItemClickCallback(bannerItem);
    }
  }, []);

  return (
    <Swiper
      autoplayTimeout={transitionTime / 1000}
      removeClippedSubviews={false}
      autoplay={true}
      loop={true}
      style={[styles.imageView]}
      showsPagination={bottomType !== 'none'}
      dotStyle={bottomType === 'dot' ? styles.dot : styles.square}
      activeDotStyle={bottomType === 'dot' ? styles.dotActive : styles.squareActive}>
      {list.map((item, index) => {
        return (
          <TouchableWithoutFeedback
            key={item}
            onPress={() => {
              bannerItemClick(item, index);
            }}>
            <RNImage source={{ uri: item }} style={[styles.image]} resizeMode="cover" />
          </TouchableWithoutFeedback>
        );
      })}
    </Swiper>
  );
};

const useGetStyle = (props: Partial<SwiperProps>) => {
  return useMemo(() => {
    const { backgroundColor, bottomMarkColor, bottomMarkActiveColor, textColor, ...rest } = props;

    return {
      dot: {
        width: 6,
        height: 6,
        borderRadius: 50,
        backgroundColor: bottomMarkColor,
      },
      dotActive: {
        width: 6,
        height: 6,
        borderRadius: 50,
        backgroundColor: bottomMarkActiveColor,
      },
      square: {
        width: 16,
        height: 3,
        borderRadius: 5,
        backgroundColor: bottomMarkColor,
      },
      squareActive: {
        width: 16,
        height: 3,
        borderRadius: 5,
        backgroundColor: bottomMarkActiveColor,
      },
      imageView: {
        overflow: 'hidden',
        height: 100,
        borderRadius: 12,
      },
      image: {
        ...generateStyle(rest),
        height: 100,
        backgroundColor: 'background-color-0',
        borderRadius: 8,
        // borderWidth: 1,
        // borderColor: 'line-color-200',
      },
    };
  }, [props]);
};
