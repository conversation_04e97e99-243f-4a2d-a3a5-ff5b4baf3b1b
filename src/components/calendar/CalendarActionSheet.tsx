/**
 * @description 日历actionsheet
 */
import React, { useMemo, useRef, useState } from 'react';
import { ActionSheet, View } from '@/components';
import CalendarView from './CalendarView';
import { useTheme } from '@/hooks';
import { convertToDateString, useCustomStyleSheet } from '@/utils';
import { DateData } from 'react-native-calendars';
import { Dimensions } from 'react-native';
import moment from "moment"

interface IProps {
  visible: boolean;
  minDate?: string | Date;
  maxDate?: string | Date;
  disabledDates?: string[];
  onClose?: () => void;
  onChange?: (dateString: string) => void;
  extraData?: any;
}

const CalendarActionSheet = (props: IProps) => {
  const { visible, onClose, onChange, minDate, maxDate, disabledDates, extraData } = props;
  const theme = useTheme();
  useCustomStyleSheet({});
  const [date, setDate] = useState('');
  const calendarRef = useRef<any>(null);

  const handleDateChange = (date: DateData) => {
    console.log('handleDateChange', date);
    setDate(date.dateString);
    onChange?.(date.dateString);
  };

  const pastScrollRange = useMemo(() => {
    if (!minDate) {
      return 0;
    }
    if (minDate instanceof Date) {
      return moment(Date.now()).diff(moment(minDate), 'months');
    }
    if (typeof minDate === 'string') {
      return moment(Date.now()).diff(moment(convertToDateString(minDate)), 'months');
    }
    return 0;
  }, [minDate]);

  const futureScrollRange = useMemo(() => {
    if (!maxDate) {
      return 1;
    }
    if (maxDate instanceof Date) {
      return moment(maxDate).diff(Date.now(), 'months') + 1;
    }
    if (typeof maxDate === 'string') {
      return moment(convertToDateString(maxDate)).diff(moment(Date.now()), 'months') + 1;
    }
    return 1;
  }, [maxDate]);

  const markedDate = useMemo(() => {
    return {
      ...disabledDates?.reduce((acc, date) => {
        //@ts-ignore
        acc[convertToDateString(date)] = { disabled: true };
        return acc;
      }, {}),
      [date]: {
        selected: true,
        selectedColor: theme['primary-color-500'],
        selectedTextColor: theme['text-color-0'],
      },
    };
  }, [disabledDates, date]);

  return (
    <ActionSheet visible={visible} onClose={onClose}>
      <CalendarView
        // ref={calendarRef}
        style={{ height: Math.round(Dimensions.get('window').height * 0.7) }}
        maxDate={maxDate}
        minDate={minDate}
        pastScrollRange={pastScrollRange}
        futureScrollRange={futureScrollRange}
        theme={{
          // loading 的时候不显示 date 占位
          //@ts-ignore
          'stylesheet.calendar-list.main': {
            placeholderText: {
              // text styles
              color: 'transparent',
            },
          },
        }}
        onDayPress={handleDateChange}
        markedDates={markedDate}
        extraData={extraData}
      />
    </ActionSheet>
  );
};

export default CalendarActionSheet;
