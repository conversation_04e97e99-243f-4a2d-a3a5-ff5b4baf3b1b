/**
 * @description: 日历组件
 * 基于react-native-calendars
 */
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { ActivityIndicator, ViewStyle } from 'react-native';
import { Calendar, CalendarProps, LocaleConfig, CalendarList } from 'react-native-calendars';
import { convertToDateString, useCustomStyleSheet } from '@/utils';
import { View } from '@/components';
import { useThemeManager } from '@/managers';

interface IProps extends Omit<Partial<CalendarProps>, 'minDate' | 'maxDate'> {
  style?: ViewStyle;
  horizontal?: boolean;
  minDate?: string | Date;
  maxDate?: string | Date;
  extraData?: any;
}

// 设置语言为西班牙语
LocaleConfig.locales['es'] = {
  monthNames: [
    'Enero',
    'Febrero',
    'Marzo',
    'Abril',
    'Mayo',
    'Jun<PERSON>',
    'Julio',
    'Agosto',
    'Septiembre',
    'Octubre',
    'Noviembre',
    'Diciembre',
  ],
  monthNamesShort: [
    'Ene',
    'Feb',
    'Mar',
    'Abr',
    'May',
    'Jun',
    'Jul',
    'Ago',
    'Sep',
    'Oct',
    'Nov',
    'Dic',
  ],
  dayNames: ['Domingo', 'Lunes', 'Martes', 'Miércoles', 'Jueves', 'Viernes', 'Sábado'],
  dayNamesShort: ['Dom', 'Lun', 'Mar', 'Mié', 'Jue', 'Vie', 'Sáb'],
};
LocaleConfig.defaultLocale = 'es';
const CalendarView = (props: IProps) => {
  const { style, horizontal = false, minDate, maxDate, extraData, ...rest } = props;
  const applicationTheme = useThemeManager().value.applicationTheme;
  const styles = useCustomStyleSheet(
    useGetStyle({
      style,
    }),
  );
  const calendarListRef = useRef<any>(null);
  const [loading, setLoading] = useState(true);
  const [uniqueKey, setUniqueKey] = useState(Date.now());

  useEffect(() => {
    const loadingTimeId = setTimeout(() => {
      setLoading(false);
      // setUniqueKey(Date.now());
    }, 500);
    return () => {
      !!loadingTimeId && clearTimeout(loadingTimeId);
    };
  }, []);

  const convertedMinDate = useMemo(() => {
    return !!minDate ? convertToDateString(minDate) : undefined;
  }, [minDate]);

  const convertedMaxDate = useMemo(() => {
    return !!maxDate ? convertToDateString(maxDate) : undefined;
  }, [maxDate]);
  const onLayout = () => {
    calendarListRef.current?.scrollToMonth('2999-12-30');
    const scrollTopTimer = setTimeout(() => {
      calendarListRef?.current?.scrollToMonth?.('1999-01-01');
      scrollTopTimer && clearTimeout(scrollTopTimer);
    }, 0);
    const loadingTimer = setTimeout(() => {
      setLoading(false);
      loadingTimer && clearTimeout(loadingTimer);
    }, 500);
  };
  if (!props.horizontal) {
    return (
      <>
        <CalendarList
          ref={calendarListRef}
          calendarHeight={300}
          pastScrollRange={0}
          futureScrollRange={2}
          removeClippedSubviews={false}
          allowSelectionOutOfRange={false}
          horizontal={false}
          style={[styles.calendar]}
          extraData={extraData || `${minDate}_${maxDate}`}
          {...rest}
          maxDate={convertedMaxDate}
          minDate={convertedMinDate}
          disableAllTouchEventsForDisabledDays
        // key={`calendar-${uniqueKey}`}
        // current={convertedMinDate}
        // onLayout={onLayout}
        />
        {loading ? (
          <View
            style={{
              backgroundColor: 'background-color-0',
              position: 'absolute',
              width: '100%',
              height: '100%',
            }}
            pointerEvents="none">
            <View
              padding="16 16 16 16"
              style={{
                backgroundColor: 'fill-color-0',
                position: 'absolute',
                top: '50%',
                left: '50%',
                borderRadius: 16,
                transform: [{ translateY: -38 }, { translateX: -38 }],
              }}>
              <ActivityIndicator
                animating
                style={{
                  width: 44,
                  height: 44,
                }}
                color={applicationTheme['primary-color-600']}
                size={'large'}
              />
            </View>
          </View>
        ) : null}
      </>
    );
  }

  return <Calendar {...rest} style={[styles.calendar]} />;
};

export default CalendarView;

const useGetStyle = (styles: { style?: ViewStyle }) => {
  return useMemo(() => {
    const { style } = styles;
    return {
      calendar: {
        ...style,
      },
    };
  }, [styles]);
};
