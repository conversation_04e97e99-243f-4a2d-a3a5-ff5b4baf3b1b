import React, { memo, ReactNode } from 'react';
import {
  Modal,
  View,
  StyleSheet,
  TouchableOpacity,
  StyleProp,
  ViewStyle,
  ModalProps,
} from 'react-native';

interface BaseModalProps extends Partial<ModalProps> {
  visible: boolean;
  onClose: () => void;
  children: ReactNode;
  containerStyle?: StyleProp<ViewStyle>;
  contentStyle?: StyleProp<ViewStyle>;
}

const BaseModal: React.FC<BaseModalProps> = ({
  visible,
  onClose,
  children,
  animationType = 'fade',
  transparent = true,
  containerStyle,
  contentStyle,
}) => {
  return (
    <Modal
      visible={visible}
      transparent={transparent}
      animationType={animationType}
      onRequestClose={onClose}>
      <TouchableOpacity
        style={[styles.container, containerStyle]}
        activeOpacity={1}
        onPressOut={onClose}>
        <View style={[styles.content, contentStyle]}>{children}</View>
      </TouchableOpacity>
    </Modal>
  );
};

export default memo(BaseModal);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#0000006C',
  },
  content: {
    width: '80%',
    padding: 20,
    backgroundColor: 'white',
    borderRadius: 10,
  },
});
