import { useCustomStyleSheet } from '@/utils';
import React, { memo, useMemo } from 'react';
import { Dimensions, ModalProps, StyleProp, TextStyle, TouchableOpacity } from 'react-native';
import * as animatable from 'react-native-animatable';
import Modal from 'react-native-modal';
import { GestureResponderEvent, PanResponderGestureState, ViewStyle } from 'react-native/types';
import Button from '../../button';
import Image from '../../image';
import Text from '../../text';
import { TStatus as TTextStatus } from '../../text/style';

import View from '../../view';

type OrNull<T> = T | null;
/** 业务类型 */
type ExtendProps = {
  /** 标题 i18nkey */
  titleKey?: string;
  /** 标题样式 */
  titleStyle?: TextStyle;
  /** 标题状态 */
  titleStatus?: TTextStatus;
  /** 文本 i18nkey */
  i18nKey?: string;
  /** 图片 key值 */
  imageKey?: string;
  /** 内容区域 */
  content?: string;
  /** 是否展示顶部颜色条 */
  hasLinearGradient?: boolean;
  /** 确认按钮 i18nkey */
  confirmBtnName?: string;
  /** 取消按钮 i18nkey */
  cancelBtnName?: string;
  /** 弹框高度 */
  height?: number;
  /** 弹框宽度 */
  width?: number;
  /** 确认按钮回调函数 */
  confirmCallback?: () => void;
  /** 取消按钮回调函数 */
  cancelCallback?: (neeCallback?: boolean) => void;
  /** 按钮类型 */
  buttonType?: 'nomal' | 'vertical-nomal' | 'vertical-special-1';
  /** 子组件 */
  children?: React.ReactElement;
  /** 底部关闭按钮 */
  isBottomIconColse?: Boolean;
  /** 顶部关闭按钮 */
  isTopIconColse?: Boolean;
  topImageKey?: string;
};
/** 基础组件类型 */
type Props = Partial<{
  animationIn:
    | 'bounce'
    | 'flash'
    | 'jello'
    | 'pulse'
    | 'rotate'
    | 'rubberBand'
    | 'shake'
    | 'swing'
    | 'tada'
    | 'wobble'
    | 'bounceIn'
    | 'bounceInDown'
    | 'bounceInUp'
    | 'bounceInLeft'
    | 'bounceInRight'
    | 'bounceOut'
    | 'bounceOutDown'
    | 'bounceOutUp'
    | 'bounceOutLeft'
    | 'bounceOutRight'
    | 'fadeIn'
    | 'fadeInDown'
    | 'fadeInDownBig'
    | 'fadeInUp'
    | 'fadeInUpBig'
    | 'fadeInLeft'
    | 'fadeInLeftBig'
    | 'fadeInRight'
    | 'fadeInRightBig'
    | 'fadeOut'
    | 'fadeOutDown'
    | 'fadeOutDownBig'
    | 'fadeOutUp'
    | 'fadeOutUpBig'
    | 'fadeOutLeft'
    | 'fadeOutLeftBig'
    | 'fadeOutRight'
    | 'fadeOutRightBig'
    | 'flipInX'
    | 'flipInY'
    | 'flipOutX'
    | 'flipOutY'
    | 'lightSpeedIn'
    | 'lightSpeedOut'
    | 'slideInDown'
    | 'slideInUp'
    | 'slideInLeft'
    | 'slideInRight'
    | 'slideOutDown'
    | 'slideOutUp'
    | 'slideOutLeft'
    | 'slideOutRight'
    | 'zoomIn'
    | 'zoomInDown'
    | 'zoomInUp'
    | 'zoomInLeft'
    | 'zoomInRight'
    | 'zoomOut'
    | 'zoomOutDown'
    | 'zoomOutUp'
    | 'zoomOutLeft'
    | 'zoomOutRight'
    | animatable.CustomAnimation<
        import('react-native').TextStyle & ViewStyle & import('react-native').ImageStyle
      >;
  animationInTiming: number;
  animationOut:
    | 'bounce'
    | 'flash'
    | 'jello'
    | 'pulse'
    | 'rotate'
    | 'rubberBand'
    | 'shake'
    | 'swing'
    | 'tada'
    | 'wobble'
    | 'bounceIn'
    | 'bounceInDown'
    | 'bounceInUp'
    | 'bounceInLeft'
    | 'bounceInRight'
    | 'bounceOut'
    | 'bounceOutDown'
    | 'bounceOutUp'
    | 'bounceOutLeft'
    | 'bounceOutRight'
    | 'fadeIn'
    | 'fadeInDown'
    | 'fadeInDownBig'
    | 'fadeInUp'
    | 'fadeInUpBig'
    | 'fadeInLeft'
    | 'fadeInLeftBig'
    | 'fadeInRight'
    | 'fadeInRightBig'
    | 'fadeOut'
    | 'fadeOutDown'
    | 'fadeOutDownBig'
    | 'fadeOutUp'
    | 'fadeOutUpBig'
    | 'fadeOutLeft'
    | 'fadeOutLeftBig'
    | 'fadeOutRight'
    | 'fadeOutRightBig'
    | 'flipInX'
    | 'flipInY'
    | 'flipOutX'
    | 'flipOutY'
    | 'lightSpeedIn'
    | 'lightSpeedOut'
    | 'slideInDown'
    | 'slideInUp'
    | 'slideInLeft'
    | 'slideInRight'
    | 'slideOutDown'
    | 'slideOutUp'
    | 'slideOutLeft'
    | 'slideOutRight'
    | 'zoomIn'
    | 'zoomInDown'
    | 'zoomInUp'
    | 'zoomInLeft'
    | 'zoomInRight'
    | 'zoomOut'
    | 'zoomOutDown'
    | 'zoomOutUp'
    | 'zoomOutLeft'
    | 'zoomOutRight'
    | animatable.CustomAnimation<
        import('react-native').TextStyle & ViewStyle & import('react-native').ImageStyle
      >;
  animationOutTiming: number;
  avoidKeyboard: boolean;
  coverScreen: boolean;
  hasBackdrop: boolean;
  backdropColor: string;
  backdropOpacity: number;
  backdropTransitionInTiming: number;
  backdropTransitionOutTiming: number;
  customBackdrop: React.ReactNode;
  useNativeDriver: boolean;
  deviceHeight: number | null;
  deviceWidth: number | null;
  hideModalContentWhileAnimating: boolean;
  propagateSwipe:
    | boolean
    | ((event: GestureResponderEvent, gestureState: PanResponderGestureState) => boolean);
  panResponderThreshold: number;
  swipeThreshold: number;
  onModalShow: () => void;
  onModalWillShow: () => void;
  onModalHide: () => void;
  onModalWillHide: () => void;
  onBackdropPress: () => void;
  onBackButtonPress: () => void;
  scrollTo: OrNull<(e: any) => void>;
  scrollOffset: number;
  scrollOffsetMax: number;
  scrollHorizontal: boolean;
  statusBarTranslucent: boolean;
  supportedOrientations: (
    | 'landscape'
    | 'portrait'
    | 'portrait-upside-down'
    | 'landscape-left'
    | 'landscape-right'
  )[];
  children: React.ReactNode;
  style?: StyleProp<ViewStyle>;
}> & {
  visible: boolean;
  modalType?: 'loading' | 'base';
} & ExtendProps;

/**
 * 基于react-native-modal封装的modal
 */
export default memo((props: Props): React.ReactElement => {
  const {
    hasLinearGradient,
    animationIn = 'fadeIn',
    animationOut = 'fadeOut',
    animationInTiming = 300,
    animationOutTiming = 300,
    topImageKey,
    titleKey,
    titleStyle = {},
    imageKey,
    content,
    i18nKey,
    cancelBtnName,
    confirmBtnName,
    confirmCallback,
    cancelCallback,
    backdropOpacity = 0.4,
    height,
    width,
    visible,
    /** 按钮类型 */
    buttonType = 'nomal',
    /** 子组件 */
    children = undefined,
    /** 底部关闭按钮 */
    isBottomIconColse = false,
    isTopIconColse = false,
    titleStatus,
    onBackdropPress,
    style = {},
  } = props;

  const styles = useCustomStyleSheet(
    useGetStyle({
      height,
      width,
      backdropOpacity,
      style,
    }),
  );

  const onConfirm = () => {
    confirmCallback && confirmCallback();
  };
  const onCancel = () => {
    cancelCallback && cancelCallback();
  };

  /** 顶部横条 */
  const ColorView = hasLinearGradient && <View style={styles.gradientBlue} />;

  /** 顶部图片组件 */
  const TopImageView = topImageKey && (
    <View
      margin={'24 0 0 0'}
      style={{
        flexDirection: 'column',
        alignItems: 'center',
      }}>
      <Image name={topImageKey as any} />
    </View>
  );

  /** 标题 */
  const TitleView = titleKey && (
    <View margin={hasLinearGradient ? '20 12 0 12' : '12 12 0 12'}>
      <Text
        i18nKey={titleKey}
        status={titleStatus}
        style={titleStyle}
        isCenter={true}
        category="h3"
      />
    </View>
  );
  /** 图片组件 */
  const ImageView = imageKey && (
    <View
      margin={TitleView ? '16 0 0 0' : '28 0 0 0'}
      style={{
        flexDirection: 'column',
        alignItems: 'center',
      }}>
      <Image name={imageKey as any} />
    </View>
  );

  /** 文本组件 */
  const TextView = content && (
    <Text
      margin={'12 12 0 12'}
      category="p1"
      textContent={content}
      style={{
        color: 'text-color-800',
        lineHeight: 28,
      }}
      isCenter={true}
    />
  );

  /** i18n文本组件 */
  const KeyTextView = i18nKey && (
    <Text
      margin={'12 12 0 12'}
      category="p1"
      i18nKey={i18nKey}
      style={{
        color: 'text-color-800',
        lineHeight: 28,
      }}
      isCenter={true}
    />
  );

  /** 按钮区域 */
  const ButtonGroup = useMemo(() => {
    switch (buttonType) {
      case 'vertical-nomal':
        return (
          (cancelBtnName || confirmBtnName) && (
            <View layoutStrategy={'flexColumnStartCenter'} margin="16 16 16 16">
              {confirmBtnName && (
                <Button
                  appearance="filled"
                  status="primary"
                  onPress={onConfirm}
                  style={{ flex: 1 }}
                  textI18nKey={confirmBtnName}
                />
              )}
              {cancelBtnName && (
                <Button
                  appearance="ghost"
                  margin={'0 10 0 0'}
                  onPress={onCancel}
                  style={{ flex: 1 }}
                  textI18nKey={cancelBtnName}
                />
              )}
            </View>
          )
        );
      case 'vertical-special-1':
        return (
          (cancelBtnName || confirmBtnName) && (
            <View layoutStrategy={'flexColumnStartCenter'} margin="16 16 16 16">
              {confirmBtnName && (
                <Button
                  width={'90%'}
                  appearance="filled"
                  status="primary"
                  onPress={onConfirm}
                  textI18nKey={confirmBtnName}
                />
              )}
              {cancelBtnName && (
                <Button appearance="ghost" margin={'12 0 0 0'} onPress={onCancel}>
                  <Text
                    status="primary"
                    i18nKey={cancelBtnName}
                    style={{ textDecorationLine: 'underline' }}
                  />
                </Button>
              )}
            </View>
          )
        );
      case 'nomal':
      default:
        return (
          (cancelBtnName || confirmBtnName) && (
            <View layoutStrategy={'flexRowBetweenCenter'} margin="16 16 16 16">
              {cancelBtnName && (
                <Button
                  appearance="outline"
                  margin={'0 10 0 0'}
                  onPress={onCancel}
                  style={{ flex: 1 }}
                  textI18nKey={cancelBtnName}
                />
              )}
              {confirmBtnName && (
                <Button
                  appearance="filled"
                  status="primary"
                  onPress={onConfirm}
                  style={{ flex: 1 }}
                  textI18nKey={confirmBtnName}
                />
              )}
            </View>
          )
        );
    }
  }, [buttonType, cancelBtnName, confirmBtnName, onConfirm, onCancel]);

  const TopCloseicon = isTopIconColse && (
    <TouchableOpacity
      style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'flex-end',
        marginTop: 10,
        marginRight: 12,
      }}
      onPress={onCancel}>
      <Image name="_modalCloseIcon" />
    </TouchableOpacity>
  );

  const BottomCloseicon = isBottomIconColse && (
    <TouchableOpacity
      style={{
        display: 'flex',
        width: '100%',
        justifyContent: 'center',
        alignItems: 'center',
        marginTop: 10,
      }}
      onPress={onCancel}>
      <Image name="_modalCloseWhiteIcon" />
    </TouchableOpacity>
  );

  /** @fix 第一次打开Modal, 没有真正关闭弹窗, 下次开启后会丢失backdrop背景层 */
  if (!visible) {
    return <></>;
  }

  return (
    <Modal
      style={styles.modalContainer}
      isVisible={visible}
      animationIn={animationIn}
      animationInTiming={animationInTiming}
      animationOut={animationOut}
      animationOutTiming={animationOutTiming}
      backdropOpacity={backdropOpacity}
      deviceWidth={Dimensions.get('window').width}
      deviceHeight={Dimensions.get('window').height}
      onBackdropPress={onBackdropPress}
      {...props}>
      <View layoutStrategy="flexColumnStartCenter">
        <View style={styles.modal}>
          {ColorView}
          {TopCloseicon}
          {TopImageView}
          {TitleView}
          {ImageView}
          {TextView}
          {KeyTextView}
          {children}
          {ButtonGroup}
        </View>
        {BottomCloseicon}
      </View>
    </Modal>
  );
});

const useGetStyle = (
  props: Partial<
    ModalProps & {
      height: number;
      width: number;
      backdropOpacity: number;
      style: any;
    }
  >,
) => {
  return useMemo(() => {
    const { height, width, style } = props;
    return {
      modalContainer: {
        position: 'relative',
        top: 0,
        left: 0,
        right: 0,
        padding: 0,
        margin: 0,
        justifyContent: 'center',
        alignSelf: 'center',
      },
      modal: {
        borderRadius: 12,
        minWidth: Number(width) || Dimensions.get('window').width * 0.8,
        maxWidth: Number(width) || Dimensions.get('window').width * 0.9,
        maxHeight: Number(height) || Dimensions.get('window').height * 0.9,
        backgroundColor: 'background-color-0',
        overflow: 'hidden',
        ...style,
      },
      gradientBlue: {
        borderTopLeftRadius: 12,
        borderTopRightRadius: 12,
        height: 24,
        backgroundColor: 'primary-color-500',
      },
    };
  }, [props]);
};
