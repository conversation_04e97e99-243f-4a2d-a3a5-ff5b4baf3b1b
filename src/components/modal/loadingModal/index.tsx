import { useCustomStyleSheet } from '@/utils';
import React, { memo, useMemo } from 'react';
import { Dimensions, ModalProps, TouchableOpacity } from 'react-native';
import * as animatable from 'react-native-animatable';
import Modal from 'react-native-modal';
import { GestureResponderEvent, PanResponderGestureState, ViewStyle } from 'react-native/types';

type OrNull<T> = T | null;
/** 业务类型 */
type ExtendProps = {
  /** 弹框高度 */
  height?: number;
  /** 弹框宽度 */
  width?: number;
};
/** 基础组件类型 */
type Props = Partial<{
  animationIn:
    | 'bounce'
    | 'flash'
    | 'jello'
    | 'pulse'
    | 'rotate'
    | 'rubberBand'
    | 'shake'
    | 'swing'
    | 'tada'
    | 'wobble'
    | 'bounceIn'
    | 'bounceInDown'
    | 'bounceInUp'
    | 'bounceInLeft'
    | 'bounceInRight'
    | 'bounceOut'
    | 'bounceOutDown'
    | 'bounceOutUp'
    | 'bounceOutLeft'
    | 'bounceOutRight'
    | 'fadeIn'
    | 'fadeInDown'
    | 'fadeInDownBig'
    | 'fadeInUp'
    | 'fadeInUpBig'
    | 'fadeInLeft'
    | 'fadeInLeftBig'
    | 'fadeInRight'
    | 'fadeInRightBig'
    | 'fadeOut'
    | 'fadeOutDown'
    | 'fadeOutDownBig'
    | 'fadeOutUp'
    | 'fadeOutUpBig'
    | 'fadeOutLeft'
    | 'fadeOutLeftBig'
    | 'fadeOutRight'
    | 'fadeOutRightBig'
    | 'flipInX'
    | 'flipInY'
    | 'flipOutX'
    | 'flipOutY'
    | 'lightSpeedIn'
    | 'lightSpeedOut'
    | 'slideInDown'
    | 'slideInUp'
    | 'slideInLeft'
    | 'slideInRight'
    | 'slideOutDown'
    | 'slideOutUp'
    | 'slideOutLeft'
    | 'slideOutRight'
    | 'zoomIn'
    | 'zoomInDown'
    | 'zoomInUp'
    | 'zoomInLeft'
    | 'zoomInRight'
    | 'zoomOut'
    | 'zoomOutDown'
    | 'zoomOutUp'
    | 'zoomOutLeft'
    | 'zoomOutRight'
    | animatable.CustomAnimation<
        import('react-native').TextStyle & ViewStyle & import('react-native').ImageStyle
      >;
  animationInTiming: number;
  animationOut:
    | 'bounce'
    | 'flash'
    | 'jello'
    | 'pulse'
    | 'rotate'
    | 'rubberBand'
    | 'shake'
    | 'swing'
    | 'tada'
    | 'wobble'
    | 'bounceIn'
    | 'bounceInDown'
    | 'bounceInUp'
    | 'bounceInLeft'
    | 'bounceInRight'
    | 'bounceOut'
    | 'bounceOutDown'
    | 'bounceOutUp'
    | 'bounceOutLeft'
    | 'bounceOutRight'
    | 'fadeIn'
    | 'fadeInDown'
    | 'fadeInDownBig'
    | 'fadeInUp'
    | 'fadeInUpBig'
    | 'fadeInLeft'
    | 'fadeInLeftBig'
    | 'fadeInRight'
    | 'fadeInRightBig'
    | 'fadeOut'
    | 'fadeOutDown'
    | 'fadeOutDownBig'
    | 'fadeOutUp'
    | 'fadeOutUpBig'
    | 'fadeOutLeft'
    | 'fadeOutLeftBig'
    | 'fadeOutRight'
    | 'fadeOutRightBig'
    | 'flipInX'
    | 'flipInY'
    | 'flipOutX'
    | 'flipOutY'
    | 'lightSpeedIn'
    | 'lightSpeedOut'
    | 'slideInDown'
    | 'slideInUp'
    | 'slideInLeft'
    | 'slideInRight'
    | 'slideOutDown'
    | 'slideOutUp'
    | 'slideOutLeft'
    | 'slideOutRight'
    | 'zoomIn'
    | 'zoomInDown'
    | 'zoomInUp'
    | 'zoomInLeft'
    | 'zoomInRight'
    | 'zoomOut'
    | 'zoomOutDown'
    | 'zoomOutUp'
    | 'zoomOutLeft'
    | 'zoomOutRight'
    | animatable.CustomAnimation<
        import('react-native').TextStyle & ViewStyle & import('react-native').ImageStyle
      >;
  animationOutTiming: number;
  avoidKeyboard: boolean;
  coverScreen: boolean;
  hasBackdrop: boolean;
  backdropColor: string;
  backdropOpacity: number;
  backdropTransitionInTiming: number;
  backdropTransitionOutTiming: number;
  customBackdrop: React.ReactNode;
  useNativeDriver: boolean;
  deviceHeight: number | null;
  deviceWidth: number | null;
  hideModalContentWhileAnimating: boolean;
  propagateSwipe:
    | boolean
    | ((event: GestureResponderEvent, gestureState: PanResponderGestureState) => boolean);
  panResponderThreshold: number;
  swipeThreshold: number;
  onModalShow: () => void;
  onModalWillShow: () => void;
  onModalHide: () => void;
  onModalWillHide: () => void;
  onBackdropPress: () => void;
  onBackButtonPress: () => void;
  scrollTo: OrNull<(e: any) => void>;
  scrollOffset: number;
  scrollOffsetMax: number;
  scrollHorizontal: boolean;
  statusBarTranslucent: boolean;
  supportedOrientations: (
    | 'landscape'
    | 'portrait'
    | 'portrait-upside-down'
    | 'landscape-left'
    | 'landscape-right'
  )[];
  children: React.ReactNode;
}> & {
  visible: boolean;
} & ExtendProps;

/**
 * 基于react-native-modal封装的modal
 */
export default memo((props: Props): React.ReactElement => {
  const {
    animationIn = 'fadeIn',
    animationOut = 'fadeOut',
    animationInTiming = 300,
    animationOutTiming = 300,
    backdropOpacity = 0.4,
    height,
    width,
    visible,
    /** 子组件 */
    children = undefined,
    onBackdropPress,
  } = props;

  const styles = useCustomStyleSheet(
    useGetStyle({
      height,
      width,
      backdropOpacity,
    }),
  );

  /** @fix 第一次打开Modal, 没有真正关闭弹窗, 下次开启后会丢失backdrop背景层 */
  if (!visible) {
    return <></>;
  }

  return (
    <Modal
      style={styles.modalContainer}
      isVisible={visible}
      animationIn={animationIn}
      animationInTiming={animationInTiming}
      animationOut={animationOut}
      animationOutTiming={animationOutTiming}
      backdropOpacity={backdropOpacity}
      deviceWidth={Dimensions.get('window').width}
      deviceHeight={Dimensions.get('window').height}
      onBackdropPress={onBackdropPress}
      {...props}>
      <>{children}</>
    </Modal>
  );
});

const useGetStyle = (
  props: Partial<ModalProps & { height: number; width: number; backdropOpacity: number }>,
) => {
  return useMemo(() => {
    return {
      modalContainer: {
        position: 'relative',
        top: 0,
        left: 0,
        right: 0,
        padding: 0,
        margin: 0,
        justifyContent: 'center',
        alignSelf: 'center',
      },
    };
  }, [props]);
};
