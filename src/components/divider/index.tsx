import { useCustomStyleSheet } from '@/utils';
import React, { useMemo } from 'react';
import { View, ViewStyle, ViewProps as _ViewProps } from 'react-native';
import {
  TDirection,
  TStatus,
  customDividerDirectionStyles,
  customDividerStatusStyles,
} from './styles';

export type DividerProps = ExtendProps & _ViewProps;
type ExtendProps = {
  /** @default horizontal 方向 */
  direction?: TDirection; //  "horizontal"
  /** 状态 @default basic  */
  status?: TStatus; //
  style?: ViewStyle;
  padding?: string | undefined;
  margin?: string | undefined;
};

export default React.memo((_props: DividerProps) => {
  const { direction, status, padding, margin, style, ...props } = _props;
  const styles = useCustomStyleSheet(
    useGetStyle({
      direction,
      status,
      padding,
      margin,
      style,
    }),
  );
  return <View style={styles.divider} {...props} />;
});

const useGetStyle = (props: Partial<ExtendProps>) => {
  return useMemo(() => {
    const {
      direction = 'horizontal',
      status = 'basic',
      padding = '0 0 0 0',
      margin = '0 0 0 0',
      style,
    } = props;
    const paddingArr = padding.split(' ');
    const marginArr = margin.split(' ');
    const customStyle = Array.isArray(style) ? Object.assign({}, ...style) : style;
    return {
      divider: {
        paddingTop: Number(paddingArr[0]),
        paddingRight: Number(paddingArr[1]),
        paddingBottom: Number(paddingArr[2]),
        paddingLeft: Number(paddingArr[3]),
        marginTop: Number(marginArr[0]),
        marginRight: Number(marginArr[1]),
        marginBottom: Number(marginArr[2]),
        marginLeft: Number(marginArr[3]),
        ...customDividerDirectionStyles[direction],
        ...customDividerStatusStyles[status],
        ...customStyle,
      },
    };
  }, [props]);
};
