import { ViewStyle } from 'react-native';

export type TStatus =
  | 'basic'
  | 'primary'
  | 'secondary'
  | 'success'
  | 'info'
  | 'warning'
  | 'danger'
  | 'control';

export type TDirection = 'horizontal' | 'vertical';

export const customDividerDirectionStyles: Record<TDirection, ViewStyle> = {
  horizontal: {
    height: 1,
    width: '100%',
  },
  vertical: {
    height: '100%',
    width: 1,
  },
};

export const customDividerStatusStyles: Record<TStatus, ViewStyle> = {
  basic: {
    backgroundColor: 'line-color-200',
  },
  primary: {
    backgroundColor: 'primary-color-500',
  },
  secondary: {
    backgroundColor: 'secondary-color-500',
  },
  success: {
    backgroundColor: 'success-color-500',
  },
  info: {
    backgroundColor: 'info-color-500',
  },
  warning: {
    backgroundColor: 'warn-color-500',
  },
  danger: {
    backgroundColor: 'danger-color-500',
  },
  control: {
    backgroundColor: 'fill-color-500',
  },
};
