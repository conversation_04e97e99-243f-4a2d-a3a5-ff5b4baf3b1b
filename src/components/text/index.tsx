import { BasicStyleType, generateStyle, useCustomStyleSheet } from '@/utils';
import React, { useMemo } from 'react';
import { Platform, StyleSheet, Text, TextStyle, TextProps as _TextProps } from 'react-native';
import { useNameSpace } from '../../i18n';
import { TCategory, TStatus, textCustomCategoryStyles, textCustomStatusStyles } from './style';

/**
 * issue
 * @description polyfill兼容了Text组件在部分android手机上的表现
 * https://stackoverflow.com/questions/35255645/how-to-set-default-font-family-in-react-native
 */
const defaultFontFamily = {
  ...Platform.select({
    android: { fontFamily: '' },
  }),
};

// @ts-ignore
const __render: any = Text.render;

// @ts-ignore
Text.render = function (props: TextProps, ref: React.RefObject<Text>) {
  if (Platform.OS === 'ios') {
    return __render.call(this, props, ref);
  }

  const { style, ..._props } = props;
  const _style = StyleSheet.flatten(style) || {};
  return __render.call(this, { ..._props, style: { ...defaultFontFamily, ..._style } }, ref);
};

export type TextProps = ExtendProps & _TextProps;
type ExtendProps = {
  bold?: 'bold' | 'normal' | '300' | '400' | '500' | '600' | '700' | '800';
  textContent?: string;
  i18nKey?: string;
  category?: TCategory;
  isCenter?: boolean;
  status?: TStatus;
} & BasicStyleType<TextStyle>;
/**
 * Basic text
 *
 * @property {string | number} bold - Can be use `bold`, `normal`, `300`, `400`, `500`, `600`, `700`, `800`
 * @property {string} category - Can be use themes/mapping.json, like `h1`, `h2`, `h3`, `p1`, `p2`, `c1`, `c2`.
 * @property {string} textContent - Can be use a string. like a state variable
 * @property {string} i18nKey - Can be use a string. like a key(homeString.loanAmountUpTo)
 * @property {string} padding - Can be use a string, like `12 0 0 0`
 * @property {string} margin - Can be use a string, like `12 0 0 0`
 * @property {boolean} isCenter - Can be use a string, ensure text is center
 * @property {number} width - Can be use a number
 * @property {number} height - Can be use a number
 * @property {object} style - can be use a object, like { popsition: 'absolute', right: 0, bottom: 0 }
 */
export default React.memo((_props: TextProps): React.ReactElement => {
  const {
    children,
    bold,
    category,
    textContent = '',
    i18nKey = '',
    padding,
    margin,
    isCenter,
    width,
    height,
    status = 'basic',
    disabled = false,
    style,
    ...props
  } = _props;
  const styles = useCustomStyleSheet(
    useGetStyle({
      category,
      bold,
      padding,
      margin,
      status,
      isCenter,
      width,
      height,
      style,
    }),
  );
  const t = useNameSpace().t;

  const textStyle = useMemo(() => {
    if (disabled) {
      return [styles.text, styles.textDisabled];
    } else {
      return styles.text;
    }
  }, [disabled, styles.text, styles.textDisabled]);

  return (
    <Text {...props} style={[textStyle]}>
      {i18nKey && t(i18nKey)}
      {textContent && textContent}
      {children && children}
    </Text>
  );
});

const useGetStyle = (props: Partial<ExtendProps>) => {
  return useMemo(() => {
    const { bold, isCenter, category = 'p1', status = 'basic', ...rest } = props;
    return {
      text: {
        ...textCustomCategoryStyles[category],
        ...textCustomStatusStyles[status],
        fontWeight: bold || 'normal',
        textAlign: isCenter ? 'center' : 'left',
        ...generateStyle(rest),
      },
      textDisabled: {
        color: 'fill-color-400',
      },
    };
  }, [props]);
};
