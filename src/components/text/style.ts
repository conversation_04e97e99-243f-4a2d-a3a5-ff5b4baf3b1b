import { TextStyle } from 'react-native';

export type TCategory = 'h1' | 'h2' | 'h3' | 'h4' | 'p1' | 'p2' | 'c1' | 'c2';

export type TStatus =
  | 'basic'
  | 'primary'
  | 'secondary'
  | 'success'
  | 'info'
  | 'warning'
  | 'danger'
  | 'control';

export const textCustomCategoryStyles: Record<TCategory, TextStyle> = {
  /** 金额 突出醒目 */
  h1: {
    fontSize: 32,
    lineHeight: 48,
    fontWeight: 'bold',
  },
  /** 空状态标题 */
  h2: {
    fontSize: 24,
    lineHeight: 32,
    fontWeight: 'normal',
  },
  /** 页面导航标题 大尺寸button，list标题 */
  h3: {
    fontSize: 20,
    lineHeight: 28,
    fontWeight: 'normal',
  },
  // v2.4 使用 h4 替换 h3， 主要替换小标题
  h4: {
    fontSize: 16,
    lineHeight: 24,
    fontWeight: 'normal',
  },
  /** 卡片标题 价格文字 内容文本 */
  p1: {
    fontSize: 16,
    lineHeight: 24,
    fontWeight: 'normal',
  },
  /** 中尺寸 Button文字，list 标题 */
  p2: {
    fontSize: 14,
    lineHeight: 20,
    fontWeight: 'normal',
  },
  /** 小尺寸 Button 文字 脚注 辅助内容 */
  c1: {
    fontSize: 12,
    lineHeight: 16,
    fontWeight: 'normal',
  },
  /** 特殊场景 tabbar tag 文字 */
  c2: {
    fontSize: 10,
    lineHeight: 14,
    fontWeight: 'normal',
  },
};

export const textCustomStatusStyles: Record<TStatus, TextStyle> = {
  basic: {
    color: 'text-color-800',
  },
  primary: {
    color: 'primary-color-500',
  },
  secondary: {
    color: 'secondary-color-500',
  },
  success: {
    color: 'success-color-500',
  },
  info: {
    color: 'info-color-500',
  },
  warning: {
    color: 'warn-color-500',
  },
  danger: {
    color: 'danger-color-500',
  },
  control: {
    color: 'text-color-0',
  },
};
