/* eslint-disable react/react-in-jsx-scope */
import { useThemeManager } from '@/managers';
import { BasicStyleType, generateStyle, useCustomStyleSheet } from '@/utils';
import React, { useEffect, useMemo, useState } from 'react';
import type {
  DimensionValue,
  ImageSourcePropType,
  ImageStyle,
  ImageProps as _ImageProps,
} from 'react-native';
import { Image, StyleSheet } from 'react-native';
import { Images } from '../../config/assetIcons';

// @ts-ignore
export interface ImageProps extends _ImageProps, BasicStyleType<ImageStyle> {
  name: keyof typeof Images | string;
  source?: ImageSourcePropType;
  uri?: string;
}
/**
 * Basic Image
 *
 * @property {number} width - Can be use a number
 * @property {number} height - Can be use a number
 * @property {string} padding - Can be use a string, like `12 0 0 0`
 * @property {string} margin - Can be use a string, like `12 0 0 0`
 * @property {object} style - can be use a object, like { popsition: 'absolute', right: 0, bottom: 0 }
 */
export default React.memo((_props: ImageProps) => {
  const { darkMode } = useThemeManager().value;
  const { width, height, margin, padding, style, name, uri = '', ...props } = _props;
  const [source, setSource] = useState<{
    width?: DimensionValue;
    height?: DimensionValue;
  }>({ width: width, height: height });

  const ApplicationThemeIcon = useMemo(() => {
    return Images;
  }, [darkMode]);

  const styles = useCustomStyleSheet(
    getStyle({
      //@ts-ignore
      width: width ? width : ApplicationThemeIcon[name]?.width || undefined,
      //@ts-ignore
      height: height ? height : ApplicationThemeIcon[name]?.height || undefined,
      padding,
      margin,
      style,
    }),
  );

  useEffect(() => {
    if (uri.includes('http')) {
      Image.getSize(uri, (_width, _height) => {
        if (typeof height === 'number' && !width) {
          setSource({
            width: _width * (height / _height),
            height,
          });
        } else if (typeof width === 'number' && !height) {
          setSource({
            width,
            height: _height * (width / _width),
          });
        } else {
          setSource({
            width: _width,
            height: _height,
          });
        }
      });
    }
  }, []);

  if (uri.includes('http')) {
    const imageStyle = {
      ...styles.image,
      width: source.width,
      height: source.height,
    };
    console.log();

    return <>{<Image source={{ uri: uri }} {...props} style={imageStyle} />}</>;
  }

  return (
    <>
      <Image
        {...props}
        source={
          ApplicationThemeIcon[name as keyof typeof Images]?.source
            ? ApplicationThemeIcon[name as keyof typeof Images]?.source
            : ApplicationThemeIcon[name as keyof typeof Images]
        }
        style={styles.image}
      />
    </>
  );
});

const getStyle = (props: BasicStyleType<ImageStyle>) => {
  return StyleSheet.create({
    image: {
      ...generateStyle(props),
    },
  });
};
