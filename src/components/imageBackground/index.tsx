/* eslint-disable react/react-in-jsx-scope */
import { useThemeManager } from '@/managers';
import { BasicStyleType, generateStyle } from '@/utils';
import React, { useMemo } from 'react';
import type {
  ImageSourcePropType,
  ViewStyle,
  ImageBackgroundProps as _ImageBackgroundProps,
} from 'react-native';
import { ImageBackground, StyleSheet } from 'react-native';
import { Images } from '../../config/assetIcons';

// @ts-ignore
export interface ImageBackgroundProps extends _ImageBackgroundProps, BasicStyleType<ViewStyle> {
  children?: React.ReactNode;
  name: keyof typeof Images | string;
  source?: ImageSourcePropType;
}
/**
 * Basic Image
 *
 * @property {number} width - Can be use a number
 * @property {number} height - Can be use a number
 * @property {string} padding - Can be use a string, like `12 0 0 0`
 * @property {string} margin - Can be use a string, like `12 0 0 0`
 * @property {object} style - can be use a object, like { popsition: 'absolute', right: 0, bottom: 0 }
 */
export default React.memo((_props: ImageBackgroundProps) => {
  const { darkMode } = useThemeManager().value;
  const {
    width,
    children,
    height,
    margin,
    padding,
    style,
    name,
    resizeMode = 'cover',
    ...props
  } = _props;

  const ApplicationThemeIcon = useMemo(() => {
    return Images;
  }, [darkMode]);

  const styles = getStyle({
    // @ts-ignore
    width: ApplicationThemeIcon[name].source ? ApplicationThemeIcon[name]?.width : width,
    // @ts-ignore
    height: ApplicationThemeIcon[name]?.height ? ApplicationThemeIcon[name]?.height : height,
    padding,
    margin,
    style,
  });

  return (
    <ImageBackground
      resizeMode={resizeMode}
      {...props}
      // @ts-ignore
      source={
        ApplicationThemeIcon[name as keyof typeof Images]?.source
          ? ApplicationThemeIcon[name as keyof typeof Images]?.source
          : ApplicationThemeIcon[name as keyof typeof Images]
      }
      style={styles.image}>
      {children}
    </ImageBackground>
  );
});

const getStyle = (props: BasicStyleType<ViewStyle>) => {
  return StyleSheet.create({
    image: {
      ...generateStyle(props),
    },
  });
};
