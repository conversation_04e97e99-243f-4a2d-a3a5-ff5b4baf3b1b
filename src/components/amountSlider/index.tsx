import { Slider, SliderProps } from '@miblanchard/react-native-slider';
import { useTheme } from '@/hooks';
import { memo } from 'react';
import Image from '../image';
import Text from '../text';
import View from '../view';
import React from 'react';

interface IProps extends SliderProps {}
const DaySlider = function DaySlider(props: IProps) {
  const theme = useTheme();
  const { minimumValue, maximumValue } = props;
  return (
    <>
      <View margin="12 0 0 0" layoutStrategy="flexRowBetweenCenter">
        <View
          style={{
            flex: 1,
            alignItems: 'stretch',
            justifyContent: 'center',
          }}
          layoutStrategy="flexRowBetweenCenter">
          <View margin="0 12 0 0" layoutStrategy="flexColumnCenterCenter">
            <Image name="_minCoin" />
            <Text
              category="c2"
              textContent={`${props.minimumValue}`.toFormatFinance(false)}
              bold="700"
              style={{
                color: 'text-color-800',
                zIndex: 1,
              }}
            />
          </View>
          <View style={{ flex: 1 }}>
            <Slider
              trackStyle={{
                borderRadius: 8,
                height: 8,
              }}
              thumbTouchSize={{ width: 22, height: 22 }}
              renderThumbComponent={() => (
                <View width={24} height={24} layoutStrategy="flexColumnCenterCenter">
                  <Image
                    name="_thumbImage"
                    style={{ justifyContent: 'center', alignItems: 'center' }}
                  />
                </View>
              )}
              minimumTrackTintColor={theme['tertiary-color-600']}
              maximumTrackTintColor={theme['slider-background']}
              thumbTintColor={theme['primary-color-500']}
              {...props}
            />
          </View>
          <View margin="0 0 0 12" layoutStrategy="flexColumnCenterCenter">
            <Image name="_maxCoin" />

            <Text
              category="c2"
              bold="700"
              textContent={`${props.maximumValue}`.toFormatFinance(false)}
              style={{
                color: 'text-color-800',
                zIndex: 1,
              }}
            />
          </View>
        </View>
      </View>
      {/* <View layoutStrategy="flexRowBetweenCenter">
        <View layoutStrategy="flexColumnStartCenter">
          <Text
            category="c2"
            textContent={`${props.minimumValue}`.toFormatFinance(false)}
            bold="700"
            style={{
              color: 'text-color-800',
              zIndex: 1,
            }}
          />
        </View>
        <View layoutStrategy="flexColumnStartCenter">
          <Text
            category="c2"
            bold="700"
            textContent={`${props.maximumValue}`.toFormatFinance(false)}
            style={{
              color: 'text-color-800',
              zIndex: 1,
            }}
          />
        </View>
      </View> */}
    </>
  );
};

export default memo(DaySlider);
