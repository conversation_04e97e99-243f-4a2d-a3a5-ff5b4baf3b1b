import { Image, Text, View } from '@/components';
import { useCustomStyleSheet } from '@/utils';
import React, { ReactNode, useMemo } from 'react';
import { Pressable, StyleProp, TextStyle, ViewStyle } from 'react-native';
import { Item, useRadioContext } from '../RadioContext';

interface RadioButtonProps<T> {
  item: Item<T>;
  children?: ReactNode;
  style?: StyleProp<ViewStyle>;
  labelStyle?: StyleProp<TextStyle>;
}

export default function RadioButton({ item, children, style, labelStyle }: RadioButtonProps<any>) {
  const [checked, onPress] = useRadioContext(item);
  const styles = useCustomStyleSheet(useGetStyle({ style, labelStyle }));

  return (
    <Pressable
      style={[styles.container, style]}
      onPress={onPress}
      hitSlop={{ top: 8, bottom: 8, left: 8, right: 8 }}>
      <Image margin="4 6 0 0" name={checked ? '_fromRadioChecked' : '_fromRadioUnchecked'} />
      <Text style={{ flex: 1 }}>
        <Text category="p1" style={[styles.label, checked ? styles.checkedLabel : undefined]}>
          {item.label}
        </Text>
        {children && children}
      </Text>
    </Pressable>
  );
}

const useGetStyle = (props: Partial<any>) => {
  return useMemo(() => {
    return {
      container: {
        ...props.style,
        width: '100%',
        flexDirection: 'row',
        justifyContent: 'flex-start',
        alignItems: 'flex-start',
      },
      label: {
        ...props.labelStyle,
        flex: 1,
        color: 'text-color-700',
      },
      checkedLabel: {
        color: 'primary-color-500',
      },
    };
  }, [props]);
};
