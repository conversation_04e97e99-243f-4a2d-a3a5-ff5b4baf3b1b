import { Log } from '@/utils';
import React, { useContext, useMemo } from 'react';

export interface Item<T> {
  label: string;
  value: T;
}

export interface CheckContext<T> {
  checkedItems: Array<Item<T>>;
  setCheckedItems: (items: Array<Item<T>>) => void;
}

export const CheckContext = React.createContext<CheckContext<any>>({
  checkedItems: [],
  setCheckedItems: () => {},
});

export function useCheckContext(item: Item<any>) {
  const { checkedItems, setCheckedItems } = useContext(CheckContext);
  const checked = checkedItems.map(checkedItem => checkedItem.label)?.includes(item.label);

  const onPress = () => {
    if (checked) {
      setCheckedItems(checkedItems.filter(i => i.label !== item.label));
    } else {
      setCheckedItems([...checkedItems, item]);
    }
  };

  return [checked, onPress] as const;
}
