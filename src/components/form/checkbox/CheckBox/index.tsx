import { Text, Image } from '@/components';
import { useCustomStyleSheet } from '@/utils';
import React, { useMemo } from 'react';
import { Pressable, StyleProp, ViewStyle } from 'react-native';
import { Item, useCheckContext } from '../CheckContext';

interface CheckBoxProps<T> {
  item: Item<T>;
  style?: StyleProp<ViewStyle>;
}

export default function CheckBox({ item, style }: CheckBoxProps<any>) {
  const [checked, onPress] = useCheckContext(item);
  const styles = useCustomStyleSheet(useGetStyle({}));

  return (
    <Pressable
      style={[styles.container, style]}
      onPress={onPress}
      hitSlop={{ top: 8, bottom: 8, left: 8, right: 8 }}>
      <Image margin="0 6 0 0" name={checked ? '_fromCheckboxChecked' : '_fromCheckboxUnchecked'} />
      <Text category="p1" style={[styles.label, checked ? styles.checkedLabel : undefined]}>
        {item.label}
      </Text>
    </Pressable>
  );
}

const useGetStyle = (props: Partial<any>) => {
  return useMemo(() => {
    return {
      container: {
        flexDirection: 'row',
        alignItems: 'center',
      },
      label: {
        color: 'text-color-700',
      },
      checkedLabel: {
        color: 'primary-color-500',
      },
    };
  }, [props]);
};
