/**
 * @description 相机组件，使用 react-native-vision-camera 实现人像自拍
 */

import { useEffect, useRef, useState } from 'react';
import { View, Image, Text } from '@/components';
import { StyleSheet, TouchableOpacity } from 'react-native';
import {
  Camera,
  CameraDevice,
  CameraPosition,
  PhotoFile,
  TakePhotoOptions,
  useCameraDevice,
  useCameraPermission,
} from 'react-native-vision-camera';

export interface CameraViewProps {
  /** 拍照成功回调 */
  onTakePhoto: (photo: PhotoFile) => void;
  /** 拍照失败回调 */
  onTakePhotoError: (error: Error) => void;
  cameraType: CameraPosition;
}

export default function CameraView(props: CameraViewProps) {
  const { cameraType = 'front', onTakePhoto, onTakePhotoError } = props;
  const cameraRef = useRef<Camera>(null);
  const [photo, setPhoto] = useState<string | null>(null);
  const { hasPermission, requestPermission } = useCameraPermission();
  const device = useCameraDevice(cameraType);

  useEffect(() => {
    checkPermission();
  }, []);

  const checkPermission = async () => {
    if (!hasPermission) {
      await requestPermission();
    }
  };

  const takePhoto = async () => {
    if (cameraRef.current) {
      try {
        const cameraOptions: TakePhotoOptions = {
          flash: 'off',
          enableShutterSound: false,
        };
        const photo: PhotoFile = await cameraRef.current.takePhoto(cameraOptions);
        setPhoto(`file://${photo.path}`);
        props.onTakePhoto(photo);
      } catch (error) {
        props.onTakePhotoError(error as Error);
        console.error('Failed to take photo:', error);
      }
    }
  };

  const retakePhoto = () => {
    setPhoto(null);
  };

  if (!hasPermission || !device) {
    return (
      <View
        style={[
          styles.container,
          {
            justifyContent: 'center',
            alignItems: 'center',
            backgroundColor: 'background-color-0',
          },
        ]}>
        <Text category="h3" i18nKey="permissionAgreeString.peso_per_req_camera" />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.cameraContainer}>
        <Camera
          ref={cameraRef}
          style={styles.camera}
          device={device as CameraDevice}
          isActive={true}
          photo={true}
        />
        <TouchableOpacity style={styles.button} onPress={takePhoto}>
          <Image name="_evidenceCameraTakePhoto" />
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  cameraContainer: {
    flex: 1,
    position: 'relative',
  },
  camera: {
    flex: 1,
  },
  previewContainer: {
    flex: 1,
    position: 'relative',
  },
  preview: {
    flex: 1,
  },
  button: {
    position: 'absolute',
    bottom: 30,
    alignSelf: 'center',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 20,
  },
});
