import { BaseInfoManager } from '@/managers';
import { ReactElement, useCallback, useEffect, useState } from 'react';
import { Linking, ScrollView } from 'react-native';
import { BaseConfig } from '@/baseConfig';
import WebView from 'react-native-webview';
import View from '../view';
import { TrackEvent } from '@/utils';
import { HitPointEnumsSpace } from '@/enums';

export default (): ReactElement => {
  useEffect(() => {
    onLoadStart();
  }, []);

  const onLoadStart = useCallback(() => {
    BaseInfoManager.changeLoadingModalVisible(true);
  }, []);

  const onLoadEnd = useCallback(() => {
    BaseInfoManager.changeLoadingModalVisible(false);
  }, []);

  const handleShouldStartLoadWithRequest = (request: { url: any }) => {
    const { url } = request;
    const tagetUrl = url.replace('http://', 'https://');
    if (tagetUrl.includes(`${BaseConfig.websiteUrl}${BaseConfig.privacyPolicyUrl}`)) {
      return true; // 允许加载链接
    } else {
      Linking.openURL(tagetUrl); // 调起用户的浏览器打开链接
      return false; // 不在 WebView 内部打开链接
    }
  };

  const handleScroll = (event: {
    nativeEvent: { layoutMeasurement: any; contentOffset: any; contentSize: any };
  }) => {
    const { layoutMeasurement, contentOffset, contentSize } = event.nativeEvent;

    // Check if the user has scrolled to the bottom
    const isBottom = layoutMeasurement.height + contentOffset.y >= contentSize.height - 20;

    // Only update the state if the scroll position has changed
    if (isBottom) {
      TrackEvent.trackCommonEvent(
        {
          p: HitPointEnumsSpace.EPageKey.P_PRAVICY,
          e: HitPointEnumsSpace.EEventKey.ROLL_TO_BOTTOM,
        },
        '1',
      );
    }
  };

  const [webViewHeight, setWebViewHeight] = useState<number>(1000);

  const onMessage = useCallback((e: { nativeEvent: { data: string } }) => {
    setWebViewHeight(parseInt(e.nativeEvent.data));
  }, []);

  return (
    <View style={{ flex: 1 }}>
      <ScrollView onScroll={handleScroll} scrollEventThrottle={20}>
        <WebView
          style={{ height: webViewHeight }}
          onLoadEnd={onLoadEnd}
          originWhitelist={['*']}
          onMessage={onMessage}
          source={{
            uri: `${BaseConfig.websiteUrl}${BaseConfig.privacyPolicyUrl}`,
          }}
          onShouldStartLoadWithRequest={handleShouldStartLoadWithRequest}
        />
      </ScrollView>
    </View>
  );
};
