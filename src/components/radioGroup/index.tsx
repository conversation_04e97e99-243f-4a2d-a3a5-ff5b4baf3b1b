import { HitPointEnumsSpace } from '@/enums';
import { trackCommonEvent } from '@/trackEvent';
import { BasicStyleType, generateStyle, useCustomStyleSheet } from '@/utils';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { ViewStyle } from 'react-native';
import Radio from '../radio';
import Text from '../text';
import View from '../view';
import { TStatus } from './styles';

export interface IRadioDataListItem {
  label?: string;
  labelI18nKey?: string;
  value: any;
}

// @ts-ignore
export interface RadioGroupProps extends BasicStyleType<ViewStyle> {
  children?: React.ReactNode;
  defalutSelectedValue?: string;
  onChange?: (selectedValue: any) => void;
  status?: TStatus;
  title?: string;
  titleI18nKey?: string;
  disabled?: boolean;
  /** 子选项布局排列方式 */
  horizontal?: boolean;
  /** 埋点事件对象 */
  pageKey?: HitPointEnumsSpace.EPageKey;
  eventKey?: HitPointEnumsSpace.EEventKey;
  radioDataList?: IRadioDataListItem[];
}
/**
 * Basic checkbox
 *
 * @property {string} padding - Can be use a string, like `12 0 0 0`
 * @property {string} margin - Can be use a string, like `12 0 0 0`
 * @property {number} width - Can be use a number
 * @property {number} height - Can be use a number
 * @property {object} style - can be use a object, like { popsition: 'absolute', right: 0, bottom: 0 }
 */
export default React.memo((_props: RadioGroupProps): React.ReactElement => {
  const {
    children,
    padding,
    margin,
    width,
    height,
    style,
    defalutSelectedValue = '',
    status = 'primary',
    title,
    titleI18nKey,
    pageKey,
    eventKey,
    onChange,
    horizontal = false,
    radioDataList,
    ...props
  } = _props;
  const styles = useCustomStyleSheet(
    useGetStyle({
      padding,
      margin,
      width,
      status,
      height,
      style,
    }),
  );

  const [selectedValue, setSelectedValue] = useState<any>(defalutSelectedValue);

  const onSelectChange = useCallback((selected: any) => {
    setSelectedValue(selected);
    onChange && onChange(selected);
  }, []);

  const $title = useMemo(() => {
    return (title || titleI18nKey) && <Text textContent={title} i18nKey={titleI18nKey} />;
  }, [title, titleI18nKey]);

  /** 点击副作用, 用于埋点上报数据 */
  useEffect(() => {
    if (pageKey && eventKey) {
      trackCommonEvent(
        {
          p: pageKey,
          e: eventKey,
        },
        selectedValue,
      );
    }
  }, [selectedValue]);

  const $radioList = useMemo(() => {
    return (
      radioDataList &&
      radioDataList.map((dataItem, index) => {
        const { value, label, labelI18nKey } = dataItem;
        const onChange = () => onSelectChange(value);
        return (
          <Radio
            margin="8 0 0 0"
            padding="4 0 4 0"
            key={`radio_${index}`}
            onChange={onChange}
            label={label}
            labelI18nKey={labelI18nKey}
            checked={selectedValue == value}
          />
        );
      })
    );
  }, [radioDataList, onSelectChange, selectedValue]);

  return (
    <View margin="8 0 0 0" style={[styles.radioGroup]} {...props}>
      {$title}
      <View layoutStrategy={horizontal ? 'flexRowStart' : 'flexColumnStart'}>{$radioList}</View>
    </View>
  );
});

const useGetStyle = (props: Partial<RadioGroupProps>) => {
  return useMemo(() => {
    const { status = 'primary', ...rest } = props;
    return {
      radioGroup: {
        justifyContent: 'flex-start',
        alignItems: 'flex-start',
        ...generateStyle(rest),
      },
    };
  }, [props]);
};
