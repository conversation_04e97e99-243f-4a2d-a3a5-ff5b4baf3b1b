import React, {
  ReactElement,
  Ref,
  forwardRef,
  memo,
  useImperativeHandle,
  useMemo,
  useState,
} from 'react';
import Text from '../text';
import View from '../view';

interface IProp {
  time: number;
  countdownFlag: boolean;
}

interface IState {
  restTime: number;
  isStart: boolean;
}

export type RefType = {
  startCountDown: Function;
  clearCountDown: Function;
};
const CountDown = (props: IProp, ref: Ref<RefType>): ReactElement => {
  const [state, setState] = useState<IState>({
    restTime: 0,
    isStart: false,
  });
  const { countdownFlag } = props;
  const { isStart, restTime } = state;

  useImperativeHandle(ref, () => ({
    startCountDown(_time: number) {
      if (!countdownFlag) {
        setState((preState: IState) => {
          return { ...preState, restTime: 0 };
        });
        return;
      }
      setState((preState: IState) => {
        return { ...preState, restTime: Math.ceil(_time / 1000) };
      });
      onStartCountDown();
    },
    clearCountDown() {
      onClearCountDown();
    },
  }));

  const onStartCountDown = () => {
    if (isStart) {
      return;
    }

    function repeat() {
      setState((preState: IState) => {
        if (preState.restTime > 1) {
          return { isStart: true, restTime: preState.restTime - 1 };
        } else {
          return { isStart: false, restTime: 0 };
        }
      });
      setTimeout(repeat, 1000);
    }

    setTimeout(repeat, 1000);
  };

  const onClearCountDown = () => {
    setState(() => {
      return { isStart: false, restTime: 0 };
    });
  };

  const hour = useMemo(() => {
    if (restTime == 0) {
      return '--';
    }
    return String(Math.floor(restTime / 3600).toFixed(0)).padStart(2, '0');
  }, [restTime]);

  const minute = useMemo(() => {
    if (restTime == 0) {
      return '--';
    }
    return String(Math.floor((restTime % 3600) / 60).toFixed(0)).padStart(2, '0');
  }, [restTime]);

  const second = useMemo(() => {
    if (restTime == 0) {
      return '--';
    }
    return String((restTime % 60).toFixed(0)).padStart(2, '0');
  }, [restTime]);

  if (!isStart) {
    return <></>;
  }

  return (
    <View margin="16 0 0 0" layoutStrategy="flexColumnStartCenter">
      <View layoutStrategy="flexRowStartCenter">
        <Text
          margin="0 16 0 0"
          category="p2"
          style={{
            color: 'text-color-600',
          }}
          i18nKey={'loanConfirmString.coin_value_valid'}
        />
        <View
          padding="8 8 8 8"
          style={{
            borderRadius: 8,
            borderWidth: 2,
            minWidth: 36,
            borderColor: 'primary-color-500',
          }}>
          <Text textContent={hour} isCenter={true} />
        </View>
        <Text margin="0 4 0 4" textContent={':'} />
        <View
          padding="8 8 8 8"
          style={{
            borderRadius: 8,
            borderWidth: 2,
            minWidth: 36,
            borderColor: 'primary-color-500',
          }}>
          <Text textContent={minute} isCenter={true} />
        </View>
        <Text margin="0 4 0 4" textContent={':'} />
        <View
          padding="8 8 8 8"
          style={{
            borderRadius: 8,
            borderWidth: 2,
            minWidth: 36,
            borderColor: 'primary-color-500',
          }}>
          <Text textContent={second} isCenter={true} />
        </View>
      </View>
    </View>
  );
};

export default memo(forwardRef(CountDown));
