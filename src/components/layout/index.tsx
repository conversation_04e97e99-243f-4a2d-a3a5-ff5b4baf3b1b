import { useCustomStyleSheet } from '@/utils';
import React, { useMemo } from 'react';
import { Dimensions, ImageStyle, SafeAreaView, View, ViewProps, StatusBar } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import tinycolor from 'tinycolor2';
import Image from '../image';
import { TLevel, TPaddingLevel, customLayoutLevelStyles } from './styles';

export interface LayoutProps extends ViewProps {
  heightMode?: 'normal' | 'short';
  /** 样式 */
  style?: object;
  /** 背景颜色的级别 0 纯白色 1 浅灰色 */
  level?: TLevel;
  /** 内边距的级别 值为 pLevel * 4 */
  pLevel?: TPaddingLevel;
  /**  顶部状态栏补偿开关 默认 true */
  topCompensateOpen?: boolean;
  /** 顶部状态栏补偿颜色 默认为 background-color-0 */
  topCompensateColor?: string;
  // 顶部背景图片 name
  topBgImageName?: string;
  // 中间背景图片 name 图片的展示形式为 cover
  middleBgImageName?: string;
  // 底部背景图片 name
  bottomBgImageName?: string;
  // 顶部背景图片样式
  topBgImageStyle?: ImageStyle;
  // 中间背景图片样式
  modifyBgImageStyle?: ImageStyle;
  // 底部背景图片样式
  bottomBgImageStyle?: ImageStyle;
}

// 检查颜色是否属于浅色系
const isLightColor = (color: string) => {
  const brightness = tinycolor(color).getBrightness();
  return brightness > 128; // 根据需要调整阈值
};

/**
 * Basic layout
 *
 */
export default (props: LayoutProps): React.ReactElement => {
  const { top } = useSafeAreaInsets();

  const {
    children,
    level,
    pLevel,
    heightMode = 'normal',
    topCompensateOpen = true,
    topCompensateColor = 'background-color-0',
    topBgImageName = undefined,
    middleBgImageName = undefined,
    bottomBgImageName = undefined,
    style,
    topBgImageStyle,
    modifyBgImageStyle,
    bottomBgImageStyle,
  } = props;
  const styles = useCustomStyleSheet(
    useGetStyle({
      level,
      pLevel,
      heightMode,
      style,
      topCompensateColor,
      topBgImageStyle,
      modifyBgImageStyle,
      bottomBgImageStyle,
    }),
  );

  /** 顶部留白 */
  const $topCompensateView = useMemo(() => {
    if (!topCompensateOpen) return null;
    return <View style={[styles.topCompensateView, { height: top || StatusBar.currentHeight }]} />;
  }, [top]);

  // const $statusBar = useMemo(() => {
  //   let barStyle: StatusBarStyle = 'light-content';
  //   if (isLightColor(topCompensateColor)) {
  //     barStyle = 'dark-content';
  //   }
  //   return (
  //     <StatusBar translucent barStyle={barStyle} backgroundColor={'#000004c'} />
  //   );
  // }, [topCompensateColor]);

  /** 顶部背景图片 */
  const $topBgImage = useMemo(() => {
    if (topBgImageName) {
      return <Image name={topBgImageName as any} style={styles.topBgImageStyle} />;
    }
    return null;
  }, [topBgImageName, styles.topBgImageStyle]);

  /** 中部背景图片 */
  const $middleBgImage = useMemo(() => {
    if (middleBgImageName) {
      return <Image name={middleBgImageName as any} style={styles.middleBgImageStyle} />;
    }
    return null;
  }, [middleBgImageName, styles.middleBgImageStyle]);

  /** 底部背景图片 */
  const $bottomBgImage = useMemo(() => {
    if (bottomBgImageName) {
      return <Image name={bottomBgImageName as any} style={styles.bottomBgImageStyle} />;
    }
    return null;
  }, [bottomBgImageName, styles.bottomBgImageStyle]);

  return (
    <SafeAreaView>
      <View style={[styles.layout]}>
        {/* {$statusBar} */}
        {/* 顶部背景图片 */}
        {$topBgImage}
        {/* 中间背景图片 */}
        {$middleBgImage}
        {/* 底部背景图片 */}
        {$bottomBgImage}
        {/* 顶部留白 */}
        {$topCompensateView}
        {/* 子内容 */}
        {children}
      </View>
    </SafeAreaView>
  );
};

const useGetStyle = (props: Partial<LayoutProps>) => {
  return useMemo(() => {
    const {
      level = '0',
      pLevel = '3',
      style,
      topCompensateColor,
      topBgImageStyle,
      modifyBgImageStyle,
      bottomBgImageStyle,
    } = props;
    return {
      layout: {
        ...customLayoutLevelStyles[level],
        paddingHorizontal: Number(pLevel) * 4,
        position: 'relative',
        height: '100%',
        // borderWidth: 1,
        // borderColor: 'red',
        width: Dimensions.get('window').width * 1,
        ...style,
      },
      topCompensateView: {
        width: Dimensions.get('window').width * 1,
        backgroundColor: topCompensateColor,
      },
      topBgImageStyle: {
        position: 'absolute',
        width: Dimensions.get('window').width * 1,
        resizeMode: 'cover',
        top: 0,
        left: 0,
        right: 0,
        ...topBgImageStyle,
      },
      modifyBgImageStyle: {
        position: 'absolute',
        width: Dimensions.get('window').width * 1,
        height: Dimensions.get('window').height * 1,
        resizeMode: 'cover',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        ...modifyBgImageStyle,
      },
      bottomBg: {
        position: 'absolute',
        width: Dimensions.get('window').width * 1,
        resizeMode: 'cover',
        left: 0,
        right: 0,
        bottom: 0,
        ...bottomBgImageStyle,
      },
    };
  }, []);
};
