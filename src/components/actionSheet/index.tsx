import { useCustomStyleSheet } from '@/utils';
import React, { ReactNode, useMemo } from 'react';
import { KeyboardAvoidingView, Modal, Platform, TouchableOpacity, View } from 'react-native';

const SUPPORTED_ORIENTATIONS:
  | ('portrait' | 'portrait-upside-down' | 'landscape' | 'landscape-left' | 'landscape-right')[]
  | undefined = [
  'portrait',
  'portrait-upside-down',
  'landscape',
  'landscape-left',
  'landscape-right',
];

export interface PropsType {
  visible: boolean;
  /**
   * @description Background animation ("none", "fade", "slide")
   * @default "none"
   **/
  animationType?: 'none' | 'slide' | 'fade';
  /**
   * @description Height of Bottom Sheet
   * @default 260
   **/
  height?: number;
  /**
   * @description Minimum height of Bottom Sheet before close
   * @default 0
   **/
  minClosingHeight?: number;
  /**
   * @description Open Bottom Sheet animation duration
   * @default 300(ms)
   **/
  openDuration?: number;
  /**
   * @description Close Bottom Sheet animation duration
   * @default 200(ms)
   **/
  closeDuration?: number;
  /**
   * @description Use gesture drag down to close Bottom Sheet
   * @default false
   **/
  closeOnDragDown?: boolean;
  /**
   * @description Drag only the top area of the draggableIcon to close Bottom Sheet instead of the whole content
   * @default false
   **/
  dragFromTopOnly?: boolean;
  /**
   * @description Press the area outside to close Bottom Sheet
   * @default true
   **/
  closeOnPressMask?: boolean;
  /**
   * @description Press back android to close Bottom Sheet (Android only)
   * @default true
   **/
  closeOnPressBack?: boolean;
  /**
   * @description Enable KeyboardAvoidingView
   * @default true(ios)
   **/
  keyboardAvoidingViewEnabled?: boolean;
  /**
   * @description Custom style to Bottom Sheet
   * @default {}
   **/
  customStyles?: {
    wrapper?: Record<string, string>;
    container?: Record<string, string>;
    draggableIcon?: Record<string, string>;
  };
  /** open actionsheet callback */
  onOpenCallback?: () => void;
  /** close actionsheet callback */
  onCloseCallback?: () => void;
  /**
   * @description Callback function when Bottom Sheet has closed
   * @default undefined
   **/
  onClose?: Function;
  /**
   * @description Callback function when Bottom Sheet has opened
   * @default undefined
   **/
  onOpen?: Function;
  /**
   * @description Background animation ("none", "fade", "slide")
   * @default ReactNode
   **/
  children?: ReactNode;
}

type RefType = React.Ref<MethodType> | undefined;

export type MethodType = {
  /**
   * @description Open Bottom Sheet
   */
  open: Function;
  /**
   * @description Close Bottom Sheet
   */
  close: Function;
};
/**
 * ActionSheet
 *
 * @description description
 *
 * @example example
 */

export default (props: PropsType): React.ReactElement => {
  const {
    animationType = 'slide',
    closeOnDragDown = false,
    keyboardAvoidingViewEnabled = Platform.OS === 'ios',
    onClose = null,
    children = null,
    visible,
  } = props;

  const styles = useCustomStyleSheet(useGetStyle());

  const handleClosePressMask = () => {
    onClose && onClose();
  };

  const handleRequestClose = () => {
    onClose && onClose();
  };
  return (
    <Modal
      transparent
      animationType={animationType}
      visible={visible}
      supportedOrientations={SUPPORTED_ORIENTATIONS}
      onRequestClose={handleRequestClose}>
      <KeyboardAvoidingView
        enabled={keyboardAvoidingViewEnabled}
        behavior="padding"
        style={[styles.wrapper]}>
        <TouchableOpacity style={styles.mask} activeOpacity={1} onPress={handleClosePressMask} />
        <View style={[styles.container]}>
          <View style={styles.childrenCon}>{children}</View>
        </View>
      </KeyboardAvoidingView>
    </Modal>
  );
};

const useGetStyle = () => {
  return useMemo(() => {
    return {
      wrapper: {
        flex: 1,
        backgroundColor: '#0000006C',
        // opcaity: 0.7,
      },
      mask: {
        flex: 1,
        backgroundColor: 'transparent',
      },
      container: {
        borderTopLeftRadius: 8,
        borderTopRightRadius: 8,
        backgroundColor: 'background-color-0',
        width: '100%',
        // height: 0,
        overflow: 'hidden',
      },
      // childrenCon: {
      //   alignItems: 'center',
      //   maxHeight: '60%',
      // },
      draggableContainer: {
        width: '100%',
        alignItems: 'center',
        backgroundColor: 'transparent',
      },
      draggableIcon: {
        width: 35,
        height: 5,
        borderRadius: 5,
        margin: 10,
        backgroundColor: 'line-color-100',
      },
    };
  }, []);
};
