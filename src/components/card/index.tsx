import { BasicStyleType, generateStyle, useCustomStyleSheet } from '@/utils';
import React, { useMemo } from 'react';
import { View, ViewStyle, ViewProps as _ViewProps } from 'react-native';
/**
 * Basic card
 *
 * @property {string} borderColor - Can be use themes/index.ts(custom-theme-dark.json, custom-theme-light.json, custom-theme.json), like `text-color-800`, `success-color-500` or `color-danger-500`
 * @property {string} backgroundColor - Can be use themes/index.ts(custom-theme-dark.json, custom-theme-light.json, custom-theme.json), like `text-color-800`, `success-color-500` or `color-danger-500`
 * @property {string} padding - Can be use a string, like `12 0 0 0`
 * @property {string} margin - Can be use a string, like `12 0 0 0`
 * @property {number} width - Can be use a number
 * @property {number} height - Can be use a number
 * @property {object} style - can be use a object, like { popsition: 'absolute', right: 0, bottom: 0 }
 */
export type CardProps = ExtendProps & _ViewProps;
type ExtendProps = {
  borderColor?: string;
  backgroundColor?: string;
} & BasicStyleType<ViewStyle>;
export default (_props: CardProps): React.ReactElement => {
  const {
    children,
    borderColor,
    backgroundColor,
    padding = '16 12 16 12',
    margin,
    width,
    height,
    style,
    ...props
  } = _props;
  const styles = useCustomStyleSheet(
    useGetStyle({
      borderColor,
      backgroundColor,
      padding,
      margin,
      width,
      height,
      style,
    }),
  );

  return (
    <View {...props} style={[styles.view]}>
      {children}
    </View>
  );
};

const useGetStyle = (props: Partial<ExtendProps>) => {
  return useMemo(() => {
    const { borderColor, backgroundColor, ...rest } = props;
    return {
      view: {
        borderColor: borderColor || 'line-color-100',
        backgroundColor: backgroundColor || 'background-color-0',
        borderRadius: 8,
        ...generateStyle(rest),
      },
    };
  }, [props]);
};
