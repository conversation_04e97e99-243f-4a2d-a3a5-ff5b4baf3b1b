import { HitPointEnumsSpace } from '@/enums';
import { trackCommonEvent } from '@/trackEvent';
import { BasicStyleType, generateStyle, useCustomStyleSheet } from '@/utils';
import React, { useCallback, useEffect, useMemo } from 'react';
import { TouchableOpacity, ViewStyle } from 'react-native';
import Image from '../image';
import Text from '../text';
import { TStatus, customCheckIconStyle, customCheckedColorStyle } from './styles';

// @ts-ignore
export interface CheckBoxProps extends BasicStyleType<ViewStyle> {
  checked?: boolean;
  onChange?: (checked: boolean) => void;
  status?: TStatus;
  label?: string;
  labelI18nKey?: string;
  disabled?: boolean;
  /** 埋点事件对象 */
  pageKey?: HitPointEnumsSpace.EPageKey;
  eventKey?: HitPointEnumsSpace.EEventKey;
}
/**
 * Basic checkbox
 *
 * @property {string} padding - Can be use a string, like `12 0 0 0`
 * @property {string} margin - Can be use a string, like `12 0 0 0`
 * @property {number} width - Can be use a number
 * @property {number} height - Can be use a number
 * @property {object} style - can be use a object, like { popsition: 'absolute', right: 0, bottom: 0 }
 */
export default React.memo((_props: CheckBoxProps): React.ReactElement => {
  const {
    padding,
    margin,
    width,
    height,
    style,
    checked = false,
    disabled = false,
    status = 'primary',
    label,
    labelI18nKey,
    pageKey,
    eventKey,
    onChange,
    ...props
  } = _props;
  const styles = useCustomStyleSheet(
    useGetStyle({
      padding,
      margin,
      width,
      status,
      height,
      style,
    }),
  );

  const onCheckedChange = useCallback(() => {
    onChange && onChange(true);
  }, []);

  const $checkBox = useMemo(() => {
    if (checked) {
      return <Image name="_radioSelected" />;
    } else {
      return <Image name="_radioUnSelect" />;
    }
  }, [checked, styles.checkIcon, styles.checkedIcon]);

  const $laber = useMemo(() => {
    return (
      (label || labelI18nKey) && (
        <Text
          textContent={label}
          onPress={onCheckedChange}
          margin="0 0 0 12"
          i18nKey={labelI18nKey}
          disabled={disabled}
        />
      )
    );
  }, [label, labelI18nKey, disabled]);

  /** 点击副作用, 用于埋点上报数据 */
  useEffect(() => {
    if (pageKey && eventKey) {
      trackCommonEvent(
        {
          p: pageKey,
          e: eventKey,
        },
        checked ? '1' : '0',
      );
    }
  }, [checked]);

  return (
    <TouchableOpacity style={[styles.checkbox]} onPress={onCheckedChange} disabled={disabled}>
      {/* <View style={checkViewStyle}></View> */}
      {$checkBox}
      {$laber}
    </TouchableOpacity>
  );
});

const useGetStyle = (props: Partial<CheckBoxProps>) => {
  return useMemo(() => {
    const { status = 'primary', ...rest } = props;
    return {
      checkbox: {
        flexDirection: 'row',
        justifyContent: 'flex-start',
        alignItems: 'center',
        ...generateStyle(rest),
        marginRight: 36,
      },
      check: {
        ...customCheckIconStyle[status],
        borderRadius: 4,
        justifyContent: 'center',
        alignItems: 'center',
      },
      checked: {
        ...customCheckedColorStyle[status],
      },
      disabled: {
        borderColor: 'fill-color-400',
        backgroundColor: 'fill-color-500',
      },
    };
  }, [props]);
};
