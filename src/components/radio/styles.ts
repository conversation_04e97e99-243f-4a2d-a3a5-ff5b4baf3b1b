import { ImageStyle } from 'react-native';

export type TStatus =
  | 'basic'
  | 'primary'
  | 'secondary'
  | 'success'
  | 'info'
  | 'warning'
  | 'danger'
  | 'control';

export const customCheckIconStyle: Record<TStatus, ImageStyle> = {
  basic: {
    borderColor: 'fill-color-400',
    backgroundColor: 'fill-color-500',
    borderWidth: 1,
  },
  primary: {
    borderColor: 'primary-color-500',
    backgroundColor: 'primary-color-100',
    borderWidth: 1,
  },
  secondary: {
    borderColor: 'secondary-color-500',
    backgroundColor: 'secondary-color-100',
    borderWidth: 1,
  },
  success: {
    borderColor: 'success-color-500',
    backgroundColor: 'success-color-100',
    borderWidth: 1,
  },
  info: {
    borderColor: 'info-color-500',
    backgroundColor: 'info-color-100',
    borderWidth: 1,
  },
  warning: {
    borderColor: 'warn-color-500',
    backgroundColor: 'warn-color-100',
    borderWidth: 1,
  },
  danger: {
    borderColor: 'danger-color-500',
    backgroundColor: 'danger-color-100',
    borderWidth: 1,
  },
  control: {
    borderColor: 'fill-color-400',
    backgroundColor: 'fill-color-500',
    borderWidth: 1,
  },
};

export const customCheckedColorStyle: Record<TStatus, ImageStyle> = {
  basic: {
    backgroundColor: 'fill-color-400',
  },
  primary: {
    backgroundColor: 'primary-color-500',
  },
  secondary: {
    backgroundColor: 'secondary-color-500',
  },
  success: {
    backgroundColor: 'success-color-500',
  },
  info: {
    backgroundColor: 'info-color-500',
  },
  warning: {
    backgroundColor: 'warn-color-500',
  },
  danger: {
    backgroundColor: 'danger-color-500',
  },
  control: {
    backgroundColor: 'fill-color-400',
  },
};
