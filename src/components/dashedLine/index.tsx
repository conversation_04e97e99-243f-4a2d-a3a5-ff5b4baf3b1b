/* eslint-disable react/react-in-jsx-scope */
import { useThemeManager } from '@/managers';
import { useCustomStyleSheet } from '@/utils';
import React from 'react';
import { useMemo } from 'react';
import DashedLine, { DashedLineProps as _DashedLineProps } from 'react-native-dashed-line';

export interface DashedLineProps extends _DashedLineProps {
  width?: number;
  height?: number;
  isRotate?: boolean;
  style?: object;
  dashColor?: string;
}
/**
 * Basic Dashedline
 *
 * @property {number} width - Can be use a number
 *
 * @property {number} height - Can be use a number
 *
 * @property {boolean} isRotate
 */
export default (_props: DashedLineProps) => {
  const { width, height, style, dashColor = 'primary-color-500', ...props } = _props;
  const styles = useCustomStyleSheet(
    useGetStyle({
      width,
      height,
      style,
    }),
  );
  const { applicationTheme } = useThemeManager().value;

  return (
    <>
      <DashedLine {...props} style={[styles.dashedLine]} dashColor={applicationTheme[dashColor]} />
    </>
  );
};

const useGetStyle = (props: Partial<DashedLineProps>) => {
  return useMemo(() => {
    const { width = 'auto', height = 'auto', style = {} } = props;
    return {
      dashedLine: {
        width,
        height,
        ...style,
      },
    };
  }, [props]);
};
