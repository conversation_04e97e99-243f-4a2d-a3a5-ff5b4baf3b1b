// 客服弹窗联系信息类型
export enum EContractConfigType {
  PHONE = 'PHONE',
  WHATSAPP = 'WHATSAPP',
  EMAIL = 'EMAIL',
}
// 客服弹窗联系信息点击动作类型
export enum EContractConfigActionType {
  CALL = 'CALL',
  COPY = 'COPY',
}

// 一键代扣的代扣状态
export enum EDirectDebitStatus {
  // 超过次数限制
  LIMITED = 'LIMITED',
  // 代扣中
  PROCESSING = 'PROCESSING',
  // 代扣失败
  FAILED = 'FAILED',
}

// 还款渠道
export enum ERepaymentChannel {
  // 一键代扣
  DIRECT_DEBIT = 'DIRECT_DEBIT',
  // 线上还款
  ONLINE = 'ONLINE',
  // 线下还款
  OFFLINE = 'OFFLINE',
}
// 代扣提示弹窗类型
export enum EWithholderTipType {
  /**
   * 首次开启
   */
  OPEN = 'OPEN',
  /**
   * 关闭
   */
  CLOSE = 'CLOSE',
}
// 启动页资源类型
export enum LaunchScreenSourceType {
  IMAGE = 'image',
  VIDEO = 'video',
  GIF = 'gif',
}
