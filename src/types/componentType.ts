import { Route, NavigationHelpers } from '@react-navigation/core';

/**
 * @type 页面路由工具类
 * @example
  type RouteParamType = {
    a: boolean;
    b: string;
  }
  ScreenProps<RouteParamType>
 */
export type ScreenProps<T extends object | undefined> = {
  route: RouteType<T>;
  navigation: NavigationType;
} & any;

/**
 * @type 路由工具类
 * 
 * @example
  type RouteParamType = {
    a: boolean;
    b: string;
  }
  RouteType<RouteParamType>
 */
export type RouteType<T extends object | undefined> = Route<string, T>;

/**
 * @type 导航工具类
 */
export type NavigationType = NavigationHelpers<
  Record<string, object | undefined>,
  Record<string, { data?: any; canPreventDefault?: boolean }>
>;
