import { UserEnumsSpace } from '@/enums';

export namespace EvidenceVOSpace {
  // 基本信息
  export type RequestBasicType = {
    /** 薪资 */
    salary: string;
    /** 姓名 */
    name: string;
    /** 父姓名 */
    fatherName: string;
    /** 母姓名 */
    motherName: string;
    /** 出生地 */
    resideAddress: string;
    /** 性别 */
    gender: string;
    /** 生日 */
    birthDate: string;
    /** email */
    email: string;
    /** gp email */
    gpEmail: string;
    /** whatsApp */
    whatsApp: string;
    /** 房子类型 */
    resideType: string;
    /** 贷款用途 */
    purpose: string;
    /** 月收入 */
    incomeSource: string;
    /** 住址邮编 */
    pincode: string;
    /** 详细地址 */
    area: string;
    /** 州 */
    state: string;
    /** 城市 */
    city: string;
    /** 街道 */
    suburb: string;
    /** 年龄 */
    age: number | string;
    /** 薪资区间 */
    salaryPeriod: string;
    /** 发薪日 */
    salaryDate: string;
    /** 贷款用途 */
    loanUse: string;
    /** 婚姻状态 */
    maritalStatus: string;
    /** 孩子数量 */
    childNum: string;
    /** 教育情况 */
    educationLevel: string;
    /** 工作情况 */
    jobType: string;
    /** 教育状态 */
    cashLoaned: string;
    /** 优惠券文案 */
    couponMsg: string;
  };

  export type RequestWorkType = {
    /** 工作类型 */
    jobType: string;
    /** 薪水 */
    salary: string;
    /** 发薪日 */
    salaryDay: string;
    /** 发薪周期 */
    salaryCycle: string;
    /** 公司名称 */
    companyName: string;
    /** 公司地址 */
    companyAddress: string;
    /** 公司邮箱 */
    companyEmail: string;
    /** 公司电话 */
    companyMobile: string;
    /** 公司邮编 */
    companyPcode: string;
    /** 公司所属区 */
    companyArea: string;
    /** 公司所属州 */
    companyState: string;
    /** 公司所属城市 */
    companyCity: string;
    /** 公司所属街道 */
    companySuburb: string;
  };

  export type RequestRfcType = {
    rfc: string;
  };

  export type RequestAdditionalType = {
    /** 选举卡密钥 */
    claveelector: string;
    /** 婚姻状态 */
    maritalStatus: string;
    /** 几个小孩 */
    childNum: string;
    /** 教育程度 */
    educationLevel: string;
  };

  export type RequestContactItemType = {
    fullName: string;
    /** 手机号 */
    mobile: string;
    /** 关系 */
    relation: string;
  };

  /**
   * @type Curp墨西哥身份证件类型
   */
  export type RequestCurpType = {
    /** curp证件正面照 */
    curpFrontImg: string;
    /** curp证件反面照 */
    curpBackImg: string;
    /** curp卡类型, "IFE"或INE */
    curpType: string;
    /** curp子卡类型, "A"或"B"或"C"或"D"或"E"或"F" */
    curpSubType: string;
    /** curp号码 */
    curpNumber: string;
    /** 名字 */
    curpName: string;
    /** 父姓名 */
    curpFatherSurname: string;
    /** 母姓名 */
    curpMotherSurname: string;
    /** 性别，H:男性; M:女性 */
    curpGender: string;
    /** 出生日期 */
    curpBirthDate: string;
    /** 街道 */
    curpStreet: string;
    /** 区 */
    curpDistrict: string;
    /** 城市 */
    curpCity: string;
    /** 序列号(卡号) */
    curpFolio: string;
    /** 选民编号 */
    curpClaveelector: string;
    /** 注册年份；丢失或更换后计数累加。 */
    curpRegistro: string;
    /** 持卡人年龄 */
    curpEdad: string;
    /** 州编号 */
    curpEstado: string;
    /** 市编号 */
    curpMunicipio: string;
    /** 区域 */
    curpLocalidad: string;
    /** 序列号 */
    curpSeccion: string;
    /** 发行年份 */
    curpEmision: string;
    /** 有效年份 */
    curpVigencia: string;
    /** 订单ID */
    applyOrderId: string;
  };

  export type RequestAddressType = {
    /**pin code*/
    zipCode?: string;
    /**城市*/
    city: string;
    /**州*/
    state: string;
    /**区*/
    municipality: string;
    /**如果是查第一级，那就传YES*/
    first: string;
    /**街道*/
    address?: string;
    /**编码*/
    zipcode?: string;
  };

  export type RequestFaceType = {
    faceImg: string;
  };

  export type RequestFaceVerifyType = {
    /** bizToken */
    bizToken: string;
    /** megliveData */
    megliveData: string;
    bizType?: UserEnumsSpace.BizType;
  };

  export type RequestFaceDetectType = {
    livenessId: string;
    /** 测试环境参数 */
    mockSimilarity?: '88';
  };

  export type RequestSkipLive = {
    skipStatus: UserEnumsSpace.ESkipLiveScene;
    applyOrderId: string;
  };

  export type RequestContactType = {
    userReferences: ContactItemType[];
  };

  export type OCRDataInfoType = {
    fatherName: string;
    frontFile: string;
    backFile: string;
    name: string;
    motherName: string;
    validity: string;
    birthDate: string;
    curp: string;
  };

  export type ContactItemType = {
    fullName: string;
    /** 手机号 */
    mobile: string;
    /** 关系 */
    relation: string;
  };

  export type RequestBankAccountType = {
    /** 编码 */
    bankCode: string;
    /** 银行名称 */
    bankName: string;
    /** 类型 */
    type: string;
    /** 卡号 */
    cardNo: string;
  };

  export type BankListDataType = {
    bankList: BankItemDataType[];
  };

  export type QuestionnaireDataType = {
    /** 复贷单数 */
    loanSequence: number;
    /** 问题列表 */
    answerList: {
      questionId: number;
      optionKey: string;
    }[];
  };

  /**
   * @type 保存银行卡信息
   * @property bankCode 编码
   * @property bankName 银行名称
   * @property cardNo 类型
   * @property type 卡号
   * @property isSelf 是否为非本人clabe号 YES or NO
   * @property othersFatherName 非本人父姓
   * @property othersMotherName 非本人母姓
   * @property othersName 非本人名字
   * @property othersCurpNumber 非本人curp
   * @property othersRelation 非本人关系
   */
  export type BankItemDataType = {
    bankCode: string;
    bankName: string;
    cardNo: string;
    type: string;
    isSelf: string;
    othersFatherName?: string;
    othersMotherName?: string;
    othersName?: string;
    othersCurpNumber?: string;
    othersRelation?: string;
    /** 是否已经授权了自动代扣服务 */
    withholdAuthorizeStatus?: string;
  };

  /** 银行索引信息数据类 */
  export type BankFastIndexDataType = {
    sortId: string;
    position: number;
  };

  /** 人脸是否需要重做信息数据类 */
  export type NeedFaceApproveDataType = {
    result: string;
  };

  /** 地址信息数据类 */
  export type AddressDataType = {
    /**城市*/
    city: string;
    /**州*/
    state: string;
    /**区*/
    municipality: string;
    /**街道*/
    address: string;
    /**编码*/
    zipcode: string;
  };

  /** face biz token信息数据类 */
  export type FaceBizTokenDataType = {
    biz_token: string;
    time_used: string;
    request_id: string;
    bizType: UserEnumsSpace.BizType;
    skippedLive: string; // YES代表直接跳过活体识别
  };

  /** face biz token信息数据类 */
  export type FaceLicenseDataType = {
    license: string;
  };

  /** face biz token信息数据类 */
  export type FaceDetectDataType = {
    /**对比是否通过*/
    passFlag: string;
  };

  /** face验证信息数据类 */
  export type FaceVerifyDataType = {
    images: FaceVerifyImageDataType;
    result_code: string;
    result_message: string;
    /** 对比是否通过 */
    passFlag: string;
    /** 测试环境参数 */
    mockFaceIdCode?: '1000';
    skippedLive: string; // YES代表直接跳过活体识别
  };

  /** 人脸图片信息数据类 */
  export type FaceVerifyImageDataType = {
    image_best: string;
  };

  /** 银行卡信息数据类 */
  export type MeBankcardDataType = {
    /** id */
    id: number;
    /** appId */
    appId: string;
    /** userId */
    userId: string;
    /** 手机号 */
    mobile: string;
    /** 姓名 */
    name: string;
    /** 卡号 */
    cardNo: string;
    /** 类型 */
    type: string;
    /** 银行名称 */
    bankName: string;
    /** 银行编号 */
    bankCode: string;
    /** 是否为默认卡 */
    isDef: 'YES' | 'NO';
    /** 是否是本人卡 */
    isSelf: 'YES' | 'NO';
    /** 是否已经开通自动代扣 */
    withholdAuthorizeStatus?: 'YES' | 'NO' | '';
  };

  /** 银行卡补件信息数据类 */
  export type MeBankcardSupplyDataType = {
    /** cardNo */
    cardNo: string;
    /** bankName */
    bankName: string;
    /** bankCode */
    bankCode: string;
    /** type */
    type: string;
    /** isDef */
    isDef: string;
    /** 是否为非本人 */
    isSelf: string;
    /** 非本人父姓 */
    othersFatherName: string;
    /** 非本人母姓 */
    othersMotherName: string;
    /** 非本人名字 */
    othersName: string;
    /** 非本人curp */
    othersCurpNumber: string;
    /** 非本人关系 */
    othersRelation: string;
    /** clabe 补件的原因 */
    message: string;
    /** 是否已经开通自动代扣 */
    withholdAuthorizeStatus?: 'YES' | 'NO' | '';
  };

  /** 提交银行卡返回的数据结构 */
  export type RespBankCard = {
    /** 是不是需要去授权绑定 */
    requireWithholdAuthorize: UserEnumsSpace.EStatusType;
  };

  export type RespWithholdAuthorization = {
    /** 用户是不是已经授权自动代扣 */
    withholdAuthorizeStatus: UserEnumsSpace.EStatusType;
  };

  /** 问卷题目 */
  export type TQuestionItem = {
    /** 问题 id */
    questionId: number;
    /** 问题名称 */
    questionName: string;
    /** 问题类型 */
    questionType: 'MULTI' | 'SINGLE' | 'TEXT' | 'TEXTAREA';
    /** 是否为必填项 */
    required: 'YES' | 'NO';
    /** 答案列表 */
    answer: TQuestionAnswerLabelItem[];
    /** 启用状态 */
    enable?: TQuestionEnable | null;
    /** 限制规则 */
    limit?: TypeLimit | null;
  };

  export type TypeLimit = {
    textMaxLength: number;
  };

  /** 问券启用状态。前置依赖项 */
  export type TQuestionEnable = {
    parentOptionValue: string;
    parentId: number;
  };
  /** 问卷答案列表 */
  export type TQuestionAnswerLabelItem = {
    label: string;
    /** 问题名称 */
    value: string;
  };

  export type RequestQuestionAnswerDataType = { qgId: number; answerList: TQuestionAnswerItem[] };

  /** 问卷提交答案列表 */
  export type TQuestionAnswerItem = {
    questionId: number;
    optionKey: string;
  };
}
