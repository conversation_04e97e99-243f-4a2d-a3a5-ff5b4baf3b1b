import { BaseEnumsSpace, UserEnumsSpace, EProductType } from '@/enums';
import { EDirectDebitStatus, ERepaymentChannel } from '../enum';

export namespace OrderVOSpace {
  /** 上传图片 */
  export type RequestImageUploadType = {
    /** 图片本地路径 */
    filePath: string;
    /** 图片类型 */
    cardType: string;
  };

  /** 上传图片 */
  export type RequestOcrImageUploadType = {
    /** 正面图片本地路径 */
    frontFilePath: string;
    /** 背面图片本地路径 */
    backFilePath: string;
    /** 图片类型 */
    cardType: string;
  };

  /** 确认贷款 */
  export type RequestLoanCreateType = {
    /** 天数 */
    configId: string;
    /** 金额 */
    amount: string;
  };

  /** 确认接收贷款 */
  export type RequestLoanApplyConfirmType = {
    /**
     * 授信
     */
    acceptCredit: string;
    /**
     * 借款订单
     */
    applyOrderId: string;
  };

  /** 计算借款额度信息 */
  export type RequestLoanCalculationType = {
    /** 配置id */
    configId: string;
    /** 金额 */
    amount: string;
  };
  /** 保存发薪日还款配置 */
  export type PaydayConfigDataType = {
    /** 发薪周期 */
    frequency: string;
    /** 发薪日 */
    salaryDays: string;
    /** 支持的产品类型 */
    productType?: string;
    /** 用户选择的产品类型 */
    repayType?: string;
  };

  /** 获取支持的贷款产品 */
  export type ProductTypeV2 = {
    productType: string;
  };
  /** 计算借款额度信息 */
  export type RequestLoanCreditCalculationType = {
    /** 订单ID */
    applyOrderId: string;
  };

  /** 查询banner */
  export type RequestBannerType = {
    /** 位置 */
    location: string;
  };

  export type RootAmountDataType = {
    /**金额*/
    amount: string;
    /**天数*/
    term: string;
  };

  export type LoanLastOrderDataType = {
    /**金额*/
    amount?: string;
    /**结清日期*/
    settleDate?: string;
    /**银行卡类型*/
    cardType?: string;
    /**银行名称*/
    bankName?: string;
    /**卡号*/
    cardNo?: string;
  };

  /** 贷款详情信息数据类 */
  export type LoanDetailDataType = {
    /** 还款金额 */
    repaymentAmount: string;
    /** 真实还款金额(减免之后) */
    realRepaymentAmount: string;
    /** 卡类型 */
    cardType: string;
    /** 银行名称 */
    bankName: string;
    /** 卡号 */
    cardNo: string;
    /** 贷款金额 */
    loanAmount: string;
    /** 到账金额 */
    realAmount: string;
    /** 贷款时间 */
    loanDate: string;
    /** 到期日期 */
    expiryDate: string;
    /** 格式化后的到期日 */
    expiryDateNewFormat: string;
    /** 利息 */
    interest: string;
    /** 手续费vat */
    processFeeVat: string;
    /** 手续费 */
    processFee: string;
    /** 天数 */
    days: string;
    /** 用信截止时间 */
    countdown: number;
    /** 用信倒计时开关 */
    countdownFlag: UserEnumsSpace.EStatusType | string;
    /** 是否用券 */
    couponFlag: string;
    /** 优惠券类型 */
    couponType: string;
    /** 优惠券金额  */
    couponAmount: string;
    /** 是否显示挽留入口 */
    showRejectBtn: boolean;
    /** 额外的银行卡信息 */
    bankCardInfo?: DefaultBankCardInfo;
    /** 是否展示开通代扣按钮 YES/NO */
    showWithholdBtn: UserEnumsSpace.EStatusType | string;
    /** 是否提额 YES/NO */
    enableWithhold: UserEnumsSpace.EStatusType | string;
    /**  开通代扣提额的额度 */
    upAmountForWithhold: number;
    /** 产品变更提示 */
    productChangeTips: string;
    /** 提示文案 */
    loanTips: string;
    /** 是否显示修改入口 */
    showModifyBtn: 'YES' | 'NO';
    /** 下一个信用额度 */
    nextCreditAmount: string;
    /** 产品定价有改变 */
    productPriceChanged: 'YES' | 'NO';
    /** 审批通过的贷款产品 */
    repayType?: 'FIXED_DATE' | 'FIXED_DAY';
    /** 当前的还款方式的状态，是不是被拒绝过 */
    repayTypeStatus?: 'APPROVE' | 'REJECT';
  };

  export type DefaultBankCardInfo = {
    /** 卡号 */
    cardNo: string;
    /** 类型 */
    type: string;
    /** 银行名称 */
    bankName: string;
    /** 银行编号 */
    bankCode: string;
    /** 是否为默认卡 */
    isDef: UserEnumsSpace.EStatusType | '';
    /** 是否是本人卡 */
    isSelf: UserEnumsSpace.EStatusType | '';
    /** 是否已经开通自动代扣 */
    withholdAuthorizeStatus?: 'YES' | 'NO' | '';
    othersFatherName: string;
    othersMotherName: string;
    othersCurpNumber: string;
    othersName: string;
    othersRelation: string;
  };

  /** 复贷首页自动代扣开关 */
  export type AutoWithholdBankCardInfo = {
    isShowCard: UserEnumsSpace.EStatusType;
    cardNo: string;
  };
  /** 复贷首页支持 */
  export type ReloanHomeWithholdBankCardInfo = {
    /** YES-才会有cardNo,NO-页面不展示card */
    isShowCard: 'YES' | 'NO' | '';
  } & DefaultBankCardInfo;

  /** 订单详情信息数据类 */
  export type OrderDetailDataType = {
    /** YES-才会有cardNo,NO-页面不展示card */
    showWithholdBtn: 'YES' | 'NO';
    /** 借款金额 */
    loanAmount: string;
    /** 利息 */
    interest: string;
    /** 手续费 */
    processFee: string;
    /** 手续费vat */
    processFeeVat: string;
    /** 银行卡类型 */
    cardType: string;
    /** 银行名称 */
    bankName: string;
    /** 卡号 */
    cardNo: string;
    /** 是否是补件放款 */
    bankcardSupply: 'YES' | 'NO' | '';
    /** 自动代扣功能状态 */
    withholdAuthorizeStatus: 'YES' | 'NO' | '';
  };

  /** 还款详情信息数据类 */
  export type BillDetailDataType = {
    orderId: string;
    /** 本金 */
    remainPrincipal: string;
    /** 放款手续费 */
    remainTransferFee: string;
    /** 放款手续费VAT */
    remainTransferFeeVat: string;
    /** 服务费 */
    remainProcessFee: string;
    /** 服务费VAT */
    remainProcessFeeVat: string;
    /** 利息 */
    remainInterest: string;
    /** 利息vat */
    remainInterestVat: string;
    /** 逾期天数*/
    overdueDay: string;
    /** 逾期费 */
    remainOverdueFee: string;
    /** 逾期费Vat */
    remainOverdueFeeVat: string;
    /** 催收费 */
    remainCollectFee: string;
    /** 催收费vat */
    remainCollectFeeVat: string;
    /** 还款日期 */
    repayDate: string;
    /** 还款日期已格式化 */
    repayDateNewFormat: string;
    /** 剩余几天 */
    remindDay: string;
    /** 已还金额 */
    realRepayAmount: string;
    /** 待还金额 */
    remindRepayAmount: string;
    /** 减免金额 */
    annulAmount: string;
    /** 是否展示还款减免文案 */
    dunRegistrationLabel: number;
    /** 减免文案 */
    annulCopywriting: string;
    /** 是否已经登记 */
    dunRegistration: 'YES' | 'NO';
    /** 抵扣金额 */
    deductionAmount: string;
    /** 是否使用了抵扣券 */
    deductionFlag: string;
    /** 按时还款是否有券 */
    couponBannerFlag: string;
    /** 卷后真实还款金额 */
    realRemindRepayAmount: string;
    /** 是否可以展期 */
    allowExt: 'YES' | 'NO';
    /** 展期状态 EXTENDING 展期中 WAIT_PAY 待支付 */
    extStatus: string;
    /** 展期费用 */
    extAmount: string;
    /** 展期以支付费用 */
    extRemainAmount: string;
    /** 展期已支付费用 */
    extPaidAmount: string;
    /** 展期倒计时时间 */
    extCountdown: number;
    /** 贷款日期 */
    loanDate: string;
    /** 贷款天数 */
    days: string;
    /** 展期天数 */
    extDays: string;
    /** 返现文案 */
    cashbackCopywriting: {
      firstHalf: string;
      amount: string;
      secondHalf: string;
    } | null;
    /** 是否在代扣中 YES--在代扣中 */
    withholding: UserEnumsSpace.EStatusType;
    /** 代扣文案，无文案时为null */
    withholdCopywriting?: string[];
    /** 还款账号更改 */
    repayAccountChanged?: 'YES' | 'NO';
    /** 代扣状态 */
    directDebitStatus?: EDirectDebitStatus;
    /** 展示的还款渠道类型 */
    repayTypeChannels?: ERepaymentChannel[];
    /** 一键还款失败原因 */
    directDebitFailedReason?: string;
    /** 代扣金额 */
    directDebitAmount?: number;
    /** 优惠券状态 */
    couponStatus?: UserEnumsSpace.ECouponsStatus;
  };

  export type LoanCostPerPeriodItem = {
    /** 0 未开始 1进行中 2逾期 3 结清 */
    curStatus: '0' | '1' | '2' | '3';
    /** 当前账期 */
    curPeriodNum: number;
    /** 应还本金 */
    curPeriodPrincipal: string;
    curPeriodProcessFee: string;
    curPeriodProcessFeeVat: string;
    curPeriodTransferFee: string;
    curPeriodTransferFeeVat: string;
    curPeriodInterest: string;
    curPeriodInterestVat: string;
    curPeriodOverdueFeeAndVat: string;
    curPeriodCollectFeeAndVat: string;
    /** 还款日期 */
    curPeriodRepayDate: string;
    /** 还款金额 */
    curPeriodRepaymentAmount: string;
    /** 当前账期天数 */
    curPeriodDays: number;
    /** 账期开始时间 */
    curPeriodStartDate: string;
    /** 当前账单逾期天数 */
    curPeriodOverdueDay: string;
  };

  /** 还款详情信息数据类 */
  export type MultiPeriodBillDetailDataType = {
    /** 贷款订单 */
    orderId: string;
    /** 贷款金额 */
    loanAmount: string;
    /** 还款日期 */
    repayDate: string;
    /** 待还金额 */
    remindRepayAmount: string;
    /** 真实待还款金额 */
    realRemindRepayAmount: string;
    /** 减免金额 */
    annulAmount: string;
    /** 剩余应还本金 */
    remainPrincipal: string;
    /** 服务费和VAT费用 */
    remainProcessFeeAndVat: string;
    /** vat */
    remainInterestAndVat: string;
    /** 逾期费和vat */
    remainOverdueFeeAndVat: string;
    remainCollectFeeAndVat: string;
    /** 线上还款类型 */
    onlineType: string;
    /** 还款银行卡号 */
    clabe: string;
    /** 还款渠道 */
    offlineType: string;
    /** 贷款天数 */
    days: number;
    /** 放款时间 */
    loanDate: string;
    /** 预期天数 */
    overdueDay: string;
    /** 账期列表 */
    loanCostPerPeriods: LoanCostPerPeriodItem[];
    /** 还款账号更改 */
    repayAccountChanged?: 'YES' | 'NO';
    /** 代扣状态 */
    directDebitStatus?: EDirectDebitStatus;
    /** 展示的还款渠道类型 */
    repayTypeChannels?: ERepaymentChannel[];
    /** 一键还款失败原因 */
    directDebitFailedReason?: string;
    /** 代扣金额 */
    directDebitAmount?: number;
  };

  /** 还款渠道配置信息数据类 */
  export type RaepayMethodConfigDataType = {
    /**线上还款类型*/
    onbaseType: UserEnumsSpace.ERepayChannel;
    /**线下还款类型*/
    offbaseType: UserEnumsSpace.ERepayChannel;
  };

  /** 还款渠道展示信息数据类 */
  export type RepayMethodConfigItemDataType = {
    type: string;
    display: string;
    content: string;
  };

  /** 线下还款信息数据类 */
  export type RepayOfflineDataType = {
    /**付款码*/
    referenceNumber: string;
    /** 付款码条形码 */
    referenceImage: string;
    /**待还金额*/
    totalAmount: string;
    /**服务费*/
    repayFee: string;
    /**服务费vat*/
    repayFeeVat: string;
    /**到期日期*/
    expirDate?: string;
    /** 格式化之后还款到期时间 */
    expiryDateNewFormat: string;
    /** 渠道名称 */
    channelName?: UserEnumsSpace.ERepayChannel;
    /** 银行配置图片 */
    bankImage?: string;
  };

  /** 线上还款信息数据类 */
  export type RepayOnlineDataType = {
    /**账号*/
    clabe: string;
    /**待还金额*/
    totalAmount?: string;
    /**服务费*/
    repayFee?: string;
    /**服务费vat*/
    repayFeeVat?: string;
    /** 不同渠道的文案说明 **/
    channelTips?: string;
  };

  /** 可代扣银行卡 */
  export type DirectDebitBank = {
    cardNo: string;
    bankName: string;
    isDirectBebitOpened: 'YES' | 'NO';
  };

  /** 可代扣银行卡信息 */
  export type DirectDebitBankInfo = {
    /** 银行卡列表 */
    cardList: DirectDebitBank[];
    tips: string;
    /** 代扣最小额度 */
    minAmount: number;
  };

  /** 上传图片返回信息数据类 */
  export type ImageUploadDataType = {
    /**图片url*/
    url: string;
    /**图片类型*/
    cardType: string;
  };

  /** ocr 图片识别返回信息 */
  export type ImageOcrDataV7Type = {
    /** 成功 失败 */
    status: 'SUCCEEDED' | 'FAILED' | '';
    /** YES 手动 NO 自动 */
    annul: 'YES' | 'NO' | '';
    /** 父姓 */
    fatherName: string;
    /** 身份证正面 */
    frontFile: string;
    /** 身份证反面 */
    backFile: string;
    /** 用户姓名 */
    name: string;
    /** 母姓 */
    motherName: string;
    /** 有效期 */
    validity: string;
    /** 生日 */
    birthDate: string;

    curp: string;
  };

  /** ocr图片识别返回信息数据类 */
  export type ImageOcrDataType = {
    estatus?: string;
    mensaje?: string;
    /** 类型 */
    tipo: string;
    /** 子类型 */
    subTipo: string;
    /** 发票 */
    folio: string;
    /** 登记 */
    registro: string;
    /** 选民钥匙 */
    claveElector: string;
    curp: string;
    /**（健康）状况 */
    estado: string;
    /** 直辖市 */
    municipio: string;
    /** 地点 */
    localidad: string;
    /** 部分 */
    seccion: string;
    /** 问题 */
    emision: string;
    /** 有效期 */
    vigencia: string;
    /** 姓 */
    primerApellido: string;
    /** 次姓 */
    segundoApellido: string;
    /** 名 */
    nombres: string;
    /** 年龄 */
    edad: string;
    /** 年龄 */
    fechaNacimiento: string;
    /** 性别 */
    sexo: string;
    /** 街道 */
    calle: string;
    /** 市郊 */
    colonia: string;
    /** 城市 */
    ciudad: string;
    /** 验证码 */
    codigoValidacion: string;
    /** 条码 */
    codigoBarras: string;
    /** ocr */
    ocr: string;
    /** 证件拍照类型 */
    cardType: string;
  };

  export type LoanAmountListType = {
    amount: string;
    status: string;
  };

  /** 借款配置数据类 */
  export type LoanConfigDataType = {
    id: string;
    appId?: string;
    /** 用户类型 */
    userType: string;
    /** 金额 */
    amount: string;
    /** 期限 */
    days: string;
    /** 配置状态 */
    status?: string;
    processRate?: number;
    vatRate?: number;
    interestRate?: number;
    collectFee?: number;
    overdueRate?: number;
    overdueDaysCapped?: number;
    createTime?: number;
    updateTime?: number;
  };

  /** banner信息数据类 */
  export type BannerDataType = {
    id: string;
    appId: string;
    /** 图片位置 */
    location: BaseEnumsSpace.EBannerLocationType;
    /** 标题 */
    title: string;
    /** 链接 */
    link: string;
    /** 图片 */
    logo: string;
    /** 状态 */
    status: string;
    /** 排序 */
    sort: string;
    /** 活动类型 */
    activityType: BaseEnumsSpace.EBannerActivityType;
  };

  /** 还款页面展示的提示文案 */
  export type DisbursingShowTipType = {
    waitLoanPage: string[];
    waitApprovePage: string[];
    waitConfirmPage: string[];
  };

  /** 还款页面展示的提示文案 */
  export type RepayShowTipType = {
    repayPage: string[];
  };

  /** 首贷额度列表 */
  export type AmountConfigOnFirstLoanType = {
    /** 金额列表 */
    amountList: string[];
    days: number[]; //可选账期
    /** 选中天数 */
    defaultDays: number;
    /** 能否修改天数 */
    canSelectDays: 'YES' | 'NO' | string;
  };

  /** 试算结果 */
  export interface CalculateCostType {
    /** 本金 */
    loanAmount: string;
    /** 手续费 */
    transferFee: string;
    /** 手续费税*/
    transferFeeVat: string;
    /** 手续费 */
    processFee: string;
    /** 手续费税 */
    processFeeVat: string;
    /** 还款总金额 */
    repaymentAmount: string;
    /** 天数 */
    days: string;
    /** 贷款日期 */
    loanDate: string;
    /** 贷款日期已格式化 */
    loanDateNewFormat: string;
    /** 还款日期 */
    repayDate: string;
    /** 还款日期已格式化 */
    repayDateNewFormat: string;
    /** 券后金额 */
    realAmount: string;
    /** 券后还款总金额 */
    realRepaymentAmount: string;
    /** 额度券金额: 100 */
    creditCouponAmount: string;
    /** 额度券比例: 0.1 */
    creditCouponPercent: string;
    /** 提额劵额度类型amount or percent */
    couponUseType: 'amount' | 'percent';
  }

  /** 试算接口首贷 */
  export interface CalculateCostOnFirstLoanType extends CalculateCostType {
    /** 是否展示还款详情 */
    showApplyCost: 'YES' | 'NO';
  }

  /** 试算接口复贷 */
  export interface CalculateCostOnReLoanType extends CalculateCostType {
    /** 收款卡信息 */
    // cardNo: string;
  }

  /** 首贷用户选择额度直接创建临时申请单 */
  type applyOrderIdType = string;
  export type FirstPreLoanApplyType = applyOrderIdType;

  /** 贷款记录 */
  export type LoanRecordItem = {
    orderId: string;
    realAmount: number;
    loanTime: string;
    repayDate: string;
    productPeriod: number;
    /** 0-未开始， 1-进行中， 2-逾期， 3-结清 */
    status: '0' | '1' | '2' | '3';
  };

  export type CreditRefuseReasonDataType = {
    isRetain: 'YES' | 'NO';
    // 挽留方式 "quota" / "coupon"  (NPP不使用, 都是发券)
    retainMethod: 'quota' | 'coupon';
    // 原始额度 (NPP不使用)
    originalQuota: string;
    // 提额后额度 (NPP不使用)
    currentQuota: string;
    couponAmount: string;
    // 优惠券类型 // DEDUCTION or CREDIT
    couponType: 'DEDUCTION' | 'CREDIT';
  };

  /** 分期 */

  export type ProductType = {
    productList: ProductTypeItem[];
    tips: string;
  };

  /** 产品类型 */
  export type ProductTypeItem = {
    /** 产品名 */
    name: string; //产品名称
    /** 产品图标地址 */
    logoUrl: string; //产品图标地址
    /** 产品类型 FIXED_DATE(固定日期),FIXED_DAY（固定天数）,INSTALLMENT（分期） */
    type: 'FIXED_DAY' | 'FIXED_DATE' | 'INSTALLMENT';
    /** 是否启用 */
    enable: UserEnumsSpace.EStatusType;
    /** 最大天数 */
    minDay: number;
    /** 最小天数 */
    maxDay: number;
    /** 账期数量 */
    minInstallmentCount: number;
    maxInstallmentCount: number;
    /** 是否限量 */
    limitedLogo: UserEnumsSpace.EStatusType;
    /** 产品描述 */
    desc: string; //产品描
  };

  /** 用户选择的首贷额度配置 */
  export type FirstLoanPriceConfigInfoType = {
    productType: EProductType;
    amount: number; //额度
    term: number; //账期8天
    repayDate: string; // 还款日期
  };

  /** 产品定价 */
  export type ProductPriceType = {
    productPriceList: ProductPriceItem[];
    tips: string;
    /** 选中的产品定价，用于回填 */
    selectedProductPrice: {
      productType: EProductType;
      amount: number;
      days: number;
      repayDate: string;
    };
  };

  export type ProductPriceItem = {
    minAmount: number;
    defaultAmount: number;
    installmentCount: number; //分期期限
    increment: number;
    days: number;
    maxAmount: number;
    enable: 'YES' | 'NO';
    productType: EProductType;
  };

  /** 分期试算 */
  export type MultiPeriodCalculateType = {
    loanAmount: string; // 放款总金额
    realRepaymentAmount: string; // 实际应还总金额
    days: number; // 总天数
    loanPeriods: number;
    firstRepayDate: string;
    firstRepaymentAmount: string;
    loanPlan: MultiPeriodCalculateLoanPlan[];
    loanCostTips: string;
  };

  /** 用信页分期试算 */
  export type MultiPeriodCalculateConfirmLoanType = {
    showWithholdBtn: string;
    countdownFlag: string;
    countdown: number;
    loanDate: string; //放款日期
    loanAmount: string; // 放款总金额
    realRepaymentAmount: string; // 实际应还总金额
    days: number; // 总天数
    loanPeriods: number;
    firstRepayDate: string;
    firstRepaymentAmount: string;
    loanPlan: MultiPeriodCalculateLoanPlan[];
    loanTips: string;
    cardNo: string;
    bankName: string;
  };

  export type MultiPeriodCalculateLoanPlan = {
    curPeriodNum: number;
    curPeriodPrincipal: string;
    curPeriodProcessFee: string;
    curPeriodProcessFeeVat: string;
    curPeriodRepayDate: string;
    curPeriodRepaymentAmount: string;
  };
  // 复贷可选日期范围和不可选的节假日
  export type LoanRepayDays = {
    /** 可选日期的开始 */
    startDate: string;
    /** 可选日期的结束 */
    endDate: string;
    /** 不可选的节假日列表 */
    holidays: string[];
  };
}
