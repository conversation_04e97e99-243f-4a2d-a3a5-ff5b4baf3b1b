import { UserEnumsSpace } from '@/enums';
import { EContractConfigActionType, EContractConfigType } from '../enum';
import { Type } from '../../pages/my/verifyBasicInfo/constants/enum';

export namespace UserVOSpace {
  /** 登录相关 */
  export type RequestLoginType = {
    /* 登录类型，sms：短信验证码，pwd则是密码 */
    loginType: string;
    /** 手机号 */
    mobile: string;
    /** 验证码 */
    smsCode: string;
    /** 密码 */
    password: string;
    /** 邀请码 */
    inviteCode: string;
    /** 渠道号 */
    //    channelCode: string;
    /** 子渠道号 */
    //    subChannelCode: string;
    /** afid */
    afId: string;
    /** afCuId */
    afCuId: string;
    /** 安装包 */
    packageName: string;
    /** ip */
    regIp: string;
  };
  export type RequestOtpType = {
    mobile?: string;
    email?: string;
    otpChannel?: UserEnumsSpace.OtpChannel;
    bizType: UserEnumsSpace.BizType;
  };
  /** 短信验证码 */
  export type RequestOtp = {
    mobile: string;
    smsType: string;
  };

  /** opt验证类型 */
  export type RequestVerifyOtp = {
    mobile: string;
    smsCode: string;
  };

  /** 修改登录手机号 */
  export type RequestModifyMobileType = {
    /** 人脸url */
    faceUrl: string;
    /** 新手机号 */
    mobile: string;
    /** 验证码 */
    smsCode: string;
    /** 原登录密码*/
    password: string;
    /** 渠道号 */
    //    channelCode: string;
    /** 子渠道号 */
    //    subChannelCode: string;
  };

  /** 用户评论 */
  export type RequestSubmitCommentType = {
    score: string;
    comment: string;
    commentStage: string;
  };

  /**
   * @type app版本信息数据类
   * @field name 产品
   * @field content 内容
   * @field gpVersion 线上版本号
   * @field linkUrl 更新链接
   * @field isFocus 是否强制更新
   **/
  export type AppVersionDataType = {
    name: string;
    content: string;
    gpVersion: string;
    linkUrl: string;
    isFocus: string;
  };

  /**
   * @type app配置信息数据类
   **/
  export type AppConfigDataType = {
    /** 复贷otp开关 */
    reLoanOTPSwitch: UserEnumsSpace.EStatusType;
    /** 人脸识别最大失败重试 */
    faceMaxErrCount: number;
    /** 语音验证码开关 */
    voiceSmsSwitchStatus: UserEnumsSpace.EStatusType;
    /** 增信功能开关 */
    enhanceCreditSwitch: UserEnumsSpace.EStatusType;
    /** 教育程度开关 */
    educationLevelSwitch: UserEnumsSpace.EStatusType;
    /** 工作类型开关 */
    jobTypeSwitch: UserEnumsSpace.EStatusType;
    /** 婚姻状态开关 */
    maritalStatusSwitch: UserEnumsSpace.EStatusType;
    /** 孩子数量开关 */
    childNumSwitch: UserEnumsSpace.EStatusType;
    /** 是否有未还款的贷款订单开关 */
    cashLoanedSwitch: UserEnumsSpace.EStatusType;
    /** 贷款用途开关 */
    loanUseSwitch: UserEnumsSpace.EStatusType;
    /** 薪资周期开关 */
    salaryPeriodSwitch: UserEnumsSpace.EStatusType;
    /** 是否开启复贷手机号修改开关 */
    oldUserMobileExpiredRemindSwitch: UserEnumsSpace.EStatusType;
    /** faceid活体 */
    liveChannel: 'ADVANCEAI' | 'FACEID';
    /** 是否采集埋点上报错误开关 */
    openRecordBuryPointErrorLogs: UserEnumsSpace.EStatusType;
    /** 复贷二单增信问题  */
    reLoanQuestionnaireSwitch: UserEnumsSpace.EStatusType;
    /** 跳过确认用信开关 */
    skipConfirmAcceptCreditSwitch: UserEnumsSpace.EStatusType;
    /** 启动页h5开关 */
    splashH5Switch: UserEnumsSpace.EStatusType;
    /** 等待审核通知权限申请开关 */
    waitAuditPush: UserEnumsSpace.EStatusType;
    /** 启动页h5页面配置 */
    splashH5Link: string;
    /** 进件页自动填写跳过开关 */
    incomeInputAutoSkipSwitch: UserEnumsSpace.EStatusType;
    /** 新户热更新强制更新状态开关 */
    newUserHotUpdateDisplaySwitch: UserEnumsSpace.EStatusType;
    /** 老户热更新强制更新状态开关 */
    oldUserHotUpdateDisplaySwitch: UserEnumsSpace.EStatusType;
    /** vip 功能开关 */
    vipSwitch: UserEnumsSpace.EStatusType;
    /** 代扣功能开关 */
    withholdSwitch: UserEnumsSpace.EStatusType;
    /** CLABE填写页(首贷和补卡) 开通代扣默认是否选中 */
    clabePage: UserEnumsSpace.EStatusType;
    /** 首贷合同确认页 开通代扣默认是否选中 */
    firstLoanConfirmationPage: UserEnumsSpace.EStatusType;
    /** 复贷申请首页 开通代扣默认是否选中 */
    reLoanHomePage: UserEnumsSpace.EStatusType;
    // /** 发薪日还款开关 */
    // salaryDaySwitch: 'YES';
    /** 分期相关配置 */
    installmentSwitch: UserEnumsSpace.EStatusType;
    /** 首贷额度选择页滑动选择方式的开关 */
    firstLoanSlideSelectAmountSwitch: UserEnumsSpace.EStatusType;
    /** 线下最小还款金额 */
    offlineRpeayChannelMinAmount: string;
    /** 复贷选择还款日 */
    repayDateSwitch: UserEnumsSpace.EStatusType;
    /** 基础信息发薪日还款开关 */
    nextSalaryDateSwitch: UserEnumsSpace.EStatusType;
  };

  /**
   * @type 登录信息数据类
   * @field token
   * @field userId 注册登录返回的用户类型：signup注册，其它为登录
   * @field registerFlag 校验手机是否注册：REGISTER 去注册 LOGIN 去登录 SETPWD 去设置密码
   * @field loginType 登录类型：密码，验证码
   **/
  export type LoginDataType = {
    token: string;
    userId: string;
    type: string;
    registerFlag: string;
    loginType: string;
  };

  export type ContractConfigItemType = {
    type: EContractConfigType; // PHONE, WHATSAPP,EMAIL
    title: string;
    value: string;
    actionType: EContractConfigActionType; // CALL, COPY
  };

  /**
   * @type
   * @field website 官网
   * @field appName app名称
   * @field mobile 手机号
   * @field tips 提示
   * @field hotIds 审核账号列表
   */
  export type ContactConfigDataType = {
    website: string;
    appName: string;
    tips: string;
    hotIds?: ContactConfigHotIdDataType[];
    contactList: ContractConfigItemType[];
    mobile?: string;
  };

  /** mobile数据类 */
  export type ContactConfigDisplayDataType = {
    /** 联系方式 */
    contact: string;
    /** 描述 */
    detail: string;
    /** 类型 0:mobile 2:email*/
    type: number;
  };

  /** 联系配置信息数据类 */
  export type ContactConfigHotIdDataType = {
    /** 审核手机号 */
    hotAcc: string;
    /** 审核token */
    hotTck: string;
    /** 审核user_id */
    hotUid: string;
    /** */
    accTk: ContactConfigGHotIdDataType[];
  };

  export type ContactConfigGHotIdDataType = {
    hotAcc: string;
  };

  /**
   * @type app版本信息数据类
   **/
  export type BackUserStateType = {
    /** 基本信息：NO未做，YES已做 */
    basic: UserEnumsSpace.EStatusType;
    /** 选择额度状态，YES已经选择了，NO还没有选择额度 */
    preApplyStatus: UserEnumsSpace.EStatusType;
    /** 是否直接放款，YES表示直接放款，NO表示没有直接放款 */
    directLending: UserEnumsSpace.EStatusType;
    /** 预贷款订单id */
    preApplyOrderId: string;
    /** 工作信息：NO未做，YES已做 */
    professional: UserEnumsSpace.EStatusType;
    /** 联系人信息：NO未做，YES已做 */
    contact: UserEnumsSpace.EStatusType;
    rfc: string;
    /** 人脸信息：NO未做，YES已做 */
    face: UserEnumsSpace.EStatusType;
    /** curp信息：NO未做，YES已做 */
    curp: UserEnumsSpace.EStatusType;
    /** 绑卡信息：NO未做，YES已做 */
    bindCard: UserEnumsSpace.EStatusType;
    /** 申请状态 */
    applyStatus: UserEnumsSpace.EApply;
    /** 订单状态 */
    orderStatus: UserEnumsSpace.EOrder;
    /** 用户姓名 */
    name: string;
    /** 是否需要签约 */
    isNeedSignUp: UserEnumsSpace.EStatusType;
    /** 用户类型 */
    userType: UserEnumsSpace.EUserType;
    /** 是否可以重传认证信息，新客可以，老客不行 */
    updInfo: UserEnumsSpace.EStatusType;
    /** 版本信息 */
    gpVersion: string;
    /** 是否强制更新 */
    focus: UserEnumsSpace.EStatusType;
    /** 谷歌绑定状态 */
    gpBind: UserEnumsSpace.EStatusType;
    /** 更新链接 */
    link: string;
    /** 更新内容 */
    appUpdatesContent?: string;
    /** 更新内容 */
    acceptCreditStatus?: string;
    /** 预贷款订单id */
    applyOrderId?: string;
    /** 是否锁定 */
    locked: string;
    /** 用户进件状态的锁定原因 */
    lockMessage: string[];
    /** 解锁的时间描述 */
    unlockTime: string;
    /** 是否可以解锁 */
    allowApply: string;
    additional: UserEnumsSpace.EStatusType;
    isLoan: UserEnumsSpace.EStatusType;
    /** 自拍照：NO未做，YES已做 */
    selfie: UserEnumsSpace.EStatusType;
    /** 新户确认用信之后。人脸识别的状态 */
    useCreditStatus: string;
    /** 人脸超时 */
    faceExpired: string;
    /** 人脸超时消息 */
    faceExpiredMessage: string[];
    /** otp 补件 */
    otp: string;
    /** 是否需要验证otp 15天是否调用过 */
    otpVerify: string;
    /** 当前贷款单数 */
    loanCount: number;
    /**@deprecated 使用questionGroupQa 是否需要填写问卷 YES代表需要填写, NO代表不需要填写; qa等价于问卷是否填写且风控是否需要填写 */
    qa: string;
    /** 补件类型 */
    supplyType: UserEnumsSpace.ESupplyType;
    /** 系统繁忙场景 */
    maintenanceInfo: {
      message: string;
      maintenance: UserEnumsSpace.EMaintenanceType;
      enabled: UserEnumsSpace.EStatusType;
    }[];
    /** 配置 */
    appConfig: UserVOSpace.AppConfigDataType;
    /** 是否需要代扣授权 */
    requireWithholdAuthorize: UserEnumsSpace.EStatusType;
    /**@deprecated 分控问券需要做,replacce with questionGroupQa*/
    rkQa: UserEnumsSpace.EStatusType;
    /** 是否已选择额度 */
    selectProductPrice: UserEnumsSpace.EStatusType;
    /** 首复贷问卷配置 */
    questionGroupQa: UserEnumsSpace.EStatusType;
    /** 产品类型 */
    productType: 'FIXED_DAY' | 'FIXED_DATE' | 'INSTALLMENT'; // FIXED_DAY(固定天数)/FIXED_DATE（固定日期）/INSTALLMENT(分期)
    /** vip 配置 */
    vip: {
      /** vip 等级 */
      level: number;
      /** vip 返息率 */
      rate: string;
      /** 最大 vip 返现率  */
      maxRate: string;
      /** 会员等级状态 NORMAL:正常 RECOVERY:恢复期 CLEAR:清空 */
      status: 'NORMAL' | 'RECOVERY' | 'CLEAR';
      /** 当banner的状态 */
      displayStatus: 'NORMAL' | 'MAX' | 'RECOVERY';
      /** 当前vip等级 */
      upgradeCount: number;
    };
    /** 展期状态 WAIT_PAY */
    extStatus: string;
    /** 是否开通过代扣，可以判断是否为首次开通 */
    withholdState: UserEnumsSpace.EStatusType;
  };

  /**
   * @type app版本信息数据类
   * @field content
   * 内容
   * @field type
   * 类型
   * @field parentId
   * parentId
   * @field id
   * id
   * @field seq
   * seq
   * @field question
   * 问题
   * @field answer
   * 答案
   * @field isExtend
   * 是否展开-自定义
   * @field category
   * 分类-自定义
   **/
  export type FaqDataType = {
    content: string;
    type: string;
    parentId?: number;
    id: number;
    seq: number;
    question: string;
    answer?: string;
    isExtend: boolean;
    category?: string;
  };

  /**
   * @type 设备信息类
   */
  export type DeviceType = {
    appId: string;
    area: string;
    brand: string;
    cpu: string;
    deviceName: string;
    frontCameraPixel: string;
    imei: string;
    ip: string;
    laguage: string;
    mac: string;
    manufacturer: string;
    networkData: string;
    networkEnvironment: string;
    phoneModel: string;
    product: string;
    ram: string;
    rearCameraPixels: string;
    rom: string;
    screenHeight: string;
    screenWidth: string;
    sysVersion: string;
    token: string;
    version: string;
  };

  /**
   * @type 第三方账户绑定状态
   */
  export type Account3rdBindingStateData = {
    FACEBOOK: UserEnumsSpace.EStatusType;
    GOOGLE: UserEnumsSpace.EStatusType;
  };

  /**
   * @type 关联第三方账户
   */
  export type Account3rdBindingData = {
    oauthType: 'FACEBOOK' | 'GOOGLE';
    /** 在facebook上。是userId gmail 没有 */
    openId?: string;
    /** 在facebook 上是accessToken 在 gmail 上是 idToken */
    accessToken: string;
  };

  /**
   * 未读消息
   */
  export type UnReadMessageCount = {
    /** 总共的未读消息 */
    total: number;
    /** 通知类消息未读的数量 */
    message: number;
    /** 活动消息的未读数量 */
    activity: number;
  };

  /** 获取用户的消息列表，已读用户的消息 */
  export type RequestMessageType = {
    /**
     * 消息类型 通知消息，活动消息
     */
    messageType: 'MESSAGE' | 'ACTIVITY';
  };

  /** 消息类型 */
  export type MessageListItem = {
    id: number;
    /** 消息标题 */
    title: string;
    /** 已读状态 */
    readFlag: 'YES' | 'NO';
    /** 内容 */
    body: string;
    /** 发送时间 */
    createTime: string;
  };
  /** 公共部分 */
  export interface BaseCouponItem {
    /** 优惠券名称 */
    name: string;
    /** 优惠券类型 */
    type: UserEnumsSpace.ECouponsType;
    /** 金额 */
    amount: string;
    /** 优惠券来源 */
    distributeScene: UserEnumsSpace.ECouponsDistributeSceneStatus;
    /** 优惠券状态 */
    couponStatus: UserEnumsSpace.ECouponsStatus;
    /** 是否可以叠加 */
    canStack: 'YES' | 'NO';
    /** 描述 */
    description: string | null;
    /** 区分是否是% */
    useType: 'amount' | 'percent';
  }
  /** 优惠券 */
  export interface CouponsItem extends BaseCouponItem {
    /** 优惠券唯一编号 */
    serialNumber: string;
    /** 范围 */
    scope: 'ALL' | 'FIRST_LOAN' | 'RE_LOAN';
    /** 最终有效期 */
    expirationDate: string;
    /** 是否可以选择 */
    canCheck: 'YES' | 'NO';
  }
  /** 兑换优惠券Item */
  export interface RedeemCouponItem extends BaseCouponItem {
    configId: number;
    grantMethod: 'amount' | 'percent';
    /** 有效天数 */
    validDays: number;
    /** 满足最小金额才能使用 */
    minAmount: number;
    /** 兑换量 */
    redeemQuantity: number;
    /** 使用范围 */
    effectiveScope: 'ALL' | 'FIRST_LOAN' | 'RE_LOAN';
  }
  /** 兑换优惠券 */
  export type RedeemCoupons = {
    redeemableCoupons: RedeemCouponItem[];
    balance: string;
  };
  /** 获取邀请code */
  export type InviteUrlType = {
    /** adjust url */
    inviteUrl: string;
    /** adjust code */
    inviteCode: string;
    /** 优惠券奖励金额 */
    couponRewardAmount: string;
  };

  /** 活动资格 */
  export type HasQualificationType = {
    /** 是否有活动资格 */
    hasQualification: boolean;
  };

  /** 当前日期是否在活动期间 */
  export type InviteDuringType = {
    /** 是否在活动中 */
    isDuring: boolean;
    /** 开始时间 */
    startTime: string;
    /** 关闭时间 */
    endTime: string;
    /** 单次奖励金额 */
    rewardAmount: string;
  };

  /** 当前日期是否在活动期间 */
  export type InviteRuleType = {
    /** 规则内容 */
    ruleContent: string;
  };

  /** 卡包信息 */
  export type WalletInfo = {
    /** 拉新余额 */
    balance: number;
    /** 拉新金额累计 */
    rewardAmount: number;
    /** 拉新邀请人数累计 */
    invitedCount: number;
    /** 提现扣除的手续费 */
    withDrawFee: number;
    /** 最小提现额度(NP提现输入框使用) */
    withDrawMinAmount: number;
    /** 提现最小倍数(NC、QP提现输入框使用) */
    withDrawMinMultiple: number;
    /** 用户的优惠券 */
    userCoupon?: CouponsItem;
    /** 钱包能够提现的最小额度 */
    canWithDrawMinAmount: number;
    /** 钱包总余额 = 邀新余额 + 返现余额 */
    totalBalance: number;
    /** 返现余额 */
    cashbackBalance: number;
    /** 总共返现金额 */
    totalCashbackAmount: number;
    /** 总共返现笔数 */
    totalCashBackCount: number;
    /** 优惠券兑换入口文案 */
    redeemCouponsTitle: string;
  };

  export type WalletFlowData = {
    index: number;
    rows: WalletFlowDataItem[];
  };

  export type WalletFlowDataItem = {
    /** 流水ID **/
    flowId: string;
    /** 金额 */
    totalAmount: string;
    /** 类型 "1" 代表收入 "2" 代表支出 **/
    side: string;
    /** 流水生成时间 **/
    createTime: string;
    /** 流水类型 */
    flowTitle: string;
    /** 交易时间 */
    transactionTime: string;
  };

  /** vip配置规则 */
  export type VipRuleType = {
    levelRules: LevelRules;
  };

  interface LevelRule {
    orderSequence: string;
    rate: string;
  }

  interface LevelRules {
    [key: string]: LevelRule; // 键是字符串，值是 LevelRule 对象
  }

  /** VIP 弹窗数据 */
  export type VipPopDataItem = {
    /** 弹窗提示类型 */
    type: 'UPGRADE' | 'VIP0_LAUNCHED' | 'VIP1_LAUNCHED' | 'MILD_OVERDUE_LAUNCHED';
    /** 当前vip等级 */
    level: number;
    /** VIP利息减免比例 */
    rate: string;
  };

  /** 用户VIP详情 */
  export type VIPInfoData = {
    status: 'NORMAL' | 'RECOVERY' | 'CLEAR'; // 会员等级状态 NORMAL:正常 :恢复期 CLEAR:清空
    level: number; // 用户会员等级
    /** 有效还款笔数 */
    effectiveCount: number;
    /** 返现比例 */
    rate: string;
    /** 升级所需单数 */
    upgradeCount: number;
    /** 还需几笔按时还款恢复正常状态(待按时还款) 注：若status为NORMAL,该字段为空 */
    recoverCount: number;
    /** 恢复期中已按时还款几笔（按时还款） 注：若status为NORMAL,该字段为空 */
    recoverSettleCount: number;
    /** 下一等级返现比例 注：若没有下一等级，该字段为空 */
    nextRate: string;
  };

  /** 用户VIP详情 */
  export type ExcessPaymentCostData = {
    isWithdraw: 'YES' | 'NO'; // 是否可以提款
    modalType: string; // 弹窗类型
    excessRepayBalance: string; // 提款总金额
    /** 提款手续费 */
    withdrawFee: string;
    /** 用户收到金额 */
    withdrawAmount: string;
    /** 提款卡号 */
    cardNo: string;
    /** 银行名 */
    bankName: string;
  };
  /** 验证用户是否存在 */
  export type ValidateUserExist = {
    currentUserId: string;
    email: string;
    validateChannels: Type[];
  };
}
