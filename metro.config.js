const {getDefaultConfig, mergeConfig} = require('@react-native/metro-config');

/**
 * Metro configuration
 * https://reactnative.dev/docs/metro
 *
 * @type {import('metro-config').MetroConfig}
 */
const config = {
  transformer: {
            minifierPath: 'metro-minify-obfuscator',
            minifierConfig: {
                // filter: (filename) => true,
                includeNodeModules: true,
                trace: false,
                obfuscatorOptions: {
                    "stringArray": false,
                    "compact": true,
                    "controlFlowFlattening": true,
                    "controlFlowFlatteningThreshold": 0.75,
                    "identifierNamesGenerator": "hexadecimal",
                    "numbersToExpressions": true,
                    "splitStrings": true,
                    "splitStringsChunkLength": 3,
                    "transformObjectKeys": true,
                    "simplify": true,
                    "disableConsoleOutput": true,
                    "log": false,
                    "selfDefending": true,
                    "unicodeEscapeSequence": true
                }
            },
            getTransformOptions: async() => ({
                transform: {
                    experimentalImportSupport: false,
                    inlineRequires: true,
                },
            }),
        },
};

module.exports = mergeConfig(getDefaultConfig(__dirname), config);
