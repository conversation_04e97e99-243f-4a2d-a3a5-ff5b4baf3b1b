{"name": "noro", "version": "0.0.1", "private": true, "license": "./LICENSE", "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start --reset-cache", "test": "jest", "postinstall": "patch-package", "format": "prettier --write .", "android:test_debug": "./android/gradlew assembleNoropresta_testDebug", "android:test_release": "./android/gradlew assembleNoropresta_testRelease", "build:bundle": "react-native bundle --entry-file ./index.bundle.js --bundle-output ./index.android.bundle", "build:releaseBundle": "react-native bundle --platform android --entry-file index.js --bundle-output ./index.android.bundle --assets-dest ./bundles --dev false", "tsc": "tsc --skip<PERSON><PERSON><PERSON><PERSON><PERSON> --noEmit", "test:auto": "adb shell monkey -p com.credit.prestamos.rapido.cash.efectivo.nero --pct-touch 40 --pct-motion 25 --pct-appswitch 0 --pct-rotation 5 -s 12358 --throttle 400 --ignore-crashes --ignore-timeouts -v 500000"}, "husky": {"hooks": {"pre-push": "yarn tsc"}}, "dependencies": {"@miblanchard/react-native-slider": "2.6.0", "@react-native-clipboard/clipboard": "1.14.1", "@react-native-community/cli": "13.6.9", "@react-native-community/datetimepicker": "3.5.2", "@react-native-community/slider": "4.5.2", "@react-native-firebase/analytics": "21.0.0", "@react-native-firebase/app": "21.0.0", "@react-native-firebase/messaging": "21.0.0", "@react-native-google-signin/google-signin": "11.0.0", "@react-navigation/bottom-tabs": "6.6.0", "@react-navigation/core": "6.4.16", "@react-navigation/native": "6.1.17", "@react-navigation/native-stack": "6.10.0", "@tanstack/react-query": "4.29.12", "ahooks": "3.8.4", "appcenter": "5.0.1", "appcenter-analytics": "5.0.1", "appcenter-crashes": "5.0.1", "crypto-js": "4.2.0", "fbjs": "3.0.5", "hermes-engine": "0.11.0", "husky": "9.0.11", "i18next": "23.4.1", "jsc-android": "250231.0.0", "lint-staged": "13.2.2", "lodash": "4.17.21", "madge": "6.1.0", "react": "18.2.0", "react-i18next": "13.0.3", "react-native": "0.74.3", "react-native-adjust": "4.38.1", "react-native-atoz-list": "1.0.5", "react-native-background-timer": "2.4.1 ", "react-native-calendars": "1.1312.1", "react-native-dashed-line": "1.1.0", "react-native-element-dropdown": "2.12.1", "react-native-fs": "2.20.0", "react-native-image-picker": "7.1.2", "react-native-input-scroll-view": "1.11.0", "react-native-linear-gradient": "2.8.3", "react-native-localize": "3.2.0", "react-native-logs": "5.0.1", "react-native-markdown-display": "7.0.0-alpha.2", "react-native-md5": "1.0.0", "react-native-mmkv-storage": "0.9.1", "react-native-modal": "13.0.1", "react-native-pager-view": "6.3.3", "react-native-performance-stats": "0.2.3", "react-native-permissions": "4.1.5", "react-native-quick-sqlite": "8.1.0", "react-native-reanimated": "3.14.0", "react-native-render-html": "6.3.4", "react-native-safe-area-context": "4.10.8", "react-native-screens": "4.11.1", "react-native-select-dropdown": "3.3.4", "react-native-splash-screen": "3.3.0", "react-native-svg": "15.6.0", "react-native-swiper": "1.6.0", "react-native-ui-datepicker": "3.1.2", "react-native-uuid": "2.0.2", "react-native-video": "6.14.1", "react-native-vision-camera": "4.6.4", "react-native-webview": "13.10.5", "react-native-webview-autoheight": "1.0.6", "react-native-wheel-picker-android": "2.0.6", "rxjs": "7.8.1", "sqlstring": "2.3.3", "tinycolor2": "1.6.0", "whatwg-fetch": "3.6.20"}, "resolutions": {"@react-native-clipboard/clipboard": "1.14.1", "@react-native-community/cli": "13.6.9", "@react-native-community/slider": "4.5.2", "@react-navigation/bottom-tabs": "6.6.0", "@react-navigation/core": "6.4.16", "@react-navigation/native": "6.1.17", "@react-navigation/native-stack": "6.10.0", "lodash": "4.17.21", "react": "18.2.0", "react-native": "0.74.3", "react-native-adjust": "4.38.1", "react-native-dashed-line": "1.1.0", "react-native-element-dropdown": "2.12.1", "react-native-fs": "2.20.0", "react-native-image-picker": "7.1.2", "react-native-linear-gradient": "2.8.3", "react-native-localize": "3.2.0", "react-native-logs": "5.0.1", "react-native-md5": "1.0.0", "react-native-mmkv-storage": "0.9.1", "react-native-modal": "13.0.1", "react-native-pager-view": "6.3.3", "react-native-performance-stats": "0.2.3", "react-native-permissions": "4.1.5", "react-native-quick-sqlite": "8.1.0", "react-native-reanimated": "3.14.0", "react-native-render-html": "6.3.4", "react-native-safe-area-context": "4.10.8", "react-native-screens": "4.11.1", "react-native-splash-screen": "3.3.0", "react-native-webview": "13.10.5", "react-native-wheel-picker-android": "2.0.6", "rxjs": "7.8.1", "sqlstring": "2.3.3", "whatwg-fetch": "3.6.20", "@react-native-community/datetimepicker": "3.5.2", "@react-native-firebase/analytics": "21.0.0", "@react-native-firebase/app": "21.0.0", "@react-native-firebase/messaging": "21.0.0", "@react-native-google-signin/google-signin": "11.0.0", "@tanstack/react-query": "4.29.12", "appcenter": "5.0.1", "appcenter-analytics": "5.0.1", "appcenter-crashes": "5.0.1", "crypto-js": "4.2.0", "fbjs": "3.0.5", "hermes-engine": "0.11.0", "husky": "9.0.11", "i18next": "23.4.1", "jsc-android": "250231.0.0", "lint-staged": "13.2.2", "madge": "6.1.0", "react-i18next": "13.0.3", "react-native-atoz-list": "1.0.5", "react-native-input-scroll-view": "1.11.0", "react-native-markdown-display": "7.0.0-alpha.2", "react-native-select-dropdown": "3.3.4", "react-native-svg": "15.6.0", "react-native-swiper": "1.6.0", "react-native-uuid": "2.0.2", "react-native-video": "6.14.1", "tinycolor2": "1.6.0"}, "devDependencies": {"@babel/core": "7.22.5", "@babel/plugin-proposal-class-properties": "7.13.0", "@babel/plugin-proposal-decorators": "7.22.7", "@babel/plugin-proposal-nullish-coalescing-operator": "7.18.6", "@babel/plugin-proposal-optional-chaining": "7.21.0", "@babel/plugin-transform-optional-chaining": "7.22.6", "@babel/preset-env": "7.22.5", "@babel/runtime": "7.22.5", "@react-native/babel-preset": "0.74.85", "@react-native/eslint-config": "0.74.85", "@react-native/metro-config": "0.74.85", "@react-native/typescript-config": "0.74.85", "@tsconfig/react-native": "2.0.2", "@types/lodash": "4.14.194", "@types/node": "20.4.0", "@types/react-native": "0.73.0", "@types/react-native-background-timer": "2.0.2", "@types/react-native-i18n": "2.0.0", "@types/react-test-renderer": "18.0.0", "@types/sqlstring": "2.3.0", "@types/tinycolor2": "1.4.6", "babel-cli": "6.26.0", "babel-plugin-module-resolver": "5.0.0", "dependency-cruiser": "13.1.5", "eslint": "8.19.0", "metro-minify-obfuscator": "1.0.1", "metro-minify-uglify": "0.76.8", "metro-react-native-babel-preset": "0.73.9", "patch-package": "8.0.0", "postinstall-postinstall": "2.1.0", "prettier": "2.8.8", "react-test-renderer": "18.2.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}