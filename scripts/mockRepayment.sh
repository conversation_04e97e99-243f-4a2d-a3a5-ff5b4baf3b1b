#!/bin/bash

# 测试环境, 自动还款脚本
# bash mockRepayment.sh

id='1230987676712312'
remindRepayAmount='641.36'
clabe='646180354800003127'

# Setp3: 模拟还款接口
curl --location --request POST "http://************:12001/neroc/inter/prod/api/v1/stp/clabeWebhok" \
--header 'Content-Type: application/json' \
--data-raw '{
    "id": "'$id'",
    "fechaOperacion": 20230712,
    "institucionOrdenante": 40072,
    "institucionBeneficiaria": 90646,
    "claveRastreo": "3843MAP4202307122400143051",
    "monto": "'$remindRepayAmount'",
    "nombreOrdenante": "GERARDO ANDRES CHAN ESPADAS",
    "tipoCuentaOrdenante": 40,
    "cuentaOrdenante": "072691012030034293",
    "rfcCurpOrdenante": "CAEG980713LK5",
    "nombreBeneficiario": "NO INGRESADO",
    "tipoCuentaBeneficiario": 40,
    "cuentaBeneficiario": "'$clabe'",
    "nombreBeneficiario2": "null",
    "tipoCuentaBeneficiario2": 0,
    "cuentaBeneficiario2": "null",
    "rfcCurpBeneficiario": "No capturado",
    "conceptoPago": "Noro",
    "referenciaNumerica": 230921,
    "empresa": "NUBE_TECNOLOGIA",
    "tipoPago": 1,
    "tsLiquidacion": "1689153586512",
    "folioCodi": ""
}'

