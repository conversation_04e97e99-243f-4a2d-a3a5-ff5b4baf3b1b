# 检查 assetIcons.tsx 中定义的图标是否在源代码中被使用
import os
import re
from typing import Set


def parse_asset_icons(file_path: str) -> Set[str]:
    """解析 assetIcons.tsx 文件，获取所有图标的 key"""
    icon_keys = set()
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
        print("\n开始解析 assetIcons.tsx 文件...")
        
        # 首先提取整个 Images 对象
        images_pattern = r'export\s+const\s+Images\s*=\s*{([\s\S]*?)};'
        images_match = re.search(images_pattern, content)
        
        if images_match:
            images_content = images_match.group(1)
            # 使用更可靠的方式匹配每个图标定义
            # 匹配形如 _iconName: { ... } 的模式，处理多行和注释
            icon_pattern = r'(_\w+)\s*:\s*{'
            keys = re.findall(icon_pattern, images_content)
            
            print(f"找到 {len(keys)} 个图标键:")
            for key in keys:
                icon_keys.add(key)
        else:
            print("未找到 Images 对象定义")
            
    return icon_keys

def find_icon_usages(src_dir: str, icon_keys: Set[str]) -> Set[str]:
    """在源代码中查找图标的使用情况"""
    used_icons = set()
    total_files = 0
    
    for root, _, files in os.walk(src_dir):
        for file in files:
            if file.endswith(('.ts', '.tsx')):
                total_files += 1
                file_path = os.path.join(root, file)
                with open(file_path, 'r', encoding='utf-8') as f:
                    try:
                        content = f.read()
                        
                        # 检查 ImageNames.xxx 的使用
                        image_names_pattern = r'ImageNames\.(_\w+)'
                        matches = re.findall(image_names_pattern, content)
                        if matches:
                            # print("找到 ImageNames.xxx 形式的使用:")
                            # for match in matches:
                            #     print(f"  - {match}")
                            used_icons.update(matches)
                        
                        # 检查字符串形式的使用 "xxx"或''形式
                        string_pattern = r'[\'"](_[\w]+)[\'"]'
                        string_matches = re.findall(string_pattern, content)
                        if string_matches:
                          # print(f"找到 name=\"xxx\" 形式的使用 ({string_matches} 个):")
                          used_icons.update(string_matches)
                            # print("找到 name=\"xxx\" 形式的使用:")
                            # for match in string_matches:
                            #     if match in icon_keys:
                            #         print(f"  - {match}")
                            #         used_icons.add(match)
                                
                        # 检查解构赋值
                        destructure_pattern = r'const\s*{\s*([^}]*)\s*}\s*=\s*ImageNames'
                        matches = re.findall(destructure_pattern, content)
                        if matches:
                            print("找到解构赋值形式的使用:")
                            for match in matches:
                                keys = re.findall(r'_\w+', match)
                                for key in keys:
                                    print(f"  - {key}")
                                used_icons.update(keys)
                            
                    except Exception as e:
                        print(f"解析文件失败: {file_path}")
                        print(f"错误信息: {str(e)}")
    
    print(f"\n总共扫描了 {total_files} 个 .ts/.tsx 文件")
    return used_icons

def main():
    # 项目路径配置
    project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    asset_icons_path = os.path.join(project_root, 'src', 'config', 'assetIcons.tsx')
    src_dir = os.path.join(project_root, 'src')
    
    # 获取所有图标
    print("正在解析 assetIcons.tsx...")
    icon_keys = parse_asset_icons(asset_icons_path)
    
    # 查找使用情况
    print("\n正在扫描源代码中的图标使用情况...")
    used_icons = find_icon_usages(src_dir, icon_keys)

    print(f"找到 {len(icon_keys)} 个图标定义")
    print(f"找到 {len(used_icons)} 个被使用的图标")
    
    # 找出未使用的图标
    unused_icons = icon_keys - used_icons
    print(f"\n总计 {len(unused_icons)} 个未使用的图标")

    # 输出结果
    print("\n未使用的图标:")
    for icon in sorted(unused_icons):
        print(f"- {icon}")
        

if __name__ == '__main__':
    main()