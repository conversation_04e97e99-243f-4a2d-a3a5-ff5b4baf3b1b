{"extends": "@react-native/typescript-config/tsconfig.json", "compilerOptions": {"target": "esnext", "module": "Node16", "types": ["react-native"], "lib": ["es2020"], "declaration": true, "declarationDir": "dist", "allowJs": true, "jsx": "react-native", "noEmit": true, "isolatedModules": true, "strict": true, "moduleResolution": "node16", "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "esModuleInterop": true, "skipLibCheck": true, "baseUrl": ".", "paths": {"@/baseConfig": ["./src/baseConfig.ts"], "@/utils": ["./src/utils/_export_.ts"], "@/types": ["./src/types/_export_.ts"], "@/enums": ["./src/enums/_export_.ts"], "@/server": ["./src/server/_export_.ts"], "@/routes": ["./src/routes/_export_.ts"], "@/pages": ["./src/pages/_export_.ts"], "@/managers": ["src/managers/_export_.ts"], "@/localStorage": ["./src/localStorage/_export_.ts"], "@/modals": ["./src/modals/_export_.ts"], "@/themes": ["src/themes/_export_.ts"], "@/hooks": ["./src/hooks/_export_.ts"], "@/config": ["./src/config/_export_.tsx"], "@/i18n": ["./src/i18n/_export_.ts"], "@/assets": ["./src/assets"], "@/image": ["./src/assets/NoroPrestaPlus/image"], "@/components": ["./src/components/_export_.ts"], "@/nativeComponents": ["./src/nativeComponents/_export_.ts"], "@/dataModel": ["./src/dataModel/_export_.ts"], "@/trackEvent": ["./src/trackEvent/_export_.ts"], "@/native": ["./src/native/_export_.ts"]}}, "exclude": ["node_modules", "babel.config.js", "metro.config.js", "jest.config.js"]}