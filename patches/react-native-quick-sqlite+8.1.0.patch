diff --git a/node_modules/react-native-quick-sqlite/android/build.gradle b/node_modules/react-native-quick-sqlite/android/build.gradle
index afcda02..70bde2f 100644
--- a/node_modules/react-native-quick-sqlite/android/build.gradle
+++ b/node_modules/react-native-quick-sqlite/android/build.gradle
@@ -63,7 +63,7 @@ android {
   }
 
   defaultConfig {
-    minSdkVersion 21 
+    minSdkVersion safeExtGet('minSdkVersion', 23)
     targetSdkVersion safeExtGet('targetSdkVersion', 28)
     versionCode 1
     versionName "1.0"
diff --git a/node_modules/react-native-quick-sqlite/android/src/main/java/com/reactnativequicksqlite/SequelModule.java b/node_modules/react-native-quick-sqlite/android/src/main/java/com/reactnativequicksqlite/SequelModule.java
index a651a90..21b9105 100644
--- a/node_modules/react-native-quick-sqlite/android/src/main/java/com/reactnativequicksqlite/SequelModule.java
+++ b/node_modules/react-native-quick-sqlite/android/src/main/java/com/reactnativequicksqlite/SequelModule.java
@@ -36,7 +36,8 @@ class SequelModule extends ReactContextBaseJavaModule {
   }
 
   @Override
-  public void onCatalystInstanceDestroy() {
+  public void invalidate() {
+    super.invalidate();
     try {
       QuickSQLiteBridge.instance.clearState();
     } catch (Exception exception) {
