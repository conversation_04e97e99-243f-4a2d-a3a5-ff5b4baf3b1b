diff --git a/node_modules/sqlstring/lib/SqlString.js b/node_modules/sqlstring/lib/SqlString.js
index 8206dad..7b6c855 100644
--- a/node_modules/sqlstring/lib/SqlString.js
+++ b/node_modules/sqlstring/lib/SqlString.js
@@ -44,9 +44,11 @@ SqlString.escape = function escape(val, stringifyObjects, timeZone) {
         return SqlString.dateToString(val, timeZone || 'local');
       } else if (Array.isArray(val)) {
         return SqlString.arrayToList(val, timeZone);
-      } else if (Buffer.isBuffer(val)) {
-        return SqlString.bufferToString(val);
-      } else if (typeof val.toSqlString === 'function') {
+      }
+      //  else if (Buffer.isBuffer(val)) {
+      //   return SqlString.bufferToString(val);
+      // }
+       else if (typeof val.toSqlString === 'function') {
         return String(val.toSqlString());
       } else if (stringifyObjects) {
         return escapeString(val.toString());
