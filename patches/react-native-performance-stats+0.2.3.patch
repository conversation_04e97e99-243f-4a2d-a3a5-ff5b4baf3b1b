diff --git a/node_modules/react-native-performance-stats/android/build/.transforms/b19cc589510ebf4c37336beca451c699/results.bin b/node_modules/react-native-performance-stats/android/build/.transforms/b19cc589510ebf4c37336beca451c699/results.bin
new file mode 100644
index 0000000..0d259dd
--- /dev/null
+++ b/node_modules/react-native-performance-stats/android/build/.transforms/b19cc589510ebf4c37336beca451c699/results.bin
@@ -0,0 +1 @@
+o/classes
diff --git a/node_modules/react-native-performance-stats/android/build/.transforms/b19cc589510ebf4c37336beca451c699/transformed/classes/classes_dex/classes.dex b/node_modules/react-native-performance-stats/android/build/.transforms/b19cc589510ebf4c37336beca451c699/transformed/classes/classes_dex/classes.dex
new file mode 100644
index 0000000..143d216
Binary files /dev/null and b/node_modules/react-native-performance-stats/android/build/.transforms/b19cc589510ebf4c37336beca451c699/transformed/classes/classes_dex/classes.dex differ
diff --git a/node_modules/react-native-performance-stats/android/build/.transforms/cf309b7b02c54fe2cb2d4d715a0d7581/results.bin b/node_modules/react-native-performance-stats/android/build/.transforms/cf309b7b02c54fe2cb2d4d715a0d7581/results.bin
new file mode 100644
index 0000000..1ed65e0
--- /dev/null
+++ b/node_modules/react-native-performance-stats/android/build/.transforms/cf309b7b02c54fe2cb2d4d715a0d7581/results.bin
@@ -0,0 +1 @@
+i/
diff --git a/node_modules/react-native-performance-stats/android/build/.transforms/d8fe75be5ed5476b1d350107c5c8cded/results.bin b/node_modules/react-native-performance-stats/android/build/.transforms/d8fe75be5ed5476b1d350107c5c8cded/results.bin
new file mode 100644
index 0000000..0d259dd
--- /dev/null
+++ b/node_modules/react-native-performance-stats/android/build/.transforms/d8fe75be5ed5476b1d350107c5c8cded/results.bin
@@ -0,0 +1 @@
+o/classes
diff --git a/node_modules/react-native-performance-stats/android/build/.transforms/d8fe75be5ed5476b1d350107c5c8cded/transformed/classes/classes.dex b/node_modules/react-native-performance-stats/android/build/.transforms/d8fe75be5ed5476b1d350107c5c8cded/transformed/classes/classes.dex
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-performance-stats/android/build/.transforms/deb2359743a8f5bd000c4c32155f4665/results.bin b/node_modules/react-native-performance-stats/android/build/.transforms/deb2359743a8f5bd000c4c32155f4665/results.bin
new file mode 100644
index 0000000..5ff383e
--- /dev/null
+++ b/node_modules/react-native-performance-stats/android/build/.transforms/deb2359743a8f5bd000c4c32155f4665/results.bin
@@ -0,0 +1 @@
+o/debug
diff --git a/node_modules/react-native-performance-stats/android/build/.transforms/deb2359743a8f5bd000c4c32155f4665/transformed/debug/nl/skillnation/perfstats/BuildConfig.dex b/node_modules/react-native-performance-stats/android/build/.transforms/deb2359743a8f5bd000c4c32155f4665/transformed/debug/nl/skillnation/perfstats/BuildConfig.dex
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-performance-stats/android/build/.transforms/deb2359743a8f5bd000c4c32155f4665/transformed/debug/nl/skillnation/perfstats/PerformanceStatsImpl$1.dex b/node_modules/react-native-performance-stats/android/build/.transforms/deb2359743a8f5bd000c4c32155f4665/transformed/debug/nl/skillnation/perfstats/PerformanceStatsImpl$1.dex
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-performance-stats/android/build/.transforms/deb2359743a8f5bd000c4c32155f4665/transformed/debug/nl/skillnation/perfstats/PerformanceStatsImpl$StatsMonitorRunnable.dex b/node_modules/react-native-performance-stats/android/build/.transforms/deb2359743a8f5bd000c4c32155f4665/transformed/debug/nl/skillnation/perfstats/PerformanceStatsImpl$StatsMonitorRunnable.dex
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-performance-stats/android/build/.transforms/deb2359743a8f5bd000c4c32155f4665/transformed/debug/nl/skillnation/perfstats/PerformanceStatsImpl.dex b/node_modules/react-native-performance-stats/android/build/.transforms/deb2359743a8f5bd000c4c32155f4665/transformed/debug/nl/skillnation/perfstats/PerformanceStatsImpl.dex
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-performance-stats/android/build/.transforms/deb2359743a8f5bd000c4c32155f4665/transformed/debug/nl/skillnation/perfstats/PerformanceStatsModule.dex b/node_modules/react-native-performance-stats/android/build/.transforms/deb2359743a8f5bd000c4c32155f4665/transformed/debug/nl/skillnation/perfstats/PerformanceStatsModule.dex
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-performance-stats/android/build/.transforms/deb2359743a8f5bd000c4c32155f4665/transformed/debug/nl/skillnation/perfstats/PerformanceStatsPackage.dex b/node_modules/react-native-performance-stats/android/build/.transforms/deb2359743a8f5bd000c4c32155f4665/transformed/debug/nl/skillnation/perfstats/PerformanceStatsPackage.dex
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-performance-stats/android/build/.transforms/deb2359743a8f5bd000c4c32155f4665/transformed/desugar_graph.bin b/node_modules/react-native-performance-stats/android/build/.transforms/deb2359743a8f5bd000c4c32155f4665/transformed/desugar_graph.bin
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-performance-stats/android/build/.transforms/e2260cf3c8e3dd00a26b3084d7768bc5/results.bin b/node_modules/react-native-performance-stats/android/build/.transforms/e2260cf3c8e3dd00a26b3084d7768bc5/results.bin
new file mode 100644
index 0000000..5ff383e
--- /dev/null
+++ b/node_modules/react-native-performance-stats/android/build/.transforms/e2260cf3c8e3dd00a26b3084d7768bc5/results.bin
@@ -0,0 +1 @@
+o/debug
diff --git a/node_modules/react-native-performance-stats/android/build/.transforms/e2260cf3c8e3dd00a26b3084d7768bc5/transformed/debug/debug_dex/nl/skillnation/perfstats/BuildConfig.dex b/node_modules/react-native-performance-stats/android/build/.transforms/e2260cf3c8e3dd00a26b3084d7768bc5/transformed/debug/debug_dex/nl/skillnation/perfstats/BuildConfig.dex
new file mode 100644
index 0000000..35f4254
Binary files /dev/null and b/node_modules/react-native-performance-stats/android/build/.transforms/e2260cf3c8e3dd00a26b3084d7768bc5/transformed/debug/debug_dex/nl/skillnation/perfstats/BuildConfig.dex differ
diff --git a/node_modules/react-native-performance-stats/android/build/.transforms/e2260cf3c8e3dd00a26b3084d7768bc5/transformed/debug/debug_dex/nl/skillnation/perfstats/PerformanceStatsImpl$StatsMonitorRunnable.dex b/node_modules/react-native-performance-stats/android/build/.transforms/e2260cf3c8e3dd00a26b3084d7768bc5/transformed/debug/debug_dex/nl/skillnation/perfstats/PerformanceStatsImpl$StatsMonitorRunnable.dex
new file mode 100644
index 0000000..877cd40
Binary files /dev/null and b/node_modules/react-native-performance-stats/android/build/.transforms/e2260cf3c8e3dd00a26b3084d7768bc5/transformed/debug/debug_dex/nl/skillnation/perfstats/PerformanceStatsImpl$StatsMonitorRunnable.dex differ
diff --git a/node_modules/react-native-performance-stats/android/build/.transforms/e2260cf3c8e3dd00a26b3084d7768bc5/transformed/debug/debug_dex/nl/skillnation/perfstats/PerformanceStatsImpl.dex b/node_modules/react-native-performance-stats/android/build/.transforms/e2260cf3c8e3dd00a26b3084d7768bc5/transformed/debug/debug_dex/nl/skillnation/perfstats/PerformanceStatsImpl.dex
new file mode 100644
index 0000000..7354ad5
Binary files /dev/null and b/node_modules/react-native-performance-stats/android/build/.transforms/e2260cf3c8e3dd00a26b3084d7768bc5/transformed/debug/debug_dex/nl/skillnation/perfstats/PerformanceStatsImpl.dex differ
diff --git a/node_modules/react-native-performance-stats/android/build/.transforms/e2260cf3c8e3dd00a26b3084d7768bc5/transformed/debug/debug_dex/nl/skillnation/perfstats/PerformanceStatsModule.dex b/node_modules/react-native-performance-stats/android/build/.transforms/e2260cf3c8e3dd00a26b3084d7768bc5/transformed/debug/debug_dex/nl/skillnation/perfstats/PerformanceStatsModule.dex
new file mode 100644
index 0000000..fbe882c
Binary files /dev/null and b/node_modules/react-native-performance-stats/android/build/.transforms/e2260cf3c8e3dd00a26b3084d7768bc5/transformed/debug/debug_dex/nl/skillnation/perfstats/PerformanceStatsModule.dex differ
diff --git a/node_modules/react-native-performance-stats/android/build/.transforms/e2260cf3c8e3dd00a26b3084d7768bc5/transformed/debug/debug_dex/nl/skillnation/perfstats/PerformanceStatsPackage.dex b/node_modules/react-native-performance-stats/android/build/.transforms/e2260cf3c8e3dd00a26b3084d7768bc5/transformed/debug/debug_dex/nl/skillnation/perfstats/PerformanceStatsPackage.dex
new file mode 100644
index 0000000..192dd63
Binary files /dev/null and b/node_modules/react-native-performance-stats/android/build/.transforms/e2260cf3c8e3dd00a26b3084d7768bc5/transformed/debug/debug_dex/nl/skillnation/perfstats/PerformanceStatsPackage.dex differ
diff --git a/node_modules/react-native-performance-stats/android/build/.transforms/e2260cf3c8e3dd00a26b3084d7768bc5/transformed/debug/desugar_graph.bin b/node_modules/react-native-performance-stats/android/build/.transforms/e2260cf3c8e3dd00a26b3084d7768bc5/transformed/debug/desugar_graph.bin
new file mode 100644
index 0000000..9269cc3
Binary files /dev/null and b/node_modules/react-native-performance-stats/android/build/.transforms/e2260cf3c8e3dd00a26b3084d7768bc5/transformed/debug/desugar_graph.bin differ
diff --git a/node_modules/react-native-performance-stats/android/build/generated/source/buildConfig/debug/nl/skillnation/perfstats/BuildConfig.java b/node_modules/react-native-performance-stats/android/build/generated/source/buildConfig/debug/nl/skillnation/perfstats/BuildConfig.java
new file mode 100644
index 0000000..df1fcc1
--- /dev/null
+++ b/node_modules/react-native-performance-stats/android/build/generated/source/buildConfig/debug/nl/skillnation/perfstats/BuildConfig.java
@@ -0,0 +1,12 @@
+/**
+ * Automatically generated file. DO NOT MODIFY
+ */
+package nl.skillnation.perfstats;
+
+public final class BuildConfig {
+  public static final boolean DEBUG = Boolean.parseBoolean("true");
+  public static final String LIBRARY_PACKAGE_NAME = "nl.skillnation.perfstats";
+  public static final String BUILD_TYPE = "debug";
+  // Field from default config.
+  public static final boolean IS_NEW_ARCHITECTURE_ENABLED = false;
+}
diff --git a/node_modules/react-native-performance-stats/android/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/AndroidManifest.xml b/node_modules/react-native-performance-stats/android/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/AndroidManifest.xml
new file mode 100644
index 0000000..80df371
--- /dev/null
+++ b/node_modules/react-native-performance-stats/android/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/AndroidManifest.xml
@@ -0,0 +1,7 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    package="nl.skillnation.perfstats" >
+
+    <uses-sdk android:minSdkVersion="23" />
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/react-native-performance-stats/android/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/output-metadata.json b/node_modules/react-native-performance-stats/android/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/output-metadata.json
new file mode 100644
index 0000000..1db34ff
--- /dev/null
+++ b/node_modules/react-native-performance-stats/android/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/output-metadata.json
@@ -0,0 +1,18 @@
+{
+  "version": 3,
+  "artifactType": {
+    "type": "AAPT_FRIENDLY_MERGED_MANIFESTS",
+    "kind": "Directory"
+  },
+  "applicationId": "nl.skillnation.perfstats",
+  "variantName": "debug",
+  "elements": [
+    {
+      "type": "SINGLE",
+      "filters": [],
+      "attributes": [],
+      "outputFile": "AndroidManifest.xml"
+    }
+  ],
+  "elementType": "File"
+}
\ No newline at end of file
diff --git a/node_modules/react-native-performance-stats/android/build/intermediates/aar_main_jar/debug/classes.jar b/node_modules/react-native-performance-stats/android/build/intermediates/aar_main_jar/debug/classes.jar
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-performance-stats/android/build/intermediates/aar_metadata/debug/aar-metadata.properties b/node_modules/react-native-performance-stats/android/build/intermediates/aar_metadata/debug/aar-metadata.properties
new file mode 100644
index 0000000..1211b1e
--- /dev/null
+++ b/node_modules/react-native-performance-stats/android/build/intermediates/aar_metadata/debug/aar-metadata.properties
@@ -0,0 +1,6 @@
+aarFormatVersion=1.0
+aarMetadataVersion=1.0
+minCompileSdk=1
+minCompileSdkExtension=0
+minAndroidGradlePluginVersion=1.0.0
+coreLibraryDesugaringEnabled=false
diff --git a/node_modules/react-native-performance-stats/android/build/intermediates/annotation_processor_list/debug/annotationProcessors.json b/node_modules/react-native-performance-stats/android/build/intermediates/annotation_processor_list/debug/annotationProcessors.json
new file mode 100644
index 0000000..9e26dfe
--- /dev/null
+++ b/node_modules/react-native-performance-stats/android/build/intermediates/annotation_processor_list/debug/annotationProcessors.json
@@ -0,0 +1 @@
+{}
\ No newline at end of file
diff --git a/node_modules/react-native-performance-stats/android/build/intermediates/annotations_typedef_file/debug/typedefs.txt b/node_modules/react-native-performance-stats/android/build/intermediates/annotations_typedef_file/debug/typedefs.txt
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-performance-stats/android/build/intermediates/compile_library_classes_jar/debug/classes.jar b/node_modules/react-native-performance-stats/android/build/intermediates/compile_library_classes_jar/debug/classes.jar
new file mode 100644
index 0000000..3ca6443
Binary files /dev/null and b/node_modules/react-native-performance-stats/android/build/intermediates/compile_library_classes_jar/debug/classes.jar differ
diff --git a/node_modules/react-native-performance-stats/android/build/intermediates/compile_r_class_jar/debug/R.jar b/node_modules/react-native-performance-stats/android/build/intermediates/compile_r_class_jar/debug/R.jar
new file mode 100644
index 0000000..9a6530b
Binary files /dev/null and b/node_modules/react-native-performance-stats/android/build/intermediates/compile_r_class_jar/debug/R.jar differ
diff --git a/node_modules/react-native-performance-stats/android/build/intermediates/compile_symbol_list/debug/R.txt b/node_modules/react-native-performance-stats/android/build/intermediates/compile_symbol_list/debug/R.txt
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-performance-stats/android/build/intermediates/incremental/debug-mergeJavaRes/merge-state b/node_modules/react-native-performance-stats/android/build/intermediates/incremental/debug-mergeJavaRes/merge-state
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-performance-stats/android/build/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties b/node_modules/react-native-performance-stats/android/build/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties
new file mode 100644
index 0000000..c067c1c
--- /dev/null
+++ b/node_modules/react-native-performance-stats/android/build/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties
@@ -0,0 +1 @@
+#Fri Aug 16 17:26:14 CST 2024
diff --git a/node_modules/react-native-performance-stats/android/build/intermediates/incremental/debug/packageDebugResources/merger.xml b/node_modules/react-native-performance-stats/android/build/intermediates/incremental/debug/packageDebugResources/merger.xml
new file mode 100644
index 0000000..0621b13
--- /dev/null
+++ b/node_modules/react-native-performance-stats/android/build/intermediates/incremental/debug/packageDebugResources/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/workshop/biluowa/peru-app/node_modules/react-native-performance-stats/android/src/main/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/workshop/biluowa/peru-app/node_modules/react-native-performance-stats/android/src/main/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/workshop/biluowa/peru-app/node_modules/react-native-performance-stats/android/src/debug/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/workshop/biluowa/peru-app/node_modules/react-native-performance-stats/android/src/debug/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/workshop/biluowa/peru-app/node_modules/react-native-performance-stats/android/build/generated/res/resValues/debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/workshop/biluowa/peru-app/node_modules/react-native-performance-stats/android/build/generated/res/resValues/debug"/></dataSet><mergedItems/></merger>
\ No newline at end of file
diff --git a/node_modules/react-native-performance-stats/android/build/intermediates/incremental/mergeDebugJniLibFolders/merger.xml b/node_modules/react-native-performance-stats/android/build/intermediates/incremental/mergeDebugJniLibFolders/merger.xml
new file mode 100644
index 0000000..1bea234
--- /dev/null
+++ b/node_modules/react-native-performance-stats/android/build/intermediates/incremental/mergeDebugJniLibFolders/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/workshop/biluowa/peru-app/node_modules/react-native-performance-stats/android/src/main/jniLibs"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/workshop/biluowa/peru-app/node_modules/react-native-performance-stats/android/src/debug/jniLibs"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/react-native-performance-stats/android/build/intermediates/incremental/mergeDebugShaders/merger.xml b/node_modules/react-native-performance-stats/android/build/intermediates/incremental/mergeDebugShaders/merger.xml
new file mode 100644
index 0000000..7f6b9b9
--- /dev/null
+++ b/node_modules/react-native-performance-stats/android/build/intermediates/incremental/mergeDebugShaders/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/workshop/biluowa/peru-app/node_modules/react-native-performance-stats/android/src/main/shaders"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/workshop/biluowa/peru-app/node_modules/react-native-performance-stats/android/src/debug/shaders"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/react-native-performance-stats/android/build/intermediates/incremental/packageDebugAssets/merger.xml b/node_modules/react-native-performance-stats/android/build/intermediates/incremental/packageDebugAssets/merger.xml
new file mode 100644
index 0000000..9f424e8
--- /dev/null
+++ b/node_modules/react-native-performance-stats/android/build/intermediates/incremental/packageDebugAssets/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/workshop/biluowa/peru-app/node_modules/react-native-performance-stats/android/src/main/assets"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/workshop/biluowa/peru-app/node_modules/react-native-performance-stats/android/src/debug/assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/workshop/biluowa/peru-app/node_modules/react-native-performance-stats/android/build/intermediates/shader_assets/debug/out"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/react-native-performance-stats/android/build/intermediates/javac/debug/classes/nl/skillnation/perfstats/BuildConfig.class b/node_modules/react-native-performance-stats/android/build/intermediates/javac/debug/classes/nl/skillnation/perfstats/BuildConfig.class
new file mode 100644
index 0000000..90037c7
Binary files /dev/null and b/node_modules/react-native-performance-stats/android/build/intermediates/javac/debug/classes/nl/skillnation/perfstats/BuildConfig.class differ
diff --git a/node_modules/react-native-performance-stats/android/build/intermediates/javac/debug/classes/nl/skillnation/perfstats/PerformanceStatsImpl$StatsMonitorRunnable.class b/node_modules/react-native-performance-stats/android/build/intermediates/javac/debug/classes/nl/skillnation/perfstats/PerformanceStatsImpl$StatsMonitorRunnable.class
new file mode 100644
index 0000000..e24cc3a
Binary files /dev/null and b/node_modules/react-native-performance-stats/android/build/intermediates/javac/debug/classes/nl/skillnation/perfstats/PerformanceStatsImpl$StatsMonitorRunnable.class differ
diff --git a/node_modules/react-native-performance-stats/android/build/intermediates/javac/debug/classes/nl/skillnation/perfstats/PerformanceStatsImpl.class b/node_modules/react-native-performance-stats/android/build/intermediates/javac/debug/classes/nl/skillnation/perfstats/PerformanceStatsImpl.class
new file mode 100644
index 0000000..bac89b0
Binary files /dev/null and b/node_modules/react-native-performance-stats/android/build/intermediates/javac/debug/classes/nl/skillnation/perfstats/PerformanceStatsImpl.class differ
diff --git a/node_modules/react-native-performance-stats/android/build/intermediates/javac/debug/classes/nl/skillnation/perfstats/PerformanceStatsModule.class b/node_modules/react-native-performance-stats/android/build/intermediates/javac/debug/classes/nl/skillnation/perfstats/PerformanceStatsModule.class
new file mode 100644
index 0000000..e71054c
Binary files /dev/null and b/node_modules/react-native-performance-stats/android/build/intermediates/javac/debug/classes/nl/skillnation/perfstats/PerformanceStatsModule.class differ
diff --git a/node_modules/react-native-performance-stats/android/build/intermediates/javac/debug/classes/nl/skillnation/perfstats/PerformanceStatsPackage.class b/node_modules/react-native-performance-stats/android/build/intermediates/javac/debug/classes/nl/skillnation/perfstats/PerformanceStatsPackage.class
new file mode 100644
index 0000000..ffe692b
Binary files /dev/null and b/node_modules/react-native-performance-stats/android/build/intermediates/javac/debug/classes/nl/skillnation/perfstats/PerformanceStatsPackage.class differ
diff --git a/node_modules/react-native-performance-stats/android/build/intermediates/local_only_symbol_list/debug/R-def.txt b/node_modules/react-native-performance-stats/android/build/intermediates/local_only_symbol_list/debug/R-def.txt
new file mode 100644
index 0000000..78ac5b8
--- /dev/null
+++ b/node_modules/react-native-performance-stats/android/build/intermediates/local_only_symbol_list/debug/R-def.txt
@@ -0,0 +1,2 @@
+R_DEF: Internal format may change without notice
+local
diff --git a/node_modules/react-native-performance-stats/android/build/intermediates/manifest_merge_blame_file/debug/manifest-merger-blame-debug-report.txt b/node_modules/react-native-performance-stats/android/build/intermediates/manifest_merge_blame_file/debug/manifest-merger-blame-debug-report.txt
new file mode 100644
index 0000000..60e7574
--- /dev/null
+++ b/node_modules/react-native-performance-stats/android/build/intermediates/manifest_merge_blame_file/debug/manifest-merger-blame-debug-report.txt
@@ -0,0 +1,7 @@
+1<?xml version="1.0" encoding="utf-8"?>
+2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+3    package="nl.skillnation.perfstats" >
+4
+5    <uses-sdk android:minSdkVersion="23" />
+6
+7</manifest>
diff --git a/node_modules/react-native-performance-stats/android/build/intermediates/merged_java_res/debug/feature-react-native-performance-stats.jar b/node_modules/react-native-performance-stats/android/build/intermediates/merged_java_res/debug/feature-react-native-performance-stats.jar
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-performance-stats/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml b/node_modules/react-native-performance-stats/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml
new file mode 100644
index 0000000..80df371
--- /dev/null
+++ b/node_modules/react-native-performance-stats/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml
@@ -0,0 +1,7 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    package="nl.skillnation.perfstats" >
+
+    <uses-sdk android:minSdkVersion="23" />
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/react-native-performance-stats/android/build/intermediates/navigation_json/debug/navigation.json b/node_modules/react-native-performance-stats/android/build/intermediates/navigation_json/debug/navigation.json
new file mode 100644
index 0000000..0637a08
--- /dev/null
+++ b/node_modules/react-native-performance-stats/android/build/intermediates/navigation_json/debug/navigation.json
@@ -0,0 +1 @@
+[]
\ No newline at end of file
diff --git a/node_modules/react-native-performance-stats/android/build/intermediates/packaged_manifests/debug/output-metadata.json b/node_modules/react-native-performance-stats/android/build/intermediates/packaged_manifests/debug/output-metadata.json
new file mode 100644
index 0000000..1e2d868
--- /dev/null
+++ b/node_modules/react-native-performance-stats/android/build/intermediates/packaged_manifests/debug/output-metadata.json
@@ -0,0 +1,18 @@
+{
+  "version": 3,
+  "artifactType": {
+    "type": "PACKAGED_MANIFESTS",
+    "kind": "Directory"
+  },
+  "applicationId": "nl.skillnation.perfstats",
+  "variantName": "debug",
+  "elements": [
+    {
+      "type": "SINGLE",
+      "filters": [],
+      "attributes": [],
+      "outputFile": "../../merged_manifest/debug/AndroidManifest.xml"
+    }
+  ],
+  "elementType": "File"
+}
\ No newline at end of file
diff --git a/node_modules/react-native-performance-stats/android/build/intermediates/runtime_library_classes_dir/debug/nl/skillnation/perfstats/BuildConfig.class b/node_modules/react-native-performance-stats/android/build/intermediates/runtime_library_classes_dir/debug/nl/skillnation/perfstats/BuildConfig.class
new file mode 100644
index 0000000..90037c7
Binary files /dev/null and b/node_modules/react-native-performance-stats/android/build/intermediates/runtime_library_classes_dir/debug/nl/skillnation/perfstats/BuildConfig.class differ
diff --git a/node_modules/react-native-performance-stats/android/build/intermediates/runtime_library_classes_dir/debug/nl/skillnation/perfstats/PerformanceStatsImpl$StatsMonitorRunnable.class b/node_modules/react-native-performance-stats/android/build/intermediates/runtime_library_classes_dir/debug/nl/skillnation/perfstats/PerformanceStatsImpl$StatsMonitorRunnable.class
new file mode 100644
index 0000000..e24cc3a
Binary files /dev/null and b/node_modules/react-native-performance-stats/android/build/intermediates/runtime_library_classes_dir/debug/nl/skillnation/perfstats/PerformanceStatsImpl$StatsMonitorRunnable.class differ
diff --git a/node_modules/react-native-performance-stats/android/build/intermediates/runtime_library_classes_dir/debug/nl/skillnation/perfstats/PerformanceStatsImpl.class b/node_modules/react-native-performance-stats/android/build/intermediates/runtime_library_classes_dir/debug/nl/skillnation/perfstats/PerformanceStatsImpl.class
new file mode 100644
index 0000000..bac89b0
Binary files /dev/null and b/node_modules/react-native-performance-stats/android/build/intermediates/runtime_library_classes_dir/debug/nl/skillnation/perfstats/PerformanceStatsImpl.class differ
diff --git a/node_modules/react-native-performance-stats/android/build/intermediates/runtime_library_classes_dir/debug/nl/skillnation/perfstats/PerformanceStatsModule.class b/node_modules/react-native-performance-stats/android/build/intermediates/runtime_library_classes_dir/debug/nl/skillnation/perfstats/PerformanceStatsModule.class
new file mode 100644
index 0000000..e71054c
Binary files /dev/null and b/node_modules/react-native-performance-stats/android/build/intermediates/runtime_library_classes_dir/debug/nl/skillnation/perfstats/PerformanceStatsModule.class differ
diff --git a/node_modules/react-native-performance-stats/android/build/intermediates/runtime_library_classes_dir/debug/nl/skillnation/perfstats/PerformanceStatsPackage.class b/node_modules/react-native-performance-stats/android/build/intermediates/runtime_library_classes_dir/debug/nl/skillnation/perfstats/PerformanceStatsPackage.class
new file mode 100644
index 0000000..ffe692b
Binary files /dev/null and b/node_modules/react-native-performance-stats/android/build/intermediates/runtime_library_classes_dir/debug/nl/skillnation/perfstats/PerformanceStatsPackage.class differ
diff --git a/node_modules/react-native-performance-stats/android/build/intermediates/runtime_library_classes_jar/debug/classes.jar b/node_modules/react-native-performance-stats/android/build/intermediates/runtime_library_classes_jar/debug/classes.jar
new file mode 100644
index 0000000..9b9a770
Binary files /dev/null and b/node_modules/react-native-performance-stats/android/build/intermediates/runtime_library_classes_jar/debug/classes.jar differ
diff --git a/node_modules/react-native-performance-stats/android/build/intermediates/symbol_list_with_package_name/debug/package-aware-r.txt b/node_modules/react-native-performance-stats/android/build/intermediates/symbol_list_with_package_name/debug/package-aware-r.txt
new file mode 100644
index 0000000..d28f23b
--- /dev/null
+++ b/node_modules/react-native-performance-stats/android/build/intermediates/symbol_list_with_package_name/debug/package-aware-r.txt
@@ -0,0 +1 @@
+nl.skillnation.perfstats
diff --git a/node_modules/react-native-performance-stats/android/build/intermediates/variant_model/debug/out b/node_modules/react-native-performance-stats/android/build/intermediates/variant_model/debug/out
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-performance-stats/android/build/outputs/aar/react-native-performance-stats-debug.aar b/node_modules/react-native-performance-stats/android/build/outputs/aar/react-native-performance-stats-debug.aar
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-performance-stats/android/build/outputs/logs/manifest-merger-debug-report.txt b/node_modules/react-native-performance-stats/android/build/outputs/logs/manifest-merger-debug-report.txt
new file mode 100644
index 0000000..10b2c02
--- /dev/null
+++ b/node_modules/react-native-performance-stats/android/build/outputs/logs/manifest-merger-debug-report.txt
@@ -0,0 +1,17 @@
+-- Merging decision tree log ---
+manifest
+ADDED from /Users/<USER>/workshop/biluowa/peru-app/node_modules/react-native-performance-stats/android/src/main/AndroidManifest.xml:1:1-3:12
+INJECTED from /Users/<USER>/workshop/biluowa/peru-app/node_modules/react-native-performance-stats/android/src/main/AndroidManifest.xml:1:1-3:12
+	package
+		ADDED from /Users/<USER>/workshop/biluowa/peru-app/node_modules/react-native-performance-stats/android/src/main/AndroidManifest.xml:2:11-45
+		INJECTED from /Users/<USER>/workshop/biluowa/peru-app/node_modules/react-native-performance-stats/android/src/main/AndroidManifest.xml
+	xmlns:android
+		ADDED from /Users/<USER>/workshop/biluowa/peru-app/node_modules/react-native-performance-stats/android/src/main/AndroidManifest.xml:1:11-69
+uses-sdk
+INJECTED from /Users/<USER>/workshop/biluowa/peru-app/node_modules/react-native-performance-stats/android/src/main/AndroidManifest.xml reason: use-sdk injection requested
+INJECTED from /Users/<USER>/workshop/biluowa/peru-app/node_modules/react-native-performance-stats/android/src/main/AndroidManifest.xml
+INJECTED from /Users/<USER>/workshop/biluowa/peru-app/node_modules/react-native-performance-stats/android/src/main/AndroidManifest.xml
+	android:targetSdkVersion
+		INJECTED from /Users/<USER>/workshop/biluowa/peru-app/node_modules/react-native-performance-stats/android/src/main/AndroidManifest.xml
+	android:minSdkVersion
+		INJECTED from /Users/<USER>/workshop/biluowa/peru-app/node_modules/react-native-performance-stats/android/src/main/AndroidManifest.xml
diff --git a/node_modules/react-native-performance-stats/android/build/tmp/compileDebugJavaWithJavac/previous-compilation-data.bin b/node_modules/react-native-performance-stats/android/build/tmp/compileDebugJavaWithJavac/previous-compilation-data.bin
new file mode 100644
index 0000000..6a029b7
Binary files /dev/null and b/node_modules/react-native-performance-stats/android/build/tmp/compileDebugJavaWithJavac/previous-compilation-data.bin differ
diff --git a/node_modules/react-native-performance-stats/android/src/main/java/nl/skillnation/perfstats/PerformanceStatsImpl.java b/node_modules/react-native-performance-stats/android/src/main/java/nl/skillnation/perfstats/PerformanceStatsImpl.java
index 621ef85..e62413c 100644
--- a/node_modules/react-native-performance-stats/android/src/main/java/nl/skillnation/perfstats/PerformanceStatsImpl.java
+++ b/node_modules/react-native-performance-stats/android/src/main/java/nl/skillnation/perfstats/PerformanceStatsImpl.java
@@ -1,6 +1,8 @@
 package nl.skillnation.perfstats;
 
 
+import android.app.ActivityManager;
+import android.content.Context;
 import android.os.Debug;
 import android.os.Handler;
 
@@ -52,13 +54,14 @@ public class PerformanceStatsImpl {
         mStatsMonitorRunnable.stop();
     }
 
-    private void setCurrentStats(double uiFPS, double jsFPS, int framesDropped, int shutters, double usedRam, double usedCpu) {
+    private void setCurrentStats(double uiFPS, double jsFPS, int framesDropped, int shutters, double usedRam, double availableRamRate, double usedCpu) {
         WritableMap state = Arguments.createMap();
         state.putDouble("uiFps", uiFPS);
         state.putDouble("jsFps", jsFPS);
         state.putInt("framesDropped", framesDropped);
         state.putInt("shutters", shutters);
         state.putDouble("usedRam", usedRam);
+        state.putDouble("availableRamRate", availableRamRate);
         state.putDouble("usedCpu", usedCpu);
 
         sendEvent(state);
@@ -104,12 +107,15 @@ public class PerformanceStatsImpl {
             }
             double usedRam = getUsedRam();
 
+            double availableRam = getAvailableRamRate();
+
             setCurrentStats(
                     fps,
                     jsFps,
                     mTotalFramesDropped,
                     mTotal4PlusFrameStutters,
                     usedRam,
+                    availableRam,
                     cpuUsage
             );
             mFrameCallback.reset();
@@ -136,6 +142,13 @@ public class PerformanceStatsImpl {
             return memoryInfo.getTotalPss() / 1000D;
         }
 
+        private double getAvailableRamRate() {
+            ActivityManager.MemoryInfo memoryInfo = new ActivityManager.MemoryInfo();
+            ActivityManager activityManager = (ActivityManager) reactContext.getSystemService(Context.ACTIVITY_SERVICE);
+            activityManager.getMemoryInfo(memoryInfo);
+            return (double) (memoryInfo.totalMem - memoryInfo.availMem) / memoryInfo.totalMem;
+        }
+
         private double getUsedCPU() throws IOException {
             String[] commands = { "top", "-n", "1", "-q", "-oCMDLINE,%CPU", "-s2", "-b" };
             BufferedReader reader = new BufferedReader(
diff --git a/node_modules/react-native-performance-stats/android/src/main/java/nl/skillnation/perfstats/PerformanceStatsPackage.java b/node_modules/react-native-performance-stats/android/src/main/java/nl/skillnation/perfstats/PerformanceStatsPackage.java
index 055cf03..7fb83e2 100644
--- a/node_modules/react-native-performance-stats/android/src/main/java/nl/skillnation/perfstats/PerformanceStatsPackage.java
+++ b/node_modules/react-native-performance-stats/android/src/main/java/nl/skillnation/perfstats/PerformanceStatsPackage.java
@@ -6,7 +6,6 @@ import com.facebook.react.bridge.ReactApplicationContext;
 import com.facebook.react.module.model.ReactModuleInfo;
 import com.facebook.react.module.model.ReactModuleInfoProvider;
 import com.facebook.react.TurboReactPackage;
-import com.facebook.react.uimanager.ViewManager;
 
 import java.util.ArrayList;
 import java.util.Collections;
diff --git a/node_modules/react-native-performance-stats/android/src/oldarch/java/nl/skillnation/perfstats/PerformanceStatsModule.java b/node_modules/react-native-performance-stats/android/src/oldarch/java/nl/skillnation/perfstats/PerformanceStatsModule.java
index 272d138..f3d7322 100644
--- a/node_modules/react-native-performance-stats/android/src/oldarch/java/nl/skillnation/perfstats/PerformanceStatsModule.java
+++ b/node_modules/react-native-performance-stats/android/src/oldarch/java/nl/skillnation/perfstats/PerformanceStatsModule.java
@@ -39,4 +39,12 @@ public class PerformanceStatsModule extends ReactContextBaseJavaModule {
     public void removeListeners(Integer count) {
         // Remove upstream listeners, stop unnecessary background tasks
     }
+
+    public void invalidate() {
+        super.invalidate();
+        try {
+            performanceStats.stop();
+        } catch (Exception exception) {
+        }
+    }
 }
\ No newline at end of file
diff --git a/node_modules/react-native-performance-stats/types.d.ts b/node_modules/react-native-performance-stats/types.d.ts
index ab218fc..e1b023a 100644
--- a/node_modules/react-native-performance-stats/types.d.ts
+++ b/node_modules/react-native-performance-stats/types.d.ts
@@ -7,6 +7,7 @@ export type PerformanceStatsData = {
     framesDropped?: number;
     usedCpu: number;
     usedRam: number;
+    availableRamRate: number;
 }
 
 type PerformanceStatsModule = {
