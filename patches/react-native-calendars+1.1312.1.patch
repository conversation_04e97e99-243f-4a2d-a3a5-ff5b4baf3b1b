diff --git a/node_modules/react-native-calendars/src/calendar-list/index.js b/node_modules/react-native-calendars/src/calendar-list/index.js
index 6cf1b71..baa5a94 100644
--- a/node_modules/react-native-calendars/src/calendar-list/index.js
+++ b/node_modules/react-native-calendars/src/calendar-list/index.js
@@ -34,16 +34,16 @@ const CalendarList = (props, ref) => {
         }
     }));
     const {
-    /** Calendar props */
-    theme, current, firstDay, markedDates, headerStyle, onMonthChange, onVisibleMonthsChange, 
-    /** CalendarList props */
-    pastScrollRange = PAST_SCROLL_RANGE, futureScrollRange = FUTURE_SCROLL_RANGE, calendarHeight = CALENDAR_HEIGHT, calendarWidth = CALENDAR_WIDTH, calendarStyle, animateScroll = false, showScrollIndicator = false, staticHeader, 
-    /** View props */
-    testID, style: propsStyle, onLayout, removeClippedSubviews, 
-    /** ScrollView props */
-    horizontal = false, pagingEnabled, scrollEnabled = true, nestedScrollEnabled = true, scrollsToTop = false, keyExtractor = (_, index) => String(index), keyboardShouldPersistTaps, onScrollBeginDrag, onScrollEndDrag, onMomentumScrollBegin, onMomentumScrollEnd, 
-    /** FlatList props */
-    contentContainerStyle, onEndReachedThreshold, onEndReached, onHeaderLayout, accessibilityElementsHidden, importantForAccessibility } = props;
+        /** Calendar props */
+        theme, current, firstDay, markedDates, headerStyle, onMonthChange, onVisibleMonthsChange,
+        /** CalendarList props */
+        pastScrollRange = PAST_SCROLL_RANGE, futureScrollRange = FUTURE_SCROLL_RANGE, calendarHeight = CALENDAR_HEIGHT, calendarWidth = CALENDAR_WIDTH, calendarStyle, animateScroll = false, showScrollIndicator = false, staticHeader,
+        /** View props */
+        testID, style: propsStyle, onLayout, removeClippedSubviews,
+        /** ScrollView props */
+        horizontal = false, pagingEnabled, scrollEnabled = true, nestedScrollEnabled = true, scrollsToTop = false, keyExtractor = (_, index) => String(index), keyboardShouldPersistTaps, onScrollBeginDrag, onScrollEndDrag, onMomentumScrollBegin, onMomentumScrollEnd,
+        /** FlatList props */
+        contentContainerStyle, onEndReachedThreshold, onEndReached, onHeaderLayout, accessibilityElementsHidden, importantForAccessibility, extraData } = props;
     const calendarProps = extractCalendarProps(props);
     const headerProps = extractHeaderProps(props);
     const calendarSize = horizontal ? calendarWidth : calendarHeight;
@@ -150,8 +150,10 @@ const CalendarList = (props, ref) => {
         };
     }, []);
     const isDateInRange = useCallback((date) => {
+        // 修复部分机型不触发onViewableItemsChanged导致currentMonth为空，用户必须滑动才会不显示空白的问题
+        const baseMonth = currentMonth || items?.[0] || initialDate.current;
         for (let i = -range.current; i <= range.current; i++) {
-            const newMonth = currentMonth?.clone().addMonths(i, true);
+            const newMonth = baseMonth?.clone().addMonths(i, true);
             if (sameMonth(date, newMonth)) {
                 return true;
             }
@@ -165,15 +167,15 @@ const CalendarList = (props, ref) => {
         const onHeaderLayoutToPass = shouldMeasureHeader.current ? onHeaderLayout : undefined;
         shouldMeasureHeader.current = false;
         return (<CalendarListItem {...calendarProps} testID={testId} markedDates={getMarkedDatesForItem(item)} item={item} style={calendarStyle}
-        // @ts-expect-error - type mismatch - ScrollView's 'horizontal' is nullable
-        horizontal={horizontal} calendarWidth={calendarWidth} calendarHeight={calendarHeight} scrollToMonth={scrollToMonth} visible={isDateInRange(item)} onHeaderLayout={onHeaderLayoutToPass}/>);
+            // @ts-expect-error - type mismatch - ScrollView's 'horizontal' is nullable
+            horizontal={horizontal} calendarWidth={calendarWidth} calendarHeight={calendarHeight} scrollToMonth={scrollToMonth} visible={isDateInRange(item)} onHeaderLayout={onHeaderLayoutToPass} />);
     }, [horizontal, calendarStyle, calendarWidth, testID, getMarkedDatesForItem, isDateInRange, calendarProps]);
     const renderStaticHeader = () => {
         if (shouldUseStaticHeader) {
             const onHeaderLayoutToPass = shouldMeasureHeader.current ? onHeaderLayout : undefined;
             shouldMeasureHeader.current = false;
             return (<CalendarHeader {...headerProps} testID={`${testID}.staticHeader`} style={staticHeaderStyle} month={currentMonth} addMonth={addMonth} onHeaderLayout={onHeaderLayoutToPass} accessibilityElementsHidden={accessibilityElementsHidden} // iOS
-             importantForAccessibility={importantForAccessibility} // Android
+                importantForAccessibility={importantForAccessibility} // Android
             />);
         }
     };
@@ -203,8 +205,8 @@ const CalendarList = (props, ref) => {
         }
     ]);
     return (<View style={style.current.flatListContainer} testID={testID}>
-      <FlatList ref={list} windowSize={shouldFixRTL ? pastScrollRange + futureScrollRange + 1 : undefined} style={listStyle} showsVerticalScrollIndicator={showScrollIndicator} showsHorizontalScrollIndicator={showScrollIndicator} data={items} renderItem={renderItem} getItemLayout={getItemLayout} initialNumToRender={range.current} initialScrollIndex={initialDateIndex} viewabilityConfigCallbackPairs={viewabilityConfigCallbackPairs.current} testID={`${testID}.list`} onLayout={onLayout} removeClippedSubviews={removeClippedSubviews} pagingEnabled={pagingEnabled} scrollEnabled={scrollEnabled} scrollsToTop={scrollsToTop} horizontal={horizontal} keyboardShouldPersistTaps={keyboardShouldPersistTaps} keyExtractor={keyExtractor} onEndReachedThreshold={onEndReachedThreshold} onEndReached={onEndReached} nestedScrollEnabled={nestedScrollEnabled} onMomentumScrollBegin={onMomentumScrollBegin} onMomentumScrollEnd={onMomentumScrollEnd} onScrollBeginDrag={onScrollBeginDrag} onScrollEndDrag={onScrollEndDrag} contentContainerStyle={contentContainerStyle}/>
-      {renderStaticHeader()}
+        <FlatList ref={list} windowSize={shouldFixRTL ? pastScrollRange + futureScrollRange + 1 : undefined} style={listStyle} showsVerticalScrollIndicator={showScrollIndicator} showsHorizontalScrollIndicator={showScrollIndicator} data={items} renderItem={renderItem} getItemLayout={getItemLayout} initialNumToRender={range.current} initialScrollIndex={initialDateIndex > 0 ? initialDateIndex : 0} viewabilityConfigCallbackPairs={viewabilityConfigCallbackPairs.current} testID={`${testID}.list`} onLayout={onLayout} removeClippedSubviews={removeClippedSubviews} pagingEnabled={pagingEnabled} scrollEnabled={scrollEnabled} scrollsToTop={scrollsToTop} horizontal={horizontal} keyboardShouldPersistTaps={keyboardShouldPersistTaps} keyExtractor={keyExtractor} onEndReachedThreshold={onEndReachedThreshold} onEndReached={onEndReached} nestedScrollEnabled={nestedScrollEnabled} onMomentumScrollBegin={onMomentumScrollBegin} onMomentumScrollEnd={onMomentumScrollEnd} onScrollBeginDrag={onScrollBeginDrag} onScrollEndDrag={onScrollEndDrag} contentContainerStyle={contentContainerStyle} extraData={extraData} />
+        {renderStaticHeader()}
     </View>);
 };
 export default forwardRef(CalendarList);
